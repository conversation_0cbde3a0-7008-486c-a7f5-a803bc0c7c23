/**
 * Constructs the full URL for a profile picture
 * @param profilePictureUrl - The profile picture URL from the API
 * @returns The complete URL for the profile picture
 */
export const getProfilePictureUrl = (profilePictureUrl?: string): string | null => {
  if (!profilePictureUrl) {
    return null;
  }

  // If it's already a full URL, return as is
  if (profilePictureUrl.startsWith('http')) {
    return profilePictureUrl;
  }

  // Construct the full URL with the API base URL
  const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000';
  const cleanUrl = profilePictureUrl.startsWith('/') ? profilePictureUrl.substring(1) : profilePictureUrl;
  
  return `${apiUrl}/${cleanUrl}`;
}; 