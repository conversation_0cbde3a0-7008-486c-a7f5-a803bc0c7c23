import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type {
  LiveChat,
  LiveChatCreateData,
  LiveChatMessage,
  LiveChatMessageCreateData,
  LiveChatNotification,
  LiveChatAnalytics,
  LiveChatFilterParams,
  LiveChatPaginatedResponse,
  LiveChatActionResponse,
  LiveChatSendMessageResponse,
  LiveChatMarkNotificationResponse
} from "@/services/api/livechat.types";
import {
  getAllLiveChats,
  getLiveChatByUuid,
  createLiveChat,
  updateLiveChat,
  partialUpdateLiveChat,
  deleteLiveChat,
  closeLiveChat,
  reopenLiveChat,
  getLiveChatMessages,
  sendLiveChatMessage,
  getLiveChatAnalytics,
  getAllLiveChatNotifications,
  getLiveChatNotificationById,
  markLiveChatNotificationAsRead,
  markAllLiveChatNotificationsAsRead,
  filterLiveChatsByStatus,
  searchLiveChats,
  getActiveLiveChats,
  getInactiveLiveChats,
  getLiveChatsSortedByDate,
  getLiveChatsSortedByLastMessage,
  updateChatStatus,
  markAllMessagesAsRead // Added service function
} from "@/services/api/livechat.service";
import { LIVE_CHAT_KEYS } from "./keys";

// Live Chat Queries
export const useLiveChatsQuery = (params?: LiveChatFilterParams) => {
  return useQuery<LiveChatPaginatedResponse<LiveChat>, Error>({
    queryKey: [LIVE_CHAT_KEYS.GET_ALL, params],
    queryFn: () => getAllLiveChats(params),
  });
};

export const useLiveChatQuery = (uuid: string) => {
  return useQuery<LiveChat, Error>({
    queryKey: [LIVE_CHAT_KEYS.GET_BY_UUID, uuid],
    queryFn: () => getLiveChatByUuid(uuid),
    enabled: !!uuid,
  });
};

export const useLiveChatMessagesQuery = (chatUuid: string) => {
  return useQuery<LiveChatMessage[], Error>({
    queryKey: [LIVE_CHAT_KEYS.GET_MESSAGES, chatUuid],
    queryFn: () => getLiveChatMessages(chatUuid),
    enabled: !!chatUuid,
  });
};

export const useLiveChatAnalyticsQuery = () => {
  return useQuery<LiveChatAnalytics, Error>({
    queryKey: [LIVE_CHAT_KEYS.GET_ANALYTICS],
    queryFn: getLiveChatAnalytics,
  });
};

export const useLiveChatNotificationsQuery = () => {
  return useQuery<LiveChatPaginatedResponse<LiveChatNotification>, Error>({
    queryKey: [LIVE_CHAT_KEYS.GET_NOTIFICATIONS],
    queryFn: getAllLiveChatNotifications,
  });
};

export const useLiveChatNotificationQuery = (id: number) => {
  return useQuery<LiveChatNotification, Error>({
    queryKey: [LIVE_CHAT_KEYS.GET_NOTIFICATION_BY_ID, id],
    queryFn: () => getLiveChatNotificationById(id),
    enabled: !!id,
  });
};

// Live Chat Mutations
export const useCreateLiveChatMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<LiveChat, Error, LiveChatCreateData>({
    mutationFn: createLiveChat,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ANALYTICS] });
    },
  });
};

export const useUpdateLiveChatMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    LiveChat,
    Error,
    { uuid: string; data: Partial<LiveChat> }
  >({
    mutationFn: ({ uuid, data }) => updateLiveChat(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_BY_UUID, uuid] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ANALYTICS] });
    },
  });
};

export const usePartialUpdateLiveChatMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    LiveChat,
    Error,
    { uuid: string; data: Partial<LiveChat> }
  >({
    mutationFn: ({ uuid, data }) => partialUpdateLiveChat(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_BY_UUID, uuid] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ANALYTICS] });
    },
  });
};

export const useDeleteLiveChatMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: (uuid) => deleteLiveChat(uuid),
    onSuccess: (_, uuid) => {
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_BY_UUID, uuid] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ANALYTICS] });
    },
  });
};

// Live Chat Action Mutations
export const useCloseLiveChatMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<LiveChatActionResponse, Error, string>({
    mutationFn: (uuid) => closeLiveChat(uuid),
    onSuccess: (_, uuid) => {
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_BY_UUID, uuid] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ANALYTICS] });
    },
  });
};

export const useReopenLiveChatMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<LiveChatActionResponse, Error, string>({
    mutationFn: (uuid) => reopenLiveChat(uuid),
    onSuccess: (_, uuid) => {
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_BY_UUID, uuid] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ANALYTICS] });
    },
  });
};

export const useUpdateChatStatusMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    LiveChat, // Return type as the API now returns LiveChat
    Error,
    { uuid: string; status: 'OPEN' | 'CLOSED' }
  >({
    mutationFn: ({ uuid, status }) => updateChatStatus(uuid, status),
    onSuccess: (data) => {
      // Invalidate relevant queries after status update
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_BY_UUID, data.uuid] }); // Use uuid from returned data
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ANALYTICS] });
    },
  });
};

export const useSendLiveChatMessageMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    LiveChatSendMessageResponse,
    Error,
    { chatUuid: string; messageData: LiveChatMessageCreateData }
  >({
    mutationFn: ({ chatUuid, messageData }) => sendLiveChatMessage(chatUuid, messageData),
    onSuccess: (_, { chatUuid }) => {
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_MESSAGES, chatUuid] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_BY_UUID, chatUuid] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_NOTIFICATIONS] });
    },
  });
};

// Notification Mutations
export const useMarkLiveChatNotificationAsReadMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<LiveChatNotification, Error, number>({
    mutationFn: (id) => markLiveChatNotificationAsRead(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_NOTIFICATIONS] });
    },
  });
};

export const useMarkAllLiveChatNotificationsAsReadMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<LiveChatMarkNotificationResponse, Error, void>({
    mutationFn: () => markAllLiveChatNotificationsAsRead(),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_NOTIFICATIONS] });
    },
  });
};

export const useMarkChatMessagesAsReadMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    { status: string; messages_marked_as_read: number },
    Error,
    string // chatUuid
  >({
    mutationFn: (chatUuid) => markAllMessagesAsRead(chatUuid),
    onSuccess: (_data, chatUuid) => {
      // Invalidate messages for this chat to reflect read status if backend updates it
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_MESSAGES, chatUuid] });
      // Invalidate notifications for the user as they've been read
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_NOTIFICATIONS] });
      // Invalidate the specific chat details as unread_count might change
      queryClient.invalidateQueries({ queryKey: [LIVE_CHAT_KEYS.GET_BY_UUID, chatUuid] });
    },
  });
};

// Filtered Queries
export const useLiveChatsByStatusQuery = (status: 'OPEN' | 'PENDING' | 'CLOSED') => {
  return useQuery<LiveChatPaginatedResponse<LiveChat>, Error>({
    queryKey: [LIVE_CHAT_KEYS.FILTER_BY_STATUS, status],
    queryFn: () => filterLiveChatsByStatus(status),
    enabled: !!status,
  });
};

export const useSearchLiveChatsQuery = (searchTerm: string, enabled = true) => {
  return useQuery<LiveChatPaginatedResponse<LiveChat>, Error>({
    queryKey: [LIVE_CHAT_KEYS.SEARCH, searchTerm],
    queryFn: () => searchLiveChats(searchTerm),
    enabled: enabled && !!searchTerm,
  });
};

export const useActiveLiveChatsQuery = () => {
  return useQuery<LiveChatPaginatedResponse<LiveChat>, Error>({
    queryKey: [LIVE_CHAT_KEYS.GET_ACTIVE],
    queryFn: getActiveLiveChats,
  });
};

export const useInactiveLiveChatsQuery = () => {
  return useQuery<LiveChatPaginatedResponse<LiveChat>, Error>({
    queryKey: [LIVE_CHAT_KEYS.GET_INACTIVE],
    queryFn: getInactiveLiveChats,
  });
};

// Sorted Queries
export const useLiveChatsSortedByDateQuery = (ascending = false) => {
  return useQuery<LiveChatPaginatedResponse<LiveChat>, Error>({
    queryKey: [LIVE_CHAT_KEYS.GET_ALL, 'sorted-by-date', ascending],
    queryFn: () => getLiveChatsSortedByDate(ascending),
  });
};

export const useLiveChatsSortedByLastMessageQuery = (ascending = false) => {
  return useQuery<LiveChatPaginatedResponse<LiveChat>, Error>({
    queryKey: [LIVE_CHAT_KEYS.GET_ALL, 'sorted-by-last-message', ascending],
    queryFn: () => getLiveChatsSortedByLastMessage(ascending),
  });
};

// Convenience Hooks
export const useOpenLiveChatsQuery = () => {
  return useLiveChatsByStatusQuery('OPEN');
};

export const usePendingLiveChatsQuery = () => {
  return useLiveChatsByStatusQuery('PENDING');
};

export const useClosedLiveChatsQuery = () => {
  return useLiveChatsByStatusQuery('CLOSED');
};

// Real-time polling hook for active chats
export const useLiveChatsWithPolling = (params?: LiveChatFilterParams, pollingInterval = 30000) => {
  return useQuery<LiveChatPaginatedResponse<LiveChat>, Error>({
    queryKey: [LIVE_CHAT_KEYS.GET_ALL, params, 'polling'],
    queryFn: () => getAllLiveChats(params),
    refetchInterval: pollingInterval, // Refetch every 30 seconds by default
  });
};

// Hook for unread notifications count
export const useUnreadNotificationsCount = () => {
  const { data: notifications } = useLiveChatNotificationsQuery();

  const unreadCount = notifications?.results?.filter(notification => !notification.is_read).length || 0;

  return unreadCount;
};
