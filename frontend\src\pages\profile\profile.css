/* Page Layout */
.profile-dashboard__content {
    padding: 30px;
    min-height: calc(100vh - 80px); /* Adjust based on your header height */
    background-color: #f5f7fa; /* Match dashboard background */
}

.profile-card {
    background-color: #ffffff;
    border-radius: 16px;
    box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
                0 2px 4px rgba(55, 183, 195, 0.04);
    padding: 30px;
    margin-bottom: 30px;
}

.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.profile-title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
}

/* Search Field */
.profile-search-field {
    position: relative;
    margin-top: 16px;
}

.profile-search-input {
    width: 100%;
    padding: 14px 18px;
    padding-left: 44px;
    border: 1px solid rgba(55, 183, 195, 0.15);
    border-radius: 12px;
    font-size: 14px;
    transition: all 0.2s ease;
    background-color: rgba(55, 183, 195, 0.03);
}

.profile-search-input:focus {
    border-color: #37B7C3;
    box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.1);
    background-color: #fff;
}

.profile-search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 18px;
}

/* Tags Styling */
.profile-tags-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.profile-tag-item {
    display: inline-block;
    padding: 8px 14px;
    background: rgba(55, 183, 195, 0.08);
    color: #37B7C3;
    border-radius: 10px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid rgba(55, 183, 195, 0.12);
}

.profile-tag-item:hover {
    background: rgba(55, 183, 195, 0.12);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(55, 183, 195, 0.12);
}

/* Form Cards Grid */
.profile-forms-grid {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(4, 1fr);
    margin-bottom: 30px;
}

/* Responsive Adjustments */
@media (max-width: 1400px) {
    .profile-forms-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 991px) {
    .profile-forms-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
}

@media (max-width: 767px) {
    .profile-forms-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

/* Form Card Animation */
.profile-forms-grid > * {
    animation: profile-fadeIn 0.3s ease-out forwards;
}

@keyframes profile-fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Transitions and Hover Effects */
.profile-hover-shadow-2 {
    transition: all 0.3s ease;
}

.profile-hover-shadow-2:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08) !important;
}

/* Utility Classes */
.profile-shadow-1 {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.profile-border-light {
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.profile-bg-light-4 {
    background-color: #f5f7fa;
}

/* Loading States */
.profile-form-checkbox__input:disabled + .profile-form-checkbox__label {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Tabs Styling */
.profile-tabs-wrapper {
    display: flex;
    gap: 1rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0;
    margin-bottom: 30px;
}

.profile-tab-button {
    background: none;
    border: none;
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    color: #6b7280;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    border-radius: 8px 8px 0 0;
}

.profile-tab-button:hover {
    color: #374151;
    background-color: rgba(55, 183, 195, 0.05);
}

.profile-tab-button.active {
    color: #37B7C3;
    background-color: rgba(55, 183, 195, 0.08);
}

.profile-tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #37B7C3;
}

/* Form Styling */
.profile-form-section {
    margin-bottom: 30px;
}

.profile-form-row {
    margin-bottom: 20px;
}

/* Button Styles */
.profile-btn-custom {
    min-width: 150px;
    height: 50px;
    border-radius: 12px;
    background-color: #37B7C3;
    color: #fff;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 25px;
    margin-left: 10px;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(55, 183, 195, 0.15);
}

.profile-btn-custom:hover {
    background-color: #2d919a;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(55, 183, 195, 0.2);
}

.profile-btn-secondary {
    background-color: #f5f7fa;
    color: #4f547b;
    border: 1px solid #e5e7eb;
    box-shadow: none;
}

.profile-btn-secondary:hover {
    background-color: #e5e7eb;
    color: #1a1a1a;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.profile-btn-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    gap: 10px;
}

/* Disable border for password input */
.profile-no-border {
    border: none !important;
    background-color: transparent !important;
    box-shadow: none !important;
}

.profile-page-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #f5f7fa;
}

.profile-layout {
    display: flex;
    height: 100%;
}

.profile-tab-button {
    cursor: pointer;
    padding: 0.75rem;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.profile-tab-button.active {
    color: #37b7c3;
}

/* Document Management Styles */
.document-list {
    margin-top: 20px;
}

.document-upload-section {
    background: rgba(55, 183, 195, 0.03);
    border: 1px solid rgba(55, 183, 195, 0.15);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 30px;
    transition: all 0.3s ease;
}

.document-upload-section:hover {
    background: rgba(55, 183, 195, 0.05);
    border-color: rgba(55, 183, 195, 0.2);
}

.document-upload-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-dark-1);
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.document-upload-title::before {
    content: "📄";
    font-size: 20px;
}

.file-input-wrapper {
    position: relative;
    margin-bottom: 15px;
}

.file-input-custom {
    width: 100%;
    padding: 16px 20px;
    border: 2px dashed rgba(55, 183, 195, 0.3);
    border-radius: 12px;
    background: rgba(55, 183, 195, 0.02);
    color: var(--color-light-1);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.file-input-custom:hover {
    border-color: rgba(55, 183, 195, 0.5);
    background: rgba(55, 183, 195, 0.05);
}

.file-input-custom::before {
    content: "📁 Choose File or Drag & Drop";
    display: block;
    font-weight: 500;
    color: var(--color-purple-1);
}

.file-input-custom input[type="file"] {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.document-alerts {
    margin-bottom: 24px;
}

.document-alert {
    padding: 16px 20px;
    border-radius: 12px;
    margin-bottom: 12px;
    border-left: 4px solid;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    font-size: 14px;
    line-height: 1.5;
}

.document-alert.alert-danger {
    background: rgba(239, 68, 68, 0.05);
    border-left-color: #ef4444;
    color: #dc2626;
}

.document-alert.alert-warning {
    background: rgba(245, 158, 11, 0.05);
    border-left-color: #f59e0b;
    color: #d97706;
}

.document-alert.alert-info {
    background: rgba(59, 130, 246, 0.05);
    border-left-color: #3b82f6;
    color: #2563eb;
}

.document-alert-icon {
    font-size: 18px;
    margin-top: 2px;
    flex-shrink: 0;
}

.document-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.document-summary-card {
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(55, 183, 195, 0.04);
}

.document-summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(55, 183, 195, 0.08);
    border-color: rgba(55, 183, 195, 0.2);
}

.document-summary-card.total {
    border-left: 4px solid var(--color-purple-1);
}

.document-summary-card.valid {
    border-left: 4px solid var(--color-green-4);
}

.document-summary-card.warning {
    border-left: 4px solid var(--color-yellow-1);
}

.document-summary-card.danger {
    border-left: 4px solid var(--color-red-1);
}

.document-summary-number {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    line-height: 1;
}

.document-summary-card.total .document-summary-number {
    color: var(--color-purple-1);
}

.document-summary-card.valid .document-summary-number {
    color: var(--color-green-4);
}

.document-summary-card.warning .document-summary-number {
    color: var(--color-yellow-1);
}

.document-summary-card.danger .document-summary-number {
    color: var(--color-red-1);
}

.document-summary-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--color-light-1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.document-table-wrapper {
    background: #ffffff;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(55, 183, 195, 0.05);
    border: 1px solid rgba(55, 183, 195, 0.1);
}

.document-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.document-table thead {
    background: linear-gradient(135deg, rgba(55, 183, 195, 0.08) 0%, rgba(55, 183, 195, 0.12) 100%);
}

.document-table th {
    padding: 16px 20px;
    text-align: left;
    font-weight: 600;
    font-size: 14px;
    color: var(--color-dark-1);
    border-bottom: 1px solid rgba(55, 183, 195, 0.15);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.document-table td {
    padding: 16px 20px;
    border-bottom: 1px solid #f1f5f9;
    font-size: 14px;
    color: var(--color-dark-1);
    vertical-align: middle;
}

.document-table tbody tr {
    transition: all 0.2s ease;
}

.document-table tbody tr:hover {
    background: rgba(55, 183, 195, 0.02);
}

.document-table tbody tr.table-danger {
    background: rgba(239, 68, 68, 0.03);
}

.document-table tbody tr.table-danger:hover {
    background: rgba(239, 68, 68, 0.05);
}

.document-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.document-status-badge.status-valid {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
}

.document-status-badge.status-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
}

.document-status-badge.status-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}

.document-status-badge.status-muted {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
}

.document-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.document-action-btn {
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.document-action-btn.btn-update {
    background: rgba(55, 183, 195, 0.1);
    color: var(--color-purple-1);
    border-color: rgba(55, 183, 195, 0.2);
}

.document-action-btn.btn-update:hover {
    background: rgba(55, 183, 195, 0.15);
    border-color: rgba(55, 183, 195, 0.3);
    transform: translateY(-1px);
}

.document-action-btn.btn-update.updating {
    background: rgba(245, 158, 11, 0.1);
    color: #d97706;
    border-color: rgba(245, 158, 11, 0.2);
}

.document-action-btn.btn-delete {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border-color: rgba(239, 68, 68, 0.2);
}

.document-action-btn.btn-delete:hover {
    background: rgba(239, 68, 68, 0.15);
    border-color: rgba(239, 68, 68, 0.3);
    transform: translateY(-1px);
}

.document-action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.document-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--color-light-1);
}

.document-empty-state-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.document-empty-state-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-dark-1);
    margin-bottom: 8px;
}

.document-empty-state-text {
    font-size: 14px;
    line-height: 1.5;
    max-width: 400px;
    margin: 0 auto;
}

.document-file-info {
    font-size: 13px;
    color: var(--color-light-1);
    margin-top: 8px;
    font-style: italic;
}

.document-type-hint {
    font-size: 12px;
    color: var(--color-light-1);
    margin-top: 4px;
    font-style: italic;
}

/* Update form styling for documents tab */
.profile-form-section .col-md-6 .file-input-wrapper,
.profile-form-section .col-md-6 .document-upload-section {
    margin-bottom: 0;
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .profile-dashboard__content {
        padding: 20px;
    }
    
    .profile-card {
        padding: 20px;
    }

    .profile-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .profile-btn-container {
        flex-direction: column;
        width: 100%;
    }

    .profile-btn-custom {
        width: 100%;
        margin-left: 0;
        margin-top: 10px;
    }

    .profile-tabs-wrapper {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .profile-tab-button {
        flex: 1 0 auto;
        min-width: 120px;
        text-align: center;
    }

    .document-summary-cards {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .document-table-wrapper {
        overflow-x: auto;
    }

    .document-table {
        min-width: 600px;
    }

    .document-upload-section {
        padding: 20px;
    }
}

@media (max-width: 767px) {
    .profile-dashboard__content {
        padding: 15px;
    }
    
    .profile-card {
        padding: 15px;
    }
    
    .profile-tabs-wrapper {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .profile-tab-button {
        width: 100%;
        text-align: left;
        border-radius: 8px;
    }
    
    .profile-tab-button.active::after {
        display: none;
    }
    
    .profile-tab-button.active {
        background-color: rgba(55, 183, 195, 0.15);
    }

    .document-summary-cards {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .document-table th,
    .document-table td {
        padding: 12px 16px;
        font-size: 13px;
    }

    .document-actions {
        flex-direction: column;
        gap: 6px;
    }

    .document-action-btn {
        width: 100%;
        justify-content: center;
    }

    .document-upload-section {
        padding: 16px;
    }

    .document-upload-title {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .document-table th:nth-child(3),
    .document-table td:nth-child(3),
    .document-table th:nth-child(6),
    .document-table td:nth-child(6) {
        display: none;
    }

    .document-summary-number {
        font-size: 24px;
    }

    .document-summary-label {
        font-size: 12px;
    }
}
