import api from "../api";

export interface Symptom {
  uuid: string;
  patient: string;
  description: string;
  start_date: string;
  start_time: string;
  severity: number;
  hospitalization_required: boolean;
  status: string;
  status_display: string;
  patient_comment: string;
  created_at: string;
  updated_at: string;
  resolved_date: string | null;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface CreateSymptomData {
  patient_uuid: string;
  description: string;
  start_date: string;
  start_time: string;
  severity: number;
  hospitalization_required: boolean;
  status: string;
  patient_comment: string;
  resolved_date?: string;
}

export interface SymptomLog {
  uuid: string;
  symptom: string;
  symptom_uuid: string;
  old_status: string;
  old_status_display: string;
  new_status: string;
  new_status_display: string;
  changed_by: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  changed_at: string;
  resolved_date: string | null;
  notes: string | null;
}

export const getAllSymptoms = async (patientUuid: string): Promise<Symptom[]> => {
  const response = await api.get<PaginatedResponse<Symptom>>(`/patient/symptoms/?patient_uuid=${patientUuid}`);
  return response.data.results;
};

export const createSymptom = async (data: CreateSymptomData): Promise<Symptom> => {
  if (!data.patient_uuid) {
    throw new Error("Patient UUID is required");
  }
  
  console.log('Creating symptom with data:', data); // Debug log
  const response = await api.post<Symptom>("/patient/symptoms/", data);
  return response.data;
};

export const updateSymptom = async (uuid: string, data: Partial<CreateSymptomData>): Promise<Symptom> => {
  const response = await api.patch<Symptom>(`/patient/symptoms/${uuid}/`, data);
  return response.data;
};

export const getSymptomLogs = async (symptomUuid: string): Promise<PaginatedResponse<SymptomLog>> => {
  const response = await api.get<PaginatedResponse<SymptomLog>>(`/patient/symptom-logs/?symptom_uuid=${symptomUuid}`);
  return response.data;
}; 