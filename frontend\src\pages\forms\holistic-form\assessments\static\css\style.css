/* .form-page {
  height: 100vh;
  overflow: scroll;
  width: 100%;
  overflow-x: hidden;
} */

/* .form-page::-webkit-scrollbar {
  display: none;
} */


.my-primary-btn {
  font-size: 15px !important;
  font-family: 'PoppinsRegular' !important;
  background-color: #37B7C3 !important;
  color: white !important;
  text-decoration: none !important;
  border: none !important;
  outline: none !important;
  padding: .5rem .6rem !important;
  text-transform: uppercase !important;
  border-radius: 5px !important;
}

.my-primary-btn:hover {
  box-shadow: 0px 0px 3px gray;
  transition: .3s;
}

.my-primary-btn:active {
  opacity: .7;
}


.options-modal {
  display: flex;
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  background-color: rgba(255, 255, 255, 0.515);
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.options-modal .center {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  background-color: white;
  box-shadow: 0px 0px 7px black;
}

.categories-form {
  width: 90vw;
  height: 100vh;
  overflow-y: scroll;
  display: flex;
  align-items: start;
  justify-content: space-around;
  padding-top: 3rem;
  flex-wrap: wrap;
}

.categories-form::-webkit-scrollbar {
  display: none;
}

.categories-form .q {
  padding-top: .5rem;
  width: 17rem;
  display: flex;  
  background-color: #37B7C3;
  flex-direction: column;
  border-radius: 10px;
}

.categories-form .q img {
  width: 100%;
  margin-bottom: .5rem;
  object-fit: contain;
}

.categories-form .q .option {
  display: flex;
  flex-direction: row;
  width: 100%;
  gap: 10px;
  padding: .5rem .8rem;
  border-bottom: 1px solid white;
  color: white;
  font-size: 19px;
  font-weight: 500;
  letter-spacing: .5px;
  font-family: 'Roboto';
}

.categories-form .q .option label {
  cursor: pointer;
}

.matched-option {
  color: black;
  background-color: #BEC2C9;
  padding: .3rem 1rem;
  border-radius: 5px;
  box-shadow: 0px 0px 5px gray;
  cursor: pointer;
}

.pain {
  color: white;
  background-color: #071952;
  padding: .5rem 1rem;
  border-radius: 10px;
  box-shadow: 0px 0px 3px #07195250;
  width: fit-content;
  font-size: 19px;
  align-items: center;
}

.medication {
  color: white;
  background-color: #071952;
  padding: .5rem 1rem;
  border-radius: 10px;
  box-shadow: 0px 0px 3px #07195250;
  width: fit-content;
  font-size: 19px;
  align-items: center;
}

.form-card {
  padding: 2rem;
  text-align: center;
  text-wrap: wrap;
  border-radius: 10px;
  cursor: pointer;
  transition: .3s;
  text-decoration: none;
  color: #37B7C3;
  box-shadow: 0px 0px 5px gray;
}

.form-card .form-icon {
  color: #37B7C3;
}

.form-card:hover {
  color: white;
  background-color: #37B7C3;
}

.form-card:hover .form-icon {
  color: white;
}


.h4profile {
  font-family: 'AntonRegular', sans-serif;
  font-size: larger;
  font-weight: 400;
}

.profileListh4span {
  display:inline-flex;
}

.buttonTitle {
  font-family: 'PoppinsRegular', 'Geneva', 'Tahoma', 'sans-serif';
  font-size: 18px;
  font-weight: 500;
  line-height: 39px;
  cursor: pointer;
  }



  .SectionHeadings {
    font-family: "DMSerifDisplay" ;
    color: #071952;
    font-size:5rem;
  }

  .headinqQuestion {
    font-family: "PoppinsRegular" ,'Times New Roman', Times, serif;
    font-weight: 700;
    font-size: 20px;
    line-height: 27px;
    background-color: #37B7C3;
    color: #ffffff;
    margin-bottom: 0.5rem;
    text-align: start;
    border-radius: 8px;
    padding: 10px 10px;
  }

  .cursorPointer {
    cursor: pointer;
  }

  .block {
    display: block;
  }

  .etiquetteHeadingForms {
    font-size: 3rem;
    color: #071952;
    font-family: 'Rakkas', Times, serif;
    text-decoration: underline rgb(14, 84, 138) 2px;
}

.inlineBlock{
  display: inline-block;
}

.imageEtiquette {
  border-radius: 50%;
  height: 50px;
  width: 50px;
  border: #37B7C3 solid 1px;
  margin-right: 10px;
  margin-bottom:  15px;
}

label:has(input:checked)  {
  background-color: #ffffff;
  color:  #37B7C3;
  font-weight: 600;
}

label:has(input:checked) > span {
  background-color: #ffffff;
  color:  #37B7C3;
  font-weight: 600;
}

.labelSBARForm input[type='radio']:after {
  width: 15px;
  height: 15px;
  border-radius: 15px;
  top: -3px;
  left: -1px;
  position: relative;
  background-color:  #ffffff;
  content: '';
  display: inline-block;
  visibility: visible;
    border: 2px solid white;
}


.labelSBARForm input[type='checkbox']:after {
  width: 16px;
  height: 16px;
  border-radius: 1px;
  top: -3px;
  left: -1px;
  position: relative;
  background-color:  #ffffff;
  content: '';
  display: inline-block;
  visibility: visible;
    border: 2px solid white;
}


.labelSBARForm input[type='radio']:checked:after {
  width: 16px;
  height: 16px;
  border-radius: 15px;
  position: relative;
  background-color:  #37B7C3;
  content: '';
  display: inline-block;
  visibility: visible;
  border: 2px solid white;
}


.labelSBARForm input[type='checkbox']:checked:after {
  width: 16px;
  height: 16px;
  border-radius: 1px;
  top: -3px;
  left: -1px;
  position: relative;
  background-color:  #37B7C3;
  content: '';
  display: inline-block;
  visibility: visible;
  border: 2px solid #ffffff;
}





.inlineInput {
  font-family: "PoppinsRegular" ,'Times New Roman', Times, serif;
  font-weight: 400;
  font-size: 15px;
  line-height: 27px;
  background-color: #37B7C3;
  color: #ffffff;
  margin-bottom: 0.5rem;
  text-align: start;
  border-radius: 8px;
  padding: 10px 10px;
}


.labelSBARForm {
  display: flex;
  gap: 0.5rem;
  cursor: pointer;
  border-radius: 0.375rem;
  border: 1px solid #20afb9;
  padding-left: 18px;
  padding-right: 18px;
  margin-bottom: 2px;
  color: black;
  width: auto;
}
  
.values {
  margin: 1rem 0;
  display: flex;
  flex-direction: row;
  gap: 20px;
}

 .values .value {
  color: white;
  background-color: #112D4E;
  width: fit-content;
  padding: .5rem 1rem;
  font-size: 17px;
  font-family: 'Roboto';
  display: flex;
  flex-direction: row;
  gap: 8px;
  border-radius: 10px;
  align-items: center;
  justify-content: center;
}

.values .value .close {
  background-color: white;  
  color: black;
  border-radius: 50%;
  height: fit-content;
  font-size: 20px;
  width: 2rem;
  height: 2rem;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}



.subheading  {
  text-align: start;
  position: relative;
  padding: 0;
  margin: 0;
  font-family: "PoppinsBold", sans-serif;
  font-weight: 300;
  font-size: 40px;
  color: #071952;
  -webkit-transition: all 0.4s ease 0s;
  -o-transition: all 0.4s ease 0s;
  transition: all 0.4s ease 0s;

}



.subheading {
  font-size:20px; font-weight:700;  letter-spacing:1px; text-transform:uppercase; margin:0; white-space:nowrap; padding-bottom:13px;
}
.subheading:before {
    background-color: #08839550;
    content: '';
    display: block;
    height: 3px;
    width: 400px;
    margin-bottom: 1rem;
    margin-top: 2rem;
}




/* CPR  */



/* CPR 2 CSS */

.App123456 {
  text-align: left;
  margin: 20px;
}

.btn123456 {
  margin: 10px;
  padding: 10px 20px;
  font-size: 16px;
}

.table123456 {
  margin-top: 20px;
  width: 100%;
  border-collapse: collapse;
}

th, td {
  border: 1px solid black;
  padding: 10px;
  text-align: left;
}


/* Add this CSS to your styles.css or a similar CSS file */

.scrollable-table {
  display: block;
  max-height: 125px; /* Adjust height as necessary for 5 rows */
  overflow-y: scroll;
}

.scrollable-table tbody {
  display: block;
  max-height: 125px; /* Adjust height as necessary for 5 rows */
  overflow-y: scroll;
}

.scrollable-table thead,
.scrollable-table tbody tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}





/* modal for from 2 */

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  background-color: white;
  padding: 20px;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
}

.modal-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal-content button {
  margin: 5px;
  padding: 10px 20px;
  cursor: pointer;
}



/* Radio  */


.checkbox-wrapper-16 *,
.checkbox-wrapper-16 *:after,
.checkbox-wrapper-16 *:before {
  box-sizing: border-box;
  width: auto;
}

.checkbox-wrapper-16 .checkbox-input {
  clip: rect(0 0 0 0);
  -webkit-clip-path: inset(100%);
          clip-path: inset(100%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}
.checkbox-wrapper-16 .checkbox-input:checked + .checkbox-tile {
  border-color: #2260ff;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  color: #2260ff;
}
.checkbox-wrapper-16 .checkbox-input:checked + .checkbox-tile:before {
  transform: scale(1);
  opacity: 1;
  background-color: #2260ff;
  border-color: #2260ff;
}
.checkbox-wrapper-16 .checkbox-input:checked + .checkbox-tile .checkbox-icon,
.checkbox-wrapper-16 .checkbox-input:checked + .checkbox-tile .checkbox-label {
  color: #2260ff;
}
.checkbox-wrapper-16 .checkbox-input:focus + .checkbox-tile {
  border-color: #2260ff;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1), 0 0 0 4px #b5c9fc;
  
}
.checkbox-wrapper-16 .checkbox-input:focus + .checkbox-tile:before {
  transform: scale(1);
  opacity: 1;
}

.checkbox-wrapper-16 .checkbox-tile {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 170px;
  height: 8rem;
  border-radius: 0.5rem;
  border: 2px solid #b5bfd9;
  background-color: #fff;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  transition: 0.15s ease;
  cursor: pointer;
  position: relative;
}
.checkbox-wrapper-16 .checkbox-tile:before {
  content: "";
  position: absolute;
  display: block;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #b5bfd9;
  background-color: #fff;
  border-radius: 50%;
  top: 0.25rem;
  left: 0.25rem;
  opacity: 0;
  transform: scale(0);
  transition: 0.25s ease;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='192' height='192' fill='%23FFFFFF' viewBox='0 0 256 256'%3E%3Crect width='256' height='256' fill='none'%3E%3C/rect%3E%3Cpolyline points='216 72.005 104 184 48 128.005' fill='none' stroke='%23FFFFFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='32'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: 12px;
  background-repeat: no-repeat;
  background-position: 50% 50%;
}
.checkbox-wrapper-16 .checkbox-tile:hover {
  border-color: #2260ff;
}
.checkbox-wrapper-16 .checkbox-tile:hover:before {
  transform: scale(1);
  opacity: 1;
}

.checkbox-wrapper-16 .checkbox-icon {
  transition: 0.375s ease;
  color: #494949;
}
.checkbox-wrapper-16 .checkbox-icon svg {
  width: 5rem;
  height: 4rem;
  justify-content: center;
}

.checkbox-wrapper-16 .checkbox-label {
  color: #707070;
  transition: 0.375s ease;
  text-align: center;
  font-weight: bolder;
}


/* Timers */

#wrappertimer {
  width: 900px;
  margin: 10px auto 0 auto;
}

/* line 22, ../sass/screen.scss */
.cool_btn1 {
  width: 190px;
  height: 190px;
  margin: 15px 15px 15px 15px;
  position: relative;
  -webkit-border-radius: 200px;
  -moz-border-radius: 200px;
  -ms-border-radius: 200px;
  -o-border-radius: 200px;
  border-radius: 200px;
  background-image: -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(0%, #fafafa), color-stop(50%, #e3e3e3), color-stop(50%, #888888), color-stop(100%, #666666));
  background-image: -webkit-linear-gradient(#fafafa, #e3e3e3 50%, #888888 50%, #666666);
  background-image: -moz-linear-gradient(#fafafa, #e3e3e3 50%, #888888 50%, #666666);
  background-image: -o-linear-gradient(#fafafa, #e3e3e3 50%, #888888 50%, #666666);
  background-image: linear-gradient(#fafafa, #e3e3e3 50%, #888888 50%, #666666);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3), inset 0px 2px 5px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3), inset 0px 2px 5px rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3), inset 0px 2px 5px rgba(0, 0, 0, 0.3);
  display: inline-block;
}
/* line 31, ../sass/screen.scss */
.cool_btn1 h1 {
  text-align: center;
  font-size: 50px;
  margin: 20px 0 0 0;
  color: #333;
  text-shadow: 0 1px 0 white, 0 -1px 0 rgba(0, 0, 0, 0.5);
  font-family: 'Lobster', cursive;
  font-weight: normal;
  line-height: 1;
}



/* line 104, ../sass/screen.scss */
.cool_btn1.orange h1 {
  color: black;
}

.cool_btn1.orange:hover {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3), inset 0px 2px 5px rgba(0, 0, 0, 0.3), 0 0 28px 6px rgba(255, 174, 0, 0.8);
  -moz-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3), inset 0px 2px 5px rgba(0, 0, 0, 0.3), 0 0 28px 6px rgba(255, 174, 0, 0.8);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3), inset 0px 2px 5px rgba(0, 0, 0, 0.3), 0 0 28px 6px rgba(255, 174, 0, 0.8);
}
