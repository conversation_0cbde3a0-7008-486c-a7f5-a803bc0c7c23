import React, { useState, useEffect } from "react";
import { X, Save, Pill, Calendar, Clipboard, Activity } from "lucide-react";
import "./EditMedicationModal.css";

interface EditMedicationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedMedication: {
    medication: string;
    non_drug_therapy?: string;
    indication: string;
    dose: number;
    dose_units: string;
    schedule: string;
    dose_form: string;
    route: string;
    start_date: string;
    end_date?: string;
    is_baseline: boolean;
    is_continuing: boolean;
  }) => void;
  medicationData: {
    uuid: string;
    medication: string;
    non_drug_therapy?: string;
    indication: string;
    dose: number;
    dose_units: string;
    dose_units_display: string;
    schedule: string;
    schedule_display: string;
    dose_form: string;
    dose_form_display: string;
    route: string;
    route_display: string;
    start_date: string;
    end_date?: string;
    is_baseline: boolean;
    is_continuing: boolean;
  };
}

const EditMedicationModal: React.FC<EditMedicationModalProps> = ({
  isOpen,
  onClose,
  onSave,
  medicationData,
}) => {
  const [medication, setMedication] = useState(medicationData.medication);
  const [nonDrugTherapy, setNonDrugTherapy] = useState(medicationData.non_drug_therapy || "");
  const [indication, setIndication] = useState(medicationData.indication);
  const [dose, setDose] = useState(medicationData.dose);
  const [doseUnits, setDoseUnits] = useState(medicationData.dose_units);
  const [schedule, setSchedule] = useState(medicationData.schedule);
  const [doseForm, setDoseForm] = useState(medicationData.dose_form);
  const [route, setRoute] = useState(medicationData.route);
  const [startDate, setStartDate] = useState(medicationData.start_date);
  const [endDate, setEndDate] = useState(medicationData.end_date || "");
  const [isBaseline, setIsBaseline] = useState(medicationData.is_baseline);
  const [isContinuing, setIsContinuing] = useState(medicationData.is_continuing);

  // Update state when the modal opens with new data
  useEffect(() => {
    if (isOpen) {
      setMedication(medicationData.medication);
      setNonDrugTherapy(medicationData.non_drug_therapy || "");
      setIndication(medicationData.indication);
      setDose(medicationData.dose);
      setDoseUnits(medicationData.dose_units);
      setSchedule(medicationData.schedule);
      setDoseForm(medicationData.dose_form);
      setRoute(medicationData.route);
      setStartDate(medicationData.start_date);
      setEndDate(medicationData.end_date || "");
      setIsBaseline(medicationData.is_baseline);
      setIsContinuing(medicationData.is_continuing);
    }
  }, [isOpen, medicationData]);

  // Format date for input field (YYYY-MM-DD)
  const formatDateForInput = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toISOString().split("T")[0];
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      medication,
      non_drug_therapy: nonDrugTherapy,
      indication,
      dose,
      dose_units: doseUnits,
      schedule,
      dose_form: doseForm,
      route,
      start_date: startDate,
      end_date: endDate,
      is_baseline: isBaseline,
      is_continuing: isContinuing
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="edit-visit-modal-overlay">
      <div className="edit-medication-modal">
        <div className="edit-medication-modal-header">
          <h2 className="edit-medication-modal-title">
            <Pill size={20} className="modal-icon" /> Edit Medication
          </h2>
          <button
            type="button"
            className="edit-medication-modal-close"
            onClick={onClose}
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="edit-medication-modal-body">
            <div className="medication-form-section">
              <div className="section-header">
                <Pill size={16} />
                <h3>Medication Information</h3>
              </div>
              <div className="row">
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="medication-name">Medication</label>
                  <input
                    type="text"
                    id="medication-name"
                    className="form-control"
                    value={medication}
                    onChange={(e) => setMedication(e.target.value)}
                    required
                  />
                </div>
              </div>
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="non-drug-therapy">Non-Drug Therapy (Optional)</label>
                  <input
                    type="text"
                    id="non-drug-therapy"
                    className="form-control"
                    value={nonDrugTherapy}
                    onChange={(e) => setNonDrugTherapy(e.target.value)}
                  />
                </div>
              </div>
              </div>

              <div className="row">
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="indication">Indication</label>
                  <input
                    type="text"
                    id="indication"
                    className="form-control"
                    value={indication}
                    onChange={(e) => setIndication(e.target.value)}
                    required
                  />
                </div>
              </div>
              <div className="col-md-3">
                <div className="form-group">
                  <label htmlFor="dose">Dose</label>
                  <input
                    type="number"
                    step="0.01"
                    id="dose"
                    className="form-control"
                    value={dose}
                    onChange={(e) => setDose(parseFloat(e.target.value))}
                    required
                  />
                </div>
              </div>
              <div className="col-md-3">
                <div className="form-group">
                  <label htmlFor="dose-units">Dose Units</label>
                  <select
                    id="dose-units"
                    className="form-select"
                    value={doseUnits}
                    onChange={(e) => setDoseUnits(e.target.value)}
                    required
                  >
                    <option value="1">g (gram)</option>
                    <option value="2">mg (milligram)</option>
                    <option value="3">µg (microgram)</option>
                    <option value="4">L (liter)</option>
                    <option value="5">mL (milliliter)</option>
                    <option value="6">IU (International Unit)</option>
                    <option value="7">Other</option>
                  </select>
                </div>
              </div>
              </div>
            </div>

            <div className="medication-form-section">
              <div className="section-header">
                <Clipboard size={16} />
                <h3>Dosage Information</h3>
              </div>
              <div className="row">
              <div className="col-md-4">
                <div className="form-group">
                  <label htmlFor="schedule">Schedule</label>
                  <select
                    id="schedule"
                    className="form-select"
                    value={schedule}
                    onChange={(e) => setSchedule(e.target.value)}
                    required
                  >
                    <option value="1">QD (once a day)</option>
                    <option value="2">BID (twice a day)</option>
                    <option value="3">TID (three times a day)</option>
                    <option value="4">QID (four times a day)</option>
                    <option value="5">QOD (every other day)</option>
                    <option value="6">QM (every month)</option>
                    <option value="7">QOM (every other month)</option>
                    <option value="8">QH (every hour)</option>
                    <option value="9">AC (before meals)</option>
                    <option value="10">PC (after meals)</option>
                    <option value="11">PRN (as needed)</option>
                    <option value="12">Other</option>
                  </select>
                </div>
              </div>
              <div className="col-md-4">
                <div className="form-group">
                  <label htmlFor="dose-form">Dose Form</label>
                  <select
                    id="dose-form"
                    className="form-select"
                    value={doseForm}
                    onChange={(e) => setDoseForm(e.target.value)}
                    required
                  >
                    <option value="1">Tablet</option>
                    <option value="2">Capsule</option>
                    <option value="3">Ointment</option>
                    <option value="4">Suppository</option>
                    <option value="5">Aerosol</option>
                    <option value="6">Spray</option>
                    <option value="7">Suspension</option>
                    <option value="8">Patch</option>
                    <option value="9">Gas</option>
                    <option value="10">Gel</option>
                    <option value="11">Cream</option>
                    <option value="12">Powder</option>
                    <option value="13">Implant</option>
                    <option value="14">Chewable</option>
                    <option value="15">Liquid</option>
                    <option value="99">Other</option>
                  </select>
                </div>
              </div>
              <div className="col-md-4">
                <div className="form-group">
                  <label htmlFor="route">Route</label>
                  <select
                    id="route"
                    className="form-select"
                    value={route}
                    onChange={(e) => setRoute(e.target.value)}
                    required
                  >
                    <option value="1">Oral</option>
                    <option value="2">Topical</option>
                    <option value="3">Subcutaneous</option>
                    <option value="4">Intradermal</option>
                    <option value="5">Transdermal</option>
                    <option value="6">Intraocular</option>
                    <option value="7">Intramuscular</option>
                    <option value="8">Inhalation</option>
                    <option value="9">Intravenous</option>
                    <option value="10">Intraperitoneal</option>
                    <option value="11">Nasal</option>
                    <option value="12">Vaginal</option>
                    <option value="13">Rectal</option>
                    <option value="14">Other</option>
                  </select>
                </div>
              </div>
              </div>
            </div>

            <div className="medication-form-section">
              <div className="section-header">
                <Calendar size={16} />
                <h3>Timing Information</h3>
              </div>
              <div className="row">
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="start-date">Start Date</label>
                  <input
                    type="date"
                    id="start-date"
                    className="form-control"
                    value={formatDateForInput(startDate)}
                    onChange={(e) => setStartDate(e.target.value)}
                    required
                  />
                </div>
              </div>
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="end-date">End Date (Optional)</label>
                  <input
                    type="date"
                    id="end-date"
                    className="form-control"
                    value={formatDateForInput(endDate)}
                    onChange={(e) => setEndDate(e.target.value)}
                  />
                </div>
              </div>
              </div>
            </div>

            <div className="medication-form-section">
              <div className="section-header">
                <Activity size={16} />
                <h3>Status Information</h3>
              </div>
              <div className="status-toggle-row">
                <div className="toggle-switch-container">
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={isBaseline}
                      onChange={(e) => setIsBaseline(e.target.checked)}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                  <span className="toggle-label">Baseline</span>
                </div>
                
                <div className="toggle-switch-container">
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={isContinuing}
                      onChange={(e) => setIsContinuing(e.target.checked)}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                  <span className="toggle-label">Continuing</span>
                </div>
              </div>
            </div>
          </div>

          <div className="edit-medication-modal-footer">
            <button type="button" className="cancel-btn" onClick={onClose}>
              <X size={16} /> Cancel
            </button>
            <button type="submit" className="update-medication-btn">
              <Save size={16} /> Update Medication
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditMedicationModal;
