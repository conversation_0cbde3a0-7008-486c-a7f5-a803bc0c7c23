import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useResetPasswordMutation } from "@/hooks/user.query";
import { Eye, EyeOff } from "lucide-react";
import Preloader from "@/components/common/Preloader";
import LightFooter from "@/shared/LightFooter";
import "./reset-password.css";

interface UserResendMailModalProps {
  uid: string;
  token: string;
}

export default function ResetPasswordModal({
  uid,
  token,
}: UserResendMailModalProps) {
  const navigate = useNavigate();
  const [password, setNewPassword] = useState("");
  const [confirm_password, setConfirmPassword] = useState("");
  const [error, setError] = useState<string>("");
  const [passwordStrength, setPasswordStrength] = useState<number>(0);
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState<boolean>(false);
  
  // Password validation
  const validatePassword = (pass: string): boolean => {
    // Reset errors
    setError("");
    
    // Check password criteria
    const hasLetter = /[A-Za-z]/.test(pass);
    const hasNumber = /\d/.test(pass);
    const hasSpecialChar = /[@$!%*#?&]/.test(pass);
    const hasMinLength = pass.length >= 8;
    
    // Calculate strength (0-4)
    let strength = 0;
    if (hasLetter) strength++;
    if (hasNumber) strength++;
    if (hasSpecialChar) strength++;
    if (hasMinLength) strength++;
    
    setPasswordStrength(strength);
    
    // Return true if all criteria are met
    return hasLetter && hasNumber && hasSpecialChar && hasMinLength;
  };

  const { mutate, isPending } = useResetPasswordMutation();

  const handleSubmit = () => {
    if (!password || !confirm_password) {
      setError("All fields are required. Please fill them out.");
      return;
    }

    if (password !== confirm_password) {
      setError("New passwords do not match. Please try again.");
      return;
    }

    if (!validatePassword(password)) {
      setError("Password must be at least 8 characters with a letter, number, and special character");
      return;
    }

    setError("");

    mutate(
      {
        uid,
        token,
        password: password,
        confirm_password: confirm_password,
      },
      {
        onSuccess: () => {
          console.log("Password reset successfully");
          navigate("/login");
        },
         
        onError: (err: any) => {
          const errorMessage = err.response?.data?.detail || err.response?.data?.message || "An error occurred. Please try again.";
          setError(errorMessage);
          console.error("Reset password error:", err);
        },
      }
    );
  };

  return (
    <div className="main-content">
      <Preloader />

      <main className="content-wrapper">
        <div className="row justify-content-center pt-5" style={{height: "100vh"}}>
          <div className="col-md-6 col-lg-5 col-xl-4">
            <div className="reset-password-card bg-white mb-5 mt-5 border-0">
              <div className="card-body p-5 text-center">
                <h3 className="mb-3">Reset Password</h3>
                <p className="text-muted mb-4">Create a new secure password for your account</p>

                <div className="password-input-container">
                  <label className="d-block text-start mb-2">New Password</label>
                  <input
                    type={showPassword ? "text" : "password"}
                    value={password}
                    onChange={(e) => {
                      const newPassword = e.target.value;
                      setNewPassword(newPassword);
                      validatePassword(newPassword);
                    }}
                    className="password-input"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="password-toggle-btn"
                    aria-label={showPassword ? "Hide password" : "Show password"}
                  >
                    {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                
                {/* Password strength indicator */}
                {password && (
                  <div className="password-strength-container">
                    <div className="password-strength-bars">
                      {[1, 2, 3, 4].map((level) => (
                        <div
                          key={level}
                          className="password-strength-bar"
                          style={{
                            backgroundColor: passwordStrength >= level 
                              ? level === 4 
                                ? "#10B981" // Strong (green)
                                : level === 3 
                                  ? "#22C55E" // Good (light green)
                                  : level === 2 
                                    ? "#F59E0B" // Fair (orange)
                                    : "#EF4444" // Weak (red)
                              : "#E2E8F0", // Empty (gray)
                          }}
                        />
                      ))}
                    </div>
                    <div className="password-strength-text text-start" style={{ 
                      color: passwordStrength === 4 
                        ? "#10B981" 
                        : passwordStrength >= 3 
                          ? "#22C55E" 
                          : passwordStrength >= 2 
                            ? "#F59E0B" 
                            : "#EF4444"
                    }}>
                      {passwordStrength === 0 && "Enter password"}
                      {passwordStrength === 1 && "Weak password"}
                      {passwordStrength === 2 && "Fair password"}
                      {passwordStrength === 3 && "Good password"}
                      {passwordStrength === 4 && "Strong password"}
                    </div>
                  </div>
                )}
                
                <div className="password-requirements text-start">
                  Password must contain:
                  <ul className="ps-3 mt-1">
                    <li className={`password-requirement ${password.length >= 8 ? 'met' : ''}`}>
                      At least 8 characters
                    </li>
                    <li className={`password-requirement ${/[A-Za-z]/.test(password) ? 'met' : ''}`}>
                      At least one letter
                    </li>
                    <li className={`password-requirement ${/\d/.test(password) ? 'met' : ''}`}>
                      At least one number
                    </li>
                    <li className={`password-requirement ${/[@$!%*#?&]/.test(password) ? 'met' : ''}`}>
                      At least one special character (@$!%*#?&)
                    </li>
                  </ul>
                </div>

                <div className="password-input-container">
                  <label className="d-block text-start mb-2">Confirm New Password</label>
                  <input
                    type={showConfirmPassword ? "text" : "password"}
                    value={confirm_password}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="password-input"
                    style={{
                      borderColor: error && error.includes("match") ? "#ed4c5c" : "",
                    }}
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="password-toggle-btn"
                    aria-label={showConfirmPassword ? "Hide password" : "Show password"}
                  >
                    {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                  </button>
                </div>
                
                {error && (
                  <div className="error-container mb-3">
                    <p className="error-message">{error}</p>
                  </div>
                )}
                
                <button 
                  className="btn btn-nurtify w-100 mb-4" 
                  onClick={handleSubmit} 
                  disabled={isPending || !password || !confirm_password}
                >
                  {isPending ? "Submitting..." : "Reset Password"}
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
      <LightFooter />
    </div>
  );
}
