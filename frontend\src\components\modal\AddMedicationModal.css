/* Add Medication Modal Styles */
.add-medication-modal {
  background-color: white;
  border-radius: 12px;
  width: 100%;
  max-width: 900px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.add-medication-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background-color: #37B7C3;
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.add-medication-modal-title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.add-medication-modal-title .modal-icon {
  margin-right: 12px;
  color: white;
}

.add-medication-modal-close {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  cursor: pointer;
  color: white;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.add-medication-modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

.add-medication-modal-body {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.medication-form-section {
  margin-bottom: 24px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #37B7C3;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.medication-form-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  color: #37B7C3;
}

.section-header svg {
  margin-right: 8px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.add-medication-modal-footer {
  display: flex;
  justify-content: flex-end;
  padding: 16px 24px;
  background-color: #f8f9fa;
  border-top: 1px solid #e5e7eb;
  gap: 12px;
}

.save-medication-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #37B7C3;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(55, 183, 195, 0.3);
}

.save-medication-btn:hover {
  background-color: #2d9aa4;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.4);
}

.save-medication-btn:active {
  transform: translateY(0);
}

/* Form Control Styles */
.form-control, .form-select {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s;
  width: 100%;
}

.form-control:focus, .form-select:focus {
  border-color: #37B7C3;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.2);
  outline: none;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

/* Toggle Switch Styles */
.toggle-switch-container {
  display: flex;
  align-items: center;
  margin-top: 12px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 30px;
  margin-right: 10px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #37B7C3;
}

input:focus + .toggle-slider {
  box-shadow: 0 0 1px #37B7C3;
}

input:checked + .toggle-slider:before {
  transform: translateX(30px);
}

.toggle-label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.status-toggle-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .add-medication-modal {
    max-width: 95%;
    margin: 0 10px;
  }
  
  .row {
    flex-direction: column;
  }
  
  .col-md-3, .col-md-4, .col-md-6 {
    width: 100%;
    margin-bottom: 12px;
  }
}
