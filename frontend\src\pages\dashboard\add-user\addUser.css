.add-user-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    padding: 15px 30px 30px 30px; /* Changed top padding */
    margin-bottom: 30px;
    width: 100%;
}

.add-user-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.add-user-title {
    margin-bottom: 10px;
}

.add-user-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.add-user-subtitle {
    color: #4F547B;
    font-size: 16px;
    font-weight: 400;
    margin-top: 5px;
}

.add-user-subtitle h6 {
    margin: 0;
    font-weight: 400;
}

.add-user-form {
    padding: 20px 0;
}

.user-form-section {
    background-color: #f8f9fa;
    border-radius: 0; /* Changed */
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: none; /* Changed */
}

.user-form-section-title {
    font-size: 18px;
    color: #2B3674;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
}

.user-form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
}

.user-btn-submit {
    background-color: #37B7C3;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 0; /* Changed */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: none; /* Changed */
}

.user-btn-submit:hover {
    background-color: #2A9A9F;
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

.user-btn-submit:disabled {
    background-color: #a0a0a0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.nurtify-select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e2e8f0;
    border-radius: 0; /* Changed */
    background-color: white;
    font-size: 14px;
    color: #4a5568;
    transition: all 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%234a5568' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
}

.nurtify-select:focus {
    outline: none;
    border-color: #37B7C3;
    box-shadow: none; /* Changed */
}

/* Responsive styles */
@media (max-width: 768px) {
    .add-user-container {
        padding: 20px;
    }
    
    .user-form-section {
        padding: 20px;
    }
    
    .user-form-actions {
        justify-content: center;
    }
}

@media (max-width: 576px) {
    .add-user-container {
        padding: 15px;
    }
    
    .user-form-section {
        padding: 15px;
    }
}
