import { useState, useEffect } from "react";
import Preloader from "@/components/common/Preloader";
import "./patient-board.css";
import LightFooter from "@/shared/LightFooter";
import DashboardSidebar from "@/components/common/DashboardSidebar";
import MobileSidebarIcons from "@/components/common/MobileSidebarIcons";
import {
  FileText,
  MessagesSquare,
  ClipboardList,
  Pill,
  ListCollapse,
  History,
  NotebookText,
  PillBottle
} from "lucide-react";
import AddPatientFormModal from "@/components/modal/AddPatientFormModal";
import DeletePatientModal from "@/components/modal/DeletePatientModal";
import { Outlet, useNavigate } from "react-router-dom";
import PatientInfoHeader from "@/components/PatientInfoHeader";
import useSelectedPatientStore from "@/store/SelectedPatientState";


type MenuItem = {
  icon: JSX.Element;
  label: string;
  path: string;
  onClick?: () => void;
};

export default function PatientBoard() {
  const [switchModal, setSwitchModal] = useState(false);
  const [isAddPatientFormModalOpen, setAddPatientFormModalOpen] = useState(false);
  const [isDeletePatientModalOpen, setDeletePatientModalOpen] = useState(false);
  const [isEditingPatient] = useState(false);
  const navigate = useNavigate();

  // Get the selected patient from the store
  const { selectedPatient, isLoading } = useSelectedPatientStore();

  // Redirect to patients list if no patient is selected
  useEffect(() => {
    if (!selectedPatient && !isLoading) {
      navigate('/patients');
    }
  }, [selectedPatient, isLoading, navigate]);

  // Show loading state while checking for selected patient
  if (isLoading) return <Preloader />;
  if (!selectedPatient) return <Preloader />;

  const menuItems: MenuItem[] = [
    {
      icon: <ListCollapse size={20} />,
      label: "Patient Details",
      path: `/patient-board/patient-details`,
    },
    {
      icon: <PillBottle size={20} />,
      label: "Patient Medications",
      path: `/patient-board/patient-medications`,
    },
    {
      icon: <FileText size={20} />,
      label: "Documentation",
      path: `/patient-board/patient-forms`,
    },
    {
      icon: <MessagesSquare size={20} />,
      label: "Forms Queries",
      path: `/patient-board/forms-queries`,
    },
    {
      icon: <FileText size={20} />,
      label: "Add New Form",
      path: `/patient-board/patient-documentation`,
    },
    {
      icon: <ClipboardList size={20} />,
      label: "Studies",
      path: `/patient-board/patient-studies`,
    },
    {
      icon: <NotebookText size={20} />,
      label: "Patient Diary",
      path: `/patient-board/patient-diary`,
    },

    // {
    //   icon: <ChartColumn size={20} />,
    //   label: "Assessment",
    //   path: `/patient-board/assessment`,
    // },
    {
      icon: <Pill size={20} />,
      label: "Prescriptions",
      path: `/patient-board/prescription`,
    },
    {
      icon: <History size={20} />,
      label: "Patient Logs",
      path: `/patient-board/patient-logs`,
    },
    {
      icon: <FileText size={20} />,
      label: "Reports/Reviews",
      path: `/patient-board/patient-reports-reviews`,
    },
    // {
    //   icon: <ClipboardList size={20} />,
    //   label: "Results/Reports",
    //   path: `/patient-board/results-reports`,
    // },
  ];

  const basePath = "/org/dashboard";
  return (
    <div className="main-content bg-light-4">
      <Preloader />

      <div className="content-wrapper js-content-wrapper">
        <div className="dashboard__content bg-light-4">
          <div className="container-fluid px-0">
            <div className="row y-gap-30">
              {/* Sidebar Column */}
              <div className="col-xl-3 col-lg-4">
                <DashboardSidebar menuItems={menuItems} basePath={basePath}/>
              </div>

              {/* Main Content */}
              <div className="col-xl-9 col-lg-8">
                <PatientInfoHeader />
                <Outlet />
              </div>
            </div>
          </div>
        </div>
        <MobileSidebarIcons menuItems={menuItems} basePath={basePath}/>
        <LightFooter />
      </div>
      <AddPatientFormModal
        isOpen={isAddPatientFormModalOpen}
        setIsModalOpen={setAddPatientFormModalOpen}
        setSwitchModal={setSwitchModal}
        switchModal={switchModal}
        isEditing={isEditingPatient}
      />
      <DeletePatientModal isOpen={isDeletePatientModalOpen} onClose={() => setDeletePatientModalOpen(false)} />
    </div>
  );
}
