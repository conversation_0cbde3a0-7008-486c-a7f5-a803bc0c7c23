import React, { useState, useEffect } from "react";
import { X, Save, Calendar, Activity, AlertCircle, Clock  } from "lucide-react";
import NurtifyInput from "../NurtifyInput";
import NurtifyTextArea from "../NurtifyTextArea";
import NurtifyCheckBox from "../NurtifyCheckBox";
import RequiredFieldIcon from "../RequiredFieldIcon";
import "./CreateVisitModal.css"; // Using dedicated CSS file

// Define available activities
const availableActivities = [
  "Local Blood Test",
  "Central Lab Test",
  "Walking Test",
  "Radiology Procedure",
  "Dosing",
  "Consenting",
  "Screening",
  "Hospitalization",
  "Baseline"
];

interface CreateVisitModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (visitData: {
    name: string;
    study_uuid: string;
    activities: string[];
    number: number;
    date: string;
    allowed_earlier_days: number;
    allowed_later_days: number;
    comments?: string;
    refered_by?: string;
    visit_status: string;
    study: number;
    patient: number;
  }) => void;
  studyUuid: string;
  enrollmentUuid: string;
  patientId?: number;
  studyId?: number;
}

const CreateVisitModal: React.FC<CreateVisitModalProps> = ({
  isOpen,
  onClose,
  onSave,
  studyUuid,
  patientId,
  studyId
}) => {
  const [visitData, setVisitData] = useState({
    name: "",
    study_uuid: studyUuid,
    activities: [] as string[],
    number: 1,
    date: new Date().toISOString().split('T')[0],
    allowed_earlier_days: 1,
    allowed_later_days: 1,
    comments: "",
    refered_by: "",
    visit_status: "Pending",
    study: studyId || 0,
    patient: patientId || 0
  });

  // Reset form data when modal opens or when props change
  useEffect(() => {
    if (isOpen) {
      setVisitData({
        name: "",
        study_uuid: studyUuid,
        activities: [] as string[],
        number: 1,
        date: new Date().toISOString().split('T')[0],
        allowed_earlier_days: 1,
        allowed_later_days: 1,
        comments: "",
        refered_by: "",
        visit_status: "Pending",
        study: studyId || 0,
        patient: patientId || 0
      });
    }
  }, [isOpen, studyUuid, patientId, studyId]);

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    
    // Handle numeric inputs
    if (
      name === "number" ||
      name === "allowed_earlier_days" ||
      name === "allowed_later_days" ||
      name === "study" ||
      name === "patient"
    ) {
      setVisitData({
        ...visitData,
        [name]: value === "" ? 0 : parseInt(value, 10)
      });
    } else {
      setVisitData({
        ...visitData,
        [name]: value
      });
    }
  };

  // Handle activity selection
  const handleActivitySelection = (activity: string) => {
    const activities = [...visitData.activities];
    
    if (activities.includes(activity)) {
      // Remove activity if already selected
      const index = activities.indexOf(activity);
      activities.splice(index, 1);
    } else {
      // Add activity if not selected
      activities.push(activity);
    }
    
    setVisitData({
      ...visitData,
      activities
    });
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Make sure all required fields are properly set with correct types
    const finalVisitData = {
      ...visitData,
      study_uuid: studyUuid,
      study: studyId || 0,
      patient: patientId || 0
    };
    
    // Verify we have all required fields with proper types
    if (!finalVisitData.study_uuid || finalVisitData.study === 0 || finalVisitData.patient === 0) {
      console.error("Missing required fields:", { 
        study_uuid: finalVisitData.study_uuid,
        study: finalVisitData.study,
        patient: finalVisitData.patient
      });
      alert("Missing required study or patient information. Please try again.");
      return;
    }
    
    onSave(finalVisitData);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="edit-visit-modal-overlay">
      <div className="edit-visit-modal" style={{ width: "600px", maxWidth: "90%" }}>
        <div className="edit-visit-modal-header">
          <h2 className="edit-visit-modal-title">
            <Calendar size={20} className="modal-icon" /> Create New Visit
          </h2>
          <button
            type="button"
            className="edit-visit-modal-close"
            onClick={onClose}
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="edit-visit-modal-body">
            <div className="form-section">
              <h3 className="form-section-title">
                <Calendar size={18} /> Visit Details
              </h3>
              
              <div className="form-group">
                <label htmlFor="name">Visit Name <RequiredFieldIcon /></label>
                <NurtifyInput
                  type="text"
                  id="name"
                  name="name"
                  value={visitData.name}
                  onChange={handleInputChange}
                  required
                  placeholder="e.g., Initial Assessment, Follow-up"
                />
              </div>
              
              <div className="form-row" style={{ display: "flex", gap: "1rem" }}>
                <div className="form-group" style={{ flex: 1 }}>
                  <label htmlFor="number">Visit Number <RequiredFieldIcon /></label>
                  <NurtifyInput
                    type="number"
                    id="number"
                    name="number"
                    value={visitData.number}
                    onChange={handleInputChange}
                    required
                    min="1"
                    placeholder="e.g., 1, 2, 3"
                  />
                </div>
                
                <div className="form-group" style={{ flex: 1 }}>
                  <label htmlFor="date">Visit Date <RequiredFieldIcon /></label>
                  <NurtifyInput
                    type="date"
                    id="date"
                    name="date"
                    value={visitData.date}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>
            </div>

            <div className="form-section">
              <h3 className="form-section-title">
                <Clock size={18} /> Visit Window
              </h3>
              
              <div className="form-row" style={{ display: "flex", gap: "1rem" }}>
                <div className="form-group" style={{ flex: 1 }}>
                  <label htmlFor="allowed_earlier_days">Days Before</label>
                  <NurtifyInput
                    type="number"
                    id="allowed_earlier_days"
                    name="allowed_earlier_days"
                    value={visitData.allowed_earlier_days}
                    onChange={handleInputChange}
                    min="0"
                    placeholder="e.g., 1"
                  />
                  <small>Days allowed before scheduled date</small>
                </div>

                <div className="form-group" style={{ flex: 1 }}>
                  <label htmlFor="allowed_later_days">Days After</label>
                  <NurtifyInput
                    type="number"
                    id="allowed_later_days"
                    name="allowed_later_days"
                    value={visitData.allowed_later_days}
                    onChange={handleInputChange}
                    min="0"
                    placeholder="e.g., 3"
                  />
                  <small>Days allowed after scheduled date</small>
                </div>
              </div>
            </div>

            <div className="form-section">
              <h3 className="form-section-title">
                <AlertCircle size={18} /> Additional Information
              </h3>
              
              <div className="form-group">
                <label htmlFor="visit-status">Status</label>
                <select
                  id="visit-status"
                  name="visit_status"
                  className="form-select"
                  value={visitData.visit_status}
                  onChange={handleInputChange}
                  style={{
                    width: "100%",
                    padding: "0.5rem",
                    border: "1px solid #ddd",
                    borderRadius: "0.25rem"
                  }}
                >
                  <option value="Pending">Pending</option>
                  <option value="Completed">Completed</option>
                  <option value="Delayed">Delayed</option>
                  <option value="Canceled">Canceled</option>
                </select>
              </div>
              
              <div className="form-group">
                <label htmlFor="refered_by">Referred By</label>
                <NurtifyInput
                  type="text"
                  id="refered_by"
                  name="refered_by"
                  value={visitData.refered_by || ""}
                  onChange={handleInputChange}
                  placeholder="Who referred this visit"
                />
              </div>
              
              <div className="form-group">
                <label htmlFor="comments">Comments</label>
                <NurtifyTextArea
                  id="comments"
                  name="comments"
                  value={visitData.comments || ""}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Add any notes or instructions for this visit"
                />
              </div>
            </div>

            <div className="form-section">
              <h3 className="form-section-title">
                <Activity size={18} /> Visit Activities
              </h3>
              
              <div className="activities-selection" style={{ display: "grid", gridTemplateColumns: "1fr 1fr", gap: "0.5rem" }}>
                {availableActivities.map((activity) => (
                  <div key={activity} className="activity-checkbox">
                    <NurtifyCheckBox
                      label={activity}
                      value={activity}
                      checked={visitData.activities.includes(activity)}
                      onChange={() => handleActivitySelection(activity)}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Hidden fields for required API parameters */}
            <input type="hidden" name="study_uuid" value={studyUuid} />
            <input type="hidden" name="study" value={studyId || 0} />
            <input type="hidden" name="patient" value={patientId || 0} />
          </div>

          <div className="edit-visit-modal-footer">
            <button type="button" className="cancel-btn" onClick={onClose}>
              <X size={16} /> Cancel
            </button>
            <button type="submit" className="save-btn">
              <Save size={16} /> Create Visit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateVisitModal;