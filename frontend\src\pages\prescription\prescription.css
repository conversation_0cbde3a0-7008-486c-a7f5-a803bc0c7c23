/* Prescription Page Styles */
.content-wrapper {
  min-height: 100vh;
}

.patient-details-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 24px;
  margin-bottom: 30px;
}

.patient-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #edf2f7;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.patient-actions {
  display: flex;
  gap: 12px;
}

/* Add Prescription Button */
.btn-primary-nurtify {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #36B6C2;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary-nurtify:hover {
  background-color: #2ea0ab;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(54, 182, 194, 0.3);
}

.btn-primary-nurtify:active {
  transform: translateY(0);
}

/* Status Styling */
.prescription-status {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  text-transform: capitalize;
}

.status-prescribed {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-administered {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-canceled {
  background-color: #fff2f0;
  color: #f5222d;
  border: 1px solid #ffccc7;
}

.status-pending {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffbb96;
}

/* No Result State */
.no-result {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background-color: #f8fafc;
  border-radius: 12px;
  border: 1px dashed #e2e8f0;
  text-align: center;
}

.no-result-icon {
  font-size: 48px;
  color: #cbd5e1;
  margin-bottom: 20px;
}

.no-result-title {
  font-size: 18px;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 10px;
}

.no-result-message {
  font-size: 14px;
  color: #94a3b8;
  max-width: 400px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .patient-details-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .patient-actions {
    width: 100%;
  }
  
  .btn-primary-nurtify {
    width: 100%;
    justify-content: center;
  }
  
  .patient-details-container {
    padding: 16px;
  }
}
