.password-container {
  position: relative;
  margin-bottom: 20px;
}

.password-input-wrapper {
  position: relative;
  width: 100%;
}

.password-input {
  width: 100%;
  height: 50px;
  border-radius: 12px;
  border: 2px solid #e4e7ea;
  background-color: #f7f8fb;
  font-size: 16px;
  padding: 0 15px;
  transition: all 0.3s ease;
  color: var(--color-dark-1);
  outline: none;
  padding-right: 40px; /* Space for the eye icon */
}

.password-input:focus {
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.2);
  background-color: white;
}

.password-toggle {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.error-container {
  background-color: rgba(237, 76, 92, 0.1);
  border-radius: 8px;
  padding: 10px;
  margin-bottom: 20px;
}

.error-message {
  color: #ed4c5c;
  font-size: 14px;
  margin: 0;
}

.success-message {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  font-size: 14px;
  font-weight: 500;
  padding: 10px;
  border-radius: 8px;
  margin-top: 10px;
  animation: fadeIn 0.3s ease-in-out;
}

.strength-meter {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
  margin-top: 8px;
}

.strength-segment {
  height: 4px;
  flex: 1;
  border-radius: 2px;
  transition: background-color 0.3s ease;
}

.strength-text {
  font-size: 12px;
  text-align: left;
}

.password-requirements {
  font-size: 12px;
  color: #64748b;
  margin-bottom: 12px;
  text-align: left;
  margin-top: 8px;
}

.password-requirements ul {
  padding-left: 16px;
  margin-top: 4px;
}

.mb-3 {
  margin-bottom: 12px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mobile responsiveness */
@media (max-width: 576px) {
  .password-input {
    height: 45px;
    font-size: 14px;
  }
}
