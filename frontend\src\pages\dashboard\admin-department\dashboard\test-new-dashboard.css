/* Test Dashboard Styles - Complete layout with header and sidebar */

/* Full Dashboard Layout */
.test-full-dashboard {
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Top Header */
.test-top-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 100;
}

.test-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 64px;
  max-width: 1400px;
  margin: 0 auto;
}

.test-logo-section {
  display: flex;
  align-items: center;
}

.test-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 700;
  color: #37B7C3;
}

.test-logo-icon {
  font-size: 28px;
}

.test-logo-text {
  color: #37B7C3;
}

.test-nav-menu {
  display: flex;
  align-items: center;
  gap: 32px;
}

.test-nav-link {
  color: #374151;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: color 0.2s;
}

.test-nav-link:hover {
  color: #37B7C3;
}

.test-header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.test-mail-icon {
  background: none;
  border: none;
  color: #37B7C3;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.test-mail-icon:hover {
  background-color: #f3f4f6;
}

.test-user-profile {
  display: flex;
  align-items: center;
}

.test-profile-image {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
}

/* Main Layout */
.test-main-layout {
  display: flex;
  min-height: calc(100vh - 64px);
}

/* Sidebar */
.test-sidebar {
  width: 250px;
  background: white;
  border-right: 1px solid #e5e7eb;
  flex-shrink: 0;
}

.test-sidebar-content {
  padding: 24px 0;
}

.test-minimize-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  color: #6b7280;
  font-size: 14px;
  cursor: pointer;
  margin-bottom: 16px;
}

.test-minimize-btn:hover {
  background-color: #f9fafb;
}

.test-sidebar-nav {
  display: flex;
  flex-direction: column;
}

.test-sidebar-link {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 24px;
  color: #6b7280;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.test-sidebar-link:hover {
  background-color: #f9fafb;
  color: #374151;
}

.test-sidebar-link.test-active {
  background-color: #37B7C3;
  color: white;
}

.test-sidebar-link.test-active:hover {
  background-color: #2ea4a9;
}

.test-sidebar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

/* Main Content */
.test-main-content {
  flex: 1;
  overflow-x: auto;
}

.test-dashboard-container {
  padding: 24px 32px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.test-dashboard-header {
  margin-bottom: 32px;
}

.test-welcome-title {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.test-user-name {
  color: #37B7C3;
}

.test-welcome-subtitle {
  font-size: 16px;
  color: #6b7280;
  margin: 0;
}

/* KPI Cards Row */
.test-kpi-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.test-kpi-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.test-kpi-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.test-kpi-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.test-patients-icon {
  background-color: #37B7C3;
}

.test-policies-icon {
  background-color: #37B7C3;
}

.test-sponsors-icon {
  background-color: #37B7C3;
}

.test-kpi-title-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.test-kpi-dropdown {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
}

.test-kpi-number {
  font-size: 48px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.test-kpi-change {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.test-change-text {
  font-size: 14px;
  color: #6b7280;
}

.test-change-percentage {
  font-size: 14px;
  font-weight: 600;
}

.test-kpi-change.test-positive .test-change-percentage {
  color: #10b981;
}

.test-kpi-change.test-negative .test-change-percentage {
  color: #ef4444;
}

/* Middle Section */
.test-middle-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

/* Staff Card */
.test-staff-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.test-staff-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.test-staff-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 8px 0;
}

.test-staff-dropdown {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
}

.test-add-user-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #37B7C3;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-add-user-btn:hover {
  background-color: #2ea4a9;
}

.test-staff-number {
  font-size: 48px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.test-staff-change {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-bottom: 24px;
}

.test-staff-change.test-positive .test-change-percentage {
  color: #10b981;
}

.test-staff-breakdown {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.test-staff-type {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-staff-type-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.test-staff-type-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.test-doctors-icon {
  background-color: #37B7C3;
}

.test-nurses-icon {
  background-color: #37B7C3;
}

.test-receptionist-icon {
  background-color: #37B7C3;
}

.test-others-icon {
  background-color: #37B7C3;
}

.test-staff-count {
  margin-left: auto;
  font-weight: 600;
}

.test-progress-bar {
  height: 8px;
  background-color: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.test-progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.test-doctors-progress {
  background-color: #37B7C3;
}

.test-nurses-progress {
  background-color: #37B7C3;
}

.test-receptionist-progress {
  background-color: #37B7C3;
}

.test-others-progress {
  background-color: #37B7C3;
}

.test-progress-percentage {
  font-size: 12px;
  color: #6b7280;
  text-align: right;
}

/* Studies Card */
.test-studies-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.test-studies-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 20px 0;
}

.test-studies-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;
}

.test-stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.test-stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.test-stat-value {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
}

.test-studies-table-container {
  overflow-x: auto;
}

.test-studies-table {
  width: 100%;
  border-collapse: collapse;
}

.test-studies-table th {
  text-align: left;
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
}

.test-studies-table td {
  padding: 12px;
  font-size: 14px;
  color: #374151;
  border-bottom: 1px solid #f3f4f6;
}

.test-studies-table tbody tr:hover {
  background-color: #f9fafb;
}

.test-status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.test-status-badge.test-pending {
  background-color: #fef3c7;
  color: #d97706;
}

.test-action-buttons {
  display: flex;
  gap: 8px;
}

.test-action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-action-btn.test-accept {
  background-color: #dcfce7;
  color: #16a34a;
}

.test-action-btn.test-accept:hover {
  background-color: #bbf7d0;
}

.test-action-btn.test-reject {
  background-color: #fee2e2;
  color: #dc2626;
}

.test-action-btn.test-reject:hover {
  background-color: #fecaca;
}

/* Requests Section */
.test-requests-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.test-requests-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.test-requests-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.test-requests-stats {
  display: flex;
  align-items: center;
  gap: 24px;
}

.test-total-requests {
  font-size: 14px;
  color: #6b7280;
}

.test-requests-chart {
  display: flex;
  align-items: end;
  gap: 4px;
  height: 40px;
}

.test-chart-bar {
  width: 12px;
  border-radius: 2px;
}

.test-chart-bar.test-accepted {
  background-color: #10b981;
}

.test-chart-bar.test-pending {
  background-color: #f59e0b;
}

.test-chart-bar.test-rejected {
  background-color: #ef4444;
}

.test-chart-legend {
  display: flex;
  gap: 16px;
}

.test-legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
}

.test-legend-color {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.test-legend-color.test-accepted {
  background-color: #10b981;
}

.test-legend-color.test-pending {
  background-color: #f59e0b;
}

.test-legend-color.test-rejected {
  background-color: #ef4444;
}

.test-requests-table-container {
  overflow-x: auto;
}

.test-requests-table {
  width: 100%;
  border-collapse: collapse;
}

.test-requests-table th {
  text-align: left;
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
}

.test-requests-table td {
  padding: 12px;
  font-size: 14px;
  color: #374151;
  border-bottom: 1px solid #f3f4f6;
}

.test-requests-table tbody tr:hover {
  background-color: #f9fafb;
}

.test-details-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: #dbeafe;
  color: #2563eb;
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.test-details-btn:hover {
  background-color: #bfdbfe;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .test-main-layout {
    flex-direction: column;
  }
  
  .test-sidebar {
    width: 100%;
    order: 2;
  }
  
  .test-main-content {
    order: 1;
  }
  
  .test-middle-section {
    grid-template-columns: 1fr;
  }
  
  .test-kpi-row {
    grid-template-columns: 1fr;
  }
  
  .test-nav-menu {
    display: none;
  }
}

@media (max-width: 768px) {
  .test-dashboard-container {
    padding: 16px;
  }
  
  .test-header-content {
    padding: 0 16px;
  }
  
  .test-kpi-card,
  .test-staff-card,
  .test-studies-card,
  .test-requests-section {
    padding: 16px;
  }
  
  .test-requests-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .test-requests-stats {
    flex-wrap: wrap;
  }
  
  .test-sidebar {
    display: none;
  }
}
