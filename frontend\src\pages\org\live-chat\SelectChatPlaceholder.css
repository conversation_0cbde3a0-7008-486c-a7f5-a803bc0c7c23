/* Inherit root variables from a global scope or LiveChat.css if this component is always nested there.
   For standalone styling or if variables might not be present, they could be redefined or imported.
   Assuming variables like --primary-color, --text-color, --text-secondary, --secondary-color are available. */

   .select-chat-placeholder-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%; /* Fill the parent .org-livechat-container */
    text-align: center;
    padding: 20px;
    background-color: var(--secondary-color, #f0f4f8); /* Fallback if var not defined */
    color: var(--text-secondary, #555);
    user-select: none; /* Prevent text selection */
  }
  .select-chat-placeholder-container2 {
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%; /* Fill the parent .org-livechat-container */
    text-align: center;
    padding: 50px 20px;
    color: var(--text-secondary, #555);
    user-select: none; /* Prevent text selection */
  }

.placeholder-icon {
  color: #37B7C3; /* Fallback */
  opacity: 0.6;
  margin-bottom: 24px;
  width: 64px; /* Explicit size for icon container if needed */
  height: 64px;
}

.placeholder-title {
  font-size: 22px;
  font-weight: 600;
  color: var(--text-color, #333); /* Fallback */
  margin-bottom: 8px;
}

.placeholder-text {
  font-size: 15px;
  max-width: 350px;
  line-height: 1.6;
  color: var(--text-secondary, #555); /* Fallback */
}
