import { faTrash } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import BLSImage from "./static/images/BLS.jpg";
import patientFaceImage from "./static/images/patientFace.jpg";
import allergyIMG from "./static/images/added/allergy.png";
import React, { useState, useEffect } from "react";
import useHolisticFormStore from "../../../../store/holisticFormState";
import MEDICATIONLIST from "../constants/MEDICATIONLIST.json";
import NurtifyRadio from "../../../../components/NurtifyRadio";
import NurtifyInput from "../../../../components/NurtifyInput";
import NurtifyCheckBox from "../../../../components/NurtifyCheckBox";

const AssessmentOverview: React.FC = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  
  // Function to update overview state
  const setOverview = (overviewData: Partial<typeof assessment.overview>) => {
    setAssessment({
      ...assessment,
      overview: {
        ...assessment.overview,
        ...overviewData
      }
    });
  };

  const [arousableOrNo, setArousableOrNo] = useState<string>("");
  const [postureAndDecubitus, setPostureAndDecubitus] = useState<string[]>(assessment?.overview?.postureAndDecubitus || []);
  const [clothingAndPharnalia, setClothingAndPharnalia] = useState<string[]>(assessment?.overview?.clothingAndPharnalia || []);
  const [form17Options, setForm17Options] = useState<string[]>(assessment?.overview?.behaviouralState || []);
  const [medicationsSelectedOptions, setMedicationsSelectedOptions] = useState<string[]>(assessment?.overview?.medications || []);
  const [matchedMedicationsOptions, setMatchedMedicationsOptions] = useState<string[]>([]);
  const [medicationSearchInput, setMedicationSearchInput] = useState<string>("");
  const [optionsModalShowing, setOptionsModalShowing] = useState<boolean>(false);
  const [form17Showing, setForm17Showing] = useState<boolean>(false);
  const [blsModalShowing, setBlsModalShowing] = useState<boolean>(false);
  const [faciesAndExpressions, setFaciesAndExpressions] = useState<string[]>(assessment?.overview?.faciesAndExpressions || []);
  const [odorsOfBreathAndBody, setOdorsOfBreathAndBody] = useState<string[]>(assessment?.overview?.odorsOfBreathAndBody || []);

  // Initialize state from assessment data
  useEffect(() => {
    if (assessment?.overview) {
      setArousableOrNo(assessment.overview.patientAwake === "sleeping" ? "no" : "");
      setPostureAndDecubitus(assessment.overview.postureAndDecubitus || []);
      setClothingAndPharnalia(assessment.overview.clothingAndPharnalia || []);
      setForm17Options(assessment.overview.behaviouralState || []);
      setMedicationsSelectedOptions(assessment.overview.medications || []);
      setFaciesAndExpressions(assessment.overview.faciesAndExpressions || []);
      setOdorsOfBreathAndBody(assessment.overview.odorsOfBreathAndBody || []);
    }
  }, [assessment?.overview]);

  // Handle medication search input change
  const handleMedicationsSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setMedicationSearchInput(value);

    // Filter the options based on the search input
    const matchedOptions = MEDICATIONLIST.filter((option) =>
      option.toLowerCase().includes(value.toLowerCase())
    );
    setMatchedMedicationsOptions(matchedOptions.slice(0, 10)); // Limit to 10 results for performance
  };

  // Handle medication search key press (Enter)
  const handleMedicationSearchKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      const inputOption = medicationSearchInput.trim();
      if (inputOption && !medicationsSelectedOptions.includes(inputOption)) {
        // Add the input option to the selectedOptions array
        const updatedOptions = [...medicationsSelectedOptions, inputOption];
        setMedicationsSelectedOptions(updatedOptions);
        setOverview({
          medications: updatedOptions,
        });
        // Clear the search input and matched options
        setMedicationSearchInput("");
        setMatchedMedicationsOptions([]);
      }
    }
  };

  // Handle medication option click
  const handleMedicationOptionClick = (option: string) => {
    if (!medicationsSelectedOptions.includes(option)) {
      // Add the selected option to the selectedOptions array
      const updatedOptions = [...medicationsSelectedOptions, option];
      setMedicationsSelectedOptions(updatedOptions);
      setOverview({
        medications: updatedOptions,
      });
      // Clear the search input and matched options
      setMedicationSearchInput("");
      setMatchedMedicationsOptions([]);
    }
  };

  // Handle medication remove option
  const handleMedicationsRemoveOption = (optionToRemove: string) => {
    // Remove the option from the selectedOptions array
    const updatedOptions = medicationsSelectedOptions.filter(
      (option) => option !== optionToRemove
    );
    setMedicationsSelectedOptions(updatedOptions);
    setOverview({
      medications: updatedOptions,
    });
  };

  // Handle form17 checkbox change (Behavioral state)
  const handleForm17CheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const updatedOptions = event.target.checked
      ? [...form17Options, value]
      : form17Options.filter((item) => item !== value);
    
    setForm17Options(updatedOptions);
    setOverview({
      behaviouralState: updatedOptions,
    });
  };

  // Handle clothing checkbox change
  const handleClothingCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const updatedOptions = event.target.checked
      ? [...clothingAndPharnalia, value]
      : clothingAndPharnalia.filter((item) => item !== value);
    
    setClothingAndPharnalia(updatedOptions);
    setOverview({
      clothingAndPharnalia: updatedOptions,
    });
  };

  // Handle facies and expressions checkbox change
  const handleFaciesAndExpressionsCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const updatedOptions = event.target.checked
      ? [...faciesAndExpressions, value]
      : faciesAndExpressions.filter((item) => item !== value);
    
    setFaciesAndExpressions(updatedOptions);
    setOverview({
      faciesAndExpressions: updatedOptions,
    });
  };

  // Handle posture and decubitus checkbox change
  const handlePostureAndDecubitusCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const updatedOptions = event.target.checked
      ? [...postureAndDecubitus, value]
      : postureAndDecubitus.filter((item) => item !== value);
    
    setPostureAndDecubitus(updatedOptions);
    setOverview({
      postureAndDecubitus: updatedOptions,
    });
  };

  // Handle odor of breath and body checkbox change
  const handleOdorOfBreathAndBodyCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    const updatedOptions = event.target.checked
      ? [...odorsOfBreathAndBody, value]
      : odorsOfBreathAndBody.filter((item) => item !== value);
    
    setOdorsOfBreathAndBody(updatedOptions);
    setOverview({
      odorsOfBreathAndBody: updatedOptions,
    });
  };

  return (
      <div className="container-fluid">
        {/* Appearance */}
        <div className="row mb-4">
          <div className="col-12">
            <div id="division-14" className="bg-light rounded p-4">
              <div className="d-flex align-items-center mb-4 headinqQuestion">
                <img
                  src={patientFaceImage}
                  className="imageEtiquette me-3"
                  alt="patient face"
                />
                <h2 className="fs-2 text-start etiquetteHeadingForms m-0">
                  Patient Appearance
                </h2>
              </div>

              <div className="row">
                <div className="col-md-8 col-lg-6">
                  <h4 className="headinqQuestion mb-3">Is the patient awake?</h4>

                  <div className="mb-3">
                    <NurtifyRadio
                      label="Patient is awake"
                      name="isPatientAwake"
                      value="awake"
                      checked={assessment?.overview?.patientAwake === "awake"}
                      onChange={() =>
                        setOverview({
                          patientAwake: "awake",
                        })
                      }
                    />
                  </div>

                  <div className="mb-3">
                    <NurtifyRadio
                      label="Patient appears sleeping, unconscious, or has collapsed"
                      name="isPatientAwake"
                      value="sleeping"
                      checked={assessment?.overview?.patientAwake === "sleeping"}
                      onChange={() => {
                        setOverview({
                          patientAwake: "sleeping",
                        });
                        setBlsModalShowing(true);
                      }}
                    />
                  </div>
                </div>
              </div>

              {assessment?.overview?.patientAwake === "sleeping" && !blsModalShowing && (
                <div id="15-b" className="mt-4">
                  <div className="row">
                    <div className="col-md-8 col-lg-6">
                      <h4 className="headinqQuestion mb-3">
                        Is the patient arousable (awaken) by voice or shaking
                        stimulation?
                      </h4>

                      <div className="mb-3">
                        <NurtifyRadio
                          label="Yes"
                          name="arousableOrNo"
                          value="yes"
                          checked={arousableOrNo === "yes"}
                          onChange={() => {
                            setArousableOrNo("yes");
                          }}
                        />
                      </div>

                      <div className="mb-3">
                        <NurtifyRadio
                          label="No"
                          name="arousableOrNo"
                          value="no"
                          checked={arousableOrNo === "no"}
                          onChange={() => {
                            setArousableOrNo("no");
                            // setBlsModalShowing(true);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* First Impression */}
        <div className="row mb-4">
          <div className="col-12">
            <div id="division-16" className="bg-light rounded p-4">
              <h4 className="headinqQuestion mb-3">First Impression</h4>

              <div className="row">
                <div className="col-md-8 col-lg-6">
                  <div className="mb-3">
                    <NurtifyRadio
                      label="Normal Appearance"
                      name="patientAppearance"
                      value="Normal"
                      checked={assessment?.overview?.patientAppearance === "Normal"}
                      onChange={() =>
                        setOverview({
                          patientAppearance: "Normal",
                        })
                      }
                    />
                  </div>

                  <div className="mb-3">
                    <NurtifyRadio
                      label="Abnormal Appearance"
                      name="patientAppearance"
                      value="Abnormal"
                      checked={assessment?.overview?.patientAppearance === "Abnormal"}
                      onChange={() => {
                        setOverview({
                          patientAppearance: "Abnormal",
                        });
                        setOptionsModalShowing(true);
                        setForm17Showing(true);
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Allergy */}
        <div className="row mb-4">
          <div className="col-12">
            <div id="division-18" className="bg-light rounded p-4">
              <div className="d-flex align-items-center mb-4 headinqQuestion">
                <img
                  src={allergyIMG}
                  className="imageEtiquette me-3"
                  alt="Allergy icon"
                />
                <h2 className="fs-2 text-start etiquetteHeadingForms m-0">
                  Allergy
                </h2>
              </div>

              <div className="row">
                <div className="col-md-8 col-lg-6">
                  <h4 className="headinqQuestion mb-3">
                    Does the patient have any known allergy?
                  </h4>

                  <div className="mb-3">
                    <NurtifyRadio
                      label="Yes"
                      name="patientHasAllergy"
                      value="Yes"
                      checked={assessment?.overview?.patientHasAllergy === "Yes"}
                      onChange={() =>
                        setOverview({
                          patientHasAllergy: "Yes",
                        })
                      }
                    />
                  </div>

                  <div className="mb-3">
                    <NurtifyRadio
                      label="No"
                      name="patientHasAllergy"
                      value="No"
                      checked={assessment?.overview?.patientHasAllergy === "No"}
                      onChange={() =>
                        setOverview({
                          patientHasAllergy: "No",
                        })
                      }
                    />
                  </div>

                  <div className="mb-3">
                    <NurtifyRadio
                      label="Unable to obtain"
                      name="patientHasAllergy"
                      value="Unable"
                      checked={assessment?.overview?.patientHasAllergy === "Unable"}
                      onChange={() =>
                        setOverview({
                          patientHasAllergy: "Unable",
                        })
                      }
                    />
                  </div>
                </div>
              </div>

              {assessment?.overview?.patientHasAllergy === "Yes" && (
                <div id="division-18-a" className="mt-4">
                  <div className="row">
                    <div className="col-md-8">
                      <h4 className="headinqQuestion mb-3">
                        Please Specify what allergy the patient has:
                      </h4>

                      <NurtifyInput
                        placeholder="Start Typing..."
                        type="text"
                        value={medicationSearchInput}
                        onChange={handleMedicationsSearchInputChange}
                        onKeyDown={handleMedicationSearchKeyPress}
                        className="mb-3"
                      />

                      {medicationsSelectedOptions.length > 0 && (
                        <div className="d-flex flex-wrap gap-2 mb-3">
                          {medicationsSelectedOptions.map((opt, index) => (
                            <div
                              key={index}
                              className="badge bg-info text-dark d-flex align-items-center p-2"
                            >
                              {opt}
                              <button
                                className="btn btn-sm ms-2 p-0"
                                onClick={() => handleMedicationsRemoveOption(opt)}
                                aria-label="Remove allergy"
                              >
                                <FontAwesomeIcon icon={faTrash} size="xs" />
                              </button>
                            </div>
                          ))}
                        </div>
                      )}

                      {matchedMedicationsOptions.length > 0 && (
                        <div className="list-group mb-3">
                          {matchedMedicationsOptions.map((opt, index) => (
                            <button
                              key={index}
                              type="button"
                              className="list-group-item list-group-item-action"
                              onClick={() => handleMedicationOptionClick(opt)}
                            >
                              {opt}
                            </button>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* BLS Alert Modal */}
        {blsModalShowing && (
          <div className="modal-overlay position-fixed top-0 start-0 w-100 h-100 bg-dark bg-opacity-75 d-flex align-items-center justify-content-center" style={{ zIndex: 1050 }}>
            <div className="modal-dialog" style={{ maxWidth: "600px", margin: "10px", maxHeight: "90vh" }}>
              <div className="modal-content" style={{ maxHeight: "90vh" }}>
                <div className="modal-header bg-danger text-white">
                  <h5 className="modal-title fw-bold">EMERGENCY ALERT</h5>
                  <button
                    type="button"
                    className="btn-close btn-close-white"
                    onClick={() => {
                      setBlsModalShowing(false);
                    }}
                  ></button>
                </div>
                <div className="modal-body p-4">
                  <div className="text-center">
                    <h4 className="text-danger fw-bold mb-4">
                      PLEASE seek appropriate help and start Basic Life
                      Support Protocol
                    </h4>
                    <div className="mb-4">
                      <img
                        src={BLSImage}
                        className="img-fluid"
                        style={{ maxWidth: "100%", maxHeight: "50vh" }}
                        alt="Basic Life Support Protocol"
                      />
                    </div>
                    <div>
                      <a
                        href="#"
                        className="fw-bold text-primary fs-5"
                      >
                        Or click here for ACLS Algorithm
                      </a>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    className="btn btn-primary"
                    onClick={() =>
                      window.open(
                        "https://www.resus.org.uk/sites/default/files/2024-01/Adult%20Advanced%20Life%20Support%20Algorithm%202021%20Aug%202023.pdf",
                        "_blank",
                        "noopener,noreferrer"
                      )
                    }
                  >
                    ACLS
                  </button>
                  <button
                    className="btn btn-danger"
                    onClick={() => {
                      setBlsModalShowing(false);
                    }}
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Abnormal Appearance Modal */}
        {optionsModalShowing && (
          <div className="modal-overlay position-fixed top-0 start-0 w-100 h-100 bg-dark bg-opacity-75 d-flex align-items-center justify-content-center" style={{ zIndex: 1050 }}>
            <div className="modal-dialog" style={{ maxWidth: "85%", margin: "10px", height: "90vh" }}>
              <div className="modal-content h-100 d-flex flex-column">
                <div className="modal-header bg-light">
                  <h5 className="modal-title fw-bold">Abnormal Appearance Details</h5>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => {
                      setOptionsModalShowing(false);
                      setForm17Showing(false);
                    }}
                  ></button>
                </div>
                <div className="modal-body p-4 overflow-auto" style={{ flex: 1 }}>
                  {form17Showing && (
                    <div className="container-fluid p-0">
                      <div className="alert alert-info mb-4">
                        <h4 className="text-center mb-0">PLEASE SELECT ALL APPLICABLE</h4>
                      </div>
                      
                      <div className="d-flex flex-column gap-4">
                        {/* Behavioral State */}
                        <div className="card border-0 shadow-sm">
                          <div className="card-header bg-primary text-white">
                            <h5 className="mb-0">Behavioural state</h5>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              {["Agitated", "Confused", "Unresponsive", "Aggressive", "Lethargic", "Drowsy", "Sleepy appearance"].map((option) => (
                                <div key={option} className="col-md-12 col-sm-6 mb-2">
                                  <NurtifyCheckBox
                                    label={option}
                                    value={option}
                                    checked={form17Options.includes(option)}
                                    onChange={handleForm17CheckboxChange}
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* Clothing and paraphernalia */}
                        <div className="card border-0 shadow-sm">
                          <div className="card-header bg-info text-dark">
                            <h5 className="mb-0">Clothing and paraphernalia</h5>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              {["Hospital Gown", "Wet clothing", "Unkempt", "No clothes", "Inappropriate heavy", "Clothing", "Other Clothing"].map((option) => (
                                <div key={option} className="col-md-12 col-sm-6 mb-2">
                                  <NurtifyCheckBox
                                    label={option}
                                    value={option}
                                    checked={clothingAndPharnalia.includes(option)}
                                    onChange={handleClothingCheckboxChange}
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* Facies and expression */}
                        <div className="card border-0 shadow-sm">
                          <div className="card-header bg-warning text-dark">
                            <h5 className="mb-0">Facies and expression</h5>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              {["In Pain", "Facial redness", "Facial drop", "Dry skin", "Facial swelling", "Loss of lateral Eyebrows", "Pale", "Periorbital edema", "Sweating"].map((option) => (
                                <div key={option} className="col-md-12 col-sm-6 mb-2">
                                  <NurtifyCheckBox
                                    label={option}
                                    value={option}
                                    checked={faciesAndExpressions.includes(option)}
                                    onChange={handleFaciesAndExpressionsCheckboxChange}
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* Posture and decubitus */}
                        <div className="card border-0 shadow-sm">
                          <div className="card-header bg-success text-white">
                            <h5 className="mb-0">Posture and decubitus</h5>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              {[
                                "Normal Posture", "Arm rigidity", "Legs rigidity", "Hemiplegic posture", 
                                "Flexor hypertonia", "Cerebellar posture", "Opisthotonos of meningitis", 
                                "Flailing in bed (Due to pain)", "Tonic distortions", "Leg shortening"
                              ].map((option) => (
                                <div key={option} className="col-md-12 col-sm-6 mb-2">
                                  <NurtifyCheckBox
                                    label={option}
                                    value={option}
                                    checked={postureAndDecubitus.includes(option)}
                                    onChange={handlePostureAndDecubitusCheckboxChange}
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>

                        {/* Odor of breath and body */}
                        <div className="card border-0 shadow-sm">
                          <div className="card-header bg-secondary text-white">
                            <h5 className="mb-0">Odor of breath and body</h5>
                          </div>
                          <div className="card-body">
                            <div className="row">
                              {[
                                "Alcohol smell", "Cannabis smell", "Fruity-smelling breath (?? Ketoacidosis)", 
                                "Faecal odor", "Urine odor", "Odor of uremia"
                              ].map((option) => (
                                <div key={option} className="col-md-12 col-sm-6 mb-2">
                                  <NurtifyCheckBox
                                    label={option}
                                    value={option}
                                    checked={odorsOfBreathAndBody.includes(option)}
                                    onChange={handleOdorOfBreathAndBodyCheckboxChange}
                                  />
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="modal-footer bg-light">
                  <button
                    type="button"
                    className="btn btn-primary px-4 py-2"
                    onClick={() => {
                      setOptionsModalShowing(false);
                      setForm17Showing(false);
                    }}
                  >
                    Submit and Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
  );
};

export default AssessmentOverview;
