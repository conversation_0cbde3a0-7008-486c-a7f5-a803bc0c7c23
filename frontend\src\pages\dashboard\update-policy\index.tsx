import Preloader from "@/components/common/Preloader";
import "./updatePolicy.css";
import { useEffect, useState } from "react";
import { FileText } from "lucide-react";
import NurtifyText from "@components/NurtifyText.tsx";
import NurtifyInput from "@components/NurtifyInput.tsx";
import NurtifySelect from "@components/NurtifySelect";
import NurtifyAttachFileBox from "@/components/common/NurtifyAttachFileBox";
import { useUpdatePolicyMutation } from "@/hooks/policy.query.ts";
import { useNavigate, useParams } from "react-router-dom";
import { getPolicyByUuid } from "@/services/api/policy.service";
import { POLICY_KEYS } from "@/hooks/keys";
import { useQuery } from "@tanstack/react-query";
import { Policy } from "@/types/types";
import { useStudiesQuery } from "@/hooks/study.query";

// Define policy categories to match backend model
const POLICY_CATEGORIES = [
  { label: "Clinical Trial Protocol", value: "CLINICAL_TRIAL_PROTOCOL" },
  { label: "Lab Manual", value: "LAB_MANUAL" },
  { label: "Pharmacy Manual", value: "PHARMACY_MANUAL" },
  { label: "Imaging Manual", value: "IMAGING_MANUAL" },
  { label: "ECG or Cardiac Monitoring Manual", value: "ECG_MANUAL" },
  { label: "Randomization and Unblinding Procedures", value: "RANDOMIZATION_PROCEDURES" },
  { label: "Patient Information Sheet", value: "PATIENT_INFO_SHEET" },
  { label: "Patient Education Brochures", value: "PATIENT_EDUCATION" },
  { label: "Safety Management Plan", value: "SAFETY_MANAGEMENT" },
  { label: "Site Visit Schedule", value: "SITE_VISIT_SCHEDULE" },
  { label: "Other", value: "OTHER" },
] as const;

interface FormData {
  title: string;
  category: string;
  author_name: string;
  job_title: string;
  description: string;
  attach_content: File | null;
  study_uuid: string | null;
}

interface ConfirmationUpdatePolicyModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  subMessage?: string;
}

interface Study {
  uuid: string;
  name: string;
}

function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  subMessage,
}: ConfirmationUpdatePolicyModalProps) {
  if (!isOpen) return null;

  return (
    <div
      style={{
        position: "fixed",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(0, 0, 0, 0.5)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 1000,
      }}
    >
      <div
        style={{
          backgroundColor: "white",
          borderRadius: "16px",
          padding: "32px",
          width: "90%",
          maxWidth: "500px",
          boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
        }}
      >
        <h2
          style={{
            color: "#1B2559",
            fontSize: "24px",
            fontWeight: "600",
            marginBottom: "16px",
          }}
        >
          {title}
        </h2>
        <p
          style={{
            color: "#1B2559",
            fontSize: "16px",
            marginBottom: "8px",
          }}
        >
          {message}
        </p>
        {subMessage && (
          <p
            style={{
              color: "#64748B",
              fontSize: "14px",
              marginBottom: "24px",
            }}
          >
            {subMessage}
          </p>
        )}
        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            gap: "12px",
            marginTop: "24px",
          }}
        >
          <button
            onClick={onClose}
            style={{
              padding: "12px 24px",
              borderRadius: "8px",
              border: "none",
              backgroundColor: "#94A3B8",
              color: "white",
              cursor: "pointer",
              fontSize: "14px",
              fontWeight: "500",
            }}
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            style={{
              padding: "12px 24px",
              borderRadius: "8px",
              border: "none",
              backgroundColor: "#37B7C3",
              color: "white",
              cursor: "pointer",
              fontSize: "14px",
              fontWeight: "500",
            }}
          >
            Proceed
          </button>
        </div>
      </div>
    </div>
  );
}

export default function UpdatePolicy() {
  const navigate = useNavigate();
  const { uuid } = useParams<{ uuid: string }>();
  const updatePolicyMutation = useUpdatePolicyMutation();
  const { data: studiesData } = useStudiesQuery();
  const [formData, setFormData] = useState<FormData>({
    title: "",
    category: "",
    author_name: "",
    job_title: "",
    description: "",
    attach_content: null,
    study_uuid: null,
  });
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData((prev) => ({
      ...prev,
      category: e.target.value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsConfirmModalOpen(true); // Show confirmation modal
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  const queryEnabled = Boolean(uuid);
  const { data: policyData, isLoading } = useQuery({
    queryKey: [POLICY_KEYS.GET_BY_UUID, uuid],
    queryFn: () => getPolicyByUuid(uuid!),
    enabled: queryEnabled,
  });

  // Convert studies data to options for the select component
  const studyOptions = studiesData?.map((study: Study) => ({
    value: study.uuid,
    label: study.name,
  })) || [];

  const handleStudyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData((prev) => ({
      ...prev,
      study_uuid: e.target.value || null,
    }));
  };

  useEffect(() => {
    if (policyData) {
      setFormData((prev) => ({
        title: policyData.title || "",
        category: policyData.category || "",
        author_name: policyData.author_name || "",
        job_title: policyData.job_title || "",
        description: policyData.description || "",
        attach_content: prev.attach_content,
        study_uuid: policyData.study?.uuid || null,
      }));
      console.log(formData);
      console.log(policyData);
    }
  }, [policyData]);

  const handleConfirmUpdate = async () => {
    if (!uuid) return;

    const submitData = new FormData();
    submitData.append("title", formData.title);
    submitData.append("category", formData.category);
    submitData.append("author_name", formData.author_name);
    submitData.append("job_title", formData.job_title);
    submitData.append("description", formData.description);

    if (formData.study_uuid) {
      submitData.append("study_uuid", formData.study_uuid);
    }

    if (formData.attach_content) {
      submitData.append(
        "attach_content",
        formData.attach_content,
        formData.attach_content.name
      );
    }

    try {
      await updatePolicyMutation.mutateAsync({
        uuid,
        data: submitData as Partial<Policy>,
      });
      setIsConfirmModalOpen(false);
      navigate("/org/dashboard/policy");
    } catch (error) {
      console.error("Error updating policy:", error);
      setIsConfirmModalOpen(false);
    }
  };

  return (
    <div className="update-policy-container">
      <div>
        {/* Header with icon and title */}
        <div className="update-policy-header">
          <div className="update-policy-title">
            <h1>
              <FileText size={24} style={{ marginRight: "10px" }} />
              Update Policy
            </h1>
          </div>
          <div className="update-policy-subtitle">
            <h6>Modify and update existing policy information</h6>
          </div>
        </div>

        {/* Fragment pour éviter l'erreur JSX */}
        <>
          {isLoading && <Preloader />}
          {!isLoading && (
            <form className="add-policy-form" onSubmit={handleSubmit}>
              <div className="form-group col-md-12">
                <NurtifyText label="Policy Title*" />
                <NurtifyInput
                  type="text"
                  name="title"
                  value={formData.title}
                  onChange={(e) =>
                    setFormData({ ...formData, title: e.target.value })
                  }
                  placeholder="Policy Title"
                  required
                />
              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="Category*" />
                <NurtifySelect
                  name="category"
                  value={formData.category}
                  onChange={handleCategoryChange}
                  options={[
                    { value: "", label: "Select a category" },
                    ...POLICY_CATEGORIES
                  ]}
                />
              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="Author Name" />
                <NurtifyInput
                  type="text"
                  name="author_name"
                  id="author_name"
                  value={formData.author_name}
                  onChange={handleInputChange}
                  placeholder="Author Name"
                  required
                />
              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="Job Title*" />
                <NurtifyInput
                  type="text"
                  name="job_title"
                  value={formData.job_title}
                  onChange={handleInputChange}
                  placeholder="Job Title"
                  required
                />
              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="Short Description*" />
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                />
              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="File Attachment" />
                <NurtifyAttachFileBox
                  onChange={(file) => {
                    console.log("File selected:", file);
                    setFormData((prev) => ({
                      ...prev,
                      attach_content: file,
                    }));
                  }}
                />
                <p>{formData.attach_content?.name} </p>

              </div>
              <div className="form-group col-md-12">
                <NurtifyText label="Study (Optional)" />
                <NurtifySelect
                  name="study_uuid"
                  value={formData.study_uuid || ""}
                  onChange={handleStudyChange}
                  options={[
                    { value: "", label: "Select a study (optional)" },
                    ...studyOptions
                  ]}
                />
              </div>
              <button
                type="submit"
                className="button text-white btn-nurtify mr-10 px-4 py-2"
                style={{ alignSelf: "flex-end" }}
                disabled={updatePolicyMutation.isPending}
              >
                {updatePolicyMutation.isPending ? "Submitting..." : "Submit"}
              </button>
            </form>
          )}
        </>
      </div>
      <ConfirmationModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={handleConfirmUpdate}
        title="Confirm Updating policy"
        message="Are you sure you want to update this policy?"
        subMessage="This action is permanent and cannot be undone. All associated data will be lost."
      />
    </div>
  );
}
