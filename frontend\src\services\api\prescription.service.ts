import api from "@/services/api.ts";

import { Prescription,PrescriptionWithDetails } from "@/services/api/types";



export const getAllPrescriptions = async () => {
    try {
        const response = await api.get('patient/prescriptions/');
        return response.data;        
    } catch (error) {
        console.error("Error fetching prescriptions:", error);
        throw error;
    }
};

export const getAllPrescriptionsByPatient = async (uuid: string) => {
    try {
        const response = await api.get(`patient/prescriptions/by-patient/${uuid}/`);
        return response.data;        
    } catch (error) {
        console.error("Error fetching prescriptions:", error);
        throw error;
    }
};

export const createPrescription = async (PrescriptionData: Prescription) => {
    const { data } = await api.post("patient/prescriptions/", PrescriptionData);
    return data;
};

export const updatePrescription = async (uuid: string, prescriptionData: Partial<PrescriptionWithDetails>) => {
    const { data } = await api.patch(`patient/prescriptions/${uuid}/`, prescriptionData);
    return data;
};

