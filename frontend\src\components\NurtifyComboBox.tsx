import TextField from '@mui/material/TextField';
import Autocomplete from '@mui/material/Autocomplete';
import './NurtifyComboBox.css';

interface Option {
    label: string;
    value: string;
}

interface NurtifyComboBoxProps {
    options: Option[];
    selectedValues: string[];
    onChange: (values: string[]) => void;
}

const NurtifyComboBox: React.FC<NurtifyComboBoxProps> = ({ options, selectedValues, onChange }) => {
    return (
        <Autocomplete
            multiple
            options={options}
            getOptionLabel={(option: Option) => option.label}
            value={options.filter(option => selectedValues.includes(option.value))}
            onChange={(_, newValue: Option[]) => {
                onChange(newValue.map(option => option.value));
            }}
            classes={{
                root: 'nurtify-combo-box',
                inputRoot: 'nurtify-combo-box-input-root',
                tag: 'nurtify-combo-box-tag',
            }}
            renderInput={(params) => (
                <TextField {...params} variant="outlined" label="" />
            )}
        />
    );
};

export default NurtifyComboBox;
