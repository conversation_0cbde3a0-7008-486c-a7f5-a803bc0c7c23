import Preloader from "@/components/common/Preloader";
import "./policyList.css";
import LightFooter from "@/shared/LightFooter";
import FilterSidebar from "@/components/FilterSidebar";
import { ChevronDown, ArrowUpRight, Search, X, RefreshCw } from "lucide-react";
import PdfDownloadButton from "@/pages/policy-list/PdfDownloadButton.tsx";
import { format } from "date-fns";
import { usePoliciesQuery } from "@/hooks/policy.query.ts";
import { useStudiesQuery } from "@/hooks/study.query";
import { useState, useCallback } from "react";
import { Policy } from "@/types/types";
import { Study } from "@/store/scheduleEventState";

export default function PolicyList() {
    const [searchTerm, setSearchTerm] = useState("");
    const { data, isLoading, isError, error, refetch } = usePoliciesQuery();
    const { data: studies } = useStudiesQuery();
    const [selectedStudy, setSelectedStudy] = useState<string>("");

    // Transform studies into filter format
    const studyFilters = studies?.map((study: Study) => ({
        id: study.uuid,
        label: study.name,
        count: data?.results?.filter(policy => policy.study?.uuid === study.uuid).length || 0
    })) || [];

    const handleStudyChange = (studyId: string) => {
        setSelectedStudy(studyId === selectedStudy ? "" : studyId);
    };

    const handleRedirect = (policyUuid: string) => {
        window.location.href = `/policy-details/${policyUuid}`;
    };

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(event.target.value);
    };

    const clearSearch = useCallback(() => {
        setSearchTerm("");
    }, []);

    // Filter policies based on search term and study
    const filteredPolicies = data?.results?.filter(policy => {
        const matchesSearch = searchTerm === "" ||
            policy.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
            policy.category?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            policy.author_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            policy.department.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            policy.hospital.name.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesStudy = selectedStudy === "" || policy.study?.uuid === selectedStudy;

        return matchesSearch && matchesStudy;
    });

    if (isLoading) {
        return <Preloader />;
    }

    if (isError) return (
        <div className="main-content bg-light-4">
            <div className="content-wrapper js-content-wrapper">
                <div className="dashboard__content bg-light-4">
                    <div className="container-fluid px-0">
                        <div className="error-container">
                            <h2>Error Loading Policies</h2>
                            <p>{(error as Error).message}</p>
                            <button className="refresh-button" onClick={() => refetch()}>
                                <RefreshCw size={16} /> Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <LightFooter />
            </div>
        </div>
    );

    return (
        <div className="main-content bg-light-4">
            <div className="content-wrapper js-content-wrapper">
                <div className="dashboard__content bg-light-4" style={{ backgroundColor: "white" }}>
                    <div className="container-fluid px-0">
                        <div className="row y-gap-30">
                            <div className="col-xl-3 col-lg-4">
                                <FilterSidebar
                                    title="Filter by Study"
                                    categories={studyFilters}
                                    selectedCategories={selectedStudy ? [selectedStudy] : []}
                                    onCategoryChange={handleStudyChange}
                                />
                            </div>

                            <div className="col-xl-9 col-lg-8">
                                <div className="policy-title"><h1>Policy List</h1></div>
                                <div className="policy-title"><h6>We're on a mission to deliver engaging, curated courses at a reasonable price.</h6></div>

                                <div className="policy-search-container">
                                    <div className="policy-search-box">
                                        <Search className="search-icon" size={20} />
                                        <input
                                            type="text"
                                            placeholder="Search policies by title..."
                                            className="policy-search-input"
                                            value={searchTerm}
                                            onChange={handleInputChange}
                                        />
                                        {searchTerm && (
                                            <button className="clear-search" onClick={clearSearch}>
                                                <X size={18} />
                                            </button>
                                        )}
                                    </div>
                                </div>

                                <div style={{ marginTop: "30px" }}>
                                    {isError && (
                                        <div className="error-message">
                                            Error loading policies. Please try again.
                                        </div>
                                    )}

                                    <div className="policy-table-container">
                                        {filteredPolicies?.length === 0 ? (
                                            <div className="no-results">
                                                <Search size={30} />
                                                <p>No policies found matching "{searchTerm}"</p>
                                            </div>
                                        ) : (
                                            <table className="policy-table">
                                                <thead>
                                                    <tr>
                                                        <th>Policy Name <ChevronDown /></th>
                                                        <th>Date Created <ChevronDown /></th>
                                                        <th>Department <ChevronDown /></th>
                                                        <th>Hospital <ChevronDown /></th>
                                                        <th>Action <ChevronDown /></th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {filteredPolicies?.map((policy: Policy) => {
                                                        // Gestion de attach_content
                                                        const attachUrl = policy.attach_content || "";
                                                        const fileName = attachUrl && typeof attachUrl === "string"
                                                            ? attachUrl.split("/").pop() || policy.title
                                                            : policy.title;

                                                        return (
                                                            <tr key={policy.uuid}>
                                                                <td>{policy.title}</td>
                                                                <td>{format(new Date(policy.created_at), "dd/MM/yyyy")}</td>
                                                                <td>{policy.department.name}</td>
                                                                <td>{policy.hospital.name}</td>
                                                                <td>
                                                                    <div className="policy-actions">
                                                                        <button
                                                                            onClick={() => handleRedirect(policy.uuid)}
                                                                            className="arrowUp"
                                                                            title="View policy details"
                                                                        >
                                                                            <ArrowUpRight size={18} />
                                                                        </button>
                                                                        {attachUrl && typeof attachUrl === "string" ? (
                                                                            <PdfDownloadButton
                                                                                fileUrl={attachUrl}
                                                                                fileName={fileName}
                                                                            />
                                                                        ) : (
                                                                            <span>No file</span>
                                                                        )}
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                        );
                                                    })}
                                                </tbody>
                                            </table>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <br /><br /><br />
                </div>
            </div>
            <LightFooter />
        </div>
    );
}
