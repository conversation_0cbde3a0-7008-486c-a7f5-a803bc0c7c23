import Preloader from "@/components/common/Preloader";
import "./patient-documentation.css";
import { useState, useMemo, useEffect } from "react";
import FormCard from "./FormCard";
import FilterBar from "@/components/FilterBar";
import {
  useGetForms,
  useGetTags,
} from "@/hooks/form.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { Plus, CheckCircle, AlertCircle, FileText } from "lucide-react";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import { usePartialUpdatePatientMutation } from "@/hooks/patient.query";
import { Form, Tag } from "@/types/types";
import { getAllStudies } from "@/services/api/study.service";

interface StudyData {
  uuid: string;
  name: string;
}

export default function PatientDocumentation() {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedStudies, setSelectedStudies] = useState<string[]>([]);
  const [selectedForms, setSelectedForms] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [availableStudies, setAvailableStudies] = useState<{ id: string; name: string }[]>([]);
  const [updateStatus, setUpdateStatus] = useState<{
    success: boolean;
    message: string;
  } | null>(null);
  
  const { selectedPatient } = useSelectedPatientStore();
  const { isLoading: isLoadingUser } = useCurrentUserQuery();
  const { data: allFormsData, isLoading: isLoadingAllForms } = useGetForms();
  const { data: tagsData, isLoading: isLoadingTags } = useGetTags();
  const updatePatientMutation = usePartialUpdatePatientMutation();

  // Load studies when component mounts
  useEffect(() => {
    const loadStudies = async () => {
      try {
        const studiesData = await getAllStudies();
        setAvailableStudies(studiesData.map((study: StudyData) => ({
          id: study.uuid,
          name: study.name
        })));
      } catch (error) {
        console.error("Error loading studies:", error);
      }
    };
    loadStudies();
  }, []);

  // Extract and normalize data
  const allForms = allFormsData?.results || [];
  const tags = Array.isArray(tagsData)
    ? tagsData
    : (tagsData as unknown as { results?: Tag[] })?.results || [];
  
  const availableTags = tags.map((tag: Tag) => ({
    id: tag.uuid,
    name: tag.name,
  }));

  // Search and filter logic
  const handleSearch = (term: string) => {
    setSearchTerm(term);
  };

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategories((prev) => {
      const newCategories = prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId];
      return newCategories;
    });
  };

  const handleStudyChange = (studyId: string) => {
    setSelectedStudies((prev) => {
      const newStudies = prev.includes(studyId)
        ? prev.filter((id) => id !== studyId)
        : [...prev, studyId];
      return newStudies;
    });
  };

  const formHasTag = (form: Form, tagId: string) => {
    // Check all possible locations for category/tag information
    if (form.active_version?.categories?.includes(tagId)) return true;
    if (form.categories?.includes(tagId)) return true;
    
    // Check in categories array
    if (form.active_version?.categories?.includes(tagId)) return true;
    
    return false;
  };

  const formHasStudy = (form: Form, studyId: string) => {
    // Check both form level and active_version level for study
    const formStudy = form.study_uuid || form.study;
    const versionStudy = form.active_version?.study_uuid || form.active_version?.study;
    return formStudy === studyId || versionStudy === studyId;
  };

  const filterFormsByCategory = (forms: Form[]) => {
    if (!selectedCategories.length) return forms;

    return forms.filter((form) => {
      return selectedCategories.some((tagId) => formHasTag(form, tagId));
    });
  };

  const filterFormsByStudy = (forms: Form[]) => {
    if (!selectedStudies.length) return forms;

    return forms.filter((form) => {
      return selectedStudies.some((studyId) => formHasStudy(form, studyId));
    });
  };

  const filterFormsBySearch = (forms: Form[]) => {
    if (!searchTerm.trim()) return forms;
    
    const term = searchTerm.toLowerCase().trim();
    return forms.filter((form) => {
      // Search in form name
      if (form.name.toLowerCase().includes(term)) return true;
      
      // Search in creator name if available
      const creatorName = formatUserName(form.user).toLowerCase();
      if (creatorName.includes(term)) return true;
      
      return false;
    });
  };

  const filteredForms = useMemo(() => {
    // Apply category, study, and search filters
    const categoryFiltered = filterFormsByCategory(allForms);
    const studyFiltered = filterFormsByStudy(categoryFiltered);
    return filterFormsBySearch(studyFiltered);
  }, [allForms, selectedCategories, selectedStudies, searchTerm]);

  const formatUserName = (user: {
    first_name?: string;
    last_name?: string;
    identifier?: string;
  } | null) => {
    if (!user) return "Unknown User";
    const firstName = user.first_name || "";
    const lastName = user.last_name || "";
    return firstName || lastName
      ? `${firstName} ${lastName}`.trim()
      : user.identifier || "Unknown User";
  };
  
  const clearStatusAfterDelay = () => {
    setTimeout(() => setUpdateStatus(null), 5000);
  };
  
  const handleFormSelect = (formUuid: string) => {
    setSelectedForms(prev => {
      if (prev.includes(formUuid)) {
        return prev.filter(id => id !== formUuid);
      } else {
        return [...prev, formUuid];
      }
    });
  };
  
  const handleAddSelectedForms = async () => {
    if (!selectedPatient || selectedForms.length === 0) {
      setUpdateStatus({
        success: false,
        message: !selectedPatient ? "No patient selected" : "No forms selected"
      });
      clearStatusAfterDelay();
      return;
    }

    try {
      await updatePatientMutation.mutateAsync({
        uuid: selectedPatient.uuid,
        data: {
          forms_uuid: selectedForms
        }
      });

      setUpdateStatus({
        success: true,
        message: `Successfully added ${selectedForms.length} forms to ${selectedPatient.first_name} ${selectedPatient.last_name}'s record`
      });
      
      // Clear selected forms after successful update
      setSelectedForms([]);
      clearStatusAfterDelay();
    } catch (error) {
      console.error("Error updating patient forms:", error);
      setUpdateStatus({
        success: false,
        message: "Failed to update patient's forms. Please try again."
      });
      clearStatusAfterDelay();
    }
  };

  const isLoading = isLoadingAllForms || isLoadingUser || isLoadingTags;

  return (
    <div className="content-wrapper js-content-wrapper">
      <Preloader />
      <div className="documentation-page">
        <div className="documentation-container">
          <div className="documentation-header">
            <div className="documentation-title">
              <FileText size={24} />
              <h1>Patient Documentation</h1>
            </div>
            
            {selectedForms.length > 0 && (
              <div className="selected-forms-counter">
                <span>{selectedForms.length} forms selected</span>
              </div>
            )}
          </div>
          
          <FilterBar 
            totalItems={filteredForms.length}
            onSearch={handleSearch}
            tags={availableTags}
            selectedTags={selectedCategories}
            onTagToggle={handleCategoryChange}
            studies={availableStudies}
            selectedStudies={selectedStudies}
            onStudyToggle={handleStudyChange}
          />
          
          <div className="patient-status-alerts">
            {selectedPatient ? (
              <div className="alert alert-info">
                <div className="alert-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#1890ff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="16" x2="12" y2="12"></line>
                    <line x1="12" y1="8" x2="12.01" y2="8"></line>
                  </svg>
                </div>
                <div>
                  <strong>Adding forms for patient:</strong> {selectedPatient.first_name} {selectedPatient.last_name}
                </div>
              </div>
            ) : (
              <div className="alert alert-warning">
                <div className="alert-icon">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#fa8c16" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
                    <line x1="12" y1="9" x2="12" y2="13"></line>
                    <line x1="12" y1="17" x2="12.01" y2="17"></line>
                  </svg>
                </div>
                <div>
                  <strong>No patient selected.</strong> Please select a patient to assign forms.
                </div>
              </div>
            )}
            
            {updateStatus && (
              <div className={`alert ${updateStatus.success ? 'alert-success' : 'alert-danger'}`}>
                <div className="alert-icon">
                  {updateStatus.success ? 
                    <CheckCircle size={20} color="#52c41a" /> : 
                    <AlertCircle size={20} color="#f5222d" />
                  }
                </div>
                <div>
                  <strong>{updateStatus.success ? 'Success!' : 'Error!'}</strong> {updateStatus.message}
                </div>
              </div>
            )}
          </div>
          
          <div className="forms-grid">
            {isLoading ? (
              <div className="loading-state">
                <div className="spinner"></div>
                <p>Loading forms...</p>
              </div>
            ) : filteredForms.length > 0 ? (
              <div className="forms-grid-content">
                {filteredForms.map((form) => (
                  <div key={form.uuid} className="form-card-wrapper">
                    <FormCard
                      uuid={form.uuid}
                      name={form.name}
                      createdBy={formatUserName(form.user)}
                      createdAt={new Date(form.created_at).toLocaleDateString()}
                      version={String(form.active_version?.version || "1.0")}
                      isSelected={selectedForms.includes(form.uuid)}
                      onSelect={handleFormSelect}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="empty-state">
                <div className="empty-state-icon">
                  <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M13 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V9L13 2Z" stroke="#37B7C3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M13 2V9H20" stroke="#37B7C3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M8 13H16" stroke="#37B7C3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M8 17H16" stroke="#37B7C3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <path d="M10 9H8" stroke="#37B7C3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <h3>No forms found</h3>
                <p>No forms match your current filter criteria. Try adjusting your filters to see more results.</p>
              </div>
            )}
          </div>
          
          {selectedForms.length > 0 && (
            <div className="floating-action-button">
              <button
                onClick={handleAddSelectedForms}
                disabled={updatePatientMutation.isPending || !selectedPatient}
                className={`add-button ${updatePatientMutation.isPending || !selectedPatient ? 'disabled' : ''}`}
              >
                {updatePatientMutation.isPending ? 'Updating...' : (
                  <>
                    <Plus size={20} />
                    Add Selected ({selectedForms.length})
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
