import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useActivateAccountMutation } from "@/hooks/user.query";
import { Eye, EyeOff } from "lucide-react";

interface UserResendMailModalProps {
  uid: string;
  token: string;
}

export default function UserResendMailModal({
  uid,
  token,
}: UserResendMailModalProps) {
  const navigate = useNavigate();
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [error, setError] = useState<string>("");
  const [passwordStrength, setPasswordStrength] = useState<number>(0);
  const [showCurrentPassword, setShowCurrentPassword] = useState<boolean>(false);
  const [showNewPassword, setShowNewPassword] = useState<boolean>(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState<boolean>(false);
  
  // Password validation
  const validatePassword = (pass: string): boolean => {
    // Reset errors
    setError("");
    
    // Check password criteria
    const hasLetter = /[A-Za-z]/.test(pass);
    const hasNumber = /\d/.test(pass);
    const hasSpecialChar = /[@$!%*#?&]/.test(pass);
    const hasMinLength = pass.length >= 8;
    
    // Calculate strength (0-4)
    let strength = 0;
    if (hasLetter) strength++;
    if (hasNumber) strength++;
    if (hasSpecialChar) strength++;
    if (hasMinLength) strength++;
    
    setPasswordStrength(strength);
    
    // Return true if all criteria are met
    return hasLetter && hasNumber && hasSpecialChar && hasMinLength;
  };

  const { mutate, isPending } = useActivateAccountMutation();

  const handleSubmit = () => {
    if (!currentPassword || !newPassword || !confirmPassword) {
      setError("All fields are required. Please fill them out.");
      return;
    }

    if (newPassword !== confirmPassword) {
      setError("New passwords do not match. Please try again.");
      return;
    }
    
    if (!validatePassword(newPassword)) {
      setError("Password must be at least 8 characters with a letter, number, and special character");
      return;
    }

    setError("");

    mutate(
      {
        uid,
        token,
        currentPassword, // Utilisé comme mot de passe actuel
        newPassword,
        confirmPassword
      },
      {
        onSuccess: () => {
          console.log("Account activated successfully");
          navigate("/login");
        },
         
        onError: (err: any) => {
          const errorMessage = err.response?.data?.detail || err.response?.data?.message || "An error occurred. Please try again.";
          setError(errorMessage);
          console.error("Activation error:", err);
          console.log("Response data:", err.response?.data);
        },
      }
    );
  };

  console.log("uid", uid);
  console.log("token", token);

  return (
    <div
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        height: "100vh",
        backgroundColor: "rgba(0, 0, 0, 0.05)",
        padding: "20px",
      }}
    >
      <div
        style={{
          backgroundColor: "#E8F4F5",
          borderRadius: "16px",
          padding: "32px",
          width: "90%",
          maxWidth: "400px",
          boxShadow: "0 4px 6px rgba(0, 0, 0, 0.1)",
        }}
      >
        <h2
          style={{
            color: "#1B2559",
            fontSize: "24px",
            fontWeight: "600",
            marginBottom: "16px",
          }}
        >
          Set Password
        </h2>

        <label
          style={{ color: "#1B2559", fontSize: "14px", fontWeight: "500" }}
        >
          Current Password
        </label>
        <div style={{ position: "relative" }}>
          <input
            type={showCurrentPassword ? "text" : "password"}
            value={currentPassword}
            onChange={(e) => setCurrentPassword(e.target.value)}
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "12px",
              borderRadius: "6px",
              border: "1px solid #ccc",
            }}
          />
          <button
            type="button"
            onClick={() => setShowCurrentPassword(!showCurrentPassword)}
            style={{
              position: "absolute",
              right: "10px",
              top: "50%",
              transform: "translateY(-50%)",
              background: "none",
              border: "none",
              color: "#94a3b8",
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {showCurrentPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        </div>

        <label
          style={{ color: "#1B2559", fontSize: "14px", fontWeight: "500" }}
        >
          New Password
        </label>
        <div style={{ position: "relative" }}>
          <input
            type={showNewPassword ? "text" : "password"}
            value={newPassword}
            onChange={(e) => {
              const newPass = e.target.value;
              setNewPassword(newPass);
              validatePassword(newPass);
            }}
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "12px",
              borderRadius: "6px",
              border: "1px solid #ccc",
            }}
          />
          <button
            type="button"
            onClick={() => setShowNewPassword(!showNewPassword)}
            style={{
              position: "absolute",
              right: "10px",
              top: "50%",
              transform: "translateY(-50%)",
              background: "none",
              border: "none",
              color: "#94a3b8",
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {showNewPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        </div>
        
        {/* Password strength indicator */}
        {newPassword && (
          <div style={{ marginBottom: "12px" }}>
            <div style={{ display: "flex", gap: "4px", marginBottom: "4px" }}>
              {[1, 2, 3, 4].map((level) => (
                <div
                  key={level}
                  style={{
                    height: "4px",
                    flex: 1,
                    backgroundColor: passwordStrength >= level 
                      ? level === 4 
                        ? "#10B981" // Strong (green)
                        : level === 3 
                          ? "#22C55E" // Good (light green)
                          : level === 2 
                            ? "#F59E0B" // Fair (orange)
                            : "#EF4444" // Weak (red)
                      : "#E2E8F0", // Empty (gray)
                    borderRadius: "2px",
                    transition: "background-color 0.3s ease",
                  }}
                />
              ))}
            </div>
            <div style={{ 
              fontSize: "12px", 
              color: passwordStrength === 4 
                ? "#10B981" 
                : passwordStrength >= 3 
                  ? "#22C55E" 
                  : passwordStrength >= 2 
                    ? "#F59E0B" 
                    : "#EF4444",
              textAlign: "left"
            }}>
              {passwordStrength === 0 && "Enter password"}
              {passwordStrength === 1 && "Weak password"}
              {passwordStrength === 2 && "Fair password"}
              {passwordStrength === 3 && "Good password"}
              {passwordStrength === 4 && "Strong password"}
            </div>
          </div>
        )}
        
        <div style={{ fontSize: "12px", color: "#64748b", marginBottom: "12px", textAlign: "left" }}>
          Password must contain:
          <ul style={{ paddingLeft: "16px", marginTop: "4px" }}>
            <li style={{ color: newPassword.length >= 8 ? "#10B981" : "#64748b" }}>
              At least 8 characters
            </li>
            <li style={{ color: /[A-Za-z]/.test(newPassword) ? "#10B981" : "#64748b" }}>
              At least one letter
            </li>
            <li style={{ color: /\d/.test(newPassword) ? "#10B981" : "#64748b" }}>
              At least one number
            </li>
            <li style={{ color: /[@$!%*#?&]/.test(newPassword) ? "#10B981" : "#64748b" }}>
              At least one special character (@$!%*#?&)
            </li>
          </ul>
        </div>

        <label
          style={{ color: "#1B2559", fontSize: "14px", fontWeight: "500" }}
        >
          Confirm New Password
        </label>
        <div style={{ position: "relative" }}>
          <input
            type={showConfirmPassword ? "text" : "password"}
            value={confirmPassword}
            onChange={(e) => setConfirmPassword(e.target.value)}
            style={{
              width: "100%",
              padding: "10px",
              marginBottom: "12px",
              borderRadius: "6px",
              border: `1px solid ${error ? "red" : "#ccc"}`,
            }}
          />
          <button
            type="button"
            onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            style={{
              position: "absolute",
              right: "10px",
              top: "50%",
              transform: "translateY(-50%)",
              background: "none",
              border: "none",
              color: "#94a3b8",
              cursor: "pointer",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
          </button>
        </div>

        {error && <p style={{ color: "red", fontSize: "12px" }}>{error}</p>}

        <div
          style={{
            display: "flex",
            justifyContent: "flex-end",
            marginTop: "20px",
          }}
        >
          <button
            onClick={handleSubmit}
            disabled={isPending}
            style={{
              padding: "12px 24px",
              borderRadius: "8px",
              border: "none",
              backgroundColor: isPending ? "#B0BEC5" : "#37B7C3",
              color: "white",
              cursor: "pointer",
              fontSize: "14px",
              fontWeight: "500",
            }}
          >
            {isPending ? "Submitting..." : "Submit"}
          </button>
        </div>
      </div>
    </div>
  );
}
