/* Add Policy Page Styles */
.add-policy-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    padding: 15px 30px 30px 30px; /* Changed top padding */
    margin-bottom: 30px;
    width: 100%;
}

.add-policy-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.add-policy-title {
    margin-bottom: 10px;
}

.add-policy-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.add-sub-title {
    color: #4F547B;
    font-size: 16px;
    font-weight: 400;
    margin-top: 5px;
}

.add-sub-title h6 {
    margin: 0;
    font-weight: 400;
}

/* Progress Indicator */
.form-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
    position: relative;
    width: 100%;
    max-width: 800px;
}

.form-progress::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #E9ECEF;
    z-index: 1;
}

.progress-step {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 33.33%;
}

.step-indicator {
    width: 34px;
    height: 34px;
    border-radius: 50%;
    background-color: white;
    border: 2px solid #E9ECEF;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    font-weight: 600;
    color: #4F547B;
    transition: all 0.3s ease;
    box-shadow: none; /* Changed */
}

.step-label {
    font-size: 14px;
    color: #4F547B;
    text-align: center;
    font-weight: 500;
}

.progress-step.active .step-indicator {
    background-color: #37B7C3;
    border-color: #37B7C3;
    color: white;
    box-shadow: none; /* Changed */
}

.progress-step.active .step-label {
    color: #37B7C3;
    font-weight: 600;
}

.progress-step.completed .step-indicator {
    background-color: #37B7C3;
    border-color: #37B7C3;
    color: white;
    box-shadow: none; /* Changed */
}

/* Form Styles */
.add-policy-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    max-width: 800px;
}

.form-section {
    margin-bottom: 30px;
    background-color: #f9fafb;
    border-radius: 0; /* Changed */
    padding: 25px;
    border: 1px solid #e5e7eb;
}

.form-section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;
}

.form-col {
    flex: 1;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    gap: 15px;
}

.btn-cancel {
    min-width: 120px;
    height: 48px;
    background-color: #f5f7fa;
    color: #4F547B;
    border: 1px solid #e5e7eb;
    border-radius: 0; /* Changed */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-cancel:hover {
    background-color: #e5e7eb;
    color: #1a1a1a;
    box-shadow: none; /* Changed */
}

.btn-submit {
    min-width: 120px;
    height: 48px;
    background-color: #37B7C3;
    color: white;
    border: none;
    border-radius: 0; /* Changed */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none; /* Changed */
}

.btn-submit:hover {
    background-color: #2A9A9F;
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

.btn-submit:disabled {
    background-color: #A8D5DA;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

/* Form Summary Styles */
.form-summary {
    background-color: #F8F9FA;
    border-radius: 0; /* Changed */
    padding: 25px;
    border-left: 4px solid #37B7C3;
    box-shadow: none; /* Changed */
}

.summary-item {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #E9ECEF;
}

.summary-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.summary-label {
    font-weight: 600;
    color: #4F547B;
    width: 140px;
    flex-shrink: 0;
}

.summary-value {
    color: #1a1a1a;
    flex-grow: 1;
    font-weight: 500;
}

/* Confirmation Modal Styles */
.confirmation-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(3px);
}

.confirmation-modal {
    background-color: white;
    border-radius: 0; /* Changed */
    padding: 32px;
    width: 90%;
    max-width: 500px;
    box-shadow: none; /* Changed */
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-title {
    color: #071952;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 16px;
}

.modal-message {
    color: #071952;
    font-size: 16px;
    margin-bottom: 8px;
}

.modal-submessage {
    color: #4F547B;
    font-size: 14px;
    margin-bottom: 24px;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
}

.modal-btn-cancel {
    padding: 12px 24px;
    border-radius: 0; /* Changed */
    border: 1px solid #e5e7eb;
    background-color: #f5f7fa;
    color: #4F547B;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-btn-cancel:hover {
    background-color: #e5e7eb;
    color: #1a1a1a;
    box-shadow: none; /* Changed */
}

.modal-btn-confirm {
    padding: 12px 24px;
    border-radius: 0; /* Changed */
    border: none;
    background-color: #37B7C3;
    color: white;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none; /* Changed */
}

.modal-btn-confirm:hover {
    background-color: #2A9A9F;
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

/* Animation Styles */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Styles */
@media (max-width: 992px) {
    .add-policy-container {
        padding: 25px;
    }
    
    .form-row {
        flex-direction: column;
        gap: 15px;
    }
    
    .form-section {
        padding: 20px;
    }
    
    .summary-item {
        flex-direction: column;
    }
    
    .summary-label {
        width: 100%;
        margin-bottom: 8px;
    }
}

@media (max-width: 768px) {
    .add-policy-container {
        padding: 20px;
    }
    
    .add-policy-title h1 {
        font-size: 20px;
    }
    
    .add-sub-title {
        font-size: 14px;
    }
    
    .form-progress {
        margin-bottom: 25px;
    }
    
    .step-label {
        font-size: 12px;
    }
    
    .form-summary {
        padding: 20px;
    }
    
    .form-section-title {
        font-size: 16px;
    }
    
    .form-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .btn-cancel, .btn-submit {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .add-policy-container {
        padding: 15px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .modal-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .modal-btn-cancel,
    .modal-btn-confirm {
        width: 100%;
    }
    
    .progress-step {
        width: auto;
        flex: 1;
    }
    
    .step-indicator {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }
}

@media (max-width: 480px) {
  .main-content {
    /* padding-bottom: 250px; */ /* Removed */
  }
}

@media (min-width: 425px) and (max-width: 768px) {
  .main-content {
    /* padding-bottom: 200px; */ /* Removed */
  }
}
