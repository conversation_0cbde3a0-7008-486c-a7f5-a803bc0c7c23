import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ConversationService } from "@/services/api/conversation.service";
import { ConversationCreateData, ConversationUpdateData } from "@/services/api/types";

export const CONVERSATION_KEYS = {
  all: ['conversations'] as const,
  pending: ['conversations', 'pending'] as const,
  forwarded: ['conversations', 'forwarded'] as const,
  completed: ['conversations', 'completed'] as const,
  rejected: ['conversations', 'rejected'] as const,
  inprogress: ['conversations', 'inprogress'] as const,
  reimbursed: ['conversations', 'reimbursed'] as const,
  detail: (id: string) => [...CONVERSATION_KEYS.all, id] as const,
  byPatient: (patientUuid: string) => ["conversations", "patient", patientUuid] as const,
  logs: (id: string) => [...CONVERSATION_KEYS.all, id, 'logs'] as const,
};

export const useConversations = () => {
  return useQuery({
    queryKey: CONVERSATION_KEYS.all,
    queryFn: ConversationService.getAll,
  });
};

export const useConversation = (uuid: string) => {
  return useQuery({
    queryKey: CONVERSATION_KEYS.detail(uuid),
    queryFn: () => ConversationService.getById(uuid),
  });
};

export const useCreateConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ConversationCreateData) => ConversationService.create(data as unknown as FormData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.all });
    },
  });
};

export const useUpdateConversation = (uuid: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ConversationUpdateData) => ConversationService.update(uuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.detail(uuid) });
    },
  });
};

export const useDeleteConversation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ConversationService.delete,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.all });
    },
  });
};

export const usePendingConversations = () => {
  return useQuery({
    queryKey: CONVERSATION_KEYS.pending,
    queryFn: ConversationService.getPending,
  });
};

export const useCompletedConversations = () => {
  return useQuery({
    queryKey: CONVERSATION_KEYS.completed,
    queryFn: ConversationService.getCompleted,
  });
};

export const useInProgressConversations = () => {
  return useQuery({
    queryKey: CONVERSATION_KEYS.inprogress,
    queryFn: ConversationService.getInProgress,
  });
};

export const useRejectedConversations = () => {
  return useQuery({
    queryKey: CONVERSATION_KEYS.rejected,
    queryFn: ConversationService.getRejected,
  });
};

export const useForwardedConversations = () => {
  return useQuery({
    queryKey: CONVERSATION_KEYS.forwarded,
    queryFn: ConversationService.getForwarded,
  });
};

export const useReimbursedConversations = () => {
  return useQuery({
    queryKey: CONVERSATION_KEYS.reimbursed,
    queryFn: ConversationService.getReimbursed,
  });
};

export const useSetInProgress = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (uuid: string) => ConversationService.setInProgress(uuid),
    onSuccess: (_, uuid) => {
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.detail(uuid) });
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.pending });
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.inprogress });
    },
  });
};

export const usePatchConversation = (uuid: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { comments: string }) => ConversationService.patch(uuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.detail(uuid) });
    },
  });
};

export const useSetCompleted = (uuid: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { comment: string }) => ConversationService.setCompleted(uuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.detail(uuid) });
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.pending });
    },
  });
};

export const useSetRejected = (uuid: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { comment: string }) => ConversationService.setRejected(uuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.detail(uuid) });
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.pending });
    },
  });
};

export const useConversationsByPatient = (patientUuid: string) => {
  return useQuery({
    queryKey: CONVERSATION_KEYS.byPatient(patientUuid),
    queryFn: () => ConversationService.getByPatient(patientUuid),
    enabled: !!patientUuid, // Only run if patientUuid is provided
  });
};

export const useSetForwardedToFinance = (uuid: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { comment: string }) => ConversationService.setForwardedToFinance(uuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.detail(uuid) });
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.pending });
    },
  });
};

export const useSetReimbursed = (uuid: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { comment: string }) => ConversationService.setReimbursed(uuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.detail(uuid) });
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.forwarded });
    },
  });
};

export const useSetReimbursementStatus = (uuid: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (status: "not_received" | "received") => ConversationService.setReimbursementStatus(uuid, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.detail(uuid) });
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.reimbursed });
    },
  });
};

export const usePatientConversations = (patientUuid: string) => {
  return useQuery({
    queryKey: CONVERSATION_KEYS.byPatient(patientUuid),
    queryFn: () => ConversationService.getByPatient(patientUuid),
    enabled: !!patientUuid,
  });
};

export const useUpdateReimbursementStatus = (uuid: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (status: "received" | "not_received") => 
      ConversationService.updateReimbursementStatus(uuid, { reimbursement_status: status }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.detail(uuid) });
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.all });
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.reimbursed });
      queryClient.invalidateQueries({ queryKey: CONVERSATION_KEYS.forwarded });
    },
  });
};

export const useConversationLogs = (uuid: string) => {
  return useQuery({
    queryKey: CONVERSATION_KEYS.logs(uuid),
    queryFn: () => ConversationService.getLogs(uuid),
    enabled: !!uuid,
  });
};