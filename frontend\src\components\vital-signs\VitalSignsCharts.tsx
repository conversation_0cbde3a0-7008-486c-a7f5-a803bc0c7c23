import React, { useState } from 'react';
import {
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  ComposedChart,
  Bar,
  Area,
  AreaChart
} from 'recharts';
import { ChartDataPoint } from '../../services/api/vital-signs.service';
import { formatDate } from '../../utils/vitalSignsUtils';
import './VitalSignsCharts.css';

interface VitalSignsChartsProps {
  data: ChartDataPoint[];
  isLoading?: boolean;
}

type ChartType = 'line' | 'area' | 'composed';
type VitalSignType = 'temperature' | 'heart_rate' | 'blood_pressure' | 'respiratory_rate' | 'oxygen_saturation' | 'news_score' | 'blood_sugar' | 'height' | 'weight';

const VitalSignsCharts: React.FC<VitalSignsChartsProps> = ({ data, isLoading = false }) => {
  const [selectedChartType, setSelectedChartType] = useState<ChartType>('line');
  const [selectedVitalSign, setSelectedVitalSign] = useState<VitalSignType>('temperature');

  if (isLoading) {
    return (
      <div className="charts-loading">
        <div className="loading-spinner"></div>
        <p>Loading charts...</p>
      </div>
    );
  }

  if (!data || data.length === 0) {
    return (
      <div className="no-chart-data">
        <p>No chart data available</p>
      </div>
    );
  }

  // Process data for charts
  const processedData = data.map(item => ({
    ...item,
    date: formatDate(item.recorded_at),
    blood_pressure_systolic: item.systolic_bp,
    blood_pressure_diastolic: item.diastolic_bp,
  }));

  // Color mapping for vital signs
  const getColorForVitalSign = (vitalSign: VitalSignType, item: ChartDataPoint): string => {
    switch (vitalSign) {
      case 'temperature':
        return item.temperature_color;
      case 'heart_rate':
        return item.heart_rate_color;
      case 'blood_pressure':
        return item.blood_pressure_color;
      case 'respiratory_rate':
        return item.respiratory_rate_color;
      case 'oxygen_saturation':
        return item.oxygen_saturation_color;
      case 'news_score':
        return item.news_score_color;
      case 'blood_sugar':
        return item.blood_sugar_color || '#6b7280';
      case 'height':
        return item.height_color || '#6b7280';
      case 'weight':
        return item.weight_color || '#6b7280';
      default:
        return '#6b7280';
    }
  };

  // Get Y-axis domain for different vital signs
  const getYAxisDomain = (vitalSign: VitalSignType): [number, number] => {
    switch (vitalSign) {
      case 'temperature':
        return [30, 45];
      case 'heart_rate':
        return [40, 200];
      case 'blood_pressure':
        return [60, 200];
      case 'respiratory_rate':
        return [5, 40];
      case 'oxygen_saturation':
        return [85, 100];
      case 'news_score':
        return [0, 15];
      case 'blood_sugar':
        return [0, 300];
      case 'height':
        return [100, 250];
      case 'weight':
        return [30, 150];
      default:
        return [0, 100];
    }
  };

  // Get Y-axis label
  const getYAxisLabel = (vitalSign: VitalSignType): string => {
    switch (vitalSign) {
      case 'temperature':
        return 'Temperature (°C)';
      case 'heart_rate':
        return 'Heart Rate (bpm)';
      case 'blood_pressure':
        return 'Blood Pressure (mmHg)';
      case 'respiratory_rate':
        return 'Respiratory Rate (/min)';
      case 'oxygen_saturation':
        return 'Oxygen Saturation (%)';
      case 'news_score':
        return 'NEWS Score';
      case 'blood_sugar':
        return 'Blood Sugar (mg/dL)';
      case 'height':
        return 'Height (cm)';
      case 'weight':
        return 'Weight (kg)';
      default:
        return 'Value';
    }
  };

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="custom-tooltip">
          <p className="tooltip-label">{label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Render chart based on type and vital sign
  const renderChart = () => {
    const yAxisDomain = getYAxisDomain(selectedVitalSign);
    const yAxisLabel = getYAxisLabel(selectedVitalSign);

    if (selectedVitalSign === 'blood_pressure') {
      // Special chart for blood pressure (systolic and diastolic)
      return (
        <ResponsiveContainer width="100%" height={400}>
          <ComposedChart data={processedData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis domain={yAxisDomain} label={{ value: yAxisLabel, angle: -90, position: 'insideLeft' }} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line
              type="monotone"
              dataKey="blood_pressure_systolic"
              stroke="#ef4444"
              strokeWidth={2}
              name="Systolic BP"
              dot={{ fill: '#ef4444', strokeWidth: 2, r: 4 }}
            />
            <Line
              type="monotone"
              dataKey="blood_pressure_diastolic"
              stroke="#3b82f6"
              strokeWidth={2}
              name="Diastolic BP"
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
            />
          </ComposedChart>
        </ResponsiveContainer>
      );
    }

    const dataKey = selectedVitalSign;
    const color = processedData[0] ? getColorForVitalSign(selectedVitalSign, processedData[0]) : '#6b7280';

    if (selectedChartType === 'line') {
      return (
        <ResponsiveContainer width="100%" height={400}>
          <LineChart data={processedData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis domain={yAxisDomain} label={{ value: yAxisLabel, angle: -90, position: 'insideLeft' }} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Line
              type="monotone"
              dataKey={dataKey}
              stroke={color}
              strokeWidth={2}
              name={yAxisLabel}
              dot={{ fill: color, strokeWidth: 2, r: 4 }}
            />
          </LineChart>
        </ResponsiveContainer>
      );
    }

    if (selectedChartType === 'area') {
      return (
        <ResponsiveContainer width="100%" height={400}>
          <AreaChart data={processedData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis domain={yAxisDomain} label={{ value: yAxisLabel, angle: -90, position: 'insideLeft' }} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Area
              type="monotone"
              dataKey={dataKey}
              stroke={color}
              fill={color}
              fillOpacity={0.3}
              name={yAxisLabel}
            />
          </AreaChart>
        </ResponsiveContainer>
      );
    }

    if (selectedChartType === 'composed') {
      return (
        <ResponsiveContainer width="100%" height={400}>
          <ComposedChart data={processedData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="date" />
            <YAxis domain={yAxisDomain} label={{ value: yAxisLabel, angle: -90, position: 'insideLeft' }} />
            <Tooltip content={<CustomTooltip />} />
            <Legend />
            <Bar dataKey={dataKey} fill={color} opacity={0.7} name={yAxisLabel} />
            <Line
              type="monotone"
              dataKey={dataKey}
              stroke={color}
              strokeWidth={2}
              name={`${yAxisLabel} Trend`}
              dot={{ fill: color, strokeWidth: 2, r: 3 }}
            />
          </ComposedChart>
        </ResponsiveContainer>
      );
    }

    return null;
  };

  return (
    <div className="vital-signs-charts">
      <div className="charts-header">
        <h3>Vital Signs Trends</h3>
        <div className="charts-controls">
          <div className="chart-type-selector">
            <label>Chart Type:</label>
            <select
              value={selectedChartType}
              onChange={(e) => setSelectedChartType(e.target.value as ChartType)}
            >
              <option value="line">Line Chart</option>
              <option value="area">Area Chart</option>
              <option value="composed">Composed Chart</option>
            </select>
          </div>
          <div className="vital-sign-selector">
            <label>Vital Sign:</label>
            <select
              value={selectedVitalSign}
              onChange={(e) => setSelectedVitalSign(e.target.value as VitalSignType)}
            >
              <option value="temperature">Temperature</option>
              <option value="heart_rate">Heart Rate</option>
              <option value="blood_pressure">Blood Pressure</option>
              <option value="respiratory_rate">Respiratory Rate</option>
              <option value="oxygen_saturation">Oxygen Saturation</option>
              <option value="news_score">NEWS Score</option>
              <option value="blood_sugar">Blood Sugar</option>
              <option value="height">Height</option>
              <option value="weight">Weight</option>
            </select>
          </div>
        </div>
      </div>

      <div className="chart-container">
        {renderChart()}
      </div>

      <div className="charts-summary">
        <h4>Data Summary</h4>
        <div className="summary-grid">
          <div className="summary-item">
            <span className="summary-label">Data Points:</span>
            <span className="summary-value">{data.length}</span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Date Range:</span>
            <span className="summary-value">
              {data.length > 0 ? `${formatDate(data[data.length - 1].recorded_at)} - ${formatDate(data[0].recorded_at)}` : 'N/A'}
            </span>
          </div>
          <div className="summary-item">
            <span className="summary-label">Latest NEWS Score:</span>
            <span className="summary-value" style={{ color: data[0]?.news_score_color }}>
              {data[0]?.news_score || 'N/A'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VitalSignsCharts; 