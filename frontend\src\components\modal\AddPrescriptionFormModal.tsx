import React from 'react';
import './addprsescriptionformmodal.css';

import '@components/modal/SubmitSuccessModal';
import { usePrescriptionStore } from '@/store/prescriptionState';
import { useCreatePrescriptionMutation } from "@/hooks/prescription.query";
import { Allergy, Prescription } from "@/services/api/types";


import NurtifyToggle from '../NurtifyToggle';

interface AddPrescriptionFormModalProps {
  isOpen: boolean;
  setIsModalOpen: (value: boolean) => void;
  setPrescriptionUpdated: (value: boolean) => void;
  patient_uuid:string;
  prescriber_identifier:string;
  PatientAllergies:Allergy[]|undefined;
}

const AddPrescriptionFormModal: React.FC<AddPrescriptionFormModalProps> = ({
  isOpen,
  setIsModalOpen,
  patient_uuid,
  prescriber_identifier,
  setPrescriptionUpdated,
  PatientAllergies,
}) => {
  const prescriptionState = usePrescriptionStore();
  const createPrescriptionMutation = useCreatePrescriptionMutation();



 
  
  if (!isOpen) return null;

  const onClose = () => {
    setIsModalOpen(false);
    prescriptionState.resetForm();
  };


  const isFormValid = (formData: Prescription): boolean => {
    return (Object.keys(formData) as (keyof Prescription)[]).every((key) => {
      const value = formData[key];
      if (key === "allergic_patient") {
        return typeof value === "boolean";
      }
      return typeof value === "string" && value.trim() !== "";
    });
  };
  

  const handleSubmit = async(e: React.FormEvent) => {
    e.preventDefault();
    const formData = {
      patient_uuid: patient_uuid||"",
      prescriber_identifier:prescriber_identifier||"",
      prescribed_at: new Date().toISOString(),
      drug_name: prescriptionState.drug_name||"",
      height:prescriptionState.height?.toString()||"",
      weight:prescriptionState.weight?.toString()||"",
      dose: prescriptionState.dose?.toString()||"",
      unit: prescriptionState.unit||"",
      route: prescriptionState.route||"",
      frequency: prescriptionState.frequency||"",
      allergic_patient: prescriptionState.allergic_patient,
    }
    if (isFormValid(formData)) {
      try {
        await createPrescriptionMutation.mutateAsync(
            formData
        );
        setIsModalOpen(false);
        prescriptionState.resetForm();
        setPrescriptionUpdated(true);
        
      } catch (error) {
        console.error("Error creating prescription:", error);
      }
    } else {
      // Move to the step with errors
      console.log("from error");
      console.log(formData);
      console.log(isFormValid(formData));
    }
  };

  
  const prescriptionUnits = [
    'mcg', 'mg', 'g', 'kg', 'mL', 'L', 'drop', 'tsp', 'tbsp', 'tablet',
    'capsule', 'sachet', 'ampoule', 'vial', 'suppository', 'patch', 'IU',
    'MEq', 'PUF', 'actuation', 'unit'
  ];

  const administrationRoutes = [
    'oral', 'intravenous', 'intramuscular', 'subcutaneous', 'topical',
    'transdermal', 'inhalation', 'nasal', 'ophthalmic', 'otic', 'rectal',
    'vaginal', 'sublingual', 'buccal', 'intradermal', 'epidural',
    'intraosseous', 'intrathecal'
  ];

  const frequencies = [
    'once only', 'qd', 'bid', 'tid', 'qid', 'qh', 'q2h', 'q4h', 'q6h',
    'q8h', 'q12h', 'qod', 'prn', 'hs', 'ac', 'pc', 'stat', 'ad lib',
    'once a week', 'once a month', 'every other week', 'other'
  ];

  return (
    <div className="add-prsescription-form-modal">
      <div className="add-prsescription-form">
        <h2 className="add-new-prsescription-form-title">Add New Prescription</h2>
        <form className="form-grid">
          <div className="form-column">
            <div className="form-group design-1">
              <label htmlFor="drugName">Drug Name</label>
              <input
                type="text"
                id="drugName"
                name="drugName"
                placeholder="Enter drug name"
                value={prescriptionState.drug_name || ''}
                onChange={(e) => prescriptionState.setField('drug_name', e.target.value)}
                required
              />
            </div>
            <div className="form-group design-1">
              <label htmlFor="dose">Dose</label>
              <input
                type="number"
                id="dose"
                name="dose"
                placeholder="Enter dose"
                value={prescriptionState.dose || ''}
                onChange={(e) => prescriptionState.setField('dose', parseFloat(e.target.value))}
                required
                min="0"
                step="0.1"
              />
            </div>
            <div className="form-group">
              <label htmlFor="unit">Unit</label>
              <select
                id="unit"
                name="unit"
                value={prescriptionState.unit || ''}
                onChange={(e) => prescriptionState.setField('unit', e.target.value)}
                required
              >
                <option value="">Select unit</option>
                {prescriptionUnits.map((unit) => (
                  <option key={unit} value={unit}>
                    {unit}
                  </option>
                ))}
              </select>
            </div>
            <div className="form-group">
              <label htmlFor="route">Route</label>
              <select
                id="route"
                name="route"
                value={prescriptionState.route || ''}
                onChange={(e) => prescriptionState.setField('route', e.target.value)}
                required
              >
                <option value="">Select route</option>
                {administrationRoutes.map((route) => (
                  <option key={route} value={route}>
                    {route.charAt(0).toUpperCase() + route.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            </div>
            <div className="form-column">
            <div className="form-group">
              <label htmlFor="frequency">Frequency</label>
              <select
                id="frequency"
                name="frequency"
                value={prescriptionState.frequency || ''}
                onChange={(e) => prescriptionState.setField('frequency', e.target.value)}
                required
              >
                <option value="">Select frequency</option>
                {frequencies.map((freq) => (
                  <option key={freq} value={freq}>
                    {freq}
                  </option>
                ))}
              </select>
            </div>
            <div className="form-group design-1">
              <label htmlFor="dose"> Patient Height (per meter)</label>
              <input
                type="number"
                id="height"
                name="height"
                placeholder="Enter height"
                value={prescriptionState.height || ''}
                onChange={(e) => prescriptionState.setField('height', parseFloat(e.target.value))}
                required
                min="0"
                step="0.1"
              />
            </div>
            <div className="form-group design-1">
              <label htmlFor="dose">Patient Weight (per kg)</label>
              <input
                type="number"
                id="weight"
                name="weight"
                placeholder="Enter weight"
                value={prescriptionState.weight || ''}
                onChange={(e) => prescriptionState.setField('weight', parseFloat(e.target.value))}
                required
                min="0"
                step="0.1"
              />
            </div>
            
            
            
            
          </div>
        </form>
        <form className="form-grid" style={{border:"1px solid #37b7c3",borderRadius:"5px" }}>
          <div className="form-column">
            <div className="form-group">
              <label htmlFor="allergyCheck">Patient Allergies :</label>
                {PatientAllergies && 
                (PatientAllergies.map((allergie,index)=>(
                <ul key={index}>
                  <li>{allergie.name}</li>
                </ul>)))}
                {!PatientAllergies && 
                
                <ul >
                  <li>Unknown drug allergy</li>
                </ul>}
            </div>
          </div>
          <div className="form-column">
            <div className="form-group">
              <label htmlFor="allergyCheck">Do you confirm that the patient is not allergic to the drug and/or its components?</label>
              <NurtifyToggle
                name="allergicprescription"
                value={prescriptionState.allergic_patient ? "true" : "false"} 
                onChange={(value) => prescriptionState.setField("allergic_patient", value === "true")} 
                labels={["No", "Yes"]} 
              />
            </div>
          </div>
        </form>
        <div className="button-container">
          <button
            type="button"
            className="cancel-button"
            onClick={onClose}
          >
            Cancel
          </button>

          <button type="button"  className="submit-button" onClick={handleSubmit} disabled={createPrescriptionMutation.isPending}>
            {createPrescriptionMutation.isPending ? "Adding..." : "Add"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AddPrescriptionFormModal;