import React, { useState, useEffect } from "react";
import { X, Plus, Trash2, Save, AlertCircle, Copy } from "lucide-react";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyTextArea from "@/components/NurtifyTextArea";
import NurtifySelect from "@/components/NurtifySelect";
import { useDuplicateConsentFormMutation } from "@/hooks/consent.query";
import { ConsentForm, ConsentQuestionCreate } from "@/types/types";
import "./DuplicateConsentModal.css";

interface DuplicateConsentModalProps {
  isOpen: boolean;
  onClose: () => void;
  consent: ConsentForm;
  studies: any[];
  onSuccess: () => void;
}

const DuplicateConsentModal: React.FC<DuplicateConsentModalProps> = ({
  isOpen,
  onClose,
  consent,
  studies,
  onSuccess,
}) => {
  const [formData, setFormData] = useState({
    name: `${consent.name} (Copy)`,
    description: consent.description || "",
    study_id: consent.study_id,
    questions: [] as ConsentQuestionCreate[],
  });
  const [currentQuestion, setCurrentQuestion] = useState<Partial<ConsentQuestionCreate>>({
    question_text: "",
    is_required: false,
    sequence: 1,
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isLoading, setIsLoading] = useState(false);

  const duplicateConsentMutation = useDuplicateConsentFormMutation();

  // Initialize questions from the original consent form
  useEffect(() => {
    if (consent.questions && consent.questions.length > 0) {
      const initialQuestions: ConsentQuestionCreate[] = consent.questions.map((q) => ({
        question_text: q.question_text,
        is_required: q.is_required,
        sequence: q.sequence,
      }));
      setFormData((prev) => ({ ...prev, questions: initialQuestions }));
    }
  }, [consent]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  const handleQuestionChange = (field: string, value: string | boolean) => {
    setCurrentQuestion((prev) => ({ ...prev, [field]: value }));
  };

  const addQuestion = () => {
    if (!currentQuestion.question_text?.trim()) {
      setErrors((prev) => ({ ...prev, question_text: "Question text is required" }));
      return;
    }

    const newQuestion: ConsentQuestionCreate = {
      question_text: currentQuestion.question_text!,
      is_required: currentQuestion.is_required || false,
      sequence: (formData.questions?.length || 0) + 1,
    };

    setFormData((prev) => ({
      ...prev,
      questions: [...(prev.questions || []), newQuestion],
    }));

    setCurrentQuestion({
      question_text: "",
      is_required: false,
      sequence: (formData.questions?.length || 0) + 2,
    });

    setErrors((prev) => ({ ...prev, question_text: "" }));
  };

  const updateQuestion = (index: number, field: string, value: string | boolean) => {
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions.map((q, i) =>
        i === index ? { ...q, [field]: value } : q
      ),
    }));
  };

  const removeQuestion = (index: number) => {
    setFormData((prev) => ({
      ...prev,
      questions: prev.questions.filter((_, i) => i !== index),
    }));
  };

  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name?.trim()) {
      newErrors.name = "Consent form name is required";
    }
    if (!formData.study_id) {
      newErrors.study_id = "Please select a study";
    }
    if (!formData.questions || formData.questions.length === 0) {
      newErrors.questions = "At least one question is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    setIsLoading(true);

    const duplicateData = {
      name: formData.name,
      description: formData.description,
      study_id: formData.study_id,
      questions: formData.questions,
    };

    duplicateConsentMutation.mutate(
      { uuid: consent.uuid, data: duplicateData },
      {
        onSuccess: () => {
          onSuccess();
          handleClose();
        },
        onError: (error: any) => {
          const errorMessage = error?.response?.data?.message || "Failed to duplicate consent form";
          setErrors((prev) => ({ ...prev, submit: errorMessage }));
          setIsLoading(false);
        },
      }
    );
  };

  const handleClose = () => {
    setFormData({
      name: `${consent.name} (Copy)`,
      description: consent.description || "",
      study_id: consent.study_id,
      questions: [],
    });
    setCurrentQuestion({
      question_text: "",
      is_required: false,
      sequence: 1,
    });
    setErrors({});
    setIsLoading(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="duplicate-consent-modal">
        <div className="modal-header">
          <h2>
            <Copy size={20} />
            Duplicate Consent Form
          </h2>
          <button className="close-button" onClick={handleClose}>
            <X size={20} />
          </button>
        </div>

        <div className="modal-content">
          <div className="original-consent-info">
            <h3>Original Consent Form</h3>
            <div className="info-grid">
              <div className="info-item">
                <label>Name</label>
                <span>{consent.name}</span>
              </div>
              <div className="info-item">
                <label>Version</label>
                <span>v{consent.version}</span>
              </div>
              <div className="info-item">
                <label>Questions</label>
                <span>{consent.questions?.length || 0} questions</span>
              </div>
            </div>
          </div>

          <div className="duplicate-form-section">
            <h3>New Consent Form Details</h3>
            
            <div className="form-group">
              <label htmlFor="name">Consent Form Name *</label>
              <NurtifyInput
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder="Enter consent form name"
                className={errors.name ? "error" : ""}
              />
              {errors.name && <span className="error-message">{errors.name}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="description">Description</label>
              <NurtifyTextArea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                placeholder="Enter description (optional)"
                rows={3}
              />
            </div>

            <div className="form-group">
              <label htmlFor="study_id">Associated Study *</label>
              <NurtifySelect
                name="study_id"
                value={formData.study_id}
                onChange={(e) => handleInputChange("study_id", e.target.value)}
                options={[
                  { value: "", label: "Select a study" },
                  ...studies.map((study) => ({
                    value: study.uuid,
                    label: study.name,
                  })),
                ]}
                className={errors.study_id ? "error" : ""}
              />
              {errors.study_id && <span className="error-message">{errors.study_id}</span>}
            </div>
          </div>

          <div className="questions-section">
            <h3>Consent Questions</h3>
            <p className="section-description">
              Modify the questions for the new consent form. You can edit, add, or remove questions as needed.
            </p>

            {formData.questions && formData.questions.length > 0 && (
              <div className="questions-list">
                {formData.questions.map((question, index) => (
                  <div key={index} className="question-card">
                    <div className="question-header">
                      <div className="question-number">Q{index + 1}</div>
                      <button
                        className="remove-question-btn"
                        onClick={() => removeQuestion(index)}
                        type="button"
                      >
                        <Trash2 size={14} />
                      </button>
                    </div>
                    <div className="question-content">
                      <div className="form-group">
                        <label>Question Text</label>
                        <NurtifyTextArea
                          value={question.question_text}
                          onChange={(e) => updateQuestion(index, "question_text", e.target.value)}
                          placeholder="Enter the consent question..."
                          rows={2}
                        />
                      </div>
                      <div className="form-group">
                        <label className="checkbox-label">
                          <input
                            type="checkbox"
                            checked={question.is_required}
                            onChange={(e) => updateQuestion(index, "is_required", e.target.checked)}
                            className="standard-checkbox"
                          />
                          <span className="checkbox-text">This question is required</span>
                        </label>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            <div className="add-question-form">
              <h4>Add New Question</h4>
              <div className="form-group">
                <label htmlFor="question_text">Question Text *</label>
                <NurtifyTextArea
                  id="question_text"
                  value={currentQuestion.question_text}
                  onChange={(e) => handleQuestionChange("question_text", e.target.value)}
                  placeholder="Enter the consent question..."
                  rows={3}
                  className={errors.question_text ? "error" : ""}
                />
                {errors.question_text && <span className="error-message">{errors.question_text}</span>}
              </div>

              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    checked={currentQuestion.is_required}
                    onChange={(e) => handleQuestionChange("is_required", e.target.checked)}
                    className="standard-checkbox"
                  />
                  <span className="checkbox-text">This question is required</span>
                </label>
              </div>

              <button className="add-question-btn" onClick={addQuestion}>
                <Plus size={16} />
                Add Question
              </button>
            </div>

            {errors.questions && <span className="error-message">{errors.questions}</span>}
          </div>

          <div className="warning-section">
            <AlertCircle size={20} />
            <div>
              <strong>Important:</strong> This will create a new version of the consent form with the modified questions. 
              The original consent form will remain unchanged to maintain audit trail integrity.
            </div>
          </div>

          {errors.submit && (
            <div className="error-alert">
              <AlertCircle size={16} />
              {errors.submit}
            </div>
          )}
        </div>

        <div className="modal-footer">
          <div className="footer-buttons">
            <button className="btn btn-secondary" onClick={handleClose}>
              Cancel
            </button>
            <button
              className="btn btn-primary"
              onClick={handleSubmit}
              disabled={isLoading}
            >
              {isLoading ? (
                "Creating..."
              ) : (
                <>
                  <Save size={16} />
                  Create Duplicate
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DuplicateConsentModal; 