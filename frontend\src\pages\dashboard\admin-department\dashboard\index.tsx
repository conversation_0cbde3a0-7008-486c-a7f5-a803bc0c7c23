import "./dashboard.css";
import { useEffect, useState, useMemo, useRef } from "react";
import { useNavigate, useParams } from "react-router-dom";
import DashboardKPICard from "@/components/DashboardKPICard";
import NoResult from "@/components/common/NoResult";
import { SquareArrowOutUpRight, Building, ChevronRight, ChevronLeft } from "lucide-react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import { User as UserData } from "@/services/api/types.ts";
import { useAnalyticsDepartmentAdminQuery } from "@/hooks/user.query";
import { useGetPendingForms } from "@/hooks/form.query";
import PendingFormRow from "./PendingFormRowProps";
import { Form } from "@/types/types";

export default function AdminDepartmentDashboard() {
  const { departmentUuid } = useParams<{ departmentUuid: string }>();
  const navigate = useNavigate();
  const scrollRef = useRef<HTMLDivElement>(null);
  const {
    data: analyticsData,

  } = useAnalyticsDepartmentAdminQuery(departmentUuid || "");
  const { data: pendingFormsData } = useGetPendingForms();
  console.log("pendingFormsData", pendingFormsData);
  const [userData, setUserData] = useState<Partial<UserData>[]>([]);

  useEffect(() => {
    if (analyticsData?.data) {
      setUserData(analyticsData.data.users || []);
      console.log("users", analyticsData.data.users);
    }
  }, [analyticsData]);

  const dashboardKPICards = [
    { title: "Staffs", number: analyticsData?.counts?.["department_staffs"] ?? "0" },
    { title: "Forms", number: analyticsData?.counts?.["forms"] ?? "0" },
    { title: "Policies", number: analyticsData?.counts?.["policies"] ?? "0" },
    { title: "Patients", number: analyticsData?.counts?.["patients"] ?? "0" },
    { title: "Sponsors", number: analyticsData?.counts?.["sponsors"] ?? "0" },
    { title: "Studies", number: analyticsData?.counts?.["studies"] ?? "0" },

  ];

  // Process user data for charts
  const userChartData = useMemo(() => {
    // Group users by department
    const departmentGroups = userData.reduce((acc, user) => {
      const departmentName = String(user.department || "Unknown");
      if (!acc[departmentName]) {
        acc[departmentName] = 0;
      }
      acc[departmentName]++;
      return acc;
    }, {} as Record<string, number>);

    // Convert to array format for Recharts
    return Object.entries(departmentGroups).map(([name, value]) => ({
      name,
      value,
    }));
  }, [userData]);

  // Colors for charts - using a more cohesive color palette that matches the app's theme

  const metricsData = useMemo(() => {
    if (!analyticsData?.counts) return [];
    return [
      {
        name: "Metrics",
        users: analyticsData.counts["department_staffs"] ?? 0,
        policies: analyticsData.counts["policies"] ?? 0,
        forms: analyticsData.counts["forms"] ?? 0,
      },
    ];
  }, [analyticsData]);
  const handleUserLists = () => navigate("/org/dashboard/users");

  const scrollLeft = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <div className="dashboard-title">
          <h1>
            <Building size={24} style={{ marginRight: "10px" }} />
            Department Dashboard
          </h1>
        </div>
        <div className="dashboard-subtitle">
          <h6>Manage and view department metrics and pending forms</h6>
        </div>
      </div>
      <div>
      <div className="kpi-cards-container">
          <button className="scroll-button left" onClick={scrollLeft}>
            <ChevronLeft size={24} />
          </button>
          <div ref={scrollRef} className="d-flex carrousel-KPIcard">
            {dashboardKPICards.map((card, index) => (
              <div key={index} style={{ scrollSnapAlign: "start", minWidth: "200px" }}>
                <DashboardKPICard title={card.title} number={card.number} />
              </div>
            ))}
          </div>
          <button className="scroll-button right" onClick={scrollRight}>
            <ChevronRight size={24} />
          </button>
        </div>
        <div className="chart-grid">
          <div className="dashboard-chart-container chart-card">
            <div className="chart-header">
              <h4 className="chart-title">
                You have {pendingFormsData?.length}/
                {analyticsData?.counts?.["forms"] ?? 0} Pending Forms
              </h4>
            </div>
            <div className="chart-wrapper">
              <table className="From-status-table">
                <thead>
                  <tr>
                    <th>Form Name</th>
                    <th>User Name</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {(pendingFormsData || []).map((form : Form) => (
                    <PendingFormRow
                      key={form.uuid}
                      form={form}
                      onUserLists={handleUserLists}
                    />
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className="dashboard-chart-container chart-card">
            {userData.length > 0 ? (
              <>
                <div className="chart-header">
                  <h4 className="chart-title">Users by Department</h4>
                  <button
                    className="iconButton editIcon"
                    aria-label="View User List"
                    onClick={handleUserLists}
                  >
                    <SquareArrowOutUpRight size={20} />
                  </button>
                </div>
                <div className="chart-summary">
                  <div className="summary-item">
                    <span className="summary-label">Total Users</span>
                    <span className="summary-value">{userData.length}</span>
                  </div>
                  <div className="summary-item">
                    <span className="summary-label">Departments</span>
                    <span className="summary-value">
                      {userChartData.length}
                    </span>
                  </div>
                  <div className="summary-item">
                    <span className="summary-label">Current Department</span>
                    <span className="summary-value">
                      {userChartData.length > 0
                        ? userChartData.reduce((prev, current) =>
                            prev.value > current.value ? prev : current
                          ).name
                        : "N/A"}
                    </span>
                  </div>
                </div>
                <div className="chart-wrapper">
                  <ResponsiveContainer width="100%" height={350}>
                    <BarChart
                      data={metricsData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [value, "Count"]} />
                      <Legend />
                      <Bar
                        dataKey="users"
                        name="Users"
                        fill="#37B7C3"
                        barSize={40}
                        radius={[10, 10, 0, 0]}
                        animationDuration={1500}
                      />
                      <Bar
                        dataKey="policies"
                        name="Policies"
                        fill="#FBB13C"
                        barSize={40}
                        radius={[10, 10, 0, 0]}
                        animationDuration={1500}
                      />
                      <Bar
                        dataKey="forms"
                        name="Forms"
                        fill="#FC6E51"
                        barSize={40}
                        radius={[10, 10, 0, 0]}
                        animationDuration={1500}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </>
            ) : (
              <NoResult addPath="/dashboard/add-user" title="User" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
