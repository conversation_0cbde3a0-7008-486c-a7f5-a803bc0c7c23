.main-box1 {
    background-color: #ffffff;
    width: 100%;
    height: auto;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.main-box1:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.tier1-box {
    display: flex;
    align-items: center;
    width: 100%;
}

.box-icon {
    width: 40px;
    height: 40px;
    margin-right: 20px;
    object-fit: contain;
}

.abss {
    flex: 1;
}

.box-title {
    color: #140342;
    font-size: 18px;
    font-weight: 600;
    line-height: 24px;
    margin-bottom: 4px;
    text-align: left;
}

.box-sub {
    font-size: 14px;
    font-weight: 400;
    color: #4f547b;
    text-align: left;
    margin: 0;
}

/* Medium screens */
@media only screen and (max-width: 992px) {
    .main-box1 {
        width: calc(50% - 15px);
    }
}

/* Small screens */
@media only screen and (max-width: 768px) {
    .main-box1 {
        width: 100%;
    }
}

/* Extra small screens */
@media only screen and (max-width: 576px) {
    .main-box1 {
        padding: 15px;
    }
    
    .box-icon {
        width: 32px;
        height: 32px;
        margin-right: 15px;
    }
    
    .box-title {
        font-size: 16px;
    }
    
    .box-sub {
        font-size: 12px;
    }
}
