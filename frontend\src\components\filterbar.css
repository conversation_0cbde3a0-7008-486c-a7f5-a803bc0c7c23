/* FilterBar Component Styles */
.filter-bar {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(55, 183, 195, 0.08);
  padding: 16px 20px;
  margin-bottom: 24px;
  border: 1px solid rgba(55, 183, 195, 0.1);
  transition: all 0.3s ease;
}

.filter-bar:hover {
  box-shadow: 0 6px 20px rgba(55, 183, 195, 0.12);
}

.filter-bar__main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.filter-bar__search {
  flex: 1;
  position: relative;
}

.filter-bar__search .search-icon {
  position: absolute;
  left: 14px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-light-1);
}

.filter-bar__search .search-input {
  width: 100%;
  padding: 12px 18px 12px 42px;
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 10px;
  font-size: 15px;
  transition: all 0.2s ease;
  background-color: rgba(55, 183, 195, 0.03);
  color: var(--color-dark-1);
}

.filter-bar__search .search-input:focus {
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
  background-color: #fff;
  outline: none;
}

.filter-bar__search .clear-search {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(55, 183, 195, 0.1);
  border: none;
  width: 22px;
  height: 22px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: var(--color-light-1);
  transition: all 0.2s ease;
}

.filter-bar__search .clear-search:hover {
  background: rgba(55, 183, 195, 0.2);
  color: var(--color-dark-1);
}

.filter-bar__actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-bar__count {
  font-size: 14px;
  color: var(--color-light-1);
  white-space: nowrap;
}

.filter-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: white;
  border: 1px solid rgba(55, 183, 195, 0.2);
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-dark-1);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.filter-toggle:hover {
  background-color: rgba(55, 183, 195, 0.05);
  border-color: rgba(55, 183, 195, 0.3);
}

.filter-toggle.active {
  background-color: rgba(55, 183, 195, 0.1);
  border-color: rgba(55, 183, 195, 0.4);
  color: var(--color-purple-1);
}

.filter-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--color-purple-1);
  color: white;
  font-size: 11px;
  font-weight: 600;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(55, 183, 195, 0.3);
}

.filter-bar__tags {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(55, 183, 195, 0.1);
}

.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag-item {
  display: inline-flex;
  align-items: center;
  padding: 8px 14px;
  background: rgba(55, 183, 195, 0.08);
  color: var(--color-dark-1);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(55, 183, 195, 0.12);
}

.tag-item:hover {
  background: rgba(55, 183, 195, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.12);
}

.tag-item.active {
  background-color: var(--color-purple-1);
  color: white;
  border-color: var(--color-purple-1);
}

.tag-item.clear-all {
  background-color: rgba(239, 68, 68, 0.08);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.2);
}

.tag-item.clear-all:hover {
  background-color: rgba(239, 68, 68, 0.15);
  border-color: rgba(239, 68, 68, 0.3);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .filter-bar__main {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-bar__actions {
    justify-content: space-between;
    margin-top: 12px;
  }
}
