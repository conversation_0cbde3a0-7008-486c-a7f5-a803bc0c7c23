import "./styles.css";
import { useState, ChangeEvent, useEffect, useRef } from 'react';
import { X, FileText, Image, FileImage, File, Trash } from 'lucide-react';

// Define a type for existing file information
export type ExistingFileInfo = {
  id: string;  // File ID from backend
  url: string; // URL to access the file
  name: string; // Original filename
  type: string; // MIME type like 'image/jpeg', 'application/pdf', etc.
};

// Enhanced props to support existing files
type NurtifyAttachFileBoxProps = {
  required?: boolean;
  onChange?: (files: FileList | null, existingFiles?: ExistingFileInfo[]) => void;
  multiple?: boolean;
  value?: FileList | null;
  existingFiles?: ExistingFileInfo[]; // New prop for existing files
  accept?: string;
  questionId?: number;
};

const NurtifyAttachFileBox: React.FC<NurtifyAttachFileBoxProps> = ({
  required = false,
  onChange,
  multiple = false,
  value,
  existingFiles = [],
  accept = ".pdf,.doc,.docx,.png,.jpg,.jpeg",
  questionId
}) => {
  const [hasFile, setHasFile] = useState(false);
  const [activeExistingFiles, setActiveExistingFiles] = useState<ExistingFileInfo[]>(existingFiles);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Log props only on initial mount for debugging
  useEffect(() => {
    console.log(`NurtifyAttachFileBox for question ${questionId} mounted with props:`, {
      required,
      onChange: !!onChange,
      multiple,
      value: value ? `${value.length} files` : 'no files',
      existingFiles: existingFiles?.length || 0,
      accept
    });
    // Empty dependency array means this runs only once on mount
  }, []);

  // Initialize existing files when the prop changes
  useEffect(() => {
    // Only update if existingFiles prop has changed
    if (JSON.stringify(existingFiles) !== JSON.stringify(activeExistingFiles)) {
      console.log(`Question ${questionId} - Updating existing files from props:`, {
        oldCount: activeExistingFiles.length,
        newCount: existingFiles.length,
        files: existingFiles.map(f => ({ id: f.id, name: f.name, url: f.url ? f.url.substring(0, 30) + '...' : 'none' }))
      });
      setActiveExistingFiles([...existingFiles]);

      // If we have existing files, make sure hasFile is set to true
      if (existingFiles.length > 0) {
        setHasFile(true);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [existingFiles]); // Intentionally excluding activeExistingFiles to prevent infinite loop

  // Update hasFile state if value prop changes or if we have existing files
  useEffect(() => {
    const hasNewFiles = value && value.length > 0;
    const hasExistingFiles = activeExistingFiles.length > 0;

    setHasFile(hasNewFiles || hasExistingFiles);

    if (hasNewFiles) {
      console.log(`Question ${questionId} - Value prop changed, files:`, Array.from(value).map(f => f.name));
    }

    if (hasExistingFiles) {
      console.log(`Question ${questionId} - Existing files:`, activeExistingFiles.map(f => f.name));
    }
  }, [value, activeExistingFiles, questionId]);

  const handleFileChange = (event: ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    const hasNewFiles = !!files && files.length > 0;

    console.log(`Question ${questionId} - File selected:`, files ? Array.from(files).map(f => f.name) : 'No files');

    // Always preserve existing files unless in single file mode
    let updatedExistingFiles = [...activeExistingFiles];

    if (!multiple && hasNewFiles) {
      // In single file mode, clear existing files when adding new ones
      updatedExistingFiles = [];
    }

    if (onChange) {
      console.log(`Question ${questionId} - Calling onChange with:`, {
        newFiles: files,
        existingFiles: updatedExistingFiles
      });
      onChange(files, updatedExistingFiles);
    }
  };

  // Handle removal of an existing file
  const handleRemoveExistingFile = (fileId: string) => {
    const updatedFiles = activeExistingFiles.filter(file => file.id !== fileId);

    // Update local state
    setActiveExistingFiles(updatedFiles);

    if (onChange) {
      // Notify parent with updated files list
      onChange(value || null, updatedFiles);
    }
  };

  // Add a method to clear all files
  const clearFiles = () => {
    // Log detailed information about the files being cleared
    console.log(`Question ${questionId} - Clearing all files:`, {
      existingFilesCount: activeExistingFiles.length,
      existingFiles: activeExistingFiles.map(f => ({ id: f.id, name: f.name })),
      hasNewFiles: value && value.length > 0,
      newFilesCount: value ? value.length : 0
    });

    // Reset the file input element
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    // Clear the existing files state
    setActiveExistingFiles([]);
    setHasFile(false);

    if (onChange) {
      console.log(`Question ${questionId} - Calling onChange after clearing all files`);
      // IMPORTANT: Clear both new files and existing files
      // This ensures the parent component knows all files have been removed
      onChange(null, []);
    }
  };

  // Get file icon based on file type
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <Image size={16} />;
    } else if (fileType === 'application/pdf') {
      return <FileText size={16} />;
    } else if (fileType.includes('document') || fileType.includes('word')) {
      return <File size={16} />;
    }
    return <FileImage size={16} />;
  };

  // Get new file names to display
  const getNewFileNames = () => {
    if (!value || value.length === 0) return null;

    return Array.from(value).map((file, index) => (
      <div key={`new-file-${index}`} className="file-item">
        <div className="file-icon">{getFileIcon(file.type)}</div>
        <div className="file-name">{file.name}</div>
        {/* No separate delete button for new files since we replace them all at once */}
      </div>
    ));
  };

  // Get existing file items to display
  const getExistingFileItems = () => {
    if (!activeExistingFiles || activeExistingFiles.length === 0) return null;

    return activeExistingFiles.map((file) => {
      // Make sure we have a valid URL
      const fileUrl = file.url || '#';
      const fileName = file.name || 'Unknown file';

      return (
        <div key={`existing-file-${file.id}`} className="file-item">
          <div className="file-icon">{getFileIcon(file.type)}</div>
          <div className="file-name">
            <a href={fileUrl} target="_blank" rel="noopener noreferrer">
              {fileName}
            </a>
          </div>
          <button
            className="remove-file-button"
            onClick={() => handleRemoveExistingFile(file.id)}
            type="button"
          >
            <Trash size={16} />
          </button>
        </div>
      );
    });
  };

  return (
    <div className="nurtify-attach-file-box" style={{ marginBottom: "15px" }}>
      {/* File input with label */}
      <label htmlFor={`file-upload-${questionId}`} className="file-upload-label">
        <svg
          width="30"
          height="30"
          viewBox="0 0 30 30"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clipPath="url(#clip0_1_2)">
            <path
              d="M3.05899 2.44566C3.14938 2.44548 3.23976 2.44526 3.33015 2.44498C3.57757 2.44438 3.82499 2.44451 4.07241 2.44476C4.33962 2.4449 4.60683 2.44437 4.87405 2.44394C5.39717 2.44319 5.92029 2.44308 6.44342 2.44322C6.86869 2.44333 7.29397 2.44322 7.71924 2.44297C7.81008 2.44292 7.81008 2.44292 7.90276 2.44287C8.0258 2.4428 8.14883 2.44273 8.27187 2.44265C9.42532 2.44202 10.5788 2.44214 11.7322 2.4425C12.787 2.4428 13.8417 2.44214 14.8965 2.44103C15.9801 2.43989 17.0637 2.43941 18.1472 2.43963C18.7554 2.43974 19.3635 2.4396 19.9716 2.43877C20.4893 2.43807 21.007 2.43799 21.5247 2.4387C21.7887 2.43904 22.0527 2.43911 22.3167 2.43841C22.5586 2.43777 22.8006 2.43795 23.0425 2.43877C23.1298 2.43893 23.217 2.43879 23.3043 2.43832C24.1615 2.43402 24.9128 2.58278 25.5647 3.17253C26.0331 3.6851 26.2791 4.27634 26.2711 4.96588C26.2713 5.01466 26.2714 5.06344 26.2716 5.11371C26.2719 5.27565 26.2709 5.43755 26.27 5.59949C26.2699 5.71618 26.2699 5.83287 26.27 5.94955C26.2701 6.26524 26.2691 6.58092 26.2678 6.89661C26.2667 7.22693 26.2666 7.55725 26.2664 7.88757C26.2659 8.51259 26.2644 9.1376 26.2627 9.76262C26.2607 10.4744 26.2597 11.1862 26.2588 11.8979C26.257 13.3616 26.2539 14.8253 26.25 16.2891C25.8246 16.2891 25.3992 16.2891 24.9609 16.2891C24.9604 16.0735 24.9604 16.0735 24.9599 15.8536C24.9566 14.5008 24.9523 13.148 24.9471 11.7953C24.9445 11.0997 24.9422 10.4042 24.9406 9.70872C24.9393 9.10252 24.9373 8.49632 24.9346 7.89012C24.9332 7.56914 24.9321 7.24816 24.9318 6.92717C24.9314 6.625 24.9303 6.32284 24.9285 6.02067C24.9279 5.90983 24.9277 5.79898 24.9278 5.68813C24.9279 5.53661 24.927 5.38514 24.9257 5.23363C24.926 5.18991 24.9263 5.14619 24.9266 5.10115C24.9215 4.7267 24.8334 4.46295 24.5911 4.1748C24.2274 3.87035 23.8844 3.80142 23.4216 3.80089C23.3098 3.80055 23.3098 3.80055 23.1958 3.80021C23.1132 3.80026 23.0306 3.80031 22.9481 3.80036C22.8603 3.80021 22.7725 3.80004 22.6847 3.79984C22.4433 3.79936 22.2019 3.79926 21.9605 3.79922C21.7002 3.79911 21.4399 3.79866 21.1796 3.79827C20.6104 3.79747 20.0411 3.79712 19.4719 3.79686C19.1164 3.7967 18.761 3.79645 18.4055 3.79619C17.4213 3.79547 16.4371 3.79487 15.4528 3.79467C15.3898 3.79466 15.3269 3.79465 15.262 3.79463C15.1672 3.79461 15.1672 3.79461 15.0706 3.79459C14.9427 3.79457 14.8147 3.79454 14.6868 3.79452C14.6233 3.7945 14.5599 3.79449 14.4945 3.79448C13.4662 3.79425 12.4379 3.79322 11.4096 3.79186C10.3538 3.79047 9.29795 3.78974 8.24212 3.78967C7.64935 3.78962 7.05659 3.78928 6.46382 3.78822C5.95905 3.78731 5.45428 3.78701 4.94951 3.7875C4.69204 3.78773 4.43458 3.78767 4.17712 3.78685C3.9412 3.7861 3.7053 3.78617 3.46938 3.78688C3.38425 3.787 3.29912 3.78682 3.21399 3.78631C2.67305 3.78326 2.18197 3.8042 1.70654 4.08325C1.39599 4.4212 1.31292 4.76446 1.31947 5.21323C1.31927 5.26411 1.31907 5.315 1.31887 5.36743C1.31848 5.53685 1.31977 5.70621 1.32105 5.87562C1.32112 5.9975 1.32108 6.11937 1.32095 6.24124C1.32088 6.57135 1.32226 6.90143 1.32389 7.23153C1.32536 7.57679 1.3255 7.92204 1.32577 8.2673C1.3265 8.92075 1.32843 9.5742 1.33078 10.2276C1.3334 10.9717 1.33469 11.7158 1.33586 12.4599C1.33831 13.9902 1.34243 15.5205 1.34766 17.0508C1.39585 17.0025 1.44403 16.9541 1.49368 16.9044C1.94705 16.4498 2.40057 15.9954 2.85424 15.5412C3.08749 15.3076 3.32069 15.074 3.55376 14.8403C3.77855 14.6148 4.00347 14.3896 4.22849 14.1644C4.31447 14.0783 4.40039 13.9922 4.48627 13.906C4.60623 13.7856 4.72636 13.6654 4.84655 13.5452C4.88229 13.5093 4.91803 13.4733 4.95485 13.4363C5.17603 13.2157 5.29178 13.115 5.60303 13.0957C5.65092 13.0915 5.6988 13.0872 5.74814 13.0829C6.18687 13.1917 6.55714 13.6976 6.86829 14.011C6.93362 14.0765 6.93362 14.0765 7.00027 14.1432C7.13769 14.281 7.2749 14.4189 7.41211 14.5569C7.50594 14.651 7.59978 14.7451 7.69364 14.8392C7.92222 15.0684 8.15063 15.2978 8.37891 15.5273C8.59169 15.4375 8.72691 15.3107 8.88903 15.1477C8.92928 15.1075 8.92928 15.1075 8.97034 15.0665C9.06004 14.9767 9.14931 14.8865 9.23859 14.7963C9.30287 14.7318 9.36718 14.6673 9.43151 14.6028C9.56993 14.4641 9.70816 14.3251 9.84625 14.186C10.0646 13.9661 10.2833 13.7466 10.5022 13.5272C10.9667 13.0615 11.4306 12.5952 11.8945 12.1289C12.3954 11.6255 12.8963 11.1222 13.3977 10.6194C13.6153 10.4013 13.8326 10.1829 14.0496 9.9642C14.1847 9.82829 14.32 9.69263 14.4553 9.55703C14.518 9.49406 14.5807 9.431 14.6432 9.36782C14.7286 9.28164 14.8142 9.19583 14.9 9.1101C14.9479 9.06196 14.9958 9.01383 15.0451 8.96424C15.2656 8.76756 15.4533 8.6916 15.7553 8.7017C16.239 8.81424 16.7029 9.48765 17.0517 9.83708C17.1216 9.90691 17.1915 9.97672 17.2615 10.0465C17.4501 10.2349 17.6385 10.4234 17.8268 10.6119C18.0242 10.8094 18.2217 11.0068 18.4192 11.2042C18.7503 11.5352 19.0812 11.8663 19.4121 12.1975C19.7951 12.5808 20.1783 12.9639 20.5616 13.3469C20.8909 13.6759 21.2201 14.005 21.5492 14.3342C21.7457 14.5308 21.9422 14.7274 22.1388 14.9238C22.3232 15.1081 22.5075 15.2925 22.6917 15.477C22.7595 15.5448 22.8273 15.6127 22.8952 15.6804C22.9875 15.7725 23.0795 15.8647 23.1715 15.957C23.2232 16.0087 23.2748 16.0604 23.3281 16.1137C23.4375 16.2305 23.4375 16.2305 23.4375 16.2891C23.3807 16.2894 23.324 16.2897 23.2655 16.2901C22.7268 16.2935 22.1882 16.2977 21.6495 16.3029C21.3727 16.3055 21.0959 16.3078 20.819 16.3094C20.5512 16.3109 20.2833 16.3133 20.0155 16.3162C19.914 16.3172 19.8125 16.3178 19.711 16.3182C18.0346 16.3249 16.6116 16.8173 15.3886 18.0059C14.5977 18.842 14.154 19.8194 13.9314 20.939C13.8867 21.1523 13.8867 21.1523 13.8281 21.2695C12.3047 21.275 10.7813 21.2791 9.25783 21.2817C8.55044 21.2829 7.84304 21.2845 7.13565 21.2872C6.51891 21.2896 5.90218 21.2911 5.28543 21.2916C4.95903 21.2919 4.63264 21.2926 4.30624 21.2943C3.99863 21.2959 3.69105 21.2964 3.38344 21.296C3.27092 21.2961 3.15839 21.2966 3.04588 21.2975C2.1423 21.3044 1.38412 21.2065 0.693971 20.5664C0.292287 20.1467 -0.00647211 19.6106 -0.007863 19.0188C-0.0081242 18.9671 -0.00838536 18.9154 -0.00865448 18.8621C-0.00864367 18.8055 -0.00863297 18.749 -0.00862184 18.6907C-0.00883727 18.6304 -0.00905278 18.5701 -0.00927474 18.508C-0.00992166 18.3054 -0.0101466 18.1028 -0.0103697 17.9001C-0.0107368 17.7551 -0.0111263 17.6101 -0.0115365 17.465C-0.0126632 17.0293 -0.0132842 16.5936 -0.0138057 16.1579C-0.0140627 15.9525 -0.014377 15.7472 -0.0146839 15.5418C-0.0156806 14.8589 -0.0165299 14.1759 -0.0169543 13.493C-0.0170662 13.3158 -0.0171788 13.1386 -0.0172934 12.9615C-0.0173218 12.9174 -0.0173501 12.8734 -0.0173793 12.828C-0.0178653 12.1152 -0.0193495 11.4024 -0.0212533 10.6896C-0.0231932 9.95719 -0.0242464 9.22482 -0.0244335 8.49244C-0.0245601 8.08145 -0.0250698 7.67047 -0.0265698 7.25949C-0.0279785 6.87268 -0.0281819 6.48587 -0.0275801 6.09906C-0.0275511 5.95731 -0.027921 5.81557 -0.0287259 5.67383C-0.0297657 5.47982 -0.0293469 5.28585 -0.0285775 5.09184C-0.0292202 5.0362 -0.029863 4.98055 -0.0305252 4.92322C-0.0243344 4.27338 0.220943 3.71128 0.644533 3.22265C1.36275 2.54836 2.10307 2.443 3.05899 2.44566Z"
              fill="black"
              fillOpacity="0.45"
            />
            <path
              d="M9.00879 5.42358C9.53709 5.83601 9.89419 6.36929 10.0195 7.03125C10.0973 7.83904 9.96094 8.51623 9.45259 9.1674C8.95788 9.70867 8.3553 10.0132 7.62105 10.047C6.85993 10.0585 6.24768 9.83197 5.68359 9.31641C5.16803 8.75232 4.94149 8.14006 4.953 7.37895C4.98675 6.6447 5.29133 6.04212 5.83259 5.54741C6.78213 4.80614 8.00552 4.75336 9.00879 5.42358Z"
              fill="black"
              fillOpacity="0.45"
            />
          </g>
          <defs>
            <clipPath id="clip0_1_2">
              <rect width="30" height="30" fill="white" />
            </clipPath>
          </defs>
        </svg>
        {hasFile ? 'Add/Replace files' : 'Attach a file'}
      </label>
      <input
        ref={fileInputRef}
        type="file"
        id={`file-upload-${questionId}`}
        name={`file-upload-${questionId}`}
        onChange={handleFileChange}
        multiple={multiple}
        required={required}
        accept={accept}
        style={{
          position: 'absolute',
          width: '1px',
          height: '1px',
          padding: '0',
          margin: '-1px',
          overflow: 'hidden',
          clip: 'rect(0,0,0,0)',
          border: '0'
        }}
      />

      {/* Files list container */}
      {hasFile && (
        <div className="files-list-container">
          {/* Show section title if we have both new and existing files */}
          {value && value.length > 0 && activeExistingFiles.length > 0 && (
            <div className="files-section-title">New Files:</div>
          )}

          {/* New files list */}
          {getNewFileNames()}

          {/* Show section title for existing files if we have any */}
          {activeExistingFiles.length > 0 && (
            <div className="files-section-title">Existing Files:</div>
          )}

          {/* Existing files list */}
          {getExistingFileItems()}

          {/* Clear all button */}
          {hasFile && (
            <button
              type="button"
              className="clear-all-files-button"
              onClick={clearFiles}
            >
              <X size={14} /> Clear all files
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default NurtifyAttachFileBox;
