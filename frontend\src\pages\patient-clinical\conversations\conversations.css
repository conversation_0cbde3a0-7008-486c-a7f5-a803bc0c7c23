.patclin-conversations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.patclin-conversation-card {
  background: white;
  border: 2px solid #3dc6d6;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  gap: 15px;
}

.patclin-conversation-icon {
  color: #3dc6d6;
  display: flex;
  align-items: flex-start;
  padding-top: 5px;
}

.patclin-conversation-content {
  flex: 1;
}

.patclin-conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.patclin-conversation-title {
  font-size: 1.1em;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.patclin-conversation-status {
  font-size: 0.9em;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.patclin-conversation-status.pending {
  background-color: #fff3cd;
  color: #856404;
}

.patclin-conversation-status.inprogress {
  background-color: #cce5ff;
  color: #004085;
}

.patclin-conversation-status.forwarded_to_finance {
  background-color: #d4edda;
  color: #155724;
}

.patclin-conversation-status.rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.patclin-conversation-status.reimbursed {
  background-color: #e2e3e5;
  color: #383d41;
}

.patclin-conversation-details {
  margin-bottom: 15px;
}

.patclin-conversation-comment {
  color: #666;
  font-size: 0.9em;
  margin-bottom: 8px;
}

.patclin-conversation-date {
  color: #666;
  font-size: 0.9em;
  margin: 0;
}

.patclin-conversation-actions {
  display: flex;
  justify-content: flex-end;
}

.patclin-conversation-button {
  background-color: #3dc6d6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.patclin-conversation-button:hover {
  background-color: #2db3c3;
}

.patclin-loading {
  text-align: center;
  padding: 20px;
  color: #666;
}

.patclin-empty-state {
  text-align: center;
  padding: 40px;
  color: #666;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-top: 20px;
} 