.edit-patient-visit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.edit-patient-visit-modal {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.edit-patient-visit-modal-header {
  padding: 1.25rem;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f9fafb;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.edit-patient-visit-modal-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-icon {
  color: #23b7cd;
}

.edit-patient-visit-modal-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.375rem;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-patient-visit-modal-close:hover {
  background-color: #f3f4f6;
  color: #111827;
}

.edit-patient-visit-modal-body {
  padding: 1.5rem;
}

.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.form-control,
.form-select {
  width: 100%;
  padding: 0.625rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: all 0.2s;
  background-color: white;
}

.form-control:focus,
.form-select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.form-control::placeholder {
  color: #9ca3af;
}

.form-control:disabled,
.form-select:disabled {
  background-color: #f3f4f6;
  cursor: not-allowed;
  opacity: 0.7;
}

.loading-text {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
}

.edit-patient-visit-modal-footer {
  padding: 1.25rem;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  background-color: #f9fafb;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

.cancel-btn,
.save-btn {
  padding: 0.625rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.2s;
  cursor: pointer;
}

.cancel-btn {
  background-color: white;
  border: 1px solid #d1d5db;
  color: #374151;
}

.cancel-btn:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.save-btn {
  background-color: #23b7cd;
  border: 1px solid #23b7cd;
  color: white;
}

.save-btn:hover {
  background-color: #07acc5;
  border-color: #07acc5;
}

.cancel-btn:disabled,
.save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.save-btn:disabled {
  background-color: #a5b4fc;
  border-color: #a5b4fc;
}

/* Scrollbar styles */
.edit-patient-visit-modal::-webkit-scrollbar {
  width: 8px;
}

.edit-patient-visit-modal::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.edit-patient-visit-modal::-webkit-scrollbar-thumb {
  background: #c5c5c5;
  border-radius: 4px;
}

.edit-patient-visit-modal::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Visit Details Modal Styles */
.visit-details-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.visit-detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.visit-detail-item strong {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.visit-detail-item span {
  font-size: 1rem;
  color: #111827;
  padding: 0.5rem;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  border: 1px solid #e5e7eb;
}
