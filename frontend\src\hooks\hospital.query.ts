import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAllHospitals, getHospitalById, createHospital, updateHospital, deleteHospital, getAllHospitalsWithDepartments } from "@/services/api/hospital.service";
import { Hospital_KEYS } from './keys';
import { Hospital } from "@/services/api/types";

export const useHospitalsQuery = () => {
    return useQuery<Hospital[], Error>({
        queryKey: [Hospital_KEYS.GET_ALL],
        queryFn: getAllHospitals,
        refetchOnWindowFocus: false,
        staleTime: 1000 * 60 * 5,
    });
};



export const useHospitalQuery = (uuid: string) => {
    return useQuery<Hospital, Error>({
        queryKey: [Hospital_KEYS.GET_BY_ID, uuid],
        queryFn: () => getHospitalById(uuid),
        enabled: !!uuid,  // Only fetch if id is provided
        refetchOnWindowFocus: false,  // Prevent refetch when switching tabs/windows
        refetchOnReconnect: false,    // Prevent refetch when reconnecting to the internet
        staleTime: 1000 * 60 * 5,      // Cache data for 5 minutes before marking it stale
    });
};

export const useCreateHospitalMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createHospital,
        onSuccess: () => {
            // Invalidate and refetch hospital list
            queryClient.invalidateQueries({ queryKey: [Hospital_KEYS.GET_ALL] });
        },
    });
};

export const useUpdateHospitalMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ uuid, data }: { uuid: string; data: Partial<Hospital> }) => 
            updateHospital(uuid, data),
        onSuccess: (_, { uuid }) => {
            // Invalidate hospital list and the updated hospital details
            queryClient.invalidateQueries({ queryKey: [Hospital_KEYS.GET_ALL] });
            queryClient.invalidateQueries({ queryKey: [Hospital_KEYS.GET_BY_ID, uuid] });
        },
    });
};

export const useDeleteHospitalMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ uuid}: { uuid: string }) => 
            deleteHospital(uuid),
        onSuccess: () => {
            // Invalidate hospital list and the updated hospital details
            queryClient.invalidateQueries({ queryKey: [Hospital_KEYS.GET_ALL] });
            
        },
    });

};


export const useHospitalsWithDepartmentsQuery = () => {
    return useQuery<Hospital[], Error>({
        queryKey: [Hospital_KEYS.GET_ALL],
        queryFn: getAllHospitalsWithDepartments,
        refetchOnWindowFocus: false,
        staleTime: 1000 * 60 * 5,
    });
};