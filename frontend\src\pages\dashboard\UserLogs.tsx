import { useMyAccessLogsQuery } from "@/hooks/patient.query";
import { format } from "date-fns";
import DataTable from "@/components/common/DataTable";
import type { PatientAccessLog } from "@/services/api/types";

const UserLogs = () => {
  const { data: logs, isLoading, error } = useMyAccessLogsQuery();

  if (isLoading) {
    return (
      <div className="content-wrapper js-content-wrapper">
        <div className="bg-light-4 px-3 py-5">
          <div className="container-fluid py-6 px-6">
            <div className="loading">Loading user logs...</div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="content-wrapper js-content-wrapper">
        <div className="bg-light-4 px-3 py-5">
          <div className="container-fluid py-6 px-6">
            <div className="error">Error loading user logs: {(error as Error).message}</div>
          </div>
        </div>
      </div>
    );
  }

  if (!Array.isArray(logs) || logs.length === 0) {
    return (
      <div className="content-wrapper js-content-wrapper">
        <div className="bg-light-4 px-3 py-5">
          <div className="container-fluid py-6 px-6">
            <div className="patient-details-container">
              <div className="patient-details-header">
                <h1 className="page-title">My Access Logs</h1>
              </div>

              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '20px',
              }}>
                <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>
                  View your patient access history
                </h3>
              </div>

              <div className="no-logs" style={{ textAlign: 'center', marginTop: '50px', fontSize: '1.2em', color: '#666' }}>
                You haven't consulted any patient yet.
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="content-wrapper js-content-wrapper">
      <div className="bg-light-4 px-3 py-5">
        <div className="container-fluid py-6 px-6">
          <div className="patient-details-container">
            <div className="patient-details-header">
              <h1 className="page-title">My Access Logs</h1>
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '20px',
            }}>
              <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>
                View your patient access history
              </h3>
            </div>

            <DataTable<PatientAccessLog>
              data={logs}
              columns={[
                {
                  key: 'patient_name',
                  header: 'Patient Name',
                  sortable: true,
                },
                {
                  key: 'access_type',
                  header: 'Access Type',
                  sortable: true,
                  render: (value) => {
                    if (typeof value === 'string') {
                      return value.charAt(0).toUpperCase() + value.slice(1);
                    }
                    return '';
                  }
                },
                {
                  key: 'created_at',
                  header: 'Date & Time',
                  sortable: true,
                  render: (value) => {
                    if (typeof value === 'string') {
                      return format(new Date(value), 'PPpp');
                    }
                    return '';
                  }
                },
                {
                  key: 'ip_address',
                  header: 'IP Address',
                  sortable: true,
                }
              ]}
              noDataMessage="No access logs available"
              defaultItemsPerPage={10}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserLogs;