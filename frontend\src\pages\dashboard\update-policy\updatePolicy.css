.update-policy-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    padding: 15px 30px 30px 30px; /* Changed top padding */
    margin-bottom: 30px;
    width: 100%;
}

.update-policy-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.update-policy-title {
    margin-bottom: 10px;
}

.update-policy-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.update-policy-subtitle {
    color: #4F547B;
    font-size: 16px;
    font-weight: 400;
    margin-top: 5px;
}

.update-policy-subtitle h6 {
    margin: 0;
    font-weight: 400;
}

.add-policy-title {
    color: #2B3674;
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 10px;
}

.add-policy-title h1 {
    margin: 0;
    padding: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.add-sub-title {
    color: #4F547B;
    font-size: 16px;
    font-weight: 400;
    margin-bottom: 30px;
}

.add-sub-title h6 {
    margin: 0;
    font-weight: 400;
}

.add-policy-form {
    padding: 20px 0;
}

.form-group {
    margin-bottom: 20px;
}

.button {
    background-color: #37B7C3;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 0; /* Changed */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: none; /* Changed */
    margin-top: 20px;
}

.button:hover {
    background-color: #2A9A9F;
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

.button:disabled {
    background-color: #a0a0a0;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Responsive styles */
@media (max-width: 768px) {
    .update-policy-container {
        padding: 20px;
    }

    .add-policy-form {
        padding: 15px 0;
    }
}

@media (max-width: 576px) {
    .update-policy-container {
        padding: 15px;
    }
}


/* Style for the textarea in the Short Description field */
.form-group textarea[name="description"] {
    width: 100%; /* Full width to match other inputs */
    min-height: 100px; /* Minimum height for the textarea to allow for multiple lines */
    padding: 10px 12px; /* Padding to match the input fields */
    border: 1px solid #37B7C3; /* Light gray border to match the other fields */
    border-radius: 0; /* Changed */
    font-size: 14px; /* Font size to match the other inputs */
    color: #1a1a1a; /* Text color to match the other fields */
    background-color: #fff; /* White background */
    resize: vertical; /* Allow vertical resizing only */
    box-sizing: border-box; /* Ensure padding and border are included in the width */
    transition: border-color 0.3s ease; /* Smooth transition for border color on focus */
}

/* Placeholder style for the textarea */
.form-group textarea[name="description"]::placeholder {
    color: #37B7C3; /* Placeholder color to match typical input placeholders */
    font-size: 14px; /* Match font size */
}

/* Focus state for the textarea */
.form-group textarea[name="description"]:focus {
    outline: none; /* Remove default outline */
    border-color: #37B7C3; /* Use the primary color for the border on focus (matches your button color) */
    box-shadow: none; /* Changed */
}

/* Hover state for the textarea */
.form-group textarea[name="description"]:hover {
    border-color: #37B7C3; /* Slightly darker border on hover */
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .form-group textarea[name="description"] {
        padding: 8px 10px; /* Slightly less padding on smaller screens */
        font-size: 13px; /* Slightly smaller font size */
    }
}
