.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
}

.modal-header h2 {
  margin: 0 0 10px;
}

.modal-body {
  margin: 20px 0;
}

.prescription-log {
  display: flex;
  flex-direction: column;
}

.log-step {
  display: flex;
  align-items: flex-start; /* Changed from center to align items at the top */
  position: relative;
  margin-bottom: 20px; /* Increased vertical spacing between steps */
}

.step-icon {
  margin-right: 10px;
  z-index: 1; /* Ensures icons stay above the line */
  position: relative;
}

.green-check {
  color: green;
}

.step-details {
  flex: 1;
}

.step-line {
  width: 2px;
  height: 40px; /* Increased height to match larger spacing */
  background: #ccc;
  position: absolute;
  left: 8px; /* Aligns with the center of the icon */
  top: 24px; /* Starts below the icon */
  z-index: 0; /* Places the line behind the icon */
}

.loader {
  width: 18px;
  height: 18px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.modal-footer {
  display: flex;
  justify-content: space-between;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-secondary {
  background: #ccc;
}

.prescription-uuid {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

.prescription-summary {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 20px;
  color: #333;
}