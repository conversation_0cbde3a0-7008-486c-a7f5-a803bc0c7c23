:root {
  --primary-color: #37B7C3; /* iOS Blue */
  --primary-gradient: linear-gradient(135deg, #007AFF, #0056b3); /* Kept gradient, can simplify */
  --secondary-color: #F0F0F0; /* Lighter grey for backgrounds and received messages */
  --text-color: #000000; /* Pure black for strong contrast on light bg */
  --text-secondary: #8A8A8E; /* iOS secondary text color */
  --background-color: #FFFFFF;
  --sent-message-bg: var(--primary-color);
  /* --sent-message-gradient: linear-gradient(135deg, #007AFF, #0062CC); */ /* Optional: refined gradient */
  --received-message-bg: #E5E5EA; /* iOS received bubble color */
  /* --received-message-gradient: linear-gradient(135deg, #E5E5EA, #DCDCE0); */ /* Optional: subtle gradient */
  --border-color: #D1D1D6; /* iOS border color */
  --border-radius: 18px; /* Modern, softer radius */
  --hover-bg: rgba(0, 0, 0, 0.05); 
  --bubble-tail-size: 6px; /* Smaller tail */
  --pinned-color: #FFCC00; /* iOS Yellow */
  --muted-color: #8A8A8E;
  --animation-duration: 0.25s; /* Slightly smoother animations */
  
  --dark-mode-bg: #000000; /* Pure black for OLED */
  --dark-mode-text: #FFFFFF;
  --dark-mode-secondary: #8D8D93;
  --dark-mode-input-bg: #1C1C1E; /* iOS dark input field */
  --dark-mode-received-bg: #2C2C2E; /* iOS dark received bubble */
  --dark-mode-sent-bg: #37B7C3; /* iOS dark mode blue for sent */
  --dark-mode-border-color: #38383A;

  --button-hover-bg: #37B7C3; 
  --button-active-bg: #004085;
  --icon-button-hover-bg: rgba(0, 0, 0, 0.08); /* Light mode icon hover */
  --icon-button-active-bg: rgba(0, 0, 0, 0.12);
  --dark-mode-icon-button-hover-bg: rgba(255, 255, 255, 0.1);
  --dark-mode-icon-button-active-bg: rgba(255, 255, 255, 0.15);
}

/* Main container */
.nurtify-conversation-container {
  display: flex;
  flex-direction: column;
  height: 100vh; 
  overflow: hidden; 
  background-color: var(--background-color); 
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif; /* Prioritize SF Pro */
  transition: background-color var(--animation-duration) ease-in-out;
}

/* Dark mode styles */
.dark-mode .nurtify-conversation-container {
  background-color: var(--dark-mode-bg);
  color: var(--dark-mode-text);
}

/* Header styling */
.nurtify-conversation-header {
  display: flex;
  align-items: center;
  padding: 10px 16px; /* Slightly reduced vertical padding */
  background: var(--background-color); 
  border-bottom: 1px solid var(--border-color); /* Use border instead of shadow for cleaner look */
  z-index: 10;
  transition: background-color var(--animation-duration) ease-in-out, border-color var(--animation-duration) ease-in-out;
}

.dark-mode .nurtify-conversation-header {
  background-color: var(--dark-mode-input-bg); /* Consistent dark element bg */
  border-bottom: 1px solid var(--dark-mode-border-color);
}

.nurtify-back-button {
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--primary-color);
  margin-right: 8px; 
  padding: 8px; 
  border-radius: 50%;
  display: flex; 
  align-items: center;
  justify-content: center;
  transition: background-color var(--animation-duration) ease-in-out;
}

.nurtify-back-button:hover {
  background-color: #37B7C3; 
}

.dark-mode .nurtify-back-button:hover {
  background-color: var(--dark-mode-icon-button-hover-bg);
}

.header-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.chat-avatar {
  position: relative;
  margin-right: 12px;
  transition: transform var(--animation-duration) ease;
}

.chat-avatar:hover {
  transform: scale(1.05);
}

.avatar-circle {
  width: 38px; 
  height: 38px; 
  border-radius: 50%;
  background-color: var(--primary-color); /* Solid color, gradient can be too much */
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500; /* Medium weight */
  font-size: 16px;
  /* box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); */ /* Softer shadow or none */
  transition: transform var(--animation-duration) ease-in-out;
}

.avatar-circle:hover {
  transform: scale(1.03); /* Subtle hover */
}

.dark-mode .avatar-circle {
  background-color: var(--dark-mode-sent-bg);
}

.online-indicator {
  position: absolute;
  bottom: 1px; /* Adjusted */
  right: 1px;  /* Adjusted */
  width: 10px;
  height: 10px;
  background-color: #34C759; /* iOS Green */
  border: 2px solid var(--background-color); /* Match header background */
  border-radius: 50%;
  transition: background-color var(--animation-duration) ease-in-out, border-color var(--animation-duration) ease-in-out;
}

.online-indicator:not(.online) { /* Style for offline state if needed, or just hide */
  background-color: #8A8A8E; /* Grey for offline */
}


.dark-mode .online-indicator {
  border-color: var(--dark-mode-input-bg); /* Match dark header background */
}


.nurtify-chat-details h2 {
  margin: 0;
  font-size: 16px; 
  font-weight: 600; /* Semibold */
  color: var(--text-color); 
}

.dark-mode .nurtify-chat-details h2 {
  color: var(--dark-mode-text);
}

.nurtify-chat-status {
  font-size: 12px; /* Smaller status text */
  color: var(--text-secondary); 
  font-weight: 400; /* Regular weight */
}

.dark-mode .nurtify-chat-status {
  color: var(--dark-mode-secondary);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-action-button {
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--primary-color); /* Action buttons are primary */
  padding: 8px; 
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--animation-duration) ease-in-out, background-color var(--animation-duration) ease-in-out;
}

.header-action-button:hover {
  background-color: var(--icon-button-hover-bg); 
  /* color: var(--primary-color); */ /* Already primary */
  transform: scale(1.05); /* Subtle scale */
}

.header-action-button:active {
  background-color: var(--icon-button-active-bg); 
  transform: scale(1);
}

.dark-mode .header-action-button {
  color: var(--dark-mode-sent-bg); /* Use the dark mode primary blue */
}

.dark-mode .header-action-button:hover {
  background-color: var(--dark-mode-icon-button-hover-bg);
}

.dark-mode .header-action-button:active {
  background-color: var(--dark-mode-icon-button-active-bg);
}

/* Messages container */
.nurtify-messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px; /* Consistent padding */
  display: flex;
  flex-direction: column;
  gap: 8px; /* Increased gap between message groups for better separation */
}

/* Custom Scrollbar for Webkit browsers */
.nurtify-messages-container::-webkit-scrollbar {
  width: 6px;
}

.nurtify-messages-container::-webkit-scrollbar-track {
  background: transparent; /* Or a very light grey */
}

.nurtify-messages-container::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.nurtify-messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

.dark-mode .nurtify-messages-container::-webkit-scrollbar-thumb {
  background: var(--dark-mode-border-color);
}

.dark-mode .nurtify-messages-container::-webkit-scrollbar-thumb:hover {
  background: var(--dark-mode-secondary);
}

/* Date separator */
.message-date-separator {
  text-align: center;
  margin: 16px 0;
  color: var(--text-secondary);
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn var(--animation-duration) ease;
}

.message-date-separator::before,
.message-date-separator::after {
  content: "";
  flex: 1;
  height: 1px;
  background-color: var(--border-color);
  margin: 0 12px;
}

.dark-mode .message-date-separator {
  color: var(--dark-mode-secondary);
}

.dark-mode .message-date-separator::before,
.dark-mode .message-date-separator::after {
  background-color: #3a3b3c;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Empty messages state */
.empty-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-secondary);
}

.empty-messages-icon {
  width: 50px;
  height: 50px;
  background-color: #f0f2f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

/* Message group styling */
.message-group {
  max-width: 80%; /* Allow slightly wider messages */
  /* margin-bottom: 8px; */ /* Gap is now on container */
  animation: messageGroupAppear var(--animation-duration) ease-in-out;
  display: flex; 
  flex-direction: column; 
}

.message-group.sent {
  align-self: flex-end;
}

.message-group.received {
  align-self: flex-start;
}

@keyframes messageGroupAppear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Bullet-style grouping */
.message-group-bullet {
  display: flex;
  align-items: center;
  margin: 8px 0;
}

.message-group-bullet-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--text-secondary);
  margin: 0 8px;
}

.message-group-bullet-text {
  font-size: 12px;
  color: var(--text-secondary);
}

.dark-mode .message-group-bullet-dot,
.dark-mode .message-group-bullet-text {
  color: var(--dark-mode-secondary);
}

/* Message styling */
.nurtify-message {
  display: flex;
  align-items: flex-end;
  margin-bottom: 1px; /* Very small margin between consecutive bubbles in a group */
  position: relative;
  animation: messageAppear var(--animation-duration) ease-in-out;
}

@keyframes messageAppear {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(5px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.nurtify-message.sent {
  justify-content: flex-end;
}

.nurtify-message-avatar {
  margin-right: 8px;
  position: relative;
}

.nurtify-message.sent .nurtify-message-avatar {
  margin-right: 0;
  margin-left: 8px;
}

.nurtify-message-avatar .avatar-circle {
  width: 30px; 
  height: 30px; 
  font-size: 13px; 
  transition: transform var(--animation-duration) ease-in-out;
  background-image: none; 
  background-color: var(--secondary-color); /* Grey for received avatar bg */
  color: var(--text-secondary); /* Text color for avatar char */
  font-weight: 500;
}

.dark-mode .nurtify-message-avatar .avatar-circle {
  background-color: var(--dark-mode-received-bg);
  color: var(--dark-mode-secondary);
}

.nurtify-message-avatar .avatar-circle:hover {
  transform: scale(1.05);
}

.nurtify-message-content {
  display: flex;
  flex-direction: column;
  position: relative;
}

.nurtify-message-header {
  margin-bottom: 2px;
  font-size: 12px;
  color: var(--text-secondary);
}

.dark-mode .nurtify-message-header {
  color: var(--dark-mode-secondary);
}

.nurtify-message-bubble {
  padding: 9px 14px; /* Adjusted padding */
  border-radius: var(--border-radius);
  max-width: 100%; 
  word-wrap: break-word;
  position: relative;
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13); /* Subtle shadow for depth */
  transition: transform var(--animation-duration) ease-in-out, box-shadow var(--animation-duration) ease-in-out;
}

/* Remove hover effect for bubbles to prevent visual noise */
/* .nurtify-message-bubble:hover {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12); 
  transform: translateY(-1px);
} */

/* Message tails - simplified and cleaner */
.nurtify-message-bubble::before,
.nurtify-message-bubble::after {
  content: "";
  position: absolute;
  bottom: 3px; /* Align with bottom of bubble */
  width: var(--bubble-tail-size);
  height: var(--bubble-tail-size);
  background: inherit; /* Inherit bubble background for seamless tail */
  /* clip-path: polygon(0 0, 100% 100%, 0 100%); */ /* Basic triangle, can be refined */
  transform-origin: bottom;
  z-index: -1; /* Place behind bubble slightly */
}

.nurtify-message.received .nurtify-message-bubble::before {
  left: calc(var(--bubble-tail-size) * -0.75 + 1px); /* Pull left */
  clip-path: polygon(100% 0, 0% 100%, 100% 100%); /* Pointing left-up */
  /* transform: rotate(45deg) skewX(-10deg) skewY(-10deg); */
}

.nurtify-message.sent .nurtify-message-bubble::after {
  right: calc(var(--bubble-tail-size) * -0.75 + 1px); /* Pull right */
  clip-path: polygon(0 0, 100% 100%, 0 100%); /* Pointing right-up */
  transform: scaleX(-1); /* Flip the received tail */
}

/* Only show tail on the last message of a group from the same sender */
.message-group > .nurtify-message:not(:last-child) .nurtify-message-bubble::before,
.message-group > .nurtify-message:not(:last-child) .nurtify-message-bubble::after {
  display: none;
}


.nurtify-message.sent .nurtify-message-bubble {
  background-color: var(--sent-message-bg); 
  color: white;
}

.nurtify-message.received .nurtify-message-bubble {
  background-color: var(--received-message-bg); 
  color: var(--text-color);
}

.dark-mode .nurtify-message.sent .nurtify-message-bubble {
  background-color: var(--dark-mode-sent-bg);
  color: var(--dark-mode-text); /* Ensure text is white on dark blue */
}

.dark-mode .nurtify-message.received .nurtify-message-bubble {
  background: var(--dark-mode-received-bg);
  color: var(--dark-mode-text);
}

/* Adjust tail color for dark mode */
.dark-mode .nurtify-message.received .nurtify-message-bubble::before {
   /* background will be inherited from .dark-mode .nurtify-message.received .nurtify-message-bubble */
}
.dark-mode .nurtify-message.sent .nurtify-message-bubble::after {
   /* background will be inherited from .dark-mode .nurtify-message.sent .nurtify-message-bubble */
}


.nurtify-message-bubble p {
  margin: 0;
  font-size: 15px; /* Standard iOS message font size */
  line-height: 1.4; 
}

.nurtify-message-meta {
  font-size: 11px;
  margin-top: 3px; 
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  padding: 0 5px; /* Add some padding to meta */
}

.dark-mode .nurtify-message.sent .nurtify-message-meta {
  color: var(--dark-mode-secondary); /* Ensure sent message meta is visible in dark mode */
}

.dark-mode .nurtify-message.received .nurtify-message-meta {
  color: var(--dark-mode-secondary);
}

.nurtify-message.sent .nurtify-message-meta {
  justify-content: flex-end;
}

.message-status {
  display: inline-flex;
  margin-left: 4px;
  position: relative;
}

.message-status-icon {
  transition: color var(--animation-duration) ease;
  color: var(--primary-color); /* Make status icons use primary color */
}

.nurtify-message.sent .message-status-icon {
  color: rgba(255, 255, 255, 0.7); /* Slightly more subtle on sent bubble */
}

.dark-mode .nurtify-message.sent .message-status-icon {
   color: rgba(255, 255, 255, 0.6); 
}

/* Received messages don't show status icons for the sender */
/* .dark-mode .nurtify-message.received .message-status-icon {
  color: var(--dark-mode-secondary); 
} */

.message-status-tooltip {
  position: absolute;
  bottom: 20px;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 10px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--animation-duration) ease, visibility var(--animation-duration) ease;
  z-index: 10;
}

.message-status:hover .message-status-tooltip {
  opacity: 1;
  visibility: visible;
}

/* Message actions */
.nurtify-message-actions {
  position: absolute;
  top: -28px;
  right: 0;
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  display: flex;
  padding: 2px;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--animation-duration) ease, visibility var(--animation-duration) ease;
  z-index: 5;
}

.dark-mode .nurtify-message-actions {
  background-color: #3a3b3c;
}

.nurtify-message:hover .nurtify-message-actions {
  opacity: 1;
  visibility: visible;
}

.message-action-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color var(--animation-duration) ease;
}

.message-action-btn:hover {
  background-color: var(--hover-bg);
}

.dark-mode .message-action-btn {
  color: var(--dark-mode-secondary);
}

.dark-mode .message-action-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Message pin/mute indicators */
.message-indicators {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 4px;
}

.message-indicator {
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-indicator.pinned {
  color: var(--pinned-color);
}

.message-indicator.muted {
  color: var(--muted-color);
}

/* Typing indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  margin-top: 8px;
  animation: fadeIn var(--animation-duration) ease;
}

.typing-text {
  font-size: 12px;
  color: var(--text-secondary);
  margin-left: 8px;
}

.typing-dots {
  display: flex;
  background-color: var(--received-message-bg);
  padding: 8px 12px;
  border-radius: var(--border-radius);
  gap: 4px;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dark-mode .typing-dots {
  background-color: var(--dark-mode-received-bg);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.typing-dots::before {
  content: "";
  position: absolute;
  left: -6px;
  bottom: 8px;
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-right: 8px solid var(--received-message-bg);
  border-bottom: 6px solid transparent;
}

.dark-mode .typing-dots::before {
  border-right-color: var(--dark-mode-received-bg);
}

.typing-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #a8a8a8;
  animation: typingAnimation 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingAnimation {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-4px);
  }
}

/* Input area styling */
.message-input-container {
  padding: 12px 16px; /* Increased padding */
  background-color: var(--background-color); /* Use variable */
  border-top: 1px solid var(--border-color);
  transition: background-color var(--animation-duration) ease;
  position: relative; /* For emoji picker positioning */
}

.dark-mode .message-input-container {
  background-color: #1e1e1e;
  border-top-color: #3a3b3c;
}

.message-input-wrapper {
  display: flex;
  align-items: flex-end; /* Align items to bottom for multi-line input */
  background-color: var(--secondary-color); /* Light grey input background */
  border-radius: 20px; /* Match message bubble radius for consistency */
  padding: 4px 4px 4px 12px; /* Fine-tuned padding */
  transition: background-color var(--animation-duration) ease-in-out, box-shadow var(--animation-duration) ease-in-out;
  position: relative; 
  border: 1px solid transparent; /* Prepare for focus border */
}

.message-input-wrapper:focus-within {
  background-color: var(--background-color); /* White when focused */
  border-color: var(--primary-color); /* Primary color border on focus */
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1); /* Subtle glow */
}

.dark-mode .message-input-wrapper {
  background-color: var(--dark-mode-input-bg);
  border-color: transparent;
}

.dark-mode .message-input-wrapper:focus-within {
  background-color: #2c2c2e; /* Slightly lighter dark for focus */
  border-color: var(--dark-mode-sent-bg);
  box-shadow: 0 0 0 3px rgba(10, 132, 255, 0.2);
}

.message-actions {
  display: flex;
  align-items: center; 
  margin-right: 6px; 
}

.message-action-button {
  background: transparent;
  border: none;
  cursor: pointer;
  color: var(--text-secondary); 
  padding: 8px; /* Standardized padding */
  border-radius: 50%;
  display: flex; 
  align-items: center; 
  justify-content: center; 
  transition: transform var(--animation-duration) ease-in-out, background-color var(--animation-duration) ease-in-out, color var(--animation-duration) ease-in-out;
}

.message-action-button:hover {
  background-color: var(--icon-button-hover-bg);
  color: var(--primary-color); 
  transform: scale(1.05); /* Subtle scale */
}

.message-action-button:active {
  background-color: var(--icon-button-active-bg);
  transform: scale(1.05);
}

.dark-mode .message-action-button {
  color: var(--dark-mode-secondary);
}

.dark-mode .message-action-button:hover {
  background-color: rgba(255, 255, 255, 0.12);
  color: #79b8ff; /* Lighter blue for dark mode hover */
}

.dark-mode .message-action-button:active {
  background-color: rgba(255, 255, 255, 0.15);
}

/* Tooltip for action buttons */
.action-tooltip {
  position: relative;
}

.action-tooltip::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--animation-duration) ease, visibility var(--animation-duration) ease;
  z-index: 10;
}

.action-tooltip:hover::after {
  opacity: 1;
  visibility: visible;
}

.message-input {
  flex: 1;
  min-height: 22px; /* Adjusted to line height */
  max-height: 100px; /* Max 4-5 lines typically */
  padding: 8px 0px; /* Remove horizontal padding, handled by wrapper */
  border: none;
  background-color: transparent;
  font-size: 15px; 
  font-family: inherit;
  resize: none;
  outline: none;
  color: var(--text-color);
  line-height: 1.4; /* Ensure consistent line height */
  align-self: center; /* Vertically center single line text */
}

.message-input::placeholder {
  color: var(--text-secondary);
}

.send-button {
  width: 36px; 
  height: 36px; 
  border: none;
  border-radius: 50%;
  background-color: var(--primary-color);
  /* background-image: var(--primary-gradient); */ /* Solid color is cleaner */
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform var(--animation-duration) ease-in-out, background-color var(--animation-duration) ease-in-out;
  /* box-shadow: 0 1px 3px rgba(0, 123, 255, 0.3); */ /* Softer or no shadow */
  margin-left: 4px; 
  flex-shrink: 0; 
}

.dark-mode .send-button {
  background-color: var(--dark-mode-sent-bg);
}

.send-button:hover:not(:disabled) {
  transform: scale(1.05); /* Subtle hover */
  /* box-shadow: 0 2px 5px rgba(0, 123, 255, 0.4); */
  background-color: #37B7C3 
}

.dark-mode .send-button:hover:not(:disabled) {
  background-color: #37B7C3; /* Slightly lighter blue for dark hover */
}

.send-button:active:not(:disabled) {
  transform: scale(1.05);
  background-color: var(--button-active-bg); /* Use variable for active bg */
  box-shadow: 0 2px 5px rgba(0, 123, 255, 0.3);
}

.send-button:disabled {
  background-color: #d1d5da; /* Adjusted disabled color */
  background-image: none;
  color: #868e96; /* Adjusted disabled text color */
  cursor: not-allowed;
  box-shadow: none;
}

.send-button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.patient-icon {
  position: absolute;
  top: -6px; /* Adjusted for better positioning */
  right: -6px; /* Adjusted for better positioning */
  background-color: white;
  border-radius: 50%;
  padding: 2px;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

/* Loading state */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 132, 255, 0.1);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Emoji Picker */
.emoji-picker-container {
  position: absolute;
  bottom: calc(100% + 8px); /* Position above the input container */
  left: 16px; /* Align with input container padding */
  z-index: 1000;
  border-radius: var(--border-radius); 
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); 
  background-color: var(--background-color);
  animation: fadeIn var(--animation-duration) ease;
  border: 1px solid var(--border-color); 
}

.dark-mode .emoji-picker-container {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

/* Recent emoji section */
.recent-emojis {
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
  gap: 4px;
  border-bottom: 1px solid var(--border-color);
}

.recent-emoji-item {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color var(--animation-duration) ease;
}

.recent-emoji-item:hover {
  background-color: var(--hover-bg);
}

/* Quick reactions */
.quick-reactions {
  position: absolute;
  top: -38px; /* Adjusted position */
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--background-color);
  border-radius: 20px; /* Consistent with other elements */
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15); /* Enhanced shadow */
  display: flex;
  padding: 6px; /* Increased padding */
  gap: 4px; /* Added gap between reaction emojis */
  z-index: 5;
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--animation-duration) ease, visibility var(--animation-duration) ease, transform var(--animation-duration) ease;
  border: 1px solid var(--border-color); /* Subtle border */
}

.dark-mode .quick-reactions {
  background-color: #3a3b3c;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.nurtify-message:hover .quick-reactions {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-3px); /* Slight upward movement on hover */
}

.reaction-emoji {
  font-size: 20px; /* Slightly larger emoji */
  width: 30px; /* Adjusted size */
  height: 30px; /* Adjusted size */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color var(--animation-duration) ease, transform var(--animation-duration) ease;
}

.reaction-emoji:hover {
  background-color: var(--icon-button-hover-bg); /* Consistent hover */
  transform: scale(1.25); /* More pronounced hover */
}

.dark-mode .reaction-emoji:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Emoji auto-complete */
.emoji-autocomplete {
  position: absolute;
  bottom: calc(100% + 8px); /* Position above the input container */
  left: 16px; /* Align with input container padding */
  background-color: var(--background-color); /* Use variable */
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  max-width: 300px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
  border: 1px solid var(--border-color); /* Add border */
}

.emoji-autocomplete.visible {
  display: block;
  animation: fadeIn var(--animation-duration) ease;
}

.emoji-suggestion {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  transition: background-color var(--animation-duration) ease;
}

.emoji-suggestion:hover,
.emoji-suggestion.active {
  background-color: var(--hover-bg);
}

.emoji-suggestion-emoji {
  margin-right: 8px;
  font-size: 18px;
}

.emoji-suggestion-name {
  font-size: 14px;
  color: var(--text-color);
}

.dark-mode .emoji-autocomplete {
  background-color: #3a3b3c;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.dark-mode .emoji-suggestion:hover,
.dark-mode .emoji-suggestion.active {
  background-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .emoji-suggestion-name {
  color: var(--dark-mode-text);
}
