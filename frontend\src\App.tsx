import React from 'react';
import { BrowserRouter } from 'react-router-dom';
import AppRouter from './AppRouter'
import AppLayout from './shared/AppLayout';
import { Toaster } from 'sonner'; // Import Toaster
import './vendors.css'
import './App.css'
import "@fortawesome/fontawesome-svg-core/styles.css";
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from './queryClient';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <div className="app-container">
        <BrowserRouter>
          <AppLayout>
            <AppRouter />
            <Toaster richColors position="top-right" /> {/* Add Toaster component */}
          </AppLayout>
        </BrowserRouter>
      </div>
      {import.meta.env.VITE_APP_MODE == "development" && <ReactQueryDevtools initialIsOpen={false} />}
    </QueryClientProvider>
  )
}

export default App
