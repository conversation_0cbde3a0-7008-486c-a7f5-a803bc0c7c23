/* Base styles and variables */
:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --success-color: #10b981;
  --success-hover: #059669;
  --success-light: #dcfce7;
  --warning-color: #f59e0b;
  --warning-hover: #d97706;
  --warning-light: #fef3c7;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --danger-light: #fee2e2;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
}

.forms-queries-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  color: var(--gray-800);
}

.forms-queries-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px;
}

.forms-queries-title {
  margin-bottom: 0;
  color: var(--gray-900);
  font-size: 1.75rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.query-tabs {
  display: flex;
  gap: 8px;
  background-color: var(--gray-100);
  padding: 4px;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.query-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: var(--radius-md);
  background: none;
  border: none;
  color: var(--gray-600);
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.query-tab:hover {
  color: var(--gray-800);
  background-color: var(--gray-200);
}

.query-tab.active {
  background-color: white;
  color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.queries-table-container {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  border: 1px solid var(--gray-200);
}

.queries-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.queries-table th,
.queries-table td {
  padding: 14px 18px;
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.queries-table th {
  background-color: var(--gray-50);
  font-weight: 600;
  color: var(--gray-700);
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  position: sticky;
  top: 0;
  z-index: 10;
}

.queries-table tr:nth-child(even) {
  background-color: var(--gray-50);
}

.queries-table tr:hover {
  background-color: var(--gray-100);
  transition: background-color var(--transition-fast);
}

.queries-table td:first-child,
.queries-table th:first-child {
  padding-left: 24px;
}

.queries-table td:last-child,
.queries-table th:last-child {
  padding-right: 24px;
}

.query-row {
  transition: transform var(--transition-fast);
}

.query-row:hover {
  transform: translateY(-1px);
}

.priority-badge {
  padding: 5px 10px;
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.priority-high {
  background-color: var(--danger-light);
  color: var(--danger-hover);
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.priority-medium {
  background-color: var(--warning-light);
  color: var(--warning-hover);
  border: 1px solid rgba(217, 119, 6, 0.2);
}

.priority-low {
  background-color: var(--success-light);
  color: var(--success-hover);
  border: 1px solid rgba(22, 163, 74, 0.2);
}

.status-badge {
  padding: 5px 10px;
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.status-badge.resolved {
  background-color: var(--success-light);
  color: var(--success-hover);
  border: 1px solid rgba(22, 163, 74, 0.2);
}

.status-badge.pending {
  background-color: var(--warning-light);
  color: var(--warning-hover);
  border: 1px solid rgba(217, 119, 6, 0.2);
}

.view-query-btn {
  padding: 8px 14px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  box-shadow: var(--shadow-sm);
}

.view-query-btn:hover {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.view-query-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.query-detail-view {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 24px;
  border: 1px solid var(--gray-200);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: none;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  color: var(--primary-color);
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 24px;
  transition: all var(--transition-fast);
}

.back-button:hover {
  color: var(--primary-hover);
  background-color: var(--gray-50);
  border-color: var(--gray-300);
  transform: translateX(-2px);
}

.back-button:active {
  transform: translateX(0);
}

.query-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: 24px;
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
}

.query-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--gray-200);
}

.query-info {
  flex: 1;
}

.query-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
  color: var(--gray-900);
  font-size: 1.25rem;
  font-weight: 600;
}

.query-description {
  color: var(--gray-700);
  line-height: 1.6;
  font-size: 1rem;
}

.query-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
  margin-left: 24px;
}

.query-date {
  color: var(--gray-500);
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

.query-status {
  font-weight: 500;
  font-size: 0.875rem;
}

.responses-section {
  margin: 24px 0;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.response-item {
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  padding: 20px;
  border: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.response-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.response-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--gray-200);
}

.responder-name {
  font-weight: 600;
  color: var(--gray-800);
  font-size: 0.95rem;
}

.response-date {
  color: var(--gray-500);
  font-size: 0.85rem;
}

.response-message {
  color: var(--gray-700);
  line-height: 1.6;
  font-size: 0.95rem;
  white-space: pre-line;
}

.response-form,
.resolved-query-notice {
  margin-top: 28px;
  border-top: 1px solid var(--gray-200);
  padding-top: 24px;
}

.resolved-query-notice {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
  color: var(--gray-600);
  padding: 24px;
  background-color: var(--gray-50);
  border-radius: var(--radius-lg);
  border: 1px dashed var(--gray-300);
}

.resolved-query-notice svg {
  color: var(--success-color);
  width: 32px;
  height: 32px;
}

.resolved-query-notice p {
  font-size: 1rem;
  margin: 0;
}

.resolution-notes {
  width: 100%;
  margin-top: 16px;
  padding: 16px;
  background-color: white;
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
  text-align: left;
}

.resolution-notes h4 {
  margin: 0 0 8px 0;
  color: var(--gray-800);
  font-size: 1rem;
  font-weight: 600;
}

.resolution-notes p {
  margin: 0;
  color: var(--gray-700);
  font-size: 0.95rem;
  line-height: 1.6;
  white-space: pre-line;
}

.response-input-container {
  margin-bottom: 20px;
}

.response-input-container textarea {
  width: 100%;
  padding: 14px;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
  font-size: 0.95rem;
  color: var(--gray-800);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.response-input-container textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.response-input-container textarea::placeholder {
  color: var(--gray-400);
}

.response-options {
  margin-top: 14px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.clarification-toggle {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: var(--radius-md);
  transition: background-color var(--transition-fast);
}

.clarification-toggle:hover {
  background-color: var(--gray-100);
}

.clarification-toggle input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--gray-600);
  font-size: 0.9rem;
  font-weight: 500;
}

.submit-response-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 18px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.submit-response-btn:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.submit-response-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.submit-response-btn:disabled {
  background-color: var(--gray-300);
  color: var(--gray-500);
  cursor: not-allowed;
  box-shadow: none;
}

.forms-queries-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 60px 40px;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  color: var(--gray-500);
  border: 1px solid var(--gray-200);
}

.forms-queries-empty svg {
  color: var(--gray-400);
  opacity: 0.8;
}

.forms-queries-empty p {
  font-size: 1.1rem;
  font-weight: 500;
}

.forms-queries-loading,
.forms-queries-error {
  text-align: center;
  padding: 60px 40px;
  color: var(--gray-600);
  font-size: 1.1rem;
  font-weight: 500;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.forms-queries-error {
  color: var(--danger-color);
  border-color: var(--danger-light);
}

.loading-responses,
.no-responses {
  text-align: center;
  padding: 30px;
  color: var(--gray-500);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  font-size: 0.95rem;
  font-weight: 500;
  border: 1px dashed var(--gray-300);
}

.response-meta {
  display: flex;
  align-items: center;
  gap: 10px;
}

.clarification-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 10px;
  background-color: var(--gray-100);
  color: var(--gray-600);
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 600;
  border: 1px solid var(--gray-200);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.clarification-badge svg {
  color: var(--gray-500);
}

.resolve-query-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: var(--success-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.resolve-query-btn:hover {
  background-color: var(--success-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.resolve-query-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn var(--transition-normal);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: white;
  padding: 28px;
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 550px;
  box-shadow: var(--shadow-lg);
  animation: slideUp var(--transition-normal);
  border: 1px solid var(--gray-200);
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content h3 {
  margin: 0 0 16px 0;
  color: var(--gray-900);
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-content p {
  margin: 0 0 20px 0;
  color: var(--gray-600);
  font-size: 0.95rem;
  line-height: 1.5;
}

.modal-content textarea {
  width: 100%;
  padding: 14px;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
  font-size: 0.95rem;
  margin-bottom: 20px;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.modal-content textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.cancel-btn {
  padding: 10px 18px;
  background: none;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  color: var(--gray-600);
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all var(--transition-fast);
}

.cancel-btn:hover {
  background-color: var(--gray-100);
  border-color: var(--gray-400);
  color: var(--gray-800);
}

.resolve-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 18px;
  background-color: var(--success-color);
  color: white;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all var(--transition-fast);
  box-shadow: var(--shadow-sm);
}

.resolve-btn:hover {
  background-color: var(--success-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.resolve-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Enhanced responsive styles */
@media (max-width: 768px) {
  .forms-queries-container {
    padding: 16px;
  }
  
  .forms-queries-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 20px;
  }
  
  .forms-queries-title {
    font-size: 1.5rem;
  }
  
  .query-tabs {
    width: 100%;
  }
  
  .query-tab {
    flex: 1;
    justify-content: center;
    padding: 8px 12px;
    font-size: 0.85rem;
  }
  
  .queries-table-container {
    overflow-x: auto;
  }
  
  .queries-table th,
  .queries-table td {
    padding: 12px 14px;
  }
  
  .query-header {
    flex-direction: column;
    gap: 16px;
  }

  .query-meta {
    align-items: flex-start;
    margin-left: 0;
  }
  
  .query-detail-view,
  .query-card {
    padding: 16px;
  }
  
  .response-item {
    padding: 16px;
  }
  
  .modal-content {
    padding: 20px;
    width: 95%;
  }
  
  .submit-response-btn,
  .resolve-btn,
  .cancel-btn {
    padding: 8px 14px;
    font-size: 0.9rem;
  }
}

/* Additional animation for better UX */
.query-row,
.response-item,
.submit-response-btn,
.resolve-query-btn,
.back-button,
.view-query-btn {
  will-change: transform;
}

/* Focus styles for better accessibility */
button:focus,
textarea:focus,
input:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Improved scrollbar for better UX */
.queries-table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.queries-table-container::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

.queries-table-container::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 4px;
}

.queries-table-container::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

.query-creator {
  margin-top: 8px;
  font-size: 0.9rem;
  color: #666;
}

.creator-label {
  font-weight: 500;
  margin-right: 4px;
}

.creator-name {
  color: #333;
}

.queries-table td:nth-child(2) {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.query-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.preview-form-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  color: #4b5563;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.preview-form-btn:hover {
  background-color: #e5e7eb;
  color: #1f2937;
}

.preview-form-btn svg {
  width: 16px;
  height: 16px;
}

.preview-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.preview-modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.preview-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #ef4444;
}

.preview-error svg {
  margin-bottom: 1rem;
}

.retry-button {
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #dc2626;
}
