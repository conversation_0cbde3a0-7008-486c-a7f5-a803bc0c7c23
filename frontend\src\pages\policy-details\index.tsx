import { useEffect } from "react";
import { useParams } from "react-router-dom";
import Preloader from "@/components/common/Preloader";
import HeroSixSection from "@/shared/HeroSixSection.tsx";
import PdfViewer from "@components/PdfViewer.tsx";
import PolicyInformations from "@/pages/policy-details/PolicyInformations.tsx";
import Footer from "@/shared/Footer.tsx";
import { usePolicyQuery } from "@/hooks/policy.query.ts";
import { usePolicyStore } from "@/store/policyState.tsx";
import "./PolicyDetails.css";

const PolicyDetails = () => {
    const { uuid } = useParams();
    const { data: policy, isLoading, error } = usePolicyQuery(uuid as string);
    const setPolicy = usePolicyStore((state) => state.setPolicy);

    useEffect(() => {
        if (policy) {
            setPolicy(policy);
        }
    }, [policy, setPolicy]);

    if (isLoading) return <Preloader />;
    if (error) return <p>Erreur lors du chargement de la policy.</p>;
    if (!policy) return <p>Aucune policy trouvée.</p>;


    const attachUrl = policy.attach_content ? `${policy.attach_content}` : null;

    // Liste des extensions d'image courantes
    const imageExtensions = [".png", ".jpg", ".jpeg", ".gif", ".bmp"];
    // Vérifie si l'URL est une image ou un PDF
    const isImage = attachUrl && imageExtensions.some(ext => attachUrl.toLowerCase().endsWith(ext));
    const isPdf = attachUrl && attachUrl.toLowerCase().endsWith(".pdf");

    console.log("attachUrl:", attachUrl);
    console.log("isImage:", isImage);
    console.log("isPdf:", isPdf);
    return (
        <div className="main-content">
            <main className="pt-5">
                <div className="details-policy">
                    <div className="title-wrapper">
                        <h3 className="details-subtitle">{policy.category}</h3>
                        <h2 className="details-title">{policy.title}</h2>
                        <p className="details-date">{new Date(policy.created_at).toLocaleDateString()}</p>
                    </div>
                    <div className="policy-grid">
                        <div className="pdf-viewer">
                        {attachUrl ? (
                                isPdf ? (
                                    <PdfViewer fileUrl={attachUrl} />
                                ) : isImage ? (
                                    <img
                                        src={attachUrl}
                                        alt="Policy attachment"
                                        style={{ maxWidth: "100%", height: "auto" }}
                                    />
                                ) : (
                                    <p>Format de fichier non pris en charge</p>
                                )
                            ) : (
                                <p>Aucune pièce jointe pour cette policy</p>
                            )}
                        </div>
                        <div className="policy-informations">
                            <PolicyInformations
                                authorName={policy.author_name}
                                jobTitle={policy.job_title}
                                description={policy.description}
                                hospital={policy.hospital}
                                department={policy.department}
                                study={policy.study}
                            />
                        </div>
                    </div>
                </div>
                <HeroSixSection />
                <Footer />
            </main>
        </div>
    );
};

export default PolicyDetails;
