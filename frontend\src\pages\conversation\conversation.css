.conv-container {
  padding: 15px;
  background-color: #f9f9f9;
  max-width: 900px;
  margin: 0 auto;
}

.conv-container header {
  text-align: center;
  margin-bottom: 15px;
}

.conv-container h1 {
  font-size: 1.8em;
  margin: 0;
}

.conv-form-description {
  font-size: 1.1em;
  color: #666;
}

.conv-form {
  margin-bottom: 25px;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.conv-form-group {
  margin-bottom: 15px;
  padding: 15px;
  border: 2px solid #3dc6d6;
  border-radius: 8px;
  background-color: #f0fcfc;
}

.conv-form-label {
  display: block;
  font-weight: bold;
  margin-bottom: 20px;
  font-size: 1em;
}

/* Input styles */
.conv-form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid #000 !important;
  border-radius: 8px;
  background-color: #e0f7f9;
  font-size: 14px;
  color: #333;
}

.conv-form-control:focus {
  outline: none;
  border-color: #000 !important;
  box-shadow: 0 0 3px #000 !important;
}

/* Select styles */
.conv-form-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #000 !important;
  border-radius: 8px;
  background-color: #e0f7f9;
  font-size: 14px;
  color: #333;
}

.conv-form-select:focus {
  outline: none;
  border-color: #000 !important;
  box-shadow: 0 0 3px #000 !important;
}

/* Checkbox styles */
.conv-form-check {
  margin: 10px 0;
}

.conv-form-check-input {
  margin-right: 10px;
}

/* Submit button */
.conv-btn-primary {
  margin-top: 15px;
  padding: 10px 20px;
  font-size: 14px;
  border: none;
  border-radius: 8px;
  background-color: #3dc6d6;
  color: #fff;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  width: 150px;
  justify-content: center;
}

.conv-btn-primary:hover {
  background-color: #2ca4b5;
}

.conv-btn-primary:disabled {
  background-color: #96c7cf;
  cursor: not-allowed;
}

/* Question numbering */
.conv-question-number {
  display: block;
  font-size: 0.9em;
  color: #555;
  margin-bottom: 5px;
  font-weight: bold;
}

/* Required field indicator */
.conv-text-danger {
  color: #dc3545;
  margin-left: 4px;
}

/* Long text inputs */
textarea.conv-form-control {
  min-height: 100px;
  resize: vertical;
}

/* Date inputs */
input[type="date"].conv-form-control {
  padding: 10px 12px;
}

/* Main content wrapper */
.conv-main-content {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.conv-content {
  flex: 1;
  padding: 20px 0;
}

.conv-patient-info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.conv-form-field {
  display: flex;
  flex-direction: column;
}

.conv-questions-section {
  margin-top: 30px;
}

.conv-questions-section h2 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.4em;
}

.conv-title {
  color: #333;
  font-size: 1.4em;
  margin-bottom: 20px;
}

/* Modal styles */
.conv-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.conv-modal-content {
  background-color: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 500px;
  width: 90%;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.conv-modal-title {
  color: #3dc6d6;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.conv-modal-message {
  color: #666;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.conv-modal-button {
  background-color: #3dc6d6;
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s;
}

.conv-modal-button:hover {
  background-color: #2ca4b5;
}
