import React, { useState, ChangeEvent } from "react";
import breathingImage from "./static/images/added/lung.png";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyText from "@/components/NurtifyText";
import NurtifySelect from "@/components/NurtifySelect";
import NurtifyCheckbox from "@/components/NurtifyCheckBox";
import NurtifyMultiInput from "@/components/NurtifyMultiInput";

const Breathing = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  const [cough, setCough] = useState<string[]>([]);
  const [respiratoryDistresses, setRespiratoryDistresses] = useState<string[]>(
    []
  );
  const [percussions, setPercussions] = useState<string[]>([]);
  const [palpations, setPalpations] = useState<string[]>([]);
  const [airEntries, setAirEntries] = useState<string[]>([]);
  const [respiratoryNoise, setRespiratoryNoise] = useState<string[]>([]);

  const handleCheckboxChange =
    (setFieldState: React.Dispatch<React.SetStateAction<string[]>>, fieldName: string) =>
    (event: ChangeEvent<HTMLInputElement>) => {
      const value = event.target.value;
      setFieldState(prev => {
        const updatedValues = event.target.checked
          ? [...prev, value]
          : prev.filter(item => item !== value);

        setAssessment({
          ...assessment,
          breathing: {
            ...assessment.breathing,
            [fieldName]: updatedValues,
          },
        });

        return updatedValues;
      });
    };
  const handleO2SupportTypeChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value;
    setAssessment({
      ...assessment,
      breathing: {
        ...assessment.breathing,
        o2SupportType: value,
        litrePerMinute:
          value === "Room Air" ? 0 : assessment.breathing.litrePerMinute,
      },
    });
  };

  const handleRespiratoryDistressCheckboxChange = handleCheckboxChange(
    setRespiratoryDistresses,
    "respiratoryDistress"
  );
  const handlePercussionsCheckboxChange = handleCheckboxChange(
    setPercussions,
    "percussions"
  );
  const handlePalpationsCheckboxChange = handleCheckboxChange(
    setPalpations,
    "palpations"
  );
  const handleAirEntriesCheckboxChange = handleCheckboxChange(
    setAirEntries,
    "airEntries"
  );
  const handleCoughCheckboxChange = handleCheckboxChange(setCough, "cough");
  const handleRespiratoryNoiseCheckboxChange = handleCheckboxChange(
    setRespiratoryNoise,
    "respiratoryNoise"
  );

  // Define the input fields for NurtifyMultiInput
  const inputFields = [
    { name: "saturationOxygen", label: "O2 Saturation (SpO2%)" },
    { name: "respiratoryRate", label: "Respiratory Rate (bpm)" },
  ];

  // Define the initial values for the input fields
  const initialValues = [
    assessment?.breathing?.saturationOxygen?.toString() || "",
    assessment?.breathing?.respiratoryRate?.toString() || "",
  ];

  // Handle the onChange event for NurtifyMultiInput
  const handleMultiInputChange = (index: number, event: ChangeEvent<HTMLInputElement>) => {
    const { value } = event.target;
    const newValues = [...initialValues];
    newValues[index] = value;

    switch (index) {
      case 0:
        setAssessment({
          ...assessment,
          breathing: {
            ...assessment.breathing,
            saturationOxygen: parseInt(value, 10) || 0,
          },
        });
        break;
      case 1:
        setAssessment({
          ...assessment,
          breathing: {
            ...assessment.breathing,
            respiratoryRate: parseInt(value, 10) || 0,
          },
        });
        break;
      default:
        break;
    }
  };

  return (
    <div className="block align-items-*-start flex-column flex-md-row p-4 gap-5">
      <div className="mt-3 " id="division-24">
        {/* Section Title  */}
        <div className="inlineBlock headinqQuestion">
          <img
            src={breathingImage}
            className="imageEtiquette"
            alt="patient face image round"
          />
          <span className="mb-2 py-2 fs-2 text-start etiquetteHeadingForms">
            Breathing
          </span>
        </div>
        {/* Measurments */}
        <div className="list-group col-xl-6 col-lg-8 col-md-12">
          {/* Saturation O2 and Resp Rate */}
          <NurtifyMultiInput
            inputs={inputFields}
            values={initialValues}
            onChange={handleMultiInputChange}
          />
          {/* O2 SUpport ?  */}
          <div className="option d-flex flex-wrap mb-2 flex-row gap-2 align-items-center justify-content-start">
            <NurtifyText label="O2 Support?" className="inlineInput" />
            <NurtifySelect
              options={[
                { value: "Room Air", label: "Room Air" },
                { value: "Nasal Cannula", label: "Nasal Cannula" },
                { value: "Non-rebreather mask", label: "Non-rebreather Mask" },
                { value: "Nebuliser Mask", label: "Nebuliser Mask" },
                { value: "Venturi Mask", label: "Venturi Mask" },
                { value: "High Flow O2", label: "High Flow O2" },
                { value: "CPAP", label: "CPAP" },
                { value: "BIPAP", label: "BIPAP" },
                {
                  value: "Mechanical Ventilator",
                  label: "Mechanical Ventilator",
                },
                { value: "Other", label: "Other" },
              ]}
              onChange={handleO2SupportTypeChange}
              name="o2SupportType"
            />
          </div>

          {/* litre per minute O2 */}
          <div
            className={`input-group mb-1 gap-2 themed-grid-col col-md-4 ${
              assessment?.breathing?.o2SupportType !== "Room Air"
                ? ""
                : "visually-hidden"
            }`}
          >
            <div className="d-flex gap-2">
              <span className="inlineInput" style={{ width: "220px" }}>
                O2 support (L/min):
              </span>
              <input
                type="range"
                placeholder="L/min"
                value={assessment?.breathing?.litrePerMinute}
                onChange={(e) => {
                  const litrePerMinute = parseFloat(e.target.value);
                  setAssessment({
                    ...assessment,
                    breathing: {
                      ...assessment.breathing,
                      litrePerMinute: litrePerMinute,
                    },
                  });
                }}
                min={0}
                max={15}
                step={0.5}
                style={{ width: "300px" }}
              />
              <NurtifyInput
                type="number"
                placeholder="L/min"
                title="L/min"
                value={assessment?.breathing?.litrePerMinute}
                onChange={(e) => {
                  const litrePerMinute = parseFloat(e.target.value);
                  setAssessment({
                    ...assessment,
                    breathing: {
                      ...assessment.breathing,
                      litrePerMinute: litrePerMinute,
                    },
                  });
                }}
                min={0}
                max={15}
                step={0.5}
              />
              <NurtifyText label="L/min" className="mt-2" />
            </div>
          </div>

          {/* FiO2 */}
          <div className="input-group mb-1 gap-2 themed-grid-col col-md-4 mb-5">
            <NurtifyText className="inlineInput" label="FiO2 (%):" />
            <input
              type="range"
              placeholder="FiO2"
              value={assessment?.breathing?.fio2}
              onChange={(e) => {
                const fio2 = parseFloat(e.target.value);
                setAssessment({
                  ...assessment,
                  breathing: {
                    ...assessment.breathing,
                    fio2: fio2,
                  },
                });
              }}
              min={21}
              max={100}
              step={1}
              className="form-control-sm"
              style={{ width: "300px" }}
              defaultValue={21}
            />
            <span className="mt-2" style={{ fontSize: "16px" }}>
              {assessment?.breathing?.fio2}%
            </span>
          </div>
        </div>
        {/* Breathing MCQ Question */}
        <div className="d-flex flex-wrap align-items-*-start flex-column flex-md-row py-md-5">
          {/* Resp Sx  */}
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">
              Any Signs of repiratory distress?
            </span>

            <NurtifyCheckbox
              id="Nill"
              label="No signs of respiratory distress"
              value="No signs of respiratory distress"
              checked={respiratoryDistresses.includes(
                "No signs of respiratory distress"
              )}
              onChange={handleRespiratoryDistressCheckboxChange}
            />

            <NurtifyCheckbox
              id="Central Cyanosis"
              label="Central Cyanosis"
              value="Central Cyanosis"
              checked={respiratoryDistresses.includes("Central Cyanosis")}
              onChange={handleRespiratoryDistressCheckboxChange}
            />

            <NurtifyCheckbox
              id="Use of the accessory muscles"
              label="Use of the accessory muscles"
              value="Use of the accessory muscles"
              checked={respiratoryDistresses.includes(
                "Use of the accessory muscles"
              )}
              onChange={handleRespiratoryDistressCheckboxChange}
            />

            <NurtifyCheckbox
              id="Abdominal Breathing"
              label="Abdominal Breathing"
              value="Abdominal Breathing"
              checked={respiratoryDistresses.includes("Abdominal Breathing")}
              onChange={handleRespiratoryDistressCheckboxChange}
            />

            <NurtifyCheckbox
              id="Sweating"
              label="Sweating"
              value="Sweating"
              checked={respiratoryDistresses.includes("Sweating")}
              onChange={handleRespiratoryDistressCheckboxChange}
            />
          </div>
          {/* Resp Noises */}
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">
              Any abnormal respiratory noise?
            </span>

            <NurtifyCheckbox
              id="None"
              label="None"
              value="No abnormal respiratory sounds"
              checked={respiratoryNoise.includes("No abnormal respiratory sounds")}
              onChange={handleRespiratoryNoiseCheckboxChange}
            />

            <NurtifyCheckbox
              id="Wheezes"
              label="Wheezes"
              value="Wheezes"
              checked={respiratoryNoise.includes("Wheezes")}
              onChange={handleRespiratoryNoiseCheckboxChange}
            />

            <NurtifyCheckbox
              id="Crackles"
              label="Crackles"
              value="Crackles"
              checked={respiratoryNoise.includes("Crackles")}
              onChange={handleRespiratoryNoiseCheckboxChange}
            />

            <NurtifyCheckbox
              id="Rattling airway noises"
              label="Rattling airway noises"
              value="Rattling airway noises"
              checked={respiratoryNoise.includes("Rattling airway noises")}
              onChange={handleRespiratoryNoiseCheckboxChange}
            />

            <NurtifyCheckbox
              id="Abscent Sound (Right)"
              label="Abscent Sound (Right)"
              value="Abscent Sound (Right)"
              checked={respiratoryNoise.includes("Abscent Sound (Right)")}
              onChange={handleRespiratoryNoiseCheckboxChange}
            />

            <NurtifyCheckbox
              id="Abscent Sound (left)"
              label="Abscent Sound (left)"
              value="Abscent Sound (left)"
              checked={respiratoryNoise.includes("Abscent Sound (left)")}
              onChange={handleRespiratoryNoiseCheckboxChange}
            />

            <NurtifyCheckbox
              id="Stridor"
              label="Stridor"
              value="Stridor"
              checked={respiratoryNoise.includes("Stridor")}
              onChange={handleRespiratoryNoiseCheckboxChange}
            />
          </div>



          {/* Percussion */}
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Percussions</span>

            <NurtifyCheckbox
              label="Normal Resonance"
              value="Normal Resonance"
              checked={percussions.includes("Normal Resonance")}
              onChange={handlePercussionsCheckboxChange}
            />

            <NurtifyCheckbox
              label="Hyper-Resonance"
              value="Hyper-Resonance"
              checked={percussions.includes("Hyper-Resonance")}
              onChange={handlePercussionsCheckboxChange}
            />

            <NurtifyCheckbox
              label="Dull"
              value="Dull"
              checked={percussions.includes("Dull")}
              onChange={handlePercussionsCheckboxChange}
            />

          </div>

          {/* Palpation */}
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Palpation</span>

            <NurtifyCheckbox
              label="Normal"
              value="Normal Percussion"
              checked={palpations.includes("Normal Percussion")}
              onChange={handlePalpationsCheckboxChange}
            />

            <NurtifyCheckbox
              label="Surgical Emphysema"
              value="Surgical Emphysema"
              checked={palpations.includes("Surgical Emphysema")}
              onChange={handlePalpationsCheckboxChange}
            />

            <NurtifyCheckbox
              label="Crepitus"
              value="Crepitus"
              checked={palpations.includes("Crepitus")}
              onChange={handlePalpationsCheckboxChange}
            />

            <NurtifyCheckbox
              label="Deviated Trachea"
              value="Deviated Trachea"
              checked={palpations.includes("Deviated Trachea")}
              onChange={handlePalpationsCheckboxChange}
            />

            <NurtifyCheckbox
              label="Distended Neck Vein"
              value="Distended Neck Vein"
              checked={palpations.includes("Distended Neck Vein")}
              onChange={handlePalpationsCheckboxChange}
            />

            <NurtifyCheckbox
              label="Chest Open Wound"
              value="Chest Open Wound"
              checked={palpations.includes("Chest Open Wound")}
              onChange={handlePalpationsCheckboxChange}
            />

          </div>
          {/* Air Entry */}
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Air Entry</span>

            <NurtifyCheckbox
              label="Bilateral Entry of Air"
              value="Bilateral Entry of Air"
              checked={airEntries.includes("Bilateral Entry of Air")}
              onChange={handleAirEntriesCheckboxChange}
            />

            <NurtifyCheckbox
              label="Unilateral (Right)"
              value="Unilateral (Right)"
              checked={airEntries.includes("Unilateral (Right)")}
              onChange={handleAirEntriesCheckboxChange}
            />

            <NurtifyCheckbox
              label="Unilateral (Left)"
              value="Unilateral (Left)"
              checked={airEntries.includes("Unilateral (Left)")}
              onChange={handleAirEntriesCheckboxChange}
            />
          </div>
          {/* Cough */}
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Cough</span>

            <NurtifyCheckbox
              label="No Cough"
              value="No Cough"
              checked={cough.includes("No Cough")}
              onChange={handleCoughCheckboxChange}
            />

            <NurtifyCheckbox
              label="Dry Cough"
              value="Dry Cough"
              checked={cough.includes("Dry Cough")}
              onChange={handleCoughCheckboxChange}
            />

            <NurtifyCheckbox
              label="Productive Cough"
              value="Productive Cough"
              checked={cough.includes("Productive Cough")}
              onChange={handleCoughCheckboxChange}
            />

            <NurtifyCheckbox
              label="Hemoptysis"
              value="Hemoptysis"
              checked={cough.includes("Hemoptysis")}
              onChange={handleCoughCheckboxChange}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Breathing;
