import React from "react";
import { Download } from "react-feather";

interface PdfDownloadButtonProps {
    fileUrl: string;
    fileName: string;
}

const PdfDownloadButton: React.FC<PdfDownloadButtonProps> = ({ fileUrl, fileName }) => {
    const handleDownload = async () => {
        try {
            // Récupérer le fichier avec fetch
            const response = await fetch(fileUrl, {
                method: "GET",
                headers: {
                    "Content-Type": "application/octet-stream",
                },
            });

            if (!response.ok) {
                throw new Error("Erreur lors de la récupération du fichier");
            }

            // Convertir la réponse en Blob
            const blob = await response.blob();
            const blobUrl = window.URL.createObjectURL(blob);

            // Créer un lien temporaire pour le téléchargement
            const link = document.createElement("a");
            link.href = blobUrl;
            link.download = fileName || "downloaded_file";

            document.body.appendChild(link);
            link.click();

            // Nettoyer après le téléchargement
            document.body.removeChild(link);
            window.URL.revokeObjectURL(blobUrl); // Libérer la mémoire
        } catch (error) {
            console.error("Erreur lors du téléchargement :", error);
            // Optionnel : informer l'utilisateur en cas d'erreur
            alert("Impossible de télécharger le fichier.");
        }
    };

    return (
        <button className="downloadd" onClick={handleDownload}>
            <Download size={23} />
        </button>
    );
};

export default PdfDownloadButton;