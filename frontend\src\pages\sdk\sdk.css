/* SDK Page Styles */
.content-wrapper-sdk {
  padding-top: 80px;
  min-height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
}

/* SDK Header */
.sdk-header {
  background: linear-gradient(135deg, #37b7c3 0%, #2a9a9f 100%);
  padding: 60px 0 40px;
  color: white;
  margin-bottom: 40px;
}

.sdk-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
}

.sdk-description {
  font-size: 18px;
  max-width: 700px;
  margin-bottom: 30px;
  opacity: 0.9;
}

.sdk-search-container {
  max-width: 600px;
  margin-bottom: 20px;
}

.sdk-search {
  width: 100%;
  padding: 14px 20px;
  border-radius: 8px;
  border: none;
  font-size: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.sdk-search:focus {
  outline: none;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

/* SDK Navigation */
.sdk-navigation {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 30px;
}

.sdk-nav-item {
  padding: 10px 20px;
  border-radius: 30px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.sdk-nav-item:hover {
  background-color: #f1f3f5;
  border-color: #dee2e6;
}

.sdk-nav-item.active {
  background-color: #37b7c3;
  border-color: #37b7c3;
  color: white;
}

/* Component Cards */
.component-card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 24px;
  margin-bottom: 30px;
  transition: all 0.3s ease;
}

.component-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.component-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e9ecef;
}

.component-title {
  font-size: 18px;
  font-weight: 600;
  color: #343a40;
}

.component-category {
  font-size: 12px;
  font-weight: 500;
  color: white;
  background-color: #37b7c3;
  padding: 4px 10px;
  border-radius: 20px;
}

.component-content {
  margin-bottom: 20px;
}

.component-description {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 16px;
}

.component-code {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  font-family: monospace;
  font-size: 14px;
  color: #343a40;
  overflow-x: auto;
}

/* Component Grid */
.component-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 60px 0;
  color: #6c757d;
}

.no-results-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #dee2e6;
}

.no-results-text {
  font-size: 18px;
  margin-bottom: 10px;
}

.no-results-subtext {
  font-size: 14px;
  color: #adb5bd;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .sdk-header {
    padding: 40px 0 30px;
  }
  
  .sdk-title {
    font-size: 28px;
  }
  
  .sdk-description {
    font-size: 16px;
  }
  
  .component-grid {
    grid-template-columns: 1fr;
  }
}
