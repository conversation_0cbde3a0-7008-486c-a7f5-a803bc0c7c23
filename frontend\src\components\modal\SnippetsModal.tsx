import "./SnippetsModal.css";
import { useState, useEffect, useCallback } from "react";
import {
  MessageCircleWarning,
  Copy,
  LayoutGrid,
  ChevronDown,
  SquareX,
  SquarePen,
  CheckCircle,
  AlertCircle,
  Loader2
} from "lucide-react";
import AddSnippetModal from "./AddSnippetModal";
import EditSnippetModal from "./EditSnippetModal";
import {
  useDeleteSnippetMutation,
  useSnippetsQuery,
  usePartialUpdateSnippetMutation,
} from "@/hooks/snippet.query";
import ConfirmationDeleteSnippetModal from "./ConfirmationDeleteSnippetModal";

interface Snippet {
  uuid: string;
  name: string;
  description: string;
  content: string;
}

const SnippetsModal = ({ isOpen, onClose }: { isOpen: boolean; onClose: () => void }) => {
  const { data: snippets, isLoading, error, refetch } = useSnippetsQuery();
  const [expandedSnippet, setExpandedSnippet] = useState<number | null>(null);
  const [isAddSnippetModalOpen, setAddSnippetModalOpen] = useState(false);
  const [isEditSnippetModalOpen, setEditSnippetModalOpen] = useState(false);
  const [selectedSnippet, setSelectedSnippet] = useState<Snippet | null>(null);
  const { mutate: deleteSnippet, isPending: isDeleting } = useDeleteSnippetMutation();
  const { mutate: updateSnippet, isPending: isUpdating } = usePartialUpdateSnippetMutation();
  const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] = useState(false);
  const [selectedSnippetUuid, setSelectedSnippetUuid] = useState<string | null>(null);
  const [isSnippetsModalVisible, setSnippetsModalVisible] = useState(isOpen);
  const [notification, setNotification] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    visible: boolean;
  }>({
    message: "",
    type: 'info',
    visible: false
  });
  
  const [copiedSnippetId, setCopiedSnippetId] = useState<string | null>(null);

  useEffect(() => {
    setSnippetsModalVisible(isOpen);
  }, [isOpen]);

  const showNotification = useCallback((message: string, type: 'success' | 'error' | 'info') => {
    setNotification({
      message,
      type,
      visible: true
    });
    
    // Auto-hide notification after 3 seconds
    setTimeout(() => {
      setNotification(prev => ({ ...prev, visible: false }));
    }, 3000);
  }, []);

  if (
    !isSnippetsModalVisible &&
    !isAddSnippetModalOpen &&
    !isConfirmDeleteModalOpen &&
    !isEditSnippetModalOpen
  )
    return null;

  const toggleSnippet = (index: number) =>
    setExpandedSnippet(expandedSnippet === index ? null : index);

  const handleSaveSnippet = () => {
    setAddSnippetModalOpen(false);
    setSnippetsModalVisible(true);
    refetch();
    showNotification("Snippet created successfully", 'success');
  };

  const handleAddSnippetClick = () => {
    setAddSnippetModalOpen(true);
    setSnippetsModalVisible(false);
  };

  const handleDelete = (uuid: string) => {
    setSelectedSnippetUuid(uuid);
    setIsConfirmDeleteModalOpen(true);
    setSnippetsModalVisible(false);
  };

  const handleConfirmDeleteSnippet = async () => {
    if (selectedSnippetUuid) {
      try {
        await deleteSnippet(selectedSnippetUuid);
        showNotification("Snippet deleted successfully", 'success');
        refetch();
      } catch (error) {
        console.error("Error deleting snippet:", error);
        showNotification("Failed to delete snippet", 'error');
      }
    }
    setIsConfirmDeleteModalOpen(false);
    setSelectedSnippetUuid(null);
    setSnippetsModalVisible(true);
  };

  const handleCancelDelete = () => {
    setIsConfirmDeleteModalOpen(false);
    setSelectedSnippetUuid(null);
    setSnippetsModalVisible(true);
  };

  const handleUpdateClick = (snippet: Snippet) => {
    setSelectedSnippet(snippet);
    setEditSnippetModalOpen(true);
    setSnippetsModalVisible(false);
  };

  const handleSaveEditSnippet = (name: string, description: string, content: string) => {
    if (selectedSnippet) {
      updateSnippet(
        { uuid: selectedSnippet.uuid, data: { name, description, content } },
        {
          onSuccess: () => {
            showNotification("Snippet updated successfully", 'success');
            refetch();
          },
          onError: (error) => {
            console.error("Error updating snippet:", error);
            showNotification("Failed to update snippet", 'error');
          },
        }
      );
    }
    setEditSnippetModalOpen(false);
    setSnippetsModalVisible(true);
  };

  const handleCancelEdit = () => {
    setEditSnippetModalOpen(false);
    setSnippetsModalVisible(true);
  };

  const handleCancelAdd = () => {
    setAddSnippetModalOpen(false);
    setSnippetsModalVisible(true);
  };

  const handleCopyClick = (uuid: string, content: string) => {
    navigator.clipboard.writeText(content);
    setCopiedSnippetId(uuid);
    showNotification("Snippet copied to clipboard", 'success');
    
    // Reset copied state after 2 seconds
    setTimeout(() => {
      setCopiedSnippetId(null);
    }, 2000);
  };

  const renderNotificationIcon = () => {
    switch (notification.type) {
      case 'success':
        return <CheckCircle size={18} className="text-green-500" />;
      case 'error':
        return <AlertCircle size={18} className="text-red-500" />;
      default:
        return <MessageCircleWarning size={18} className="text-blue-500" />;
    }
  };

  return (
    <>
      {isSnippetsModalVisible && (
        <div className="modal-overlay">
          <div className="snippets-modal-content">
            <div className="paragraph-content">
              <p>
                <MessageCircleWarning style={{ color: "rgba(32, 110, 18, 0.7)", marginRight: "10px" }} />
                Save time typing on nurtify. Create shortcuts to sentences you type all the time.
              </p>
            </div>
            
            {notification.visible && (
              <div className={`notification ${notification.type === 'success' ? 'bg-green-50 border-green-200' : notification.type === 'error' ? 'bg-red-50 border-red-200' : 'bg-blue-50 border-blue-200'}`}
                   style={{
                     padding: '10px 16px',
                     borderRadius: '6px',
                     marginBottom: '16px',
                     display: 'flex',
                     alignItems: 'center',
                     gap: '8px',
                     borderLeft: '4px solid',
                     animation: 'fadeIn 0.3s ease-out'
                   }}>
                {renderNotificationIcon()}
                <span>{notification.message}</span>
              </div>
            )}
            
            <button className="add-snippet-button" onClick={handleAddSnippetClick}>
              Add New Snippet <LayoutGrid size={16} style={{ marginLeft: "8px" }} />
            </button>
            
            <div className="snippet-list-container">
              <p className="snippet-list-title">List of Snippets:</p>
              {isLoading ? (
                <div style={{ display: 'flex', justifyContent: 'center', padding: '20px' }}>
                  <Loader2 size={24} className="animate-spin text-blue-500" />
                </div>
              ) : error ? (
                <div style={{ padding: '20px', textAlign: 'center', color: '#d32f2f' }}>
                  <AlertCircle size={24} style={{ margin: '0 auto 10px' }} />
                  <p>Error loading snippets. Please try again.</p>
                </div>
              ) : snippets?.length === 0 ? (
                <div style={{ padding: '20px', textAlign: 'center', color: '#666' }}>
                  <p>No snippets found. Create your first snippet by clicking "Add New Snippet".</p>
                </div>
              ) : (
                <ul className="snippet-list">
                  {snippets?.map((snippet, index) => (
                    <li className="snippet-item" key={snippet.uuid}>
                      <div className="snippet-header">
                        <span>{snippet.name}</span>
                        <div className="snippet-actions">
                          <ChevronDown
                            size={18}
                            onClick={() => toggleSnippet(index)}
                            style={{
                              cursor: "pointer",
                              transform: expandedSnippet === index ? "rotate(180deg)" : "rotate(0deg)",
                              transition: "transform 0.3s",
                              color: "#666",
                              marginRight: "8px"
                            }}
                          />
                          <button 
                            className="copy-button" 
                            onClick={() => handleCopyClick(snippet.uuid, snippet.content)}
                            style={{ 
                              backgroundColor: copiedSnippetId === snippet.uuid ? '#d0f0f3' : undefined,
                              color: copiedSnippetId === snippet.uuid ? '#2da8b4' : undefined
                            }}
                          >
                            {copiedSnippetId === snippet.uuid ? 'Copied!' : 'Copy'} 
                            <Copy size={16} style={{ marginLeft: "6px" }} />
                          </button>
                          <button
                            className="SquarePen"
                            onClick={() => handleUpdateClick(snippet)}
                            title="Edit snippet"
                            aria-label="Edit snippet"
                          >
                            <SquarePen size={16} />
                          </button>
                          <button
                            className="SquareX"
                            onClick={() => handleDelete(snippet.uuid)}
                            title="Delete snippet"
                            aria-label="Delete snippet"
                          >
                            <SquareX size={16} />
                          </button>
                        </div>
                      </div>
                      {expandedSnippet === index && (
                        <div className="snippet-details">
                          <p>
                            <strong>Subject:</strong> {snippet.description}
                          </p>
                          <p>
                            <strong>Content:</strong> {snippet.content}
                          </p>
                        </div>
                      )}
                    </li>
                  ))}
                </ul>
              )}
            </div>
            
            <div className="action-buttons">
              <button
                className="close-button"
                onClick={() => {
                  setSnippetsModalVisible(false);
                  onClose();
                }}
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
      
      <AddSnippetModal 
        isOpen={isAddSnippetModalOpen} 
        onClose={handleCancelAdd} 
        onSave={handleSaveSnippet} 
      />
      
      <ConfirmationDeleteSnippetModal
        isOpen={isConfirmDeleteModalOpen}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDeleteSnippet}
        title="Confirm Deleting Snippet"
        message="Are you sure you want to delete this snippet?"
        subMessage="This action is permanent and cannot be undone. All associated data will be lost."
        isLoading={isDeleting}
      />
      
      <EditSnippetModal
        isOpen={isEditSnippetModalOpen}
        onClose={handleCancelEdit}
        onSave={handleSaveEditSnippet}
        isLoading={isUpdating}
        snippet={
          selectedSnippet
            ? {
                title: selectedSnippet.name,
                subject: selectedSnippet.description,
                description: selectedSnippet.content,
              }
            : null
        }
      />
    </>
  );
};

export default SnippetsModal;
