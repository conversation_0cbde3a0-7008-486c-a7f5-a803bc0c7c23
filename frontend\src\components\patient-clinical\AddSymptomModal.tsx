import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import { type CreateSymptomData } from "@/services/api/symptom.service";
import { useCreateSymptomMutation } from "@/hooks/symptom.query";

interface AddSymptomModalProps {
  isOpen: boolean;
  onClose: () => void;
  patientUuid: string;
  onSuccess: () => void;
}

const AddSymptomModal: React.FC<AddSymptomModalProps> = ({
  isOpen,
  onClose,
  patientUuid,
  onSuccess,
}) => {
  console.log('Modal received patientUuid:', patientUuid); // Debug log

  const [formData, setFormData] = useState<CreateSymptomData>({
    patient_uuid: patientUuid,
    description: "",
    start_date: new Date().toISOString().split("T")[0],
    start_time: new Date().toTimeString().slice(0, 5),
    severity: 1,
    hospitalization_required: false,
    status: "3",
    patient_comment: "",
    resolved_date: "",
  });

  const resetForm = () => {
    setFormData({
      patient_uuid: patientUuid,
      description: "",
      start_date: new Date().toISOString().split("T")[0],
      start_time: new Date().toTimeString().slice(0, 5),
      severity: 1,
      hospitalization_required: false,
      status: "3",
      patient_comment: "",
    });
  };

  // Update formData when patientUuid changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      patient_uuid: patientUuid
    }));
  }, [patientUuid]);

  const { mutate: createSymptom, isPending, error } = useCreateSymptomMutation(patientUuid);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const dataToSubmit = {
      ...formData,
      patient_uuid: patientUuid,
      // Only include resolved_date if status is "1" (resolved)
      ...(formData.status === "1" ? { resolved_date: formData.resolved_date } : { resolved_date: undefined })
    };
    
    console.log('Submitting symptom data:', dataToSubmit); // Debug log
    
    try {
      await createSymptom(dataToSubmit, {
        onSuccess: () => {
          resetForm(); // Reset form after successful submission
          onSuccess();
        },
        onError: (error) => {
          console.error('Error creating symptom:', error);
        }
      });
    } catch (error) {
      console.error('Error creating symptom:', error);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? (e.target as HTMLInputElement).checked : value,
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Add New Symptom</h3>
          <button className="close-button" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="row g-3">
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="description">Description</label>
                <input
                  type="text"
                  className="form-control"
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="severity">Severity</label>
                <select
                  className="form-control"
                  id="severity"
                  name="severity"
                  value={formData.severity}
                  onChange={handleChange}
                  required
                >
                  <option value="1">Mild</option>
                  <option value="2">Moderate</option>
                  <option value="3">Severe</option>
                </select>
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="start_date">Start Date</label>
                <input
                  type="date"
                  className="form-control"
                  id="start_date"
                  name="start_date"
                  value={formData.start_date}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="start_time">Start Time</label>
                <input
                  type="time"
                  className="form-control"
                  id="start_time"
                  name="start_time"
                  value={formData.start_time}
                  onChange={handleChange}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="status">Status</label>
                <select
                  id="status"
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  required
                  className="form-control"
                >
                  <option value="1">Resolved</option>
                  <option value="2">Recovered with sequelae</option>
                  <option value="3">Ongoing / Continuing treatment</option>
                  <option value="4">Condition worsening</option>
                  <option value="5">Unknown</option>
                </select>
              </div>
            </div>
            {formData.status === "1" && (
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="resolved_date">Resolved Date</label>
                  <input
                    type="date"
                    className="form-control"
                    id="resolved_date"
                    name="resolved_date"
                    value={formData.resolved_date}
                    onChange={handleChange}
                    required
                  />
                </div>
              </div>
            )}
            <div className="col-md-6">
              <div className="form-group">
                <label className="checkbox-label">
                  <input
                    type="checkbox"
                    name="hospitalization_required"
                    checked={formData.hospitalization_required}
                    onChange={handleChange}
                  />
                  Hospitalization Required
                </label>
              </div>
            </div>
            <div className="col-12">
              <div className="form-group">
                <label htmlFor="patient_comment">Additional Comments</label>
                <textarea
                  id="patient_comment"
                  name="patient_comment"
                  value={formData.patient_comment}
                  onChange={handleChange}
                  className="form-control"
                  rows={3}
                />
              </div>
            </div>
          </div>

          {error && <div className="error-message">{error.message}</div>}

          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={isPending}
            >
              Cancel
            </button>
            <button type="submit" className="btn btn-primary" disabled={isPending}>
              {isPending ? "Saving..." : "Save Symptom"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddSymptomModal; 