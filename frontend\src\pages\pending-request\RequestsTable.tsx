import React from 'react';
import { ChevronDown, ChevronUp, Eye } from 'lucide-react';

interface FormData {
  // Common fields
  name?: string;
  email?: string;
  phone?: string;
  age?: string;
  
  // Patient specific fields
  postcode?: string;
  hospital?: string;
  location?: string;
  departments?: string[];
  availableTrials?: string;
  medicalHistory?: string;
  medications?: string;
  familyHistory?: string;
  
  // Doctor specific fields
  doctorName?: string;
  nhsEmail?: string;
  practice?: string;
  patientInitials?: string;
  condition?: string;
  nhsNumber?: string;
  notes?: string;
}

interface Request {
  id: string;
  name: string;
  age: string;
  nhsId: string;
  source: string;
  study: string;
  trials: string;
  date: string;
  status: "Pending" | "Approved" | "Rejected" | "In Progress";
  department: string;
  formData: FormData;
}

interface RequestsTableProps {
  requests: Request[];
  onViewDetails: (request: Request) => void;
}

const RequestsTable: React.FC<RequestsTableProps> = ({ requests, onViewDetails }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Pending':
        return '#F59E0B'; // Amber/Orange
      case 'Approved':
        return '#10B981'; // Green
      case 'Rejected':
        return '#EF4444'; // Red
      case 'In Progress':
        return '#3B82F6'; // Blue
      default:
        return '#6B7280'; // Gray
    }
  };

  return (
    <div className="pending-requests-table">
      <div className="table-header d-flex align-items-center justify-content-between mb-4">
        <h4 className="text-16 fw-500">Referrals</h4>
        <div className="table-actions d-flex align-items-center">
          <div className="table-sort me-3">
            <button className="sort-button d-flex align-items-center">
              <span className="me-2">Sort</span>
              <div className="sort-icons d-flex flex-column">
                <ChevronUp size={14} />
                <ChevronDown size={14} style={{ marginTop: '-5px' }} />
              </div>
            </button>
          </div>
          <div className="table-filter">
            <div className="search-container">
              <input 
                type="text" 
                placeholder="Search" 
                className="search-input"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="table-responsive">
        <table className="table table-hover">
          <thead>
            <tr className="bg-light-4">
              <th>Name</th>
              <th>Age</th>
              <th>NHS ID</th>
              <th>Source</th>
              <th>Study</th>
              <th>Trials</th>
              <th>Date</th>
              <th>Status</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {requests.length > 0 ? (
              requests.map((request) => (
                <tr key={request.id}>
                  <td>{request.name}</td>
                  <td>{request.age}</td>
                  <td>{request.nhsId}</td>
                  <td>
                    <span className={`source-tag ${request.source.toLowerCase()}`}>
                      {request.source}
                    </span>
                  </td>
                  <td>{request.study}</td>
                  <td>{request.trials}</td>
                  <td>{request.date}</td>
                  <td>
                    <div className="status-container" 
                      style={{ 
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      <span className="status-indicator" 
                        style={{
                          width: '10px',
                          height: '10px',
                          borderRadius: '50%',
                          backgroundColor: getStatusColor(request.status),
                          display: 'inline-block',
                          marginRight: '6px'
                        }}
                      ></span>
                      <span className="status-text"
                        style={{
                          color: getStatusColor(request.status),
                          fontWeight: '500'
                        }}
                      >
                        {request.status}
                      </span>
                      <ChevronDown size={16} className="ms-1" color={getStatusColor(request.status)} />
                    </div>
                  </td>
                  <td>
                    <button 
                      className="details-button" 
                      onClick={() => onViewDetails(request)}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '6px 12px',
                        backgroundColor: '#f9fafb',
                        border: '1px solid #e5e7eb',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease'
                      }}
                    >
                      <span className="me-2">Details</span>
                      <Eye size={16} />
                      <ChevronDown size={16} className="ms-1" />
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={9} className="text-center py-4">
                  No pending requests found
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default RequestsTable;
