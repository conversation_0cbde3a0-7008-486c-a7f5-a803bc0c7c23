.visit-calendar {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  max-width: 100%;
  overflow-x: hidden;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 10px;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

/* Media query for small screens */
@media (max-width: 768px) {
  .visit-calendar {
    padding: 10px;
  }

  .calendar-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .view-controls {
    width: 100%;
    justify-content: space-between;
    gap: 10px;
  }
}

.view-selector {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
}

.view-btn {
  padding: 8px 16px;
  background: #f5f5f5;
  border: none;
  cursor: pointer;
}

.view-btn.active {
  background: #23b7cd;
  color: white;
}

@media (max-width: 768px) {
  .view-selector {
    flex: 1;
    min-width: 200px;
  }

  .view-btn {
    flex: 1;
    padding: 6px 10px;
    font-size: 0.9em;
  }
}

@media (max-width: 480px) {
  .view-btn {
    padding: 5px 8px;
    font-size: 0.8em;
  }
}

.date-navigator {
  display: flex;
  align-items: center;
  gap: 10px;
}

.nav-btn {
  background: #23b7cd;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
}

@media (max-width: 768px) {
  .date-navigator {
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .nav-btn {
    width: 28px;
    height: 28px;
  }

  .date-navigator {
    gap: 5px;
  }
}

.date-picker {
  width: 200px;
}

@media (max-width: 768px) {
  .date-picker {
    width: 140px;
  }
}

/* Day view styles */
.day-view .visits-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.visit-card {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
}

@media (max-width: 768px) {
  .day-view .visits-list {
    gap: 8px;
  }

  .visit-card {
    padding: 10px;
  }

  .detail-item {
    padding: 8px;
    margin-top: 8px;
    font-size: 0.9em;
  }

  .patient-name {
    font-size: 0.95em;
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .visit-card {
    padding: 8px;
  }

  .detail-item {
    padding: 6px;
    font-size: 0.85em;
  }
}

.patient-name {
  font-weight: bold;
  margin-bottom: 10px;
}

.detail-item {
  padding: 10px;
  background: #f9f9f9;
  border-radius: 4px;
  margin-top: 10px;
}

.actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.view-btn, .edit-btn {
  padding: 5px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.view-btn {
  background: #23b7cd;
  color: white;
}

.edit-btn {
  background: #f0ad4e;
  color: white;
}

/* Week view styles */
.week-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 10px;
  overflow-x: auto;
}

.day-cell {
  border: 1px solid #ddd;
  border-radius: 4px;
  min-height: 150px;
  padding: 10px;
}

@media (max-width: 768px) {
  .week-grid {
    grid-template-columns: repeat(7, minmax(100px, 1fr));
    gap: 5px;
  }

  .day-cell {
    min-height: 120px;
    padding: 5px;
    font-size: 0.9em;
  }

  .day-header {
    margin-bottom: 5px;
    padding-bottom: 3px;
  }

  .day-name {
    font-size: 0.8em;
  }

  .day-date {
    font-size: 0.8em;
  }

  .visit-item {
    padding: 3px;
    font-size: 0.8em;
  }

  .actions {
    flex-direction: column;
    gap: 5px;
  }

  .view-btn, .edit-btn {
    padding: 3px 6px;
    font-size: 0.8em;
  }
}

.day-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #eee;
}

.day-name {
  font-weight: bold;
}

.visit-item {
  margin-bottom: 10px;
  padding: 5px;
  background: #f9f9f9;
  border-radius: 4px;
  font-size: 0.9em;
}

/* Month view styles */
.month-grid {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.week-header, .week-row {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 5px;
}

.week-header {
  font-weight: bold;
  text-align: center;
  margin-bottom: 10px;
}

@media (max-width: 768px) {
  .month-grid {
    gap: 3px;
  }

  .week-header, .week-row {
    grid-template-columns: repeat(7, 1fr);
    gap: 3px;
  }

  .week-header {
    font-size: 0.8em;
    margin-bottom: 5px;
  }

  .day-cell {
    min-height: 60px;
  }

  .day-number {
    font-size: 0.9em;
  }

  .visit-count {
    font-size: 0.7em;
    padding: 1px 4px;
  }
}

@media (max-width: 480px) {
  .week-header div {
    font-size: 0.7em;
  }

  .day-cell {
    min-height: 50px;
    padding: 3px;
  }
}

.day-cell.other-month {
  opacity: 0.5;
}

.clickable-day {
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
}

.clickable-day:hover {
  background-color: #f0f8ff;
  transform: scale(1.02);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.day-number {
  font-weight: bold;
  margin-bottom: 5px;
}

.visit-count {
  background: #23b7cd;
  color: white;
  border-radius: 10px;
  padding: 2px 6px;
  font-size: 0.8em;
  display: inline-block;
  margin-bottom: 5px;
}

.month-visit-details {
  margin-top: 5px;
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
}

.month-visit-item {
  display: flex;
  flex-wrap: wrap;
  gap: 3px;
}

.month-view-btn {
  background: #23b7cd;
  color: white;
  border: none;
  border-radius: 3px;
  padding: 2px 5px;
  font-size: 0.7em;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.month-view-btn:hover {
  background: #1a8a9a;
}

.no-visits {
  color: #999;
  text-align: center;
  padding: 20px;
}

.loading, .error {
  text-align: center;
  padding: 40px;
  font-size: 18px;
  color: #666;
}

.error {
  color: #d9534f;
}

.visit-detail {
  margin-top: 5px;
  padding: 5px;
  border-radius: 4px;
  background-color: #f5f5f5;
}
