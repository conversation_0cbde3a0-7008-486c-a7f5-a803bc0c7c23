import Preloader from "@/components/common/Preloader";
import LightFooter from "@/shared/LightFooter";
import { useState } from "react";
import Elements from "./Elements";
import "./sdk.css";

const SDK = () => {
  const [activeCategory, setActiveCategory] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");

  const categories = [
    { id: "all", name: "All Components" },
    { id: "inputs", name: "Input Components" },
    { id: "selection", name: "Selection Components" },
    { id: "interactive", name: "Interactive Components" },
    { id: "file", name: "File & Signature" },
  ];

  return (
    <div className="main-content">
      <Preloader />

      <main className="content-wrapper-sdk">
        <div className="sdk-header">
          <div className="container">
            <h1 className="sdk-title">Nurtify Component Library</h1>
            <p className="sdk-description">
              Explore and interact with our UI components to build consistent and beautiful interfaces.
            </p>
            
            <div className="sdk-search-container">
              <input 
                type="text" 
                className="sdk-search" 
                placeholder="Search components..." 
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </div>
        
        <div className="sdk-content">
          <div className="container">
            <div className="sdk-navigation">
              {categories.map((category) => (
                <button
                  key={category.id}
                  className={`sdk-nav-item ${activeCategory === category.id ? 'active' : ''}`}
                  onClick={() => setActiveCategory(category.id)}
                >
                  {category.name}
                </button>
              ))}
            </div>
            
            <Elements 
              activeCategory={activeCategory} 
              searchQuery={searchQuery} 
            />
          </div>
        </div>
      </main>
      
      <LightFooter />
    </div>
  );
};

export default SDK;
