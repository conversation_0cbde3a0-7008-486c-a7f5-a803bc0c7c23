import { Home, Hospital, LayoutDashboard, TextSelect, Settings, Calendar, FileText } from "lucide-react";

type MenuItem = {
  icon: JSX.Element;
  label: string;
  path: string;
  onClick?: () => void;
};

export const menuItems: MenuItem[] = [
  {
    icon: <Home size={20} />,
    label: "Dashboard",
    path: "/analytics",
  },
  {
    icon: <Hospital size={20} />,
    label: "Hospitals",
    path: "/hospitals",
  },
  {
    icon: <LayoutDashboard size={20} fill="#4F547B" />,
    label: "Department",
    path: "/department",
  },
  {
    icon: <Calendar size={20} />,
    label: "Study Settings",
    path: "/dashboard/study-settings",
  },
  {
    icon: <FileText size={20} />,
    label: "Studies",
    path: "/dashboard/studies",
  },
  {
    icon: <TextSelect size={20} />,
    label: "Create Form",
    path: "/dashboard/delete",
    onClick: () => {
      if (window.confirm("Are you sure you want to delete this patient?")) {
        // Delete patient logic
      }
    },
  },
  {
    icon: <Settings size={20} />,
    label: "Settings",
    path: "/dashboard/snippets",
  },
];
