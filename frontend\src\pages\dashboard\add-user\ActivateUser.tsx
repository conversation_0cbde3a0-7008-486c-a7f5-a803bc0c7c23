import { useParams, useNavigate } from "react-router-dom";
import { useState } from "react";
import { useActivateAccountMutation } from "@/hooks/user.query";
import { Eye, EyeOff } from "lucide-react";
import Preloader from "@/components/common/Preloader";
import LightFooter from "@/shared/LightFooter";
import "./activate-user.css";

export default function ActivateUser() {
    const params = useParams<Record<string, string>>();
    const uid = params.uid ?? "";
    const token = params.token ?? "";
    const navigate = useNavigate();
    const { mutate, isPending } = useActivateAccountMutation();
    
    const [currentPassword, setCurrentPassword] = useState("");
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [error, setError] = useState<string>("");
    const [passwordStrength, setPasswordStrength] = useState<number>(0);
    const [showCurrentPassword, setShowCurrentPassword] = useState<boolean>(false);
    const [showNewPassword, setShowNewPassword] = useState<boolean>(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState<boolean>(false);

    // Password validation
    const validatePassword = (pass: string): boolean => {
        // Reset errors
        setError("");
        
        // Check password criteria
        const hasLetter = /[A-Za-z]/.test(pass);
        const hasNumber = /\d/.test(pass);
        const hasSpecialChar = /[@$!%*#?&]/.test(pass);
        const hasMinLength = pass.length >= 8;
        
        // Calculate strength (0-4)
        let strength = 0;
        if (hasLetter) strength++;
        if (hasNumber) strength++;
        if (hasSpecialChar) strength++;
        if (hasMinLength) strength++;
        
        setPasswordStrength(strength);
        
        // Return true if all criteria are met
        return hasLetter && hasNumber && hasSpecialChar && hasMinLength;
    };

    if (!uid || !token) {
        return (
            <div className="main-content">
                <Preloader />
                <main className="content-wrapper">
                    <div className="row justify-content-center pt-5" style={{height: "100vh"}}>
                        <div className="col-md-6 col-lg-5 col-xl-4">
                            <div className="activate-card bg-white mb-5 mt-5 border-0">
                                <div className="card-body p-5 text-center">
                                    <h3 className="mb-3">Invalid Activation Link</h3>
                                    <p className="text-muted mb-4">The activation link is invalid or has expired.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
                <LightFooter />
            </div>
        );
    }

    const handleSubmit = () => {
        if (!currentPassword || !newPassword || !confirmPassword) {
            setError("All fields are required. Please fill them out.");
            return;
        }

        if (newPassword !== confirmPassword) {
            setError("New passwords do not match. Please try again.");
            return;
        }
        
        if (!validatePassword(newPassword)) {
            setError("Password must be at least 8 characters with a letter, number, and special character");
            return;
        }

        setError("");

        mutate(
            {
                uid,
                token,
                currentPassword,
                newPassword,
                confirmPassword
            },
            {
                onSuccess: () => {
                    console.log("Account activated successfully");
                    navigate("/login");
                },
                 
                onError: (err: any) => {
                    const errorMessage = err.response?.data?.detail || err.response?.data?.message || "An error occurred. Please try again.";
                    setError(errorMessage);
                    console.error("Activation error:", err);
                    console.log("Response data:", err.response?.data);
                },
            }
        );
    };

    return (
        <div className="main-content">
            <Preloader />

            <main className="content-wrapper">
                <div className="row justify-content-center pt-5" style={{height: "100vh"}}>
                    <div className="col-md-6 col-lg-5 col-xl-4">
                        <div className="activate-card bg-white mb-5 mt-5 border-0">
                            <div className="card-body p-5 text-center">
                                <h3 className="mb-3">Set Your Password</h3>
                                <p className="text-muted mb-4">Create a secure password for your account</p>

                                <div className="password-container">
                                    <label>Current Password</label>
                                    <input
                                        type={showCurrentPassword ? "text" : "password"}
                                        value={currentPassword}
                                        onChange={(e) => setCurrentPassword(e.target.value)}
                                        className="password-input"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                                        className="password-toggle"
                                    >
                                        {showCurrentPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                                    </button>
                                </div>

                                <div className="password-container">
                                    <label>New Password</label>
                                    <input
                                        type={showNewPassword ? "text" : "password"}
                                        value={newPassword}
                                        onChange={(e) => {
                                            const newPass = e.target.value;
                                            setNewPassword(newPass);
                                            validatePassword(newPass);
                                        }}
                                        className="password-input"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowNewPassword(!showNewPassword)}
                                        className="password-toggle"
                                    >
                                        {showNewPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                                    </button>
                                </div>
                                
                                {/* Password strength indicator */}
                                {newPassword && (
                                    <div className="mb-3">
                                        <div className="strength-meter">
                                            {[1, 2, 3, 4].map((level) => (
                                                <div
                                                    key={level}
                                                    className="strength-segment"
                                                    style={{
                                                        backgroundColor: passwordStrength >= level 
                                                            ? level === 4 
                                                                ? "#10B981" // Strong (green)
                                                                : level === 3 
                                                                    ? "#22C55E" // Good (light green)
                                                                    : level === 2 
                                                                        ? "#F59E0B" // Fair (orange)
                                                                        : "#EF4444" // Weak (red)
                                                            : "#E2E8F0", // Empty (gray)
                                                    }}
                                                />
                                            ))}
                                        </div>
                                        <div className="strength-text" style={{ 
                                            color: passwordStrength === 4 
                                                ? "#10B981" 
                                                : passwordStrength >= 3 
                                                    ? "#22C55E" 
                                                    : passwordStrength >= 2 
                                                        ? "#F59E0B" 
                                                        : "#EF4444"
                                        }}>
                                            {passwordStrength === 0 && "Enter password"}
                                            {passwordStrength === 1 && "Weak password"}
                                            {passwordStrength === 2 && "Fair password"}
                                            {passwordStrength === 3 && "Good password"}
                                            {passwordStrength === 4 && "Strong password"}
                                        </div>
                                    </div>
                                )}
                                
                                <div className="password-requirements">
                                    Password must contain:
                                    <ul>
                                        <li style={{ color: newPassword.length >= 8 ? "#10B981" : "#64748b" }}>
                                            At least 8 characters
                                        </li>
                                        <li style={{ color: /[A-Za-z]/.test(newPassword) ? "#10B981" : "#64748b" }}>
                                            At least one letter
                                        </li>
                                        <li style={{ color: /\d/.test(newPassword) ? "#10B981" : "#64748b" }}>
                                            At least one number
                                        </li>
                                        <li style={{ color: /[@$!%*#?&]/.test(newPassword) ? "#10B981" : "#64748b" }}>
                                            At least one special character (@$!%*#?&)
                                        </li>
                                    </ul>
                                </div>

                                <div className="password-container">
                                    <label>Confirm New Password</label>
                                    <input
                                        type={showConfirmPassword ? "text" : "password"}
                                        value={confirmPassword}
                                        onChange={(e) => setConfirmPassword(e.target.value)}
                                        className="password-input"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                                        className="password-toggle"
                                    >
                                        {showConfirmPassword ? <EyeOff size={18} /> : <Eye size={18} />}
                                    </button>
                                </div>

                                {error && (
                                    <div className="error-container mb-3">
                                        <p className="error-message">{error}</p>
                                    </div>
                                )}
                                
                                <button 
                                    className="submit-button" 
                                    onClick={handleSubmit} 
                                    disabled={isPending}
                                >
                                    {isPending ? "Activating..." : "Activate Account"}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            <LightFooter />
        </div>
    );
}
