.kpi-dashboard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.kpi-dashboard:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.kpi-dashboard h2 {
  color: var(--color-dark-1);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid var(--color-light-2);
}

/* KPI Cards */
.kpi-dashboard .grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.kpi-dashboard .bg-blue-50,
.kpi-dashboard .bg-green-50,
.kpi-dashboard .bg-red-50 {
  padding: 1.5rem;
  border-radius: 10px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  overflow: hidden;
}

.kpi-dashboard .bg-blue-50 {
  background: linear-gradient(135deg, #EBF5FF 0%, #E1F0FF 100%);
  border: 1px solid #B3D7FF;
}

.kpi-dashboard .bg-green-50 {
  background: linear-gradient(135deg, #E6F7ED 0%, #DCF5E6 100%);
  border: 1px solid #A3E0B9;
}

.kpi-dashboard .bg-red-50 {
  background: linear-gradient(135deg, #FFEBEB 0%, #FFE1E1 100%);
  border: 1px solid #FFB3B3;
}

.kpi-dashboard .bg-blue-50:hover,
.kpi-dashboard .bg-green-50:hover,
.kpi-dashboard .bg-red-50:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.kpi-dashboard h3 {
  color: var(--color-dark-2);
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
}

.kpi-dashboard p.text-2xl {
  color: var(--color-dark-1);
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

/* Top Sites Section */
.kpi-dashboard .mt-6 {
  background: var(--color-light-1);
  padding: 1.5rem;
  border-radius: 10px;
  margin-top: 2rem;
}

.kpi-dashboard .mt-6 h3 {
  color: var(--color-dark-1);
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.kpi-dashboard ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.kpi-dashboard ul li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--color-light-2);
  color: var(--color-dark-2);
  font-size: 0.875rem;
}

.kpi-dashboard ul li:last-child {
  border-bottom: none;
}

.kpi-dashboard ul li strong {
  color: var(--color-dark-1);
  font-weight: 600;
  background: var(--color-light-2);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
}

/* Loading State */
.kpi-dashboard p:first-child {
  text-align: center;
  padding: 2rem;
  color: var(--color-dark-2);
  font-size: 0.875rem;
  background: var(--color-light-1);
  border-radius: 8px;
  margin: 1rem 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .kpi-dashboard .grid {
    grid-template-columns: 1fr;
  }

  .kpi-dashboard h2 {
    font-size: 1.25rem;
  }

  .kpi-dashboard p.text-2xl {
    font-size: 1.75rem;
  }

  .kpi-dashboard .mt-6 {
    padding: 1rem;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.kpi-dashboard .grid > div {
  animation: fadeIn 0.3s ease-out forwards;
}

.kpi-dashboard .grid > div:nth-child(2) {
  animation-delay: 0.1s;
}

.kpi-dashboard .grid > div:nth-child(3) {
  animation-delay: 0.2s;
}
