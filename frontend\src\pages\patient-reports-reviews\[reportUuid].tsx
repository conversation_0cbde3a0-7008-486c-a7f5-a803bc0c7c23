import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useReportQuery, useCreateReviewReportMutation, useReviewReportsByReportQuery, useDownloadReportAttachmentMutation } from '@/hooks/report.query';
import { useCurrentUserQuery } from '@/hooks/user.query';
import { useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { ChevronLeft, FileText, CheckCircle2, Plus, AlertCircle, Clock, User } from 'lucide-react';
import type { ReviewReportCreateData } from '@/services/api/types';
import PdfViewer from '@/components/PdfViewer';
import './ReportsReviews.css';

interface ReviewFormData {
  section_title: string;
  category: 'NORMAL' | 'ABNORMAL';
  abnormal_type?: 'CLINICALLY_SIGNIFICANT' | 'NOT_CLINICALLY_SIGNIFICANT';
  comment: string;
}

const ReportDetails: React.FC = () => {
  const { reportUuid } = useParams<{ reportUuid: string }>();
  const navigate = useNavigate();

  const { data: report, isLoading: reportLoading, refetch: refetchReport } = useReportQuery(reportUuid || '');
  const { data: reviews, isLoading: reviewsLoading, refetch: refetchReviews } = useReviewReportsByReportQuery(reportUuid || '');
  const { data: currentUser } = useCurrentUserQuery();
  const createReviewMutation = useCreateReviewReportMutation();
  const downloadMutation = useDownloadReportAttachmentMutation();
  const queryClient = useQueryClient();

  const [showAddReview, setShowAddReview] = useState(false);
  const [reviewForm, setReviewForm] = useState<ReviewFormData>({
    section_title: '',
    category: 'NORMAL',
    abnormal_type: undefined,
    comment: ''
  });
  const [formError, setFormError] = useState<string | null>(null);

  const handleAddReview = () => {
    setShowAddReview(true);
    setFormError(null);
  };

  const handleCancelReview = () => {
    setShowAddReview(false);
    setReviewForm({
      section_title: '',
      category: 'NORMAL',
      abnormal_type: undefined,
      comment: ''
    });
    setFormError(null);
  };

  const handleSubmitReview = async () => {
    if (!reportUuid || !reviewForm.section_title.trim() || !reviewForm.comment.trim()) {
      setFormError('Please fill in all required fields (Section Title and Comment).');
      return;
    }

    if (reviewForm.category === 'ABNORMAL' && !reviewForm.abnormal_type) {
      setFormError('Please select abnormal type when category is Abnormal.');
      return;
    }

    if (!currentUser) {
      setFormError('User information not available. Please refresh the page and try again.');
      return;
    }

    // Use UUID if available, otherwise extract UUID from identifier
    let reviewerId = currentUser.uuid;

    if (!reviewerId && currentUser.identifier) {
      // Extract UUID from identifier if it contains the pattern "orgusr$_$uuid"
      const identifierParts = currentUser.identifier.split('$_$');
      if (identifierParts.length === 2) {
        reviewerId = identifierParts[1]; // Get the UUID part
      } else {
        reviewerId = currentUser.identifier; // Use as is if no pattern found
      }
    }

    if (!reviewerId) {
      setFormError('User ID not available. Please refresh the page and try again.');
      return;
    }

    const reviewData: ReviewReportCreateData = {
      report: reportUuid,
      section_title: reviewForm.section_title,
      category: reviewForm.category,
      comment: reviewForm.comment,
      ...(reviewForm.category === 'ABNORMAL' && reviewForm.abnormal_type && {
        abnormal_type: reviewForm.abnormal_type
      })
    };

    try {
      await createReviewMutation.mutateAsync(reviewData);
      // Invalidate and refetch reviews data for automatic refresh
      queryClient.invalidateQueries({
        queryKey: ['reviewreport/getByReport', reportUuid]
      });
      // Invalidate and refetch report data to update status
      queryClient.invalidateQueries({
        queryKey: ['report/getByUuid', reportUuid]
      });
      // Also trigger immediate refetch for instant update
      refetchReviews();
      refetchReport();
      handleCancelReview();
    } catch (error: any) {
      setFormError(error?.message || 'Failed to add review. Please try again.');
    }
  };

  const handleInputChange = (field: keyof ReviewFormData, value: any) => {
    setReviewForm(prev => ({
      ...prev,
      [field]: value,
      // Reset abnormalType when category changes to Normal
      ...(field === 'category' && value === 'NORMAL' && { abnormal_type: undefined })
    }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCategoryBadge = (category: string) => {
    const isNormal = category === 'NORMAL';
    return (
      <span className={`category-badge ${isNormal ? 'normal' : 'abnormal'}`}>
        {isNormal ? <CheckCircle2 size={14} /> : <AlertCircle size={14} />}
        {category}
      </span>
    );
  };

  const getAbnormalTypeBadge = (abnormalType?: string) => {
    if (!abnormalType) return null;
    const isClinical = abnormalType === 'CLINICALLY_SIGNIFICANT';
    return (
      <span className={`abnormal-type-badge ${isClinical ? 'clinical' : 'non-clinical'}`}>
        {isClinical ? 'Clinically Significant' : 'Not Clinically Significant'}
      </span>
    );
  };

  const handleDownloadReport = async () => {
    if (!reportUuid) return;

    try {
      const blob = await downloadMutation.mutateAsync(reportUuid);

      // Create a download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      // Generate filename based on report data
      const filename = `report_${report?.type || 'document'}_${formatDate(report?.exam_date || new Date().toISOString()).replace(/[/,\s:]/g, '_')}.pdf`;
      link.download = filename;

      // Trigger download
      document.body.appendChild(link);
      link.click();

      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
      // You could add a toast notification here
    }
  };

  if (reportLoading) {
    return <div className="reports-reviews-loading">Loading report...</div>;
  }

  if (!report) {
    return <div className="reports-reviews-error">Report not found.</div>;
  }

  return (
    <motion.div
      className="reports-reviews-container"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="report-header">
        <button className="back-button" onClick={() => navigate(-1)}>
          <ChevronLeft size={20} /> Back to Reports
        </button>
        <h1 className="page-title">Report Review</h1>
      </div>

      {/* Report Information Card */}
      <div className="report-info-card">
        <div className="card-header">
          <h2 className="card-title">
            <FileText size={24} /> Report Details
          </h2>
          {/*<span className={`status-badge ${report.status === 'REVIEWED' ? 'reviewed' : 'not-reviewed'}`}>
            {report.status === 'REVIEWED' ? <CheckCircle2 size={16} /> : <Clock size={16} />}
            {report.status === 'REVIEWED' ? 'Reviewed' : 'Not Reviewed'}
          </span> */}
        </div>

        <div className="report-meta-grid">
          <div className="meta-item">
            <label>Type:</label>
            <span>{report.type}</span>
          </div>
          <div className="meta-item">
            <label>Content Type:</label>
            <span className={`content-type-badge ${report.content_type.toLowerCase()}`}>
              {report.content_type}
            </span>
          </div>
          <div className="meta-item">
            <label>Exam Date:</label>
            <span>{formatDate(report.exam_date)}</span>
          </div>
          <div className="meta-item">
            <label>Uploaded At:</label>
            <span>{formatDate(report.uploaded_at)}</span>
          </div>
          <div className="meta-item">
            <label>Sub Investigator:</label>
            <span>
              {report.sub_investigator_name && report.sub_investigator_last_name
                ? `${report.sub_investigator_name} ${report.sub_investigator_last_name}`
                : report.sub_investigator?.first_name && report.sub_investigator?.last_name
                ? `${report.sub_investigator.first_name} ${report.sub_investigator.last_name}`
                : report.sub_investigator_name
                ? report.sub_investigator_name
                : 'Not assigned'
              }
            </span>
          </div>
        </div>

        {report.text_content && (
          <div className="report-content">
            <label>Content:</label>
            <div className="content-text">{report.text_content}</div>
          </div>
        )}

        {report.content_type === 'PDF' && report.attachment_url && (
          <div className="report-pdf-viewer">
            <label>PDF Document:</label>
            <div className="pdf-viewer-container">
              <PdfViewer fileUrl={`${import.meta.env.VITE_API_BASE_URL?.replace('/api', '') || 'http://localhost:8000'}/${report.attachment_url}`} />
            </div>
          </div>
        )}

        {report.attachment_url && (
          <div className="report-attachment">
            <label>Download:</label>
            <button
              onClick={handleDownloadReport}
              disabled={downloadMutation.isPending}
              className="attachment-link download-btn"
            >
              {downloadMutation.isPending ? (
                <>
                  <Clock size={16} className="spinning" />
                  Downloading...
                </>
              ) : (
                <>
                  <FileText size={16} />
                  Download Report
                </>
              )}
            </button>
          </div>
        )}
      </div>

      {/* Reviews Section */}
      <div className="reviews-section">
        <div className="section-header">
          <h3 className="section-title">Reviews</h3>
          <button
            className="add-review-btn"
            onClick={handleAddReview}
            disabled={showAddReview}
          >
            <Plus size={18} />
            Add Review
          </button>
        </div>

        {/* Add Review Form */}
        {showAddReview && (
          <motion.div
            className="review-form-card"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="form-header">
              <h4>Add New Review</h4>
            </div>

            <div className="form-content">
              <div className="form-group">
                <label htmlFor="sectionTitle" className="form-label">
                  Section Title / Focus Point <span className="required">*</span>
                </label>
                <input
                  id="sectionTitle"
                  type="text"
                  value={reviewForm.section_title}
                  onChange={(e) => handleInputChange('section_title', e.target.value)}
                  placeholder="e.g., White Blood Cell Count, Liver Function, etc."
                  className="form-input"
                />
              </div>

              <div className="form-group">
                <label className="form-label">
                  Category <span className="required">*</span>
                </label>
                <div className="radio-group">
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="category"
                      value="NORMAL"
                      checked={reviewForm.category === 'NORMAL'}
                      onChange={(e) => handleInputChange('category', e.target.value)}
                    />
                    <span className="radio-label">Normal</span>
                  </label>
                  <label className="radio-option">
                    <input
                      type="radio"
                      name="category"
                      value="ABNORMAL"
                      checked={reviewForm.category === 'ABNORMAL'}
                      onChange={(e) => handleInputChange('category', e.target.value)}
                    />
                    <span className="radio-label">Abnormal</span>
                  </label>
                </div>
              </div>

              {reviewForm.category === 'ABNORMAL' && (
                <motion.div
                  className="form-group"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  transition={{ duration: 0.3 }}
                >
                  <label className="form-label">
                    Abnormal Type <span className="required">*</span>
                  </label>
                  <div className="radio-group">
                    <label className="radio-option">
                      <input
                        type="radio"
                        name="abnormalType"
                        value="CLINICALLY_SIGNIFICANT"
                        checked={reviewForm.abnormal_type === 'CLINICALLY_SIGNIFICANT'}
                        onChange={(e) => handleInputChange('abnormal_type', e.target.value)}
                      />
                      <span className="radio-label">Clinically Significant</span>
                    </label>
                    <label className="radio-option">
                      <input
                        type="radio"
                        name="abnormalType"
                        value="NOT_CLINICALLY_SIGNIFICANT"
                        checked={reviewForm.abnormal_type === 'NOT_CLINICALLY_SIGNIFICANT'}
                        onChange={(e) => handleInputChange('abnormal_type', e.target.value)}
                      />
                      <span className="radio-label">Not Clinically Significant</span>
                    </label>
                  </div>
                </motion.div>
              )}

              <div className="form-group">
                <label htmlFor="comment" className="form-label">
                  Comment <span className="required">*</span>
                </label>
                <textarea
                  id="comment"
                  value={reviewForm.comment}
                  onChange={(e) => handleInputChange('comment', e.target.value)}
                  placeholder="Enter your review comments and observations..."
                  className="form-textarea"
                  rows={4}
                />
              </div>

              {formError && (
                <div className="form-error">
                  <AlertCircle size={16} />
                  {formError}
                </div>
              )}
            </div>

            <div className="form-actions">
              <button
                className="cancel-btn"
                onClick={handleCancelReview}
                disabled={createReviewMutation.isPending}
              >
                Cancel
              </button>
              <button
                className="submit-btn"
                onClick={handleSubmitReview}
                disabled={createReviewMutation.isPending}
              >
                {createReviewMutation.isPending ? (
                  <>
                    <Clock size={16} className="spinning" />
                    Adding...
                  </>
                ) : (
                  <>
                    <CheckCircle2 size={16} />
                    Add Review
                  </>
                )}
              </button>
            </div>
          </motion.div>
        )}

        {/* Reviews List */}
        <div className="reviews-list">
          {reviewsLoading ? (
            <div className="reviews-loading">
              <Clock size={24} className="spinning" />
              Loading reviews...
            </div>
          ) : !reviews || reviews.length === 0 ? (
            <div className="reviews-empty">
              <FileText size={48} />
              <p>No reviews yet for this report.</p>
              <p className="empty-subtitle">Click "Add Review" to create the first review.</p>
            </div>
          ) : (
            <div className="reviews-grid">
              {reviews.map((review) => (
                <motion.div
                  key={review.uuid}
                  className="review-card"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <div className="review-header">
                    <div className="review-title">
                      <h4>{review.section_title}</h4>
                      <div className="review-badges">
                        {getCategoryBadge(review.category)}
                        {getAbnormalTypeBadge(review.abnormal_type)}
                      </div>
                    </div>
                  </div>

                  <div className="review-content">
                    <p>{review.comment}</p>
                  </div>

                  <div className="review-footer">
                    <div className="reviewer-info">
                      <User size={14} />
                      <span>
                        {review.reviewed_by.first_name} {review.reviewed_by.last_name}
                      </span>
                    </div>
                    <div className="review-date">
                      <Clock size={14} />
                      <span>{formatDate(review.reviewed_at)}</span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

export default ReportDetails;
