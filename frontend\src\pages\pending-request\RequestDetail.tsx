import React, { useState, useEffect } from 'react';
import { Arrow<PERSON>eft, UserPlus, CheckCircle, XCircle, Clock, Info } from 'lucide-react';
import NurtifyTextArea from '@/components/NurtifyTextArea';
import NurtifyComboBox from '@/components/NurtifyComboBox';
import { 
  updateDoctorReferralStatus, 
  updatePatientApplicationStatus,
  getDoctorReferralStatusLogs,
  getPatientApplicationStatusLogs,
  StatusLog
} from '@/services/api/contact.service';
import "./styles.css";

interface Option {
  value: string;
  label: string;
}

interface FormData {
  // Common fields
  name?: string;
  email?: string;
  phone?: string;
  age?: string;
  date_of_birth?: string;  // For patient applications
  patient_dob?: string;    // For doctor referrals
  
  // Patient specific fields
  postcode?: string;
  hospital?: string;
  location?: string;
  departments?: string[];
  availableTrials?: string;
  medicalHistory?: string;
  medications?: string;
  familyHistory?: string;
  gender?: string;
  ethnicity?: string;
  smokingStatus?: string;
  alcoholConsumption?: string;
  physicalActivityLevel?: string;
  pregnancyStatus?: boolean;
  willingToTravel?: boolean;
  dataSharingConsent?: boolean;
  
  // Doctor specific fields
  doctorName?: string;
  nhsEmail?: string;
  practice?: string;
  patientInitials?: string;
  condition?: string;
  nhsNumber?: string;
  notes?: string;
}

interface Request {
  id: string;
  uuid: string;
  name: string;
  age: string;
  nhsId: string;
  source: string;
  study: string;
  trials: string;
  date: string;
  status: "Pending" | "Approved" | "Rejected" | "In Progress";
  department: string;
  formData: FormData;
}

interface CurrentUser {
  first_name?: string;
  last_name?: string;
  is_superuser?: boolean;
  is_admin?: boolean;
  department?: {
    name: string;
  };
}

interface RequestDetailProps {
  request: Request;
  onBack: () => void;
  onUpdateStatus: (id: string, newStatus: "Pending" | "Approved" | "Rejected" | "In Progress", comment: string) => void;
  onAssignUsers: (id: string, userIds: string[]) => void;
  currentUser: CurrentUser;
}

const RequestDetail: React.FC<RequestDetailProps> = ({ 
  request, 
  onBack, 
  onUpdateStatus,
  onAssignUsers,
  currentUser 
}) => {
  const [newStatus, setNewStatus] = useState<"Pending" | "Approved" | "Rejected" | "In Progress">(request.status);
  const [statusComment, setStatusComment] = useState('');
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
  const [assignmentType, setAssignmentType] = useState<'users' | 'department'>('users');
  const [statusLogs, setStatusLogs] = useState<StatusLog[]>([]);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch status logs when component mounts
  useEffect(() => {
    const fetchStatusLogs = async () => {
      try {
        const response = request.source === 'Doctor' 
          ? await getDoctorReferralStatusLogs(request.uuid!)
          : await getPatientApplicationStatusLogs(request.uuid!);
        setStatusLogs(response.results);
      } catch (error) {
        console.error('Failed to fetch status logs:', error);
      }
    };

    fetchStatusLogs();
  }, [request.uuid, request.source]);

  // Mock users for assignment
  const availableUsers: Option[] = [
    { value: '1', label: 'Dr. Sarah Johnson (<EMAIL>)' },
    { value: '2', label: 'Dr. Michael Brown (<EMAIL>)' },
    { value: '3', label: 'Dr. Emily Davis (<EMAIL>)' },
    { value: '4', label: 'Dr. Robert Wilson (<EMAIL>)' },
    { value: '5', label: 'Dr. James Smith (<EMAIL>)' },
    { value: '6', label: 'Dr. Jennifer Lee (<EMAIL>)' },
    { value: '7', label: 'Dr. William Chen (<EMAIL>)' },
    { value: '8', label: 'Dr. Maria Rodriguez (<EMAIL>)' }
  ];

  // Mock departments for assignment
  const availableDepartments: Option[] = [
    { value: 'cardiology', label: 'Cardiology Department' },
    { value: 'neurology', label: 'Neurology Department' },
    { value: 'oncology', label: 'Oncology Department' },
    { value: 'orthopedics', label: 'Orthopedics Department' },
    { value: 'pediatrics', label: 'Pediatrics Department' },
    { value: 'radiology', label: 'Radiology Department' }
  ];

  const handleStatusChange = (status: "Pending" | "Approved" | "Rejected" | "In Progress") => {
    setNewStatus(status);
  };

  const handleSubmitStatusChange = async () => {
    if (statusComment.trim() === '') {
      setError('Please provide a comment for the status change');
      return;
    }

    setIsUpdating(true);
    setError(null);
    try {
      // Map UI status to API status format
      const statusMap: Record<string, "Pending" | "In_Progress" | "Approved" | "Rejected"> = {
        "Pending": "Pending",
        "In Progress": "In_Progress",
        "Approved": "Approved",
        "Rejected": "Rejected"
      };
      
      const apiStatus = statusMap[newStatus];
      
      // Call appropriate API based on request source
      if (request.source === 'Doctor') {
        await updateDoctorReferralStatus(request.uuid!, {
          status: apiStatus,
          notes: statusComment
        });
      } else {
        await updatePatientApplicationStatus(request.uuid!, {
          status: apiStatus,
          notes: statusComment
        });
      }

      // Update local state
      await onUpdateStatus(request.id, newStatus, statusComment);

      // Refresh status logs
      const response = request.source === 'Doctor'
        ? await getDoctorReferralStatusLogs(request.uuid!)
        : await getPatientApplicationStatusLogs(request.uuid!);
      setStatusLogs(response.results);

      // Reset the comment field
      setStatusComment('');
    } catch (error) {
      console.error('Failed to update status:', error);
      setError('Failed to update status. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleAssignUsers = () => {
    if (assignmentType === 'users') {
      onAssignUsers(request.id, selectedUsers);
    } else {
      // In a real app, you would handle department assignment differently
      console.log(`Request ${request.id} assigned to departments: ${selectedDepartments.join(', ')}`);
    }
    setShowAssignModal(false);
  };

  const handleUserSelectionChange = (values: string[]) => {
    setSelectedUsers(values);
  };

  const handleDepartmentSelectionChange = (values: string[]) => {
    setSelectedDepartments(values);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Approved':
        return <CheckCircle size={16} className="text-success" />;
      case 'Rejected':
        return <XCircle size={16} className="text-danger" />;
      case 'In_Progress':
        return <Clock size={16} className="text-primary" />;
      default:
        return <Info size={16} className="text-warning" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderFormDetailsPatient = () => {
    const formData = request.formData;
    return (
      <div className="form-details patient-form">
        <div className="row mb-4">
          <div className="col-md-6">
            <h5 className="section-title">Hospital & Trial Selection</h5>
            <div className="detail-item">
              <span className="detail-label">Preferred Hospital: </span>
              <span className="detail-value">{formData.hospital || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Trial of Interest: </span>
              <span className="detail-value">{request.study || 'N/A'}</span>
            </div>
          </div>

          <div className="col-md-6">
            <h5 className="section-title">Personal Information</h5>
            <div className="detail-item">
              <span className="detail-label">First Name: </span>
              <span className="detail-value">{formData.name?.split(' ')[0] || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Last Name: </span>
              <span className="detail-value">{formData.name?.split(' ')[1] || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Date of Birth: </span>
              <span className="detail-value">{request.source === 'Patient' ? 
                new Date(request.formData.date_of_birth || '').toLocaleDateString() : 
                new Date(request.formData.patient_dob || '').toLocaleDateString()}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Age: </span>
              <span className="detail-value">{formData.age || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Gender: </span>
              <span className="detail-value">{formData.gender || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Ethnicity: </span>
              <span className="detail-value">{formData.ethnicity || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">NHS Number: </span>
              <span className="detail-value">{formData.nhsNumber || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Postcode: </span>
              <span className="detail-value">{formData.postcode || 'N/A'}</span>
            </div>
          </div>
        </div>

        <div className="row mb-4">
          <div className="col-md-6">
            <h5 className="section-title">Contact Details</h5>
            <div className="detail-item">
              <span className="detail-label">Email: </span>
              <span className="detail-value">{formData.email || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Phone Number: </span>
              <span className="detail-value">{formData.phone || 'N/A'}</span>
            </div>
          </div>

          <div className="col-md-6">
            <h5 className="section-title">Medical History</h5>
            <div className="detail-item">
              <span className="detail-label">Past Medical History: </span>
              <div className="detail-text-area">{formData.medicalHistory || 'N/A'}</div>
            </div>
            <div className="detail-item">
              <span className="detail-label">Current Medications: </span>
              <div className="detail-text-area">{formData.medications || 'N/A'}</div>
            </div>
            <div className="detail-item">
              <span className="detail-label">Allergies: </span>
              <div className="detail-text-area">{formData.familyHistory || 'N/A'}</div>
            </div>
            <div className="detail-item">
              <span className="detail-label">Smoking Status: </span>
              <span className="detail-value">{formData.smokingStatus || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Alcohol Consumption: </span>
              <span className="detail-value">{formData.alcoholConsumption || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Physical Activity Level: </span>
              <span className="detail-value">{formData.physicalActivityLevel || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Pregnancy Status: </span>
              <span className="detail-value">{formData.pregnancyStatus ? 'Yes' : 'No'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Willing to Travel: </span>
              <span className="detail-value">{formData.willingToTravel ? 'Yes' : 'No'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Data Sharing Consent: </span>
              <span className="detail-value">{formData.dataSharingConsent ? 'Yes' : 'No'}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderFormDetailsDoctor = () => {
    const formData = request.formData;
    return (
      <div className="form-details doctor-form">
        <div className="row mb-4">
          <div className="col-md-6">
            <h5 className="section-title">Doctor/HCP Information</h5>
            <div className="detail-item">
              <span className="detail-label">Full Name: </span>
              <span className="detail-value">{formData.doctorName || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">NHS Email Address: </span>
              <span className="detail-value">{formData.nhsEmail || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Practice/Organization Name: </span>
              <span className="detail-value">{formData.practice || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Contact Phone Number: </span>
              <span className="detail-value">{formData.phone || 'N/A'}</span>
            </div>
          </div>

          <div className="col-md-6">
            <h5 className="section-title">Patient Information</h5>
            <div className="detail-item">
              <span className="detail-label">Patient Name: </span>
              <span className="detail-value">{formData.patientInitials || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Date of Birth: </span>
              <span className="detail-value">{new Date(request.formData.patient_dob || '').toLocaleDateString()}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Age: </span>
              <span className="detail-value">{formData.age || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">NHS Number: </span>
              <span className="detail-value">{formData.nhsNumber || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Email: </span>
              <span className="detail-value">{formData.email || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Phone: </span>
              <span className="detail-value">{formData.phone || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Previous Medical History: </span>
              <div className="detail-text-area">{formData.medicalHistory || 'N/A'}</div>
            </div>
            <div className="detail-item">
              <span className="detail-label">Current Medications: </span>
              <div className="detail-text-area">{formData.medications || 'N/A'}</div>
            </div>
          </div>
        </div>

        <div className="row mb-4">
          <div className="col-12">
            <h5 className="section-title">Trial-Specific Details</h5>
            <div className="detail-item">
              <span className="detail-label">Trial of Interest: </span>
              <span className="detail-value">{formData.condition || 'N/A'}</span>
            </div>
            <div className="detail-item">
              <span className="detail-label">Reason for Referral: </span>
              <div className="detail-text-area">{formData.notes || 'N/A'}</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="request-detail">
      <div className="detail-header d-flex justify-content-between align-items-center mb-4">
        <button 
          className="back-button d-flex align-items-center"
          onClick={onBack}
          style={{
            backgroundColor: 'transparent',
            border: 'none',
            cursor: 'pointer',
            padding: '5px 10px',
            display: 'flex',
            alignItems: 'center',
            color: '#6B7280'
          }}
        >
          <ArrowLeft size={16} style={{ marginRight: '5px' }} />
          <span>Back to list</span>
        </button>
        
        {(currentUser?.is_superuser || currentUser?.is_admin) && (
          <button 
            className="assign-button"
            onClick={() => setShowAssignModal(true)}
            style={{
              display: 'flex',
              alignItems: 'center',
              padding: '8px 16px',
              backgroundColor: '#37B7C3',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              transition: 'background-color 0.2s ease'
            }}
          >
            <UserPlus size={16} style={{ marginRight: '5px' }} />
            <span>Assign Users</span>
          </button>
        )}
      </div>

      <div className="request-info mb-4 p-4 rounded-16 bg-light-2">
        <div className="row">
          <div className="col-md-3">
            <div className="info-item">
              <span className="info-label">Name: </span>
              <span className="info-value fw-medium">{request.name}</span>
            </div>
          </div>
          <div className="col-md-3">
            <div className="info-item">
              <span className="info-label">NHS ID: </span>
              <span className="info-value fw-medium">{request.nhsId}</span>
            </div>
          </div>
          <div className="col-md-3">
            <div className="info-item">
              <span className="info-label">Source: </span>
              <span className="info-value fw-medium">{request.source}</span>
            </div>
          </div>
          <div className="col-md-3">
            <div className="info-item">
              <span className="info-label">Date Submitted: </span>
              <span className="info-value fw-medium">{request.date}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="row g-4">
        <div className="col-md-8">
          <div className="form-content p-4 rounded-16 bg-white shadow-sm mb-4">
            <h4 className="section-title mb-4">Request Details</h4>
            {request.source === 'Patient' ? renderFormDetailsPatient() : renderFormDetailsDoctor()}
          </div>
        </div>

        <div className="col-md-4">
          <div className="status-section p-4 rounded-16 bg-white shadow-sm mb-4">
            <h4 className="section-title mb-4">Status Management</h4>
            
            {error && (
              <div className="alert alert-danger mb-3" role="alert">
                {error}
              </div>
            )}
            
            <div className="current-status mb-3">
              <span className="status-label">Current Status: </span>
              <span 
                className={`status-badge ${request.status.toLowerCase()}`}
                style={{
                  padding: '5px 10px',
                  borderRadius: '15px',
                  backgroundColor: request.status === 'Pending' ? '#FEF3C7' :
                                   request.status === 'Approved' ? '#D1FAE5' :
                                   request.status === 'Rejected' ? '#FEE2E2' : '#DBEAFE',
                  color: request.status === 'Pending' ? '#D97706' :
                         request.status === 'Approved' ? '#059669' :
                         request.status === 'Rejected' ? '#DC2626' : '#2563EB',
                  fontWeight: '500',
                  fontSize: '0.875rem'
                }}
              >
                {request.status}
              </span>
            </div>
            
            <div className="status-options mb-3">
              <div className="d-flex flex-wrap gap-2">
                <button 
                  className={`status-option-btn ${newStatus === 'Pending' ? 'active' : ''}`}
                  onClick={() => handleStatusChange('Pending')}
                  style={{
                    padding: '8px 12px',
                    borderRadius: '4px',
                    border: '1px solid #E5E7EB',
                    backgroundColor: newStatus === 'Pending' ? '#FEF3C7' : '#FFFFFF',
                    color: newStatus === 'Pending' ? '#D97706' : '#6B7280',
                    cursor: 'pointer',
                    fontWeight: newStatus === 'Pending' ? '500' : 'normal'
                  }}
                >
                  Pending
                </button>
                <button 
                  className={`status-option-btn ${newStatus === 'In Progress' ? 'active' : ''}`}
                  onClick={() => handleStatusChange('In Progress')}
                  style={{
                    padding: '8px 12px',
                    borderRadius: '4px',
                    border: '1px solid #E5E7EB',
                    backgroundColor: newStatus === 'In Progress' ? '#DBEAFE' : '#FFFFFF',
                    color: newStatus === 'In Progress' ? '#2563EB' : '#6B7280',
                    cursor: 'pointer',
                    fontWeight: newStatus === 'In Progress' ? '500' : 'normal'
                  }}
                >
                  In Progress
                </button>
                <button 
                  className={`status-option-btn ${newStatus === 'Approved' ? 'active' : ''}`}
                  onClick={() => handleStatusChange('Approved')}
                  style={{
                    padding: '8px 12px',
                    borderRadius: '4px',
                    border: '1px solid #E5E7EB',
                    backgroundColor: newStatus === 'Approved' ? '#D1FAE5' : '#FFFFFF',
                    color: newStatus === 'Approved' ? '#059669' : '#6B7280',
                    cursor: 'pointer',
                    fontWeight: newStatus === 'Approved' ? '500' : 'normal'
                  }}
                >
                  Approved
                </button>
                <button 
                  className={`status-option-btn ${newStatus === 'Rejected' ? 'active' : ''}`}
                  onClick={() => handleStatusChange('Rejected')}
                  style={{
                    padding: '8px 12px',
                    borderRadius: '4px',
                    border: '1px solid #E5E7EB',
                    backgroundColor: newStatus === 'Rejected' ? '#FEE2E2' : '#FFFFFF',
                    color: newStatus === 'Rejected' ? '#DC2626' : '#6B7280',
                    cursor: 'pointer',
                    fontWeight: newStatus === 'Rejected' ? '500' : 'normal'
                  }}
                >
                  Rejected
                </button>
              </div>
            </div>
            
            <div className="status-comment mb-3">
              <label className="form-label fw-medium">Comment</label>
              <NurtifyTextArea
                value={statusComment}
                onChange={(e) => setStatusComment(e.target.value)}
                placeholder="Enter comment for status change..."
                name="statusComment"
                rows={3}
                disabled={isUpdating}
              />
            </div>
            
            <button 
              className="btn-update-status w-100"
              onClick={handleSubmitStatusChange}
              disabled={isUpdating}
              style={{
                padding: '10px 16px',
                backgroundColor: isUpdating ? '#9CA3AF' : '#37B7C3',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: isUpdating ? 'not-allowed' : 'pointer',
                transition: 'background-color 0.2s ease',
                fontWeight: '500'
              }}
            >
              {isUpdating ? 'Updating...' : 'Update Status'}
            </button>
          </div>
          
          <div className="status-log p-4 rounded-16 bg-white shadow-sm">
            <h4 className="section-title mb-4">Status Log</h4>
            
            <div className="log-timeline">
              {statusLogs.map((log) => (
                <div className="log-item d-flex mb-3" key={log.uuid}>
                  <div className="log-icon me-2 mt-1">
                    {getStatusIcon(log.new_status)}
                  </div>
                  <div className="log-content">
                    <div className="log-header d-flex justify-content-between">
                      <span className="log-status fw-medium">
                        {log.old_status} → {log.new_status}
                      </span>
                      <span className="log-timestamp text-muted small">
                        {formatDate(log.changed_at)}
                      </span>
                    </div>
                    <div className="log-comment mt-1">
                      {log.notes}
                    </div>
                    <div className="log-user text-muted small mt-1">
                      By: {log.changed_by.first_name} {log.changed_by.last_name}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Assign Users Modal */}
      {showAssignModal && (
        <div className="modal-overlay" 
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 100,
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        >
          <div className="modal-content"
            style={{
              backgroundColor: 'white',
              borderRadius: '8px',
              padding: '24px',
              width: '500px',
              maxWidth: '90%'
            }}
          >
            <h4 className="modal-title mb-4">Assign Users to Request</h4>
            
            <div className="assignment-type-selector mb-4">
              <div className="d-flex">
                <button 
                  className={`tab-button ${assignmentType === 'users' ? 'active-tab' : ''}`}
                  onClick={() => setAssignmentType('users')}
                  style={{
                    flex: 1,
                    padding: '10px',
                    cursor: 'pointer',
                    backgroundColor: assignmentType === 'users' ? '#EBF4F6' : '#f9fafb',
                    color: assignmentType === 'users' ? '#37B7C3' : '#6B7280',
                    border: '1px solid #E5E7EB',
                    borderBottom: assignmentType === 'users' ? '2px solid #37B7C3' : '1px solid #E5E7EB',
                    borderRadius: '4px 4px 0 0',
                    fontWeight: assignmentType === 'users' ? 500 : 400
                  }}
                >
                  Assign to Users
                </button>
                <button 
                  className={`tab-button ${assignmentType === 'department' ? 'active-tab' : ''}`}
                  onClick={() => setAssignmentType('department')}
                  style={{
                    flex: 1,
                    padding: '10px',
                    cursor: 'pointer',
                    backgroundColor: assignmentType === 'department' ? '#EBF4F6' : '#f9fafb',
                    color: assignmentType === 'department' ? '#37B7C3' : '#6B7280',
                    border: '1px solid #E5E7EB',
                    borderBottom: assignmentType === 'department' ? '2px solid #37B7C3' : '1px solid #E5E7EB',
                    borderRadius: '4px 4px 0 0',
                    fontWeight: assignmentType === 'department' ? 500 : 400
                  }}
                >
                  Assign to Department
                </button>
              </div>
            </div>
            
            <div className="combobox-container mb-4">
              {assignmentType === 'users' ? (
                <>
                  <label className="form-label mb-2">Search and select users by email:</label>
                  <NurtifyComboBox
                    options={availableUsers}
                    selectedValues={selectedUsers}
                    onChange={handleUserSelectionChange}
                  />
                </>
              ) : (
                <>
                  <label className="form-label mb-2">Search and select departments:</label>
                  <NurtifyComboBox
                    options={availableDepartments}
                    selectedValues={selectedDepartments}
                    onChange={handleDepartmentSelectionChange}
                  />
                </>
              )}
            </div>
            
            <div className="modal-actions d-flex justify-content-end">
              <button 
                className="cancel-btn me-2"
                onClick={() => setShowAssignModal(false)}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#F3F4F6',
                  color: '#374151',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>
              <button 
                className="assign-btn"
                onClick={handleAssignUsers}
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#37B7C3',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Assign {assignmentType === 'users' ? 'Users' : 'to Department'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RequestDetail;
