import React from "react";
import { usePatientAccessLogsQuery } from "@/hooks/patient.query";
import DataTable from "@/components/common/DataTable";
import { format } from "date-fns";
import type { PatientAccessLog } from "@/services/api/types";

const PatientLogs: React.FC = () => {
  const { data: logs, isLoading, error } = usePatientAccessLogsQuery();

  if (isLoading) {
    return <div className="loading">Loading patient logs...</div>;
  }

  if (error) {
    return <div className="error">Error loading patient logs: {(error as Error).message}</div>;
  }

  if (!logs || logs.length === 0) {
    return <div className="no-logs">No patient access logs available.</div>;
  }

  return (
    <div className="content-wrapper js-content-wrapper">
      <div className="bg-light-4 px-3 py-5">
        <div className="container-fluid py-6 px-6">
          <div className="patient-details-container">
            <div className="patient-details-header">
              <h1 className="page-title">Patient Access Logs</h1>
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '20px',
            }}>
              <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}>
                View all patient access logs and track who accessed patient information.
              </h3>
            </div>

            <DataTable<PatientAccessLog>
              data={logs}
              columns={[
                {
                  key: 'patient_name',
                  header: "Patient Name",
                  sortable: true,
                },
                {
                  key: 'accessed_by_name',
                  header: 'Accessed By',
                  sortable: true,
                },
                {
                  key: 'access_type',
                  header: 'Access Type',
                  sortable: true,
                  render: (value) => {
                    if (typeof value === 'string') {
                      return value.charAt(0).toUpperCase() + value.slice(1);
                    }
                    return '';
                  }
                },
                {
                  key: 'created_at',
                  header: 'Date & Time',
                  sortable: true,
                  render: (value) => {
                    if (typeof value === 'string') {
                      return format(new Date(value), 'PPpp');
                    }
                    return '';
                  }
                },
                {
                  key: 'ip_address',
                  header: 'IP Address',
                  sortable: true,
                }
              ]}
              noDataMessage="No access logs available"
              defaultItemsPerPage={10}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PatientLogs;
