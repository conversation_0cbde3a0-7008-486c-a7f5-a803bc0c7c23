import Preloader from "@/components/common/Preloader";
import "./mytemplates.css";
import { useState, useEffect } from "react";
import TemplatesCard from "@/components/TemplatesCard";
import LightFooter from "@/shared/LightFooter";
import FilterSidebar from "@/components/FilterSidebar";
import {
  useGetForms,
  useGetFormsByUser,
  useGetPendingForms,
  useGetTags,
} from "@/hooks/form.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { getAllStudies } from "@/services/api/study.service";

interface StudyData {
  uuid: string;
  name: string;
}

interface FormVersion {
  uuid: string;
  version: number;
  description: string;
  categories: string[];
  study: string;
  study_uuid: string;
  password?: string;
  structure: string;
  status: 'pending' | 'accepted' | 'rejected' | 'archived';
  created_at: string;
  is_active: boolean;
  form_structure: Record<string, unknown>;
}

interface Form {
  uuid: string;
  name: string;
  user: {
    identifier: string;
    first_name?: string;
    last_name?: string;
    email: string;
  };
  created_at: string;
  active_version: FormVersion;
}

export default function MyTemplates() {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedStudies, setSelectedStudies] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<"all" | "myForms" | "pending">("all");
  const [availableTags, setAvailableTags] = useState<{ id: string; name: string }[]>([]);
  const [availableStudies, setAvailableStudies] = useState<{ id: string; name: string }[]>([]);

  const { data: currentUser, isLoading: isLoadingUser } = useCurrentUserQuery();
  const userIdentifier = currentUser?.identifier || "";

  const { data: allFormsData, isLoading: isLoadingAllForms } = useGetForms();
  const { data: userFormsData, isLoading: isLoadingUserForms } = useGetFormsByUser(userIdentifier);
  const { data: pendingFormsData, isLoading: isLoadingPendingForms } = useGetPendingForms();
  const { data: tagsData, isLoading: isLoadingTags } = useGetTags();

  const allForms = allFormsData?.results || [];
  const userForms = userFormsData || [];
  const pendingForms = pendingFormsData || [];

  // Load studies when component mounts
  useEffect(() => {
    const loadStudies = async () => {
      try {
        const studiesData = await getAllStudies();
        setAvailableStudies(studiesData.map((study: StudyData) => ({
          id: study.uuid,
          name: study.name
        })));
      } catch (error) {
        console.error("Error loading studies:", error);
      }
    };
    loadStudies();
  }, []);

  useEffect(() => {
    if (tagsData) {
      const tags = Array.isArray(tagsData)
        ? tagsData
        : (tagsData as { results?: { uuid: string; name: string }[] })?.results ?? [];
      const processedTags = tags.map((tag: { uuid: string; name: string }) => ({
        id: tag.uuid,
        name: tag.name,
      }));
      setAvailableTags(processedTags);
    }
  }, [tagsData]);

  const calculateTagCounts = (forms: unknown[]) => {
    const counts = new Map<string, number>();
    
    forms.forEach((form: unknown) => {
      const formTags = (form as Form).active_version?.categories || [];
      formTags.forEach(tagId => {
        counts.set(tagId, (counts.get(tagId) || 0) + 1);
      });
    });
    
    return counts;
  };

  const calculateStudyCounts = (forms: unknown[]) => {
    const counts = new Map<string, number>();
    
    forms.forEach((form: unknown) => {
      const formVersion = (form as Form).active_version;
      if (formVersion) {
        // Use study_uuid if available, otherwise fall back to study
        const studyId = formVersion.study_uuid || formVersion.study;
        if (studyId) {
          counts.set(studyId, (counts.get(studyId) || 0) + 1);
        }
      }
    });
    
    return counts;
  };

  const getDisplayForms = () => {
    let forms: unknown[];
    switch (activeTab) {
      case "all":
        forms = allForms;
        break;
      case "myForms":
        forms = userForms;
        break;
      case "pending":
        forms = pendingForms;
        break;
      default:
        forms = [];
    }

    return filterForms(forms);
  };

  const filterForms = (forms: unknown[]) => {
    return forms.filter((form: unknown) => {
      const formVersion = (form as Form).active_version;
      if (!formVersion) return false;

      const formTags = formVersion.categories || [];
      // Use study_uuid if available, otherwise fall back to study
      const formStudy = formVersion.study_uuid || formVersion.study;

      const matchesTags = selectedCategories.length === 0 || 
        selectedCategories.some(tagId => formTags.includes(tagId));
      
      const matchesStudies = selectedStudies.length === 0 || 
        (formStudy && selectedStudies.includes(formStudy));

      return matchesTags && matchesStudies;
    });
  };

  const tagCounts = calculateTagCounts(allForms);
  const studyCounts = calculateStudyCounts(allForms);

  const categories = availableTags.map((tag) => ({
    id: tag.id,
    label: tag.name,
    count: tagCounts.get(tag.id) || 0,
  }));

  const studies = availableStudies.map((study) => ({
    id: study.id,
    label: study.name,
    count: studyCounts.get(study.id) || 0,
  }));

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategories((prev) => {
      const newCategories = prev.includes(categoryId)
        ? prev.filter((id) => id !== categoryId)
        : [...prev, categoryId];
      return newCategories;
    });
  };

  const handleStudyChange = (studyId: string) => {
    setSelectedStudies((prev) => {
      const newStudies = prev.includes(studyId)
        ? prev.filter((id) => id !== studyId)
        : [...prev, studyId];
      return newStudies;
    });
  };

  const formatUserName = (
    user: {
      first_name?: string;
      last_name?: string;
      identifier?: string;
    } | null
  ) => {
    if (!user) return "Unknown User";
    const firstName = user.first_name || "";
    const lastName = user.last_name || "";
    return firstName || lastName
      ? `${firstName} ${lastName}`.trim()
      : user.identifier || "Unknown User";
  };

  const displayForms = getDisplayForms();
  const isLoading =
    isLoadingAllForms ||
    isLoadingUserForms ||
    isLoadingPendingForms ||
    isLoadingUser ||
    isLoadingTags;

  const shouldShowActions = activeTab === "myForms";

  return (
    <div className="main-content bg-light-4">
      <Preloader />
      <div className="content-wrapper js-content-wrapper">
        <div className="dashboard__content bg-light-4">
          <div className="container-fluid px-0">
            <div className="row y-gap-30">
              <div className="col-xl-3 col-lg-4">
                <FilterSidebar
                  title="Filter Templates"
                  categories={categories}
                  selectedCategories={selectedCategories}
                  onCategoryChange={handleCategoryChange}
                  studies={studies}
                  selectedStudies={selectedStudies}
                  onStudyChange={handleStudyChange}
                />
              </div>
              <div className="col-xl-9 col-lg-8">
                <div className="d-flex justify-content-between align-items-center mb-4">
                  <h2 className="mb-0" style={{ fontSize: '24px', fontWeight: '600', color: 'var(--color-dark-1)' }}>My Templates</h2>
                  <div className="d-flex align-items-center">
                    <span style={{ marginRight: '15px', color: 'var(--color-light-1)' }}>
                      {displayForms.length} {displayForms.length === 1 ? 'template' : 'templates'} found
                    </span>
                  </div>
                </div>
                <div className="tabs-wrapper">
                  <button
                    className={`tab-button ${
                      activeTab === "all" ? "active" : ""
                    }`}
                    onClick={() => setActiveTab("all")}
                  >
                    All Templates
                  </button>
                  <button
                    className={`tab-button ${
                      activeTab === "myForms" ? "active" : ""
                    }`}
                    onClick={() => setActiveTab("myForms")}
                    disabled={!userIdentifier}
                  >
                    My Templates
                  </button>
                  <button
                    className={`tab-button ${
                      activeTab === "pending" ? "active" : ""
                    }`}
                    onClick={() => setActiveTab("pending")}
                  >
                    Pending Review
                  </button>
                </div>
                <div>
                  {isLoading ? (
                    <div className="text-center py-5">
                      <div className="spinner-border text-primary" role="status" style={{ color: 'var(--color-purple-1) !important' }}>
                        <span className="visually-hidden">Loading...</span>
                      </div>
                      <p className="mt-3" style={{ color: 'var(--color-light-1)' }}>Loading templates...</p>
                    </div>
                  ) : displayForms.length > 0 ? (
                    <div className="mt-4">
                      {Array.from({
                        length: Math.ceil(displayForms.length / 4),
                      }).map((_, rowIndex) => (
                        <div
                          key={rowIndex}
                          className="row y-gap-30 mt-30"
                          style={{ marginBottom: "30px" }}
                        >
                          {displayForms
                            .slice(rowIndex * 4, rowIndex * 4 + 4)
                            .map((form) => {
                              const typedForm = form as {
                                uuid: string;
                                name: string;
                                user: {
                                  first_name?: string;
                                  last_name?: string;
                                  identifier?: string;
                                };
                                created_at: string;
                                active_version?: {
                                  version: string;
                                };
                              };
                              return (
                                <div
                                  key={typedForm.uuid}
                                  className="col-xl-3 col-lg-6 col-md-6"
                                >
                                  <TemplatesCard
                                    label={typedForm.name}
                                    link={`/form/preview/${typedForm.uuid}`}
                                    createdBy={formatUserName(typedForm.user)}
                                    createdAt={new Date(
                                      typedForm.created_at
                                    ).toLocaleDateString()}
                                    formVersion={typedForm.active_version?.version || "1.0"}
                                    showActions={shouldShowActions} 
                                    formUuid={typedForm.uuid} 
                                  />
                                </div>
                              );
                            })}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-5" style={{ backgroundColor: 'rgba(55, 183, 195, 0.05)', borderRadius: '12px', border: '1px dashed rgba(55, 183, 195, 0.2)' }}>
                      <div style={{ marginBottom: '15px' }}>
                        <svg width="64" height="64" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M13 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V9L13 2Z" stroke="#37B7C3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M13 2V9H20" stroke="#37B7C3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M8 13H16" stroke="#37B7C3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M8 17H16" stroke="#37B7C3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M10 9H8" stroke="#37B7C3" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                      </div>
                      <h4 style={{ color: 'var(--color-dark-1)', marginBottom: '10px' }}>No templates found</h4>
                      <p style={{ color: 'var(--color-light-1)', maxWidth: '400px', margin: '0 auto' }}>
                        No templates match your current filter criteria. Try adjusting your filters to see more results.
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
        <LightFooter />
      </div>
    </div>
  );
}
