import React, { useState } from "react";
import { motion } from "framer-motion";
import Preloader from "@/components/common/Preloader";
import Footer from "@/shared/Footer";

import "./LandingPage.css";
import LandingHeader from "@/shared/LandingHeader";

const LandingPage: React.FC = () => {
  // Define testimonials data
  const testimonials = [
    {
      name: "Dr. <PERSON>",
      title: "Clinical Research Director",
      organization: "Parkview Medical Center",
      quote: "Nurtify transformed how we run clinical trials. Recruitment timelines shortened by 45%, data quality improved dramatically, and our team spends less time on paperwork and more time with patients.",
      avatar: "/assets/img/landing/avatar-placeholder.svg"
    },
    {
      name: "Dr. <PERSON>",
      title: "Head of Clinical Operations",
      organization: "Sunrise University Hospital",
      quote: "Since implementing Nurtify, we've seen a 38% increase in patient retention rates. The platform's intuitive design makes it easy for both our staff and trial participants to stay engaged throughout the entire process.",
      avatar: "/assets/img/landing/avatar-placeholder.svg"
    },
    {
      name: "Dr. <PERSON>",
      title: "Research Program Director",
      organization: "Sunrise Medical Center",
      quote: "The real-time data visibility has been a game-changer for our multi-site trials. We can now identify and address issues before they become problems, saving us countless hours and resources.",
      avatar: "/assets/img/landing/avatar-placeholder.svg"
    }
  ];

  // State to track current testimonial
  const [currentTestimonialIndex, setCurrentTestimonialIndex] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  // Auto-rotate testimonials
  React.useEffect(() => {
    const autoRotateInterval = setInterval(() => {
      if (!isAnimating) {
        const currentCard = document.querySelector('.testimonial-card-large');
        if (currentCard) {
          setIsAnimating(true);
          currentCard.classList.add('flip-out-right');

          // Reduced timeout to make transition faster and reduce blank period
          setTimeout(() => {
            const nextIndex = (currentTestimonialIndex + 1) % testimonials.length;
            setCurrentTestimonialIndex(nextIndex);
            currentCard.classList.remove('flip-out-right');
            currentCard.classList.add('flip-in-left');

            // Reduced timeout for smoother transition
            setTimeout(() => {
              currentCard.classList.remove('flip-in-left');
              setIsAnimating(false);
            }, 400);
          }, 300);
        }
      }
    }, 7000); // Change testimonial every 7 seconds

    return () => clearInterval(autoRotateInterval);
  }, [currentTestimonialIndex, isAnimating, testimonials.length]);
  return (
    <div>
      <Preloader />
      <div>
        {/* HEADER */}

        {/* HERO SECTION */}
        <div className="hero-container-landing">
          <LandingHeader />
          <div className="hero-split-background">
            <div
              className="header__container px-5"
              style={{
                paddingTop: "120px",
              }}
            >
              <div className="hero-content">
                <motion.h1
                  className="hero-title"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.2 }}
                >
                  Transform Your
                  <br />
                  <span className="text-lighter">Clinical Trials</span>
                </motion.h1>
                <motion.p
                  className="hero-subtitle"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.3 }}
                >
                  Accelerating Research. Empowering Teams.
                  <br />
                  Improving Patient Outcomes.
                </motion.p>

                <motion.p
                  className="hero-description"
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 0.4 }}
                >
                  Nurtify eliminates your clinical trial daily challenges with
                  our comprehensive, user-friendly platform, delivering faster
                  recruitment, real-time oversight, seamless data capture, and
                  unmatched participant retention.
                </motion.p>

                <motion.div
                  className="hero-buttons"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.6 }}
                >
                  <button className="hero-cta-button">REQUEST A DEMO</button>
                  <button className="hero-secondary-button">
                    GET IN TOUCH TODAY
                  </button>
                </motion.div>
              </div>
            </div>
          </div>
        </div>

        {/* WHY CHOOSE NURTIFY SECTION */}
        <section className="why-choose-section">
          <div className="container">
            <div className="why-choose-header text-center">
              <h2 className="why-choose-title">
                Why Leading Research Teams
                <br />
                <span className="why-choose-highlight">Choose Nurtify</span>
              </h2>
              <p className="why-choose-subtitle">
                Powering Smarter Clinical Trials
              </p>
            </div>

            <div className="why-choose-grid">
              <div className="row">
                <div className="col-md-6">
                  <motion.div
                    className="why-choose-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/AcceleratedRecruitment.svg" alt="Accelerated Recruitment" width="32" height="32" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Accelerated Recruitment
                      </h3>
                      <p className="why-choose-description">
                        Say goodbye to enrollment delays. Our AI-powered
                        matching algorithm helps connect research sites with
                        qualified participants faster, getting your trials fully
                        enrolled and moving forward sooner.
                      </p>
                    </div>
                  </motion.div>
                </div>
                <div className="col-md-6">
                  <motion.div
                    className="why-choose-card-with-image"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                  >
                    <img src="/assets/img/landing/leading-1.png"></img>
                  </motion.div>
                </div>
              </div>
              <div className="row mt-4">
                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/RealTime.svg" alt="Real-Time Trial Visibility" width="32" height="32" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Real-Time Trial Visibility
                      </h3>
                      <p className="why-choose-description">
                        Stay informed with real-time insights on enrollment
                        progress, protocol compliance, and data quality. Our
                        dashboard gives you complete visibility into trial
                        performance at every stage.
                      </p>
                    </div>
                  </motion.div>
                </div>

                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/Robust.svg" alt="Robust Source Data" width="32" height="32" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Robust Source Data
                      </h3>
                      <p className="why-choose-description">
                        Eliminate transcription errors and audit findings. Our
                        digitized source system captures clean, compliant data
                        directly at the point of care, minimizing transcription
                        errors and maintaining full regulatory compliance.
                      </p>
                    </div>
                  </motion.div>
                </div>
                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/FullyDigital.svg" alt="Fully Digital Workflows" width="32" height="32" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Fully Digital Workflows
                      </h3>
                      <p className="why-choose-description">
                        Step into the future with end-to-end digital workflows.
                        Our platform eliminates paper-based administrative
                        burdens by digitizing every process, streamlined for
                        maximum efficiency.
                      </p>
                    </div>
                  </motion.div>
                </div>
              </div>
              <div className="row mt-4">
                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/ParticipantEngagement.svg" alt="Participant Engagement" width="32" height="32" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Participant Engagement
                      </h3>
                      <p className="why-choose-description">
                        Dramatically reduce dropout rates with our
                        patient-centered engagement tools. Keep participants
                        informed and connected throughout the trial with
                        automated reminders, tracking, and direct messaging to
                        strengthen the research relationship.
                      </p>
                    </div>
                  </motion.div>
                </div>
                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card-with-image"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                  >
                    <img src="/assets/img/landing/leading-2.png"></img>
                  </motion.div>
                </div>
                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/UnifiedPatient.svg" alt="Unified Patient Profiles" width="32" height="32" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Unified Patient Profiles
                      </h3>
                      <p className="why-choose-description">
                        Break down information silos with our comprehensive
                        patient profiles. Your entire team has complete access
                        to consolidated medical history, study participation,
                        and current status from a single, secure location.
                      </p>
                    </div>
                  </motion.div>
                </div>
              </div>

              <div className="row mt-4">
                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card-with-image"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                  >
                    <img src="/assets/img/landing/leading-3.png"></img>
                  </motion.div>
                </div>
                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/AutomatedWorksheet.svg" alt="Automated Worksheet Generation" width="32" height="32" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Automated Worksheet Generation
                      </h3>
                      <p className="why-choose-description">
                        Reclaim hours with smart worksheets that auto-populate
                        based on protocol requirements. Our templates adapt to
                        protocol changes, ensuring your documentation is always
                        in compliance.
                      </p>
                    </div>
                  </motion.div>
                </div>

                <div className="col-md-4">
                  <motion.div
                    className="why-choose-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.8 }}
                  >
                    <div className="why-choose-icon">
                      <img src="/assets/img/landing/icons/BuiltInRegulatory.svg" alt="Built-in Regulatory Compliance" width="32" height="32" />
                    </div>
                    <div className="why-choose-content">
                      <h3 className="why-choose-card-title">
                        Built-in Regulatory Compliance
                      </h3>
                      <p className="why-choose-description">
                        Rest easy knowing your trial data meets all FDA/EMA
                        compliance. Our built-in regulatory safeguards ensure
                        your documentation meets the highest standards, reducing
                        inspection findings.
                      </p>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* SEAMLESS CONNECTION SECTION */}
        <section className="seamless-connection-section">
          <motion.h2
            className="seamless-connection-title"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            Seamless Connection Between Patients
            <br />
            and Research Teams
          </motion.h2>
          <motion.p
            className="seamless-connection-description"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            Our intuitive mobile app bridges the gap between patients and
            research teams, creating a continuous feedback loop that
            improves compliance and outcomes.
          </motion.p>

          <div className="seamless-connection-content">

            <div className="mobile-devices-container">
              {/* Left side feature bubbles */}
              <div className="feature-bubbles-left">
                <motion.div
                  className="feature-bubble feature-bubble-left-1"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">Easy form completion</span>
                </motion.div>
                <motion.div
                  className="feature-bubble feature-bubble-left-2"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">Appointment reminders</span>
                </motion.div>
                <motion.div
                  className="feature-bubble feature-bubble-left-3"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">
                    Secure messaging with research staff
                  </span>
                </motion.div>
              </div>

              {/* Combined mobile phones image */}
              <motion.div
                className="combined-mobile-image"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, delay: 0.2 }}
              >
                <img
                  src="/assets/img/landing/mobiles-images.png"
                  alt="Patient and doctor mobile communication"
                  className="mobile-phones-image"
                />
              </motion.div>

              {/* Right side feature bubbles */}
              <div className="feature-bubbles-right">
                <motion.div
                  className="feature-bubble feature-bubble-right-1"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">
                    Real-time symptom reporting
                  </span>
                </motion.div>
                <motion.div
                  className="feature-bubble feature-bubble-right-2"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">
                    Medication adherence tracking
                  </span>
                </motion.div>
                <motion.div
                  className="feature-bubble feature-bubble-right-3"
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  <span className="bubble-icon">
                    <svg
                      width="32"
                      height="32"
                      viewBox="0 0 32 32"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <rect width="32" height="32" rx="8" fill="#37B7C3" />
                      <path
                        d="M21 6V8M16 6V8M11 6V8"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M7.5 14C7.5 10.7002 7.5 9.05025 8.52513 8.02513C9.55025 7 11.2002 7 14.5 7H17.5C20.7998 7 22.4497 7 23.4749 8.02513C24.5 9.05025 24.5 10.7002 24.5 14V19C24.5 22.2998 24.5 23.9497 23.4749 24.9749C22.4497 26 20.7998 26 17.5 26H14.5C11.2002 26 9.55025 26 8.52513 24.9749C7.5 23.9497 7.5 22.2998 7.5 19V14Z"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M17.5 20H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M17.5 13H21"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                      />
                      <path
                        d="M11 14C11 14 11.5 14 12 15C12 15 13.5882 12.5 15 12"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                      <path
                        d="M11 21C11 21 11.5 21 12 22C12 22 13.5882 19.5 15 19"
                        stroke="white"
                        stroke-width="1.5"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      />
                    </svg>
                  </span>
                  <span className="bubble-text">
                    Visit scheduling and reminders
                  </span>
                </motion.div>
              </div>
            </div>



            <div className="connection-buttons">
              <motion.a
                href="#"
                className="app-store-button"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 1.0 }}
              >
                <div className="app-button-content">
                  <span className="app-button-icon">
                    <svg
                      width="19"
                      height="23"
                      viewBox="0 0 19 23"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M18.6563 16.7009C18.308 17.817 17.7589 18.933 17.0089 20.0491C15.8571 21.7991 14.7098 22.6741 13.567 22.6741C13.1295 22.6741 12.5045 22.5313 11.692 22.2455C10.9241 21.9598 10.25 21.817 9.66964 21.817C9.125 21.817 8.49107 21.9643 7.76786 22.2589C7.04464 22.5625 6.45536 22.7143 6 22.7143C4.64286 22.7143 3.29911 21.558 1.96875 19.2455C0.65625 16.9152 0 14.6696 0 12.5089C0 10.4732 0.504464 8.80357 1.51339 7.5C2.52232 6.21428 3.79018 5.57143 5.31696 5.57143C5.95982 5.57143 6.75 5.70536 7.6875 5.97321C8.61607 6.24107 9.23214 6.375 9.53571 6.375C9.9375 6.375 10.5759 6.22321 11.4509 5.91964C12.3616 5.61607 13.1339 5.46428 13.7679 5.46428C14.8304 5.46428 15.7813 5.75446 16.6205 6.33482C17.0848 6.65625 17.5491 7.10268 18.0134 7.67411C17.308 8.27232 16.7991 8.79911 16.4866 9.25446C15.9063 10.0937 15.6161 11.0179 15.6161 12.0268C15.6161 13.1339 15.9241 14.1295 16.5402 15.0134C17.1563 15.8973 17.8616 16.4598 18.6563 16.7009ZM13.6205 0.991071C13.6205 1.53571 13.4911 2.14286 13.2321 2.8125C12.9643 3.48214 12.5491 4.09821 11.9866 4.66071C11.5045 5.14286 11.0223 5.46428 10.5402 5.625C10.2098 5.72321 9.74554 5.79911 9.14732 5.85268C9.17411 4.52232 9.52232 3.375 10.192 2.41071C10.8527 1.45536 11.9688 0.794641 13.5402 0.42857C13.5491 0.455356 13.558 0.504463 13.567 0.575892C13.5848 0.647321 13.5982 0.696428 13.6071 0.723214C13.6071 0.758928 13.6071 0.803571 13.6071 0.857142C13.6161 0.910713 13.6205 0.955356 13.6205 0.991071Z"
                        fill="white"
                      />
                    </svg>{" "}
                  </span>
                  <div className="app-button-text">
                    <span className="app-button-small">Download on the</span>
                    <span className="app-button-large">App Store</span>
                  </div>
                </div>
              </motion.a>
              <motion.a
                href="#"
                className="google-play-button"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 1.2 }}
              >
                <div className="app-button-content">
                  <span className="app-button-icon">
                    <svg
                      width="21"
                      height="21"
                      viewBox="0 0 21 21"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M10.2857 8.95536H19.9955C20.1027 9.55357 20.1563 10.125 20.1563 10.6696C20.1563 12.6071 19.75 14.3393 18.9375 15.8661C18.125 17.3839 16.9643 18.5714 15.4554 19.4286C13.9554 20.2857 12.2321 20.7143 10.2857 20.7143C8.88393 20.7143 7.54911 20.442 6.28125 19.8973C5.01339 19.3616 3.91964 18.6339 3 17.7143C2.08036 16.7946 1.34821 15.7009 0.803571 14.433C0.267857 13.1652 0 11.8304 0 10.4286C0 9.02679 0.267857 7.69196 0.803571 6.42411C1.34821 5.15625 2.08036 4.0625 3 3.14286C3.91964 2.22321 5.01339 1.49554 6.28125 0.959821C7.54911 0.415178 8.88393 0.142856 10.2857 0.142856C12.9643 0.142856 15.2634 1.04018 17.183 2.83482L14.3839 5.52678C13.2857 4.46428 11.9196 3.93303 10.2857 3.93303C9.13393 3.93303 8.06696 4.22321 7.08482 4.80357C6.11161 5.38393 5.33929 6.17411 4.76786 7.17411C4.19643 8.16518 3.91071 9.25 3.91071 10.4286C3.91071 11.6071 4.19643 12.6964 4.76786 13.6964C5.33929 14.6875 6.11161 15.4732 7.08482 16.0536C8.06696 16.6339 9.13393 16.9241 10.2857 16.9241C11.0625 16.9241 11.7768 16.817 12.4286 16.6027C13.0804 16.3884 13.6161 16.1205 14.0357 15.7991C14.4554 15.4777 14.8214 15.1116 15.1339 14.7009C15.4464 14.2902 15.6741 13.9018 15.817 13.5357C15.9688 13.1696 16.0714 12.8214 16.125 12.4911H10.2857V8.95536Z"
                        fill="white"
                      />
                    </svg>
                  </span>
                  <div className="app-button-text">
                    <span className="app-button-small">Get it on</span>
                    <span className="app-button-large">Google Play</span>
                  </div>
                </div>
              </motion.a>
            </div>
          </div>
        </section>

        {/* ARTICLES SECTION */}
        <section className="articles-section">
          <div className="container">
            <div className="articles-header">
              <h2 className="articles-title">
                Latest Articles From
                <br />
                <span className="articles-highlight">
                  Clinical Research
                </span>{" "}
                Experts
              </h2>
              <button className="articles-view-all">View All Articles</button>
            </div>

            <div className="articles-grid">
              <div className="row">
                <div className="col-md-4">
                  <motion.div
                    className="article-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                  >
                    <div className="article-image">
                      <img
                        src="/assets/img/landing/Image_one.png"
                        alt="Decentralized Trials"
                      />
                    </div>
                    <div className="article-content">
                      <h3 className="article-title" style={{color:"black"}}>
                        The Future of Decentralized Trials: What's Working Now
                      </h3>
                      <p className="article-description">
                        Lorem ipsum dolor sit amet consectetur. Ultrices varius
                        nisl quis suscipit elementum elit molestie.
                      </p>
                      <a href="#" className="article-link">
                        Read More
                      </a>
                    </div>
                  </motion.div>
                </div>

                <div className="col-md-4">
                  <motion.div
                    className="article-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <div className="article-image">
                      <img
                        src="/assets/img/landing/Image_second.png"
                        alt="Recruitment Time"
                      />
                    </div>
                    <div className="article-content">
                    <h3 className="article-title" style={{color:"black"}}>
                    How Top Research Sites Cut Recruitment Time by 30%
                      </h3>
                      <p className="article-description">
                        Lorem ipsum dolor sit amet consectetur. Ultrices varius
                        nisl quis suscipit elementum elit molestie.
                      </p>
                      <a href="#" className="article-link">
                        Read More
                      </a>
                    </div>
                  </motion.div>
                </div>

                <div className="col-md-4">
                  <motion.div
                    className="article-card"
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <div className="article-image">
                      <img
                        src="/assets/img/landing/Image_third.png"
                        alt="Patient-Centered Design"
                      />
                    </div>
                    <div className="article-content">
                    <h3 className="article-title" style={{color:"black"}}>
                    Patient-Centered Design: The Key to Trial Retention
                      </h3>
                      <p className="article-description">
                        Lorem ipsum dolor sit amet consectetur. Ultrices varius
                        nisl quis suscipit elementum elit molestie.
                      </p>
                      <a href="#" className="article-link">
                        Read More
                      </a>
                    </div>
                  </motion.div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* TRUSTED BY INDUSTRY LEADERS SECTION */}
        <section className="trusted-leaders-section">
          <div className="container">
            <motion.h2
              className="trusted-leaders-title"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              Trusted by Industry Leaders
            </motion.h2>

            <div className="testimonial-carousel">
              <button
                className="carousel-arrow carousel-arrow-prev"
                onClick={() => {
                  const currentCard = document.querySelector('.testimonial-card-large');
                  currentCard?.classList.add('flip-out-left');

                  // Reduced timeout for faster transition
                  setTimeout(() => {
                    const prevIndex = (currentTestimonialIndex - 1 + testimonials.length) % testimonials.length;
                    setCurrentTestimonialIndex(prevIndex);
                    currentCard?.classList.remove('flip-out-left');
                    currentCard?.classList.add('flip-in-right');

                    // Reduced timeout for smoother transition
                    setTimeout(() => {
                      currentCard?.classList.remove('flip-in-right');
                    }, 400);
                  }, 300);
                }}
                aria-label="Previous testimonial"
              >
                <span className="arrow-icon">&#8249;</span>
              </button>

              <motion.div
                className="testimonial-card-large"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <div className="testimonial-header">
                  <div className="testimonial-avatar">
                    <img
                      src={testimonials[currentTestimonialIndex].avatar || "/assets/img/landing/avatar-placeholder.png"}
                      alt={testimonials[currentTestimonialIndex].name}
                    />
                  </div>
                  <div className="testimonial-author-info">
                    <h3 className="testimonial-author-name" style={{ color: "black" }}>{testimonials[currentTestimonialIndex].name}</h3>
                    <p className="testimonial-author-title">
                      {testimonials[currentTestimonialIndex].title}<br />
                      {testimonials[currentTestimonialIndex].organization}
                    </p>
                  </div>
                </div>
                <div className="testimonial-body">
                  <p className="testimonial-quote">
                    "{testimonials[currentTestimonialIndex].quote}"
                  </p>
                </div>
              </motion.div>

              <button
                className="carousel-arrow carousel-arrow-next"
                onClick={() => {
                  const currentCard = document.querySelector('.testimonial-card-large');
                  currentCard?.classList.add('flip-out-right');

                  // Reduced timeout for faster transition
                  setTimeout(() => {
                    const nextIndex = (currentTestimonialIndex + 1) % testimonials.length;
                    setCurrentTestimonialIndex(nextIndex);
                    currentCard?.classList.remove('flip-out-right');
                    currentCard?.classList.add('flip-in-left');

                    // Reduced timeout for smoother transition
                    setTimeout(() => {
                      currentCard?.classList.remove('flip-in-left');
                    }, 400);
                  }, 300);
                }}
                aria-label="Next testimonial"
              >
                <span className="arrow-icon">&#8250;</span>
              </button>
            </div>

            <motion.div
              className="trusted-logos"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <img src="/assets/img/landing/companies/forbes-logo.png" alt="Forbes" className="trusted-logo" />
              <img src="/assets/img/landing/companies/bca-logo.png" alt="BCA" className="trusted-logo" />
              <img src="/assets/img/landing/companies/gopay-logo.png" alt="Gopay" className="trusted-logo" />
              <img src="/assets/img/landing/companies/paypal-logo.png" alt="PayPal" className="trusted-logo" />
              <img src="/assets/img/landing/companies/youtube-logo.png" alt="YouTube" className="trusted-logo" />
            </motion.div>
          </div>
        </section>

        {/* PATIENT PARTICIPATION SECTION */}
        <section className="participation-section">
          <div className="participation-container">
            <div className="participation-row">
              <div className="participation-image-col">
                <motion.div
                  className="participation-image-container"
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                >
                  <img
                    src="/assets/img/landing/patient_with_nurse.png"
                    alt="Doctor consulting with patient"
                    className="participation-image"
                  />
                </motion.div>
              </div>
              <div className="participation-content-col">
                <motion.div
                  className="participation-content"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6 }}
                >
                  <h2 className="participation-title">
                    Interested in{" "}
                    <span className="participation-highlight">
                      Participating or Referring
                    </span>{" "}
                    a Patient?
                  </h2>
                  <p className="participation-description">
                    Whether you're considering joining a study or referring a
                    patient to one of our research centers, we're here to help.
                  </p>
                  <p className="participation-description">
                    Get in touch today to explore opportunities and take the
                    next step in advancing medical research.
                  </p>
                  <div className="participation-action">
                    <button className="participation-button">
                      Get in touch
                    </button>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </section>

        {/* SERVICE SECTION */}
        <section className="service-section">
          <div className="service-container">
            <motion.div
              className="service-content text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6 }}
            >
              <h4 className="service-subtitle">Take The Next Step</h4>
              <h2 className="service-title">
                Ready to transform your
                <br />
                clinical trials?
              </h2>

              <div className="service-buttons">
                <motion.button
                  className="service-button waitlist-button"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 0.2 }}
                >
                  JOIN OUR WAITLIST
                </motion.button>
                <motion.button
                  className="service-button demo-button"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 0.3 }}
                >
                  REQUEST A DEMO
                </motion.button>
                <motion.button
                  className="service-button sales-button"
                  initial={{ opacity: 0, y: 10 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.4, delay: 0.4 }}
                >
                  CONTACT SALES
                </motion.button>
              </div>
            </motion.div>
          </div>
        </section>

        <Footer />
      </div>
    </div>
  );
};

export default LandingPage;
