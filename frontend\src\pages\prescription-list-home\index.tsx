import Preloader from "@/components/common/Preloader";
import React, { useEffect } from "react";
import "./prescriptionList.css";
import LightFooter from "@/shared/LightFooter";
//import FilterSidebar from "@/components/FilterSidebar";
import { ChevronDown, Search, X, Refresh<PERSON><PERSON>, Eye } from "lucide-react";

import { format } from "date-fns";
import { usePrescriptionsQuery } from "@/hooks/prescription.query";
import { useState, useCallback } from "react";
import { PrescriptionWithDetails } from "@/services/api/types";
import PrescriptionDescriptionModal from "@/components/modal/PrescriptionDescriptionModal";
import { useCurrentUserQuery } from "@/hooks/user.query";

type TabType = "All Prescriptions" | "New Prescriptions" | "Prepared" | "Collected";

export default function PrescriptionList() {
    const [searchTerm, setSearchTerm] = useState("");
    const [activeTab, setActiveTab] = React.useState<TabType>("All Prescriptions");
    const { data, isLoading, isError, error, refetch } = usePrescriptionsQuery();
    //const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
    const [isShowPresDescModalOpen,setIsShowPresDescModalOpen] = useState(false);
    const [prescriptionDetails, setPrescriptionDetails] = useState<PrescriptionWithDetails | null>(null);
    const {data:CurrentUser} = useCurrentUserQuery();
    const user_identifier = CurrentUser?.identifier || "";
    const [prescriptionUpdated,setPrescriptionUpdated] = useState(false);


    useEffect(() => {
        if(prescriptionUpdated){
            refetch();
          setPrescriptionUpdated(false);
        }
        
      }, [prescriptionUpdated]);

    const normalizeStatus = (status?: string): string => {
        return status
          ? (status.charAt(0).toUpperCase() + status.slice(1).toLowerCase() )
          : 'Prescribed';
      };
/*

    const categories = [
        { id: "pain", label: "Pain Relief Medications", count: 24 },
        { id: "infection", label: "Antibiotics & Antivirals", count: 16 },
        { id: "cardio", label: "Cardiovascular Medications", count: 18 },
        { id: "diabetes", label: "Diabetes & Endocrine Treatments", count: 32 },
        { id: "respiratory", label: "Respiratory & Allergy Medications", count: 12 },
        { id: "mental_health", label: "Neurological & Psychiatric Medications", count: 20 },
        { id: "digestive", label: "Gastrointestinal Treatments", count: 15 },
    ];

  
    const handleCategoryChange = (categoryId: string) => {
        setSelectedCategories((prev) =>
            prev.includes(categoryId) ? prev.filter((id) => id !== categoryId) : [...prev, categoryId]
        );
    };

    */

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(event.target.value);
    };

    const clearSearch = useCallback(() => {
        setSearchTerm("");
    }, []);

    const handlePrescriptionDescription = (row :PrescriptionWithDetails) => {
        setPrescriptionDetails(row);
        setIsShowPresDescModalOpen(true);
    } 


    const filteredPrescriptions = data?.filter(prescription => {
        // Search term filtering
        const matchesSearch = searchTerm === "" ||
            prescription.drug_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            prescription.dose?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            prescription.patient.nhs_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            prescription.status?.toLowerCase().includes(searchTerm.toLowerCase());
    
        // Tab-based status filtering
        let matchesTab = true;
        switch (activeTab) {
            case "New Prescriptions":
                matchesTab = prescription.status?.toLowerCase() === "prescribed";
                break;
            case "Prepared":
                matchesTab = prescription.status?.toLowerCase() === "prepared";
                break;
            case "Collected":
                matchesTab = prescription.status?.toLowerCase() === "collected";
                break;
            case "All Prescriptions":
                matchesTab = true; // No status filtering for "All Prescriptions"
                break;
        }
    
        return matchesSearch && matchesTab;
    });

    if (isLoading) {
        return <Preloader />;
    }

    if (isError) return (
        <div className="main-content bg-light-4">
            <div className="content-wrapper js-content-wrapper">
                <div className="dashboard__content bg-light-4">
                    <div className="container-fluid px-0">
                        <div className="error-container">
                            <h2>Error Loading Prescriptions</h2>
                            <p>{(error as Error).message}</p>
                            <button className="refresh-button" onClick={() => refetch()}>
                                <RefreshCw size={16} /> Try Again
                            </button>
                        </div>
                    </div>
                </div>
                <LightFooter />
            </div>
        </div>
    );

    return (
        <div className="main-content bg-light-4">
            <div className="content-wrapper js-content-wrapper">
                <div className="dashboard__content bg-light-4" style={{ backgroundColor: "white" }}>
                    <div className="container-fluid px-0">
                        <div className="row y-gap-30">
                            <div className="col-xl-3 col-lg-4">
                                {/* 
                                <FilterSidebar
                                    title="Filter Prescriptions"
                                    categories={categories}
                                    selectedCategories={selectedCategories}
                                    onCategoryChange={handleCategoryChange}
                                />
                                */}
                            </div>

                            <div className="col-xl-9 col-lg-8">
                                <div className="policy-title"><h1>Prescription List</h1></div>
                                <div className="policy-title"><h6>Manage and organize prescriptions efficiently.</h6></div>

                                <div className="ask-tabs-wrapper">
                            <button
                                className={`ask-tab-button ${activeTab === "All Prescriptions" ? "active" : ""}`}
                                onClick={() => setActiveTab("All Prescriptions")}
                            >
                                All Prescriptions
                            </button>
                            <button
                                className={`ask-tab-button ${activeTab === "New Prescriptions" ? "active" : ""}`}
                                onClick={() => setActiveTab("New Prescriptions")}
                            >
                                New Prescriptions
                            </button>
                            <button
                                className={`ask-tab-button ${activeTab === "Prepared" ? "active" : ""}`}
                                onClick={() => setActiveTab("Prepared")}
                            >
                                Prepared
                            </button>
                            <button
                                className={`ask-tab-button ${activeTab === "Collected" ? "active" : ""}`}
                                onClick={() => setActiveTab("Collected")}
                            >
                                Collected
                            </button>
                        </div>

                                <div className="policy-search-container">
                                    <div className="policy-search-box">
                                        <Search className="search-icon" size={20} />
                                        <input
                                            type="text"
                                            placeholder="Search prescriptions..."
                                            className="policy-search-input"
                                            value={searchTerm}
                                            onChange={handleInputChange}
                                        />
                                        {searchTerm && (
                                            <button className="clear-search" onClick={clearSearch}>
                                                <X size={18} />
                                            </button>
                                        )}
                                    </div>
                                </div>

                                <div style={{ marginTop: "30px" }}>
                                    {isError && (
                                        <div className="error-message">
                                            Error loading policies. Please try again.
                                        </div>
                                    )}
                                    
                                    <div className="policy-table-container">
                                        {filteredPrescriptions?.length === 0 ? (
                                            <div className="no-results">
                                                <Search size={30} />
                                                <p>No prescriptions found matching "{searchTerm}"</p>
                                            </div>
                                        ) : (
                                            <table className="policy-table">
                                                <thead>
                                                    <tr>
                                                        <th>Drug Name <ChevronDown /></th>
                                                        <th>Dose  <ChevronDown /></th>
                                                        <th>Patient Name <ChevronDown /></th>
                                                        <th>NHS Number <ChevronDown /></th>
                                                        <th>Prescribed_at <ChevronDown /></th>
                                                        {activeTab==="All Prescriptions" && <th>Status <ChevronDown /></th>}
                                                        
                                                        <th>Action</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {filteredPrescriptions?.map((prescription: PrescriptionWithDetails) => {
                                                        

                                                        return (
                                                            <tr key={prescription.uuid}>
                                                                <td>{prescription.drug_name}</td>
                                                                <td>{prescription.dose} {prescription.unit}</td>
                                                                <td>{prescription.patient.first_name} {prescription.patient.last_name}</td>
                                                                <td>{prescription.patient.nhs_number}</td>
                                                                <td>
                                                                    {prescription.prescribed_at && typeof prescription.prescribed_at === 'string' 
                                                                    ? format(new Date(prescription.prescribed_at), 'MMMM d, yyyy, hh:mm a')
                                                                    : 'N/A'}
                                                                </td>
                                                               {activeTab==="All Prescriptions" && <td>{prescription.status}</td>}
                                                               
                                                                
                                                                <td>
                                                                <div className="policy-actions">
                                                                
                                                                    <button
                                                                        className="iconButton"
                                                                        onClick={() => handlePrescriptionDescription(prescription)}
                                                                        title="Show Details"
                                                                        aria-label="Edit policy"
                                                                    >
                                                                        <Eye size={18} />
                                                                    </button>
                                                                    </div>
                                                                </td>
                                                                
                                                            </tr>
                                                        );
                                                    })}
                                                </tbody>
                                            </table>
                                        )}
                                        <PrescriptionDescriptionModal
                                        isOpen={isShowPresDescModalOpen}
                                        setIsModalOpen={setIsShowPresDescModalOpen}
                                        prescriptionDetails={prescriptionDetails}
                                        userIdentifier={user_identifier}
                                        initialStatus={normalizeStatus(prescriptionDetails?.status)}
                                        setPrescriptionUpdated={setPrescriptionUpdated}
                                        role="pharmacy"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <br /><br /><br />
                </div>
            </div>
            <LightFooter />
        </div>
    );
}
