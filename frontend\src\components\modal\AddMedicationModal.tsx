import React, { useState, useEffect } from "react";
import { X, Save, Pill, Calendar, Clipboard, Activity } from "lucide-react";
import "./EditVisitModal.css"; // Reuse the same CSS
import type { CreateConcomitantMedicationData } from "@/services/api/types";
import "./AddMedicationModal.css";

interface AddMedicationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (newMedication: CreateConcomitantMedicationData) => void;
  patientUuid: string;
}

const AddMedicationModal: React.FC<AddMedicationModalProps> = ({
  isOpen,
  onClose,
  onSave,
  patientUuid,
}) => {
  const [newMedication, setNewMedication] = useState<CreateConcomitantMedicationData>({
    patient_uuid: patientUuid,
    medication: "",
    indication: "",
    dose: 0,
    dose_units: "1",
    schedule: "1",
    dose_form: "1",
    route: "1",
    start_date: "",
    is_baseline: false,
    is_continuing: true
  });

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen) {
      setNewMedication({
        patient_uuid: patientUuid,
        medication: "",
        indication: "",
        dose: 0,
        dose_units: "1",
        schedule: "1",
        dose_form: "1",
        route: "1",
        start_date: "",
        is_baseline: false,
        is_continuing: true
      });
    }
  }, [isOpen, patientUuid]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const medicationData = { ...newMedication };
    
    // Remove end_date if it's an empty string
    if (medicationData.end_date === "") {
      delete medicationData.end_date;
    }
    
    onSave(medicationData);
  };

  if (!isOpen) return null;

  return (
    <div className="edit-visit-modal-overlay">
      <div className="add-medication-modal">
        <div className="add-medication-modal-header">
          <h2 className="add-medication-modal-title">
            <Pill size={20} className="modal-icon" /> Add New Medication
          </h2>
          <button
            type="button"
            className="add-medication-modal-close"
            onClick={onClose}
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="add-medication-modal-body">
            <div className="medication-form-section">
              <div className="section-header">
                <Pill size={16} />
                <h3>Medication Information</h3>
              </div>
              <div className="row">
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="medication-name">Medication</label>
                  <input
                    type="text"
                    id="medication-name"
                    className="form-control"
                    value={newMedication.medication}
                    onChange={(e) => setNewMedication(prev => ({ ...prev, medication: e.target.value }))}
                    required
                  />
                </div>
              </div>
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="non-drug-therapy">Non-Drug Therapy (Optional)</label>
                  <input
                    type="text"
                    id="non-drug-therapy"
                    className="form-control"
                    value={newMedication.non_drug_therapy || ""}
                    onChange={(e) => setNewMedication(prev => ({ ...prev, non_drug_therapy: e.target.value }))}
                  />
                </div>
              </div>
              </div>

              <div className="row">
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="indication">Indication</label>
                  <input
                    type="text"
                    id="indication"
                    className="form-control"
                    value={newMedication.indication}
                    onChange={(e) => setNewMedication(prev => ({ ...prev, indication: e.target.value }))}
                    required
                  />
                </div>
              </div>
              <div className="col-md-3">
                <div className="form-group">
                  <label htmlFor="dose">Dose</label>
                  <input
                    type="number"
                    step="0.01"
                    id="dose"
                    className="form-control"
                    value={newMedication.dose}
                    onChange={(e) => setNewMedication(prev => ({ ...prev, dose: parseFloat(e.target.value) }))}
                    required
                  />
                </div>
              </div>
              <div className="col-md-3">
                <div className="form-group">
                  <label htmlFor="dose-units">Dose Units</label>
                  <select
                    id="dose-units"
                    className="form-select"
                    value={newMedication.dose_units}
                    onChange={(e) => setNewMedication(prev => ({ ...prev, dose_units: e.target.value }))}
                    required
                  >
                    <option value="1">g (gram)</option>
                    <option value="2">mg (milligram)</option>
                    <option value="3">µg (microgram)</option>
                    <option value="4">L (liter)</option>
                    <option value="5">mL (milliliter)</option>
                    <option value="6">IU (International Unit)</option>
                    <option value="7">Other</option>
                  </select>
                </div>
              </div>
              </div>
            </div>

            <div className="medication-form-section">
              <div className="section-header">
                <Clipboard size={16} />
                <h3>Dosage Information</h3>
              </div>
              
              <div className="row">
              <div className="col-md-4">
                <div className="form-group">
                  <label htmlFor="schedule">Schedule</label>
                  <select
                    id="schedule"
                    className="form-select"
                    value={newMedication.schedule}
                    onChange={(e) => setNewMedication(prev => ({ ...prev, schedule: e.target.value }))}
                    required
                  >
                    <option value="1">QD (once a day)</option>
                    <option value="2">BID (twice a day)</option>
                    <option value="3">TID (three times a day)</option>
                    <option value="4">QID (four times a day)</option>
                    <option value="5">QOD (every other day)</option>
                    <option value="6">QM (every month)</option>
                    <option value="7">QOM (every other month)</option>
                    <option value="8">QH (every hour)</option>
                    <option value="9">AC (before meals)</option>
                    <option value="10">PC (after meals)</option>
                    <option value="11">PRN (as needed)</option>
                    <option value="12">Other</option>
                  </select>
                </div>
              </div>
              <div className="col-md-4">
                <div className="form-group">
                  <label htmlFor="dose-form">Dose Form</label>
                  <select
                    id="dose-form"
                    className="form-select"
                    value={newMedication.dose_form}
                    onChange={(e) => setNewMedication(prev => ({ ...prev, dose_form: e.target.value }))}
                    required
                  >
                    <option value="1">Tablet</option>
                    <option value="2">Capsule</option>
                    <option value="3">Ointment</option>
                    <option value="4">Suppository</option>
                    <option value="5">Aerosol</option>
                    <option value="6">Spray</option>
                    <option value="7">Suspension</option>
                    <option value="8">Patch</option>
                    <option value="9">Gas</option>
                    <option value="10">Gel</option>
                    <option value="11">Cream</option>
                    <option value="12">Powder</option>
                    <option value="13">Implant</option>
                    <option value="14">Chewable</option>
                    <option value="15">Liquid</option>
                    <option value="99">Other</option>
                  </select>
                </div>
              </div>
              <div className="col-md-4">
                <div className="form-group">
                  <label htmlFor="route">Route</label>
                  <select
                    id="route"
                    className="form-select"
                    value={newMedication.route}
                    onChange={(e) => setNewMedication(prev => ({ ...prev, route: e.target.value }))}
                    required
                  >
                    <option value="1">Oral</option>
                    <option value="2">Topical</option>
                    <option value="3">Subcutaneous</option>
                    <option value="4">Intradermal</option>
                    <option value="5">Transdermal</option>
                    <option value="6">Intraocular</option>
                    <option value="7">Intramuscular</option>
                    <option value="8">Inhalation</option>
                    <option value="9">Intravenous</option>
                    <option value="10">Intraperitoneal</option>
                    <option value="11">Nasal</option>
                    <option value="12">Vaginal</option>
                    <option value="13">Rectal</option>
                    <option value="14">Other</option>
                  </select>
                </div>
              </div>
              </div>
            </div>

            <div className="medication-form-section">
              <div className="section-header">
                <Calendar size={16} />
                <h3>Timing Information</h3>
              </div>
              
              <div className="row">
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="start-date">Start Date</label>
                  <input
                    type="date"
                    id="start-date"
                    className="form-control"
                    value={newMedication.start_date}
                    onChange={(e) => setNewMedication(prev => ({ ...prev, start_date: e.target.value }))}
                    required
                  />
                </div>
              </div>
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="end-date">End Date (Optional)</label>
                  <input
                    type="date"
                    id="end-date"
                    className="form-control"
                    value={newMedication.end_date || ""}
                    onChange={(e) => setNewMedication(prev => ({ ...prev, end_date: e.target.value }))}
                  />
                </div>
              </div>
              </div>
            </div>

            <div className="medication-form-section">
              <div className="section-header">
                <Activity size={16} />
                <h3>Status Information</h3>
              </div>
              
              <div className="status-toggle-row">
                <div className="toggle-switch-container">
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={newMedication.is_baseline}
                      onChange={(e) => setNewMedication(prev => ({ ...prev, is_baseline: e.target.checked }))}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                  <span className="toggle-label">Baseline</span>
                </div>
                
                <div className="toggle-switch-container">
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={newMedication.is_continuing}
                      onChange={(e) => setNewMedication(prev => ({ ...prev, is_continuing: e.target.checked }))}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                  <span className="toggle-label">Continuing</span>
                </div>
              </div>
            </div>
          </div>

          <div className="add-medication-modal-footer">
            <button type="button" className="cancel-btn" onClick={onClose}>
              <X size={16} /> Cancel
            </button>
            <button type="submit" className="save-medication-btn">
              <Save size={16} /> Save Medication
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddMedicationModal;
