

import './burger.css';

const Checkbox = () => {
  return (
    
      <label className="popup">
        <input type="checkbox" />
        <div className="burger" tabIndex={0}>
          <span />
          <span />
          <span />
        </div>
        <nav className="popup-window">
          <legend>Actions</legend>
          <ul>
            <li>
              <button>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="16" height="16">
  <path fill="#F44336" d="M40 10H28V6c0-2.2-1.8-4-4-4H8C5.8 2 4 3.8 4 6v36c0 2.2 1.8 4 4 4h32c2.2 0 4-1.8 4-4V14c0-2.2-1.8-4-4-4z"/>
  <path fill="#FFF" d="M31 10h9L31 1v9zM12 22h24v2H12zm0 6h24v2H12zm0 6h24v2H12z"/>
</svg>
                <span className="">Export to PDF</span>
              </button>
            </li>
            <li>
              <button>
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" width="16" height="16">
  <path fill="#4CAF50" d="M40 10H28V6c0-2.2-1.8-4-4-4H8C5.8 2 4 3.8 4 6v36c0 2.2 1.8 4 4 4h32c2.2 0 4-1.8 4-4V14c0-2.2-1.8-4-4-4z"/>
  <path fill="#FFF" d="M31 10h9L31 1v9zM12 22h24v2H12zm0 6h24v2H12zm0 6h24v2H12z"/>
  <path fill="#2E7D32" d="M20 22h-4v4h4v-4zm0 6h-4v4h4v-4zm0 6h-4v4h4v-4zm6-12h-4v4h4v-4zm0 6h-4v4h4v-4zm0 6h-4v4h4v-4zm6-12h-4v4h4v-4zm0 6h-4v4h4v-4zm0 6h-4v4h4v-4z"/>
</svg>
                <span className="">Export to Excel</span>
              </button>
            </li>
            
          </ul>
        </nav>
      </label>
    
  );
}


export default Checkbox;
