import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  getAllStudies,
  getStudyByUuid,
  createStudy,
  updateStudy,
  patchStudy,
  deleteStudy,
  getAllVisits,
  getVisitByUuid,
  getVisitsByStudy,
  updateVisit,
  patchVisit,
  deleteVisit,
  getPatientsPerStudy,
  getPatientsPerStatus,
  createVisit,
  createVisitTemplate,  
  getVisitTemplates,
  getVisitTemplatesByStudy,
  getVisitTemplateByUuid,
  getStudyEnrollmentsByPatient,
  getVisitsByEnrollment,
  updateEnrollmentStatus,
  getStudyTeamMembers,
  getStudyEnrollmentsByStudy,
  createPatientEnrollment,
  createRescheduleRequest,
  getRescheduleRequestsByPatient,
  acceptRescheduleRequest,
  rejectRescheduleRequest,
  getAcceptedRescheduleRequestsByPatient,
  getPendingRescheduleRequestsByPatient,
  getRejectedRescheduleRequestsByPatient,
  updateVisitTemplate,
  deleteVisitTemplate,
  getStudiesBySponsor,
  createStudyInvitation,
  getStudyInvitationsByDepartment,
  getStudiesByDepartment,
  getStudyInvitationsByInviter
} from "@/services/api/study.service";
import { STUDY_KEYS, VISIT_KEYS } from "./keys";
import type { Study, Visit, PatientEnrollment } from "@/store/scheduleEventState";
import { RescheduleRequest } from '@/types/study';

// Extend Visit type to include enrollment_uuid
interface ExtendedVisit extends Partial<Visit> {
  enrollment_uuid?: string;
}

// Study query hooks
export const useStudiesQuery = () => {
  return useQuery({
    queryKey: [STUDY_KEYS.GET_ALL],
    queryFn: getAllStudies,
  });
};

export const useStudiesBySponsorQuery = (identifier: string) => {
  return useQuery({
    queryKey: [STUDY_KEYS.GET_BY_SPONSOR, identifier],
    queryFn: () => getStudiesBySponsor(identifier),
    enabled: !!identifier,
  });
};

export const usePatientsPerStudyQuery = () => {
  return useQuery({
    queryKey: [STUDY_KEYS.GET_PATIENTS_PER_STUDY],
    queryFn: getPatientsPerStudy,
  });
};

export const usePatientsPerStatusQuery = (studyUuid: string) => {
  return useQuery({
    queryKey: [STUDY_KEYS.GET_PATIENTS_PER_STATUS, studyUuid],
    queryFn: () => getPatientsPerStatus(studyUuid),
    enabled: !!studyUuid,
  });
};

export const useStudyQuery = (uuid: string) => {
  return useQuery({
    queryKey: [STUDY_KEYS.GET_BY_UUID, uuid],
    queryFn: () => getStudyByUuid(uuid),
    enabled: !!uuid,
  });
};

export const useStudyEnrollmentsByPatientQuery = (patientUuid: string) => {
  return useQuery({
    queryKey: [STUDY_KEYS.GET_PATIENT_ENROLLMENTS, patientUuid],
    queryFn: () => getStudyEnrollmentsByPatient(patientUuid),
    enabled: !!patientUuid,
  });
};

export const useStudyEnrollmentsByStudyQuery = (studyUuid: string) => {
  return useQuery({
    queryKey: [STUDY_KEYS.GET_PATIENT_ENROLLMENTS, studyUuid],
    queryFn: () => getStudyEnrollmentsByStudy(studyUuid),
    enabled: !!studyUuid,
  });
};

export const useCreateStudyMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (studyData: Partial<Study>) => createStudy(studyData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_BY_SPONSOR] });
    },
  });
};

export const useUpdateStudyMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: Partial<Study> }) =>
      updateStudy(uuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_BY_SPONSOR] });
    },
  });
};

export const usePatchStudyMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: Partial<Study> }) =>
      patchStudy(uuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_ALL] });
    },
  });
};

export const useDeleteStudyMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (uuid: string) => deleteStudy(uuid),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_BY_SPONSOR] });
    },
  });
};

// Visit query hooks
export const useVisitsQuery = () => {
  return useQuery({
    queryKey: [VISIT_KEYS.GET_ALL],
    queryFn: getAllVisits,
  });
};

export const useVisitQuery = (uuid: string) => {
  return useQuery({
    queryKey: [VISIT_KEYS.GET_BY_UUID, uuid],
    queryFn: () => getVisitByUuid(uuid),
    enabled: !!uuid,
  });
};

export const useVisitsByStudyQuery = (studyUuid: string) => {
  return useQuery({
    queryKey: [VISIT_KEYS.GET_BY_STUDY, studyUuid],
    queryFn: () => getVisitsByStudy(studyUuid),
    enabled: !!studyUuid,
  });
};

export const useVisitsByEnrollmentQuery = (enrollmentUuid: string, options?: { enabled?: boolean }) => {
  return useQuery({
    queryKey: [VISIT_KEYS.GET_BY_ENROLLMENT, enrollmentUuid],
    queryFn: () => getVisitsByEnrollment(enrollmentUuid),
    enabled: options?.enabled !== false && !!enrollmentUuid,
  });
};

export const useCreateVisitMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (visitData: ExtendedVisit) => createVisit(visitData),
    onSuccess: (_, visitData) => {
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_ALL] });
      if (visitData.study_uuid) {
        queryClient.invalidateQueries({ 
          queryKey: [VISIT_KEYS.GET_BY_STUDY, visitData.study_uuid] 
        });
      }
      if (visitData.enrollment_uuid) {
        queryClient.invalidateQueries({
          queryKey: [VISIT_KEYS.GET_BY_ENROLLMENT, visitData.enrollment_uuid]
        });
      }
    },
  });
};

export const useUpdateVisitMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: Partial<Visit> }) =>
      updateVisit(uuid, data),
    onSuccess: (_, { data }) => {
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_BY_UUID, data.id] });
      if (data.study_uuid) {
        queryClient.invalidateQueries({ 
          queryKey: [VISIT_KEYS.GET_BY_STUDY, data.study_uuid] 
        });
      }
    },
  });
};

export const usePatchVisitMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: Partial<Visit> }) =>
      patchVisit(uuid, data),
    onSuccess: (_, { data }) => {
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_BY_UUID, data.id] });
      if (data.study_uuid) {
        queryClient.invalidateQueries({ 
          queryKey: [VISIT_KEYS.GET_BY_STUDY, data.study_uuid] 
        });
      }
    },
  });
};

export const useDeleteVisitMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (uuid: string) => deleteVisit(uuid),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_ALL] });
    },
  });
};

// Visit Template query hooks
export const useVisitTemplatesQuery = () => {
  return useQuery({
    queryKey: [VISIT_KEYS.GET_TEMPLATES],
    queryFn: getVisitTemplates,
  });
};

export const useVisitTemplatesByStudyQuery = (studyUuid: string) => {
  return useQuery({
    queryKey: [VISIT_KEYS.GET_TEMPLATES_BY_STUDY, studyUuid],
    queryFn: () => getVisitTemplatesByStudy(studyUuid),
    enabled: !!studyUuid,
  });
};

export const useVisitTemplateQuery = (uuid: string) => {
  return useQuery({
    queryKey: [VISIT_KEYS.GET_TEMPLATE_BY_UUID, uuid],
    queryFn: () => getVisitTemplateByUuid(uuid),
    enabled: !!uuid,
  });
};

export const useCreateVisitTemplateMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (visitData: Partial<Visit>) => createVisitTemplate(visitData),
    onSuccess: (_, visitData) => {
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_TEMPLATES] });
      if (visitData.study_uuid) {
        queryClient.invalidateQueries({ 
          queryKey: [VISIT_KEYS.GET_TEMPLATES_BY_STUDY, visitData.study_uuid] 
        });
      }
    },
  });
};

export const useUpdateVisitTemplateMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: Partial<Visit> }) =>
      updateVisitTemplate(uuid, data),
    onSuccess: (_, { data }) => {
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_TEMPLATES] });
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_TEMPLATE_BY_UUID, data.uuid] });
      if (data.study_uuid) {
        queryClient.invalidateQueries({ 
          queryKey: [VISIT_KEYS.GET_TEMPLATES_BY_STUDY, data.study_uuid] 
        });
      }
    },
  });
};

export const useDeleteVisitTemplateMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (uuid: string) => deleteVisitTemplate(uuid),
    onSuccess: (_, uuid) => {
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_TEMPLATES] });
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_TEMPLATE_BY_UUID, uuid] });
      queryClient.invalidateQueries({ queryKey: [VISIT_KEYS.GET_TEMPLATES_BY_STUDY] });
    },
  });
};

// Enrollment status mutation
export const useUpdateEnrollmentStatusMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ enrollmentUuid, statusData }: { enrollmentUuid: string; statusData: { status: string } }) =>
      updateEnrollmentStatus(enrollmentUuid, statusData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_PATIENT_ENROLLMENTS] });
    },
  });
};

// Patient enrollment mutation
export const usePatientEnrollmentsMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (enrollment: PatientEnrollment) => createPatientEnrollment(enrollment),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [STUDY_KEYS.GET_PATIENT_ENROLLMENTS] });
    },
  });
};

// Reschedule request mutations
export const useCreateRescheduleRequestMutation = () => {
  return useMutation({
    mutationFn: (data: RescheduleRequest) => createRescheduleRequest(data),
  });
};

export const useRescheduleRequestsByPatientQuery = (patientUuid: string) => {
  return useQuery({
    queryKey: ['rescheduleRequests', patientUuid],
    queryFn: () => getRescheduleRequestsByPatient(patientUuid),
    enabled: !!patientUuid,
  });
};

export const useAcceptRescheduleRequestMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (uuid: string) => acceptRescheduleRequest(uuid),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rescheduleRequests'] });
    },
  });
};

export const useRejectRescheduleRequestMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (uuid: string) => rejectRescheduleRequest(uuid),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['rescheduleRequests'] });
    },
  });
};

export function useAcceptedRescheduleRequestsByPatientQuery(patientUuid: string) {
  return useQuery({
    queryKey: ['acceptedRescheduleRequests', patientUuid],
    queryFn: () => getAcceptedRescheduleRequestsByPatient(patientUuid),
    enabled: !!patientUuid,
  });
}

export function usePendingRescheduleRequestsByPatientQuery(patientUuid: string) {
  return useQuery({
    queryKey: ['pendingRescheduleRequests', patientUuid],
    queryFn: () => getPendingRescheduleRequestsByPatient(patientUuid),
    enabled: !!patientUuid,
  });
}

export function useRejectedRescheduleRequestsByPatientQuery(patientUuid: string) {
  return useQuery({
    queryKey: ['rejectedRescheduleRequests', patientUuid],
    queryFn: () => getRejectedRescheduleRequestsByPatient(patientUuid),
    enabled: !!patientUuid,
  });
}

export const useStudyTeamMembersQuery = (studyUuid: string) => {
  return useQuery({
    queryKey: [STUDY_KEYS.GET_TEAM_MEMBERS, studyUuid],
    queryFn: () => getStudyTeamMembers(studyUuid),
    enabled: !!studyUuid,
  });
};

export const useCreateStudyInvitationMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: createStudyInvitation,
    onSuccess: () => {
      // Invalidate and refetch studies query
      queryClient.invalidateQueries({ queryKey: ['studiesBySponsor'] });
    },
  });
};

export const useStudyInvitationsByDepartmentQuery = (departmentUuid: string) => {
  return useQuery({
    queryKey: [STUDY_KEYS.GET_INVITATIONS_BY_DEPARTMENT, departmentUuid],
    queryFn: () => getStudyInvitationsByDepartment(departmentUuid),
    enabled: !!departmentUuid,
  });
};

export const useStudiesByDepartmentQuery = (departmentUuid: string) => {
  return useQuery({
    queryKey: [STUDY_KEYS.GET_BY_DEPARTMENT, departmentUuid],
    queryFn: () => getStudiesByDepartment(departmentUuid),
    enabled: !!departmentUuid,
  });
};

export const useStudyInvitationsByInviterQuery = (inviterIdentifier: string) => {
  return useQuery({
    queryKey: [STUDY_KEYS.GET_INVITATIONS_BY_DEPARTMENT, inviterIdentifier],
    queryFn: () => getStudyInvitationsByInviter(inviterIdentifier),
    enabled: !!inviterIdentifier,
  });
};