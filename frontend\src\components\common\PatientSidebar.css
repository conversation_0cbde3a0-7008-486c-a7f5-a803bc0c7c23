/* Patient Sidebar - Base Styles */
.patient-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 1000;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

/* Collapsed State */
.patient-sidebar.collapsed {
  width: 60px;
  background-color: #201E43; /* Dark blue background from color palette */
  border-radius: 0 8px 8px 0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

/* Expanded State */
.patient-sidebar:not(.collapsed) {
  width: 240px;
  background-color: #201E43; /* Dark blue background from color palette */
  color: #EEEEEE; /* Light text color from color palette */
  border-right: 1px solid #134B70;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

/* Toggle Button */
.patient-toggle-button {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #EEEEEE;
  background-color: #134B70;
  border-radius: 4px;
  position: absolute;
  border: 1px solid #508C9B;
  top: 10px;
  right: -5px;
  margin-right: 10px;
  z-index: 1001;
}

.patient-sidebar:not(.collapsed) .patient-toggle-button {
  color: #EEEEEE;
  background-color: #134B70;
  border: 1px solid #508C9B;
}

/* User Avatar Styles */
.user-avatar-collapsed {
  width: 40px;
  height: 40px;
  background-color: #508C9B;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 15px auto 25px;
  color: #EEEEEE;
}

.user-avatar-expanded {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 15px;
  margin-bottom: 20px;
  border-bottom: 1px solid #134B70;
}

.avatar-circle {
  width: 40px;
  height: 40px;
  background-color: #508C9B;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  /* margin-right: 10px; */
  color: #EEEEEE;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  color: #EEEEEE;
  font-size: 14px;
}

/* Icons Container - Collapsed State */
.patient-icons-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 50px;
  height: 100%;
}

.patient-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: #EEEEEE;
  cursor: pointer;
  position: relative;
  border-radius: 4px;
}

.patient-icon.active {
  background-color: #508C9B;
}

.patient-icon:hover {
  background-color: #134B70;
}

.patient-icon.logout {
  margin-top: auto;
  margin-bottom: 30px;
  color: #EEEEEE;
  background-color: rgba(255, 107, 107, 0.2);
}

.patient-icon.logout:hover {
  background-color: rgba(255, 107, 107, 0.4);
}

/* Content Container - Expanded State */
.patient-content-container {
  padding: 50px 15px 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Menu Items */
.patient-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  cursor: pointer;
  color: #EEEEEE;
  font-size: 14px;
  margin-bottom: 5px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.patient-menu-item:hover {
  background-color: #134B70;
}

.patient-menu-item.active {
  background-color: #508C9B;
  color: #EEEEEE;
  font-weight: 600;
}

.patient-menu-item.logout {
  margin-top: auto;
  color: #EEEEEE;
  background-color: rgba(255, 107, 107, 0.2);
}

.patient-menu-item.logout:hover {
  background-color: rgba(255, 107, 107, 0.4);
}

/* Notification Badge */
.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ff6b6b;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
}

.menu-icon-wrapper {
  position: relative;
}

/* Mobile Adjustments */
@media (max-width: 991px) {
  .patient-sidebar {
    top: 0;
    height: 100vh;
    transform: none;
  }
  
  .patient-sidebar.collapsed {
    width: 40px;
  }
  
  .patient-sidebar:not(.collapsed) {
    width: 220px;
  }

  .patient-sidebar.mobile.collapsed {
    transform: translateX(0);
  }

  .patient-sidebar.mobile:not(.collapsed) {
    transform: translateX(0);
  }
}

/* Small mobile screens */
@media (max-width: 576px) {
  .patient-toggle-button {
    width: 24px;
    height: 24px;
    right: -12px;
  }

  .patient-icon {
    width: 34px;
    height: 34px;
    margin-bottom: 15px;
  }

  .patient-menu-item {
    padding: 10px 12px;
    font-size: 13px;
  }
}
