import React, { useState, useEffect } from "react";
import { X, Save, Clipboard } from "lucide-react";
import "../modal/EditVisitModal.css"; // Reusing the same CSS

interface EditEnrollmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedEnrollment: { status: string }) => void;
  enrollmentData: {
    uuid: string;
    study_name: string;
    study_status: string;
  };
}

const EditEnrollmentModal: React.FC<EditEnrollmentModalProps> = ({
  isOpen,
  onClose,
  onSave,
  enrollmentData,
}) => {
  const [status, setStatus] = useState(enrollmentData.study_status || "");

  // Update state when the modal opens with new data
  useEffect(() => {
    if (isOpen) {
      setStatus(enrollmentData.study_status || "");
    }
  }, [isOpen, enrollmentData]);

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave({
      status: status,
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="edit-visit-modal-overlay">
      <div className="edit-visit-modal">
        <div className="edit-visit-modal-header">
          <h2 className="edit-visit-modal-title">
            <Clipboard size={20} className="modal-icon" /> Edit Enrollment Status: {enrollmentData.study_name}
          </h2>
          <button
            type="button"
            className="edit-visit-modal-close"
            onClick={onClose}
            aria-label="Close"
          >
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="edit-visit-modal-body">
            <div className="form-group">
              <label htmlFor="enrollment-status">Status</label>
              <select
                id="enrollment-status"
                className="form-select"
                value={status}
                onChange={(e) => setStatus(e.target.value)}
                required
              >
                <option value="" disabled>Select a status</option>
                <option value="Accepted to participate">Accepted to participate</option>
                <option value="Contacted">Contacted</option>
                <option value="PIS Sent">PIS Sent</option>
                <option value="Unable to contact">Unable to contact</option>
                <option value="To be called by doctor">To be called by doctor</option>
                <option value="Decline participation">Decline participation</option>
                <option value="Booked for screening">Booked for screening</option>
                <option value="Screen Failure">Screen Failure</option>
                <option value="Enrolled in study">Enrolled in study</option>
                <option value="Withdrawn">Withdrawn</option>
                <option value="Completed">Completed</option>
                <option value="Not eligible">Not eligible</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>

          <div className="edit-visit-modal-footer">
            <button type="button" className="cancel-btn" onClick={onClose}>
              <X size={16} /> Cancel
            </button>
            <button type="submit" className="save-btn">
              <Save size={16} /> Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditEnrollmentModal;