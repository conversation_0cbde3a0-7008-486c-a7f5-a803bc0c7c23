/* Create Consent Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.create-consent-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Progress Bar */
.progress-bar {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px 32px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.progress-step {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin: 0 8px;
  transition: all 0.3s ease;
  position: relative;
}

.progress-step.active {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
  transform: scale(1.1);
}

.progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  right: -16px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 2px;
  background-color: #e5e7eb;
}

.progress-step.active::after {
  background-color: #37b7c3;
}

/* Modal Content */
.modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
}

.step-content h3 {
  margin: 0 0 24px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.step-description {
  color: #6b7280;
  margin-bottom: 24px;
  line-height: 1.6;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #37b7c3;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
  border-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

/* Add Question Form */
.add-question-form {
  background-color: #f9fafb;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

/* Standard Checkbox Styles */
.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  padding: 8px 0;
  user-select: none;
}

.standard-checkbox {
  width: 18px;
  height: 18px;
  margin: 0;
  cursor: pointer;
  accent-color: #37b7c3;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-text {
  font-size: 14px;
  color: #374151;
  line-height: 1.5;
  margin: 0;
}

.checkbox-label:hover .checkbox-text {
  color: #37b7c3;
}

.add-question-btn {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.add-question-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

/* Questions List */
.questions-list {
  margin-top: 24px;
}

.questions-list h4 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
}

.question-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 16px;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.question-item:hover {
  border-color: #37b7c3;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.1);
}

.question-content {
  flex: 1;
}

.question-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.question-number {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.required-badge {
  background-color: #ef4444;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.question-text {
  margin: 0;
  color: #374151;
  line-height: 1.5;
}

.remove-question-btn {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
  margin-left: 12px;
}

.remove-question-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

.no-questions {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 24px;
  background-color: #f9fafb;
  border-radius: 8px;
  border: 2px dashed #d1d5db;
}

/* Review Section */
.review-section {
  background-color: #f9fafb;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
}

.review-section h4 {
  margin: 0 0 16px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
}

.review-item {
  margin-bottom: 12px;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.review-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.warning-section {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  color: #92400e;
}

.warning-section strong {
  color: #92400e;
}

.error-alert {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #fef2f2;
  border: 1px solid #ef4444;
  border-radius: 8px;
  color: #dc2626;
  margin-top: 16px;
}

/* Modal Footer */
.modal-footer {
  padding: 24px 32px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.footer-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
  font-size: 14px;
}

.btn-primary {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
  transform: translateY(-1px);
}

.btn-success {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.btn-success:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .create-consent-modal {
    margin: 10px;
    max-height: 95vh;
  }

  .modal-header,
  .modal-content,
  .modal-footer {
    padding: 16px 20px;
  }

  .progress-bar {
    padding: 16px 20px;
  }

  .progress-step {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .footer-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}