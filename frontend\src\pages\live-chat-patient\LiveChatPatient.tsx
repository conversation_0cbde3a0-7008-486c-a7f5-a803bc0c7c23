import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Search, MessageCircle } from 'lucide-react';
import './LiveChatPatient.css';
import { useLiveChatsQuery } from '@/hooks/livechat.query';
import StartLiveChatModal from '@/components/modal/StartLiveChatModal';
import type { LiveChat } from '@/services/api/livechat.types';

const LiveChatPatient: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const navigate = useNavigate();

  const { data: chatsData, isLoading, refetch } = useLiveChatsQuery({
    ordering: '-last_message_at'
  });

  const chats = chatsData?.results || [];

  // Filter chats based on search term
  const filteredChats = chats.filter((chat: LiveChat) =>
    chat.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chat.department.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleNewChatSuccess = (chatUuid: string) => {
    // Refresh the chat list
    refetch();
    // Navigate to the new chat conversation
    navigate(`/patient/live-chat/${chatUuid}`);
  };

  const handleChatClick = (chatUuid: string) => {
    navigate(`/patient/live-chat/${chatUuid}`);
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `${diffInMinutes}m`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d`;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return '#10b981'; // green
      case 'PENDING':
        return '#f59e0b'; // yellow
      case 'CLOSED':
        return '#6b7280'; // gray
      default:
        return '#6b7280';
    }
  };

  if (isLoading) {
    return (
      <div className="live-chat-container">
        <div className="live-chat-header">
          <h1>Messages</h1>
        </div>
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>Loading conversations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="live-chat-container">
      <div className="live-chat-header">
        <h1>Messages</h1>
      </div>

      <div className="search-container">
        <div className="search-input-wrapper">
          <Search size={20} className="search-icon" />
          <input
            type="text"
            placeholder="search"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>
      </div>

      <div className="ai-agent-section">
        <div className="ai-agent-item">
          <div className="ai-agent-icon">
            <MessageCircle size={24} />
          </div>
          <span>Talk to our AI Agent</span>
        </div>
      </div>

      <div className="chat-list">
        {filteredChats.length === 0 ? (
          <div className="empty-state">
            <MessageCircle size={48} className="empty-icon" />
            <p>No conversations yet</p>
            <p className="empty-subtitle">Start a new conversation with a department</p>
          </div>
        ) : (
          filteredChats.map((chat: LiveChat) => (
            <div
              key={chat.uuid}
              className="chat-item clickable"
              onClick={() => handleChatClick(chat.uuid)}
            >
              <div className="chat-avatar">
                <div className="avatar-circle">
                  {chat.department.name.charAt(0)}
                </div>
              </div>
              <div className="chat-content">
                <div className="chat-header">
                <div className="department-tag">

                    {chat.department.name}
                    <span
                      className="status-dot"
                      style={{ backgroundColor: getStatusColor(chat.status) }}
                    ></span>
                  </div>
                  <div className="chat-time">
                    {chat.last_message_at ? formatTime(chat.last_message_at) : formatTime(chat.created_at)}
                  </div>
                </div>
                <div className="chat-message">
                  {chat.last_message?.content || 'No messages yet'}
                </div>
                <div className="chat-footer">
                  {/* <div className="department-tag">
                    <span
                      className="status-dot"
                      style={{ backgroundColor: getStatusColor(chat.status) }}
                    ></span>
                    {chat.department.name}
                  </div> */}
                  {chat.unread_count > 0 && (
                    <div className="unread-badge">{chat.unread_count}</div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="start-conversation-container">
        <button
          className="start-conversation-btn"
          onClick={() => setIsModalOpen(true)}
        >
          Start new conversation
        </button>
      </div>

      <StartLiveChatModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleNewChatSuccess}
      />
    </div>
  );
};

export default LiveChatPatient;
