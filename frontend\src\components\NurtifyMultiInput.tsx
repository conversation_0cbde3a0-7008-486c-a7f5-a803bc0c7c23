import { useState, ChangeEvent, useEffect } from "react";
import "./styles.css";

interface InputField {
    name: string;
    label: string;
}

interface NurtifyMultiInputProps {
    inputs: InputField[];
    values: string[];
    onChange: (index: number, event: ChangeEvent<HTMLInputElement>) => void;
}

const NurtifyMultiInput: React.FC<NurtifyMultiInputProps> = ({ inputs, values, onChange }) => {
    const [inputValues, setInputValues] = useState<string[]>(values);
    
    // Update local state when values prop changes
    useEffect(() => {
        setInputValues(values);
    }, [values]);

    const handleChange = (index: number) => (e: ChangeEvent<HTMLInputElement>) => {
        const newValues = [...inputValues];
        newValues[index] = e.target.value;
        setInputValues(newValues);
        onChange(index, e);
    };

    return (
        <div className="nurtify-multi-input">
            <table className="table" style={{
                width: '100%',
                borderCollapse: 'collapse',
                border: "1px solid #09005DA3",
                borderRadius: "8px"
            }}>
                <tbody>
                    {inputs.map((input, index) => (
                        <tr key={input.name}>
                            <td style={{
                                borderRight: "2px solid #09005DA3",
                                borderBottom: index !== inputs.length - 1 ? "2px solid #09005DA3" : "none",
                                width: "200px",
                                backgroundColor: "#37B7C31F",
                                textAlign: "left",
                                paddingLeft: "10px"
                            }}>
                                <label htmlFor={input.name} style={{ fontSize: "14px", fontWeight: "500" }}>{input.label}</label>
                            </td>
                            <td style={{ 
                                borderBottom: index !== inputs.length - 1 ? "2px solid #09005DA3" : "none"
                            }}>
                                <input
                                    style={{ 
                                        width: "100%",
                                        paddingLeft: "12px",
                                        fontSize: "15px",
                                    }}
                                    type="text"
                                    id={input.name}
                                    name={input.name}
                                    value={inputValues[index]}
                                    onChange={handleChange(index)}
                                    placeholder="write your text here"
                                />
                            </td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
}

export default NurtifyMultiInput;
