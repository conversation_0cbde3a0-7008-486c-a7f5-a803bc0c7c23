import React, { useState, useEffect, useCallback } from 'react';
import {
  ChevronLeft,
  ChevronRight,
  Bell,
  MessageSquare,
  BookOpen,
  Book,
  Calendar,
  HelpCircle,
  LogOut,
  AlertCircle,
  Signature,
  Search,
  User,
  FileText,
  MessageCircle
} from 'lucide-react';
import './PatientSidebar.css';
import { useCurrentUserQuery } from "@/hooks/user.query";
import { useKeycloak } from "@react-keycloak/web";

type PatientSidebarProps = {
  onTabChange?: (tab: string) => void;
  currentTab?: string;
  notificationCount?: number;
};

const PatientSidebar: React.FC<PatientSidebarProps> = ({
  onTabChange,
  currentTab = 'report',
  notificationCount = 0
}) => {
  const { keycloak } = useKeycloak();

  // State for collapsed sidebar
  const { data: currentUser } = useCurrentUserQuery();
  const [isCollapsed, setIsCollapsed] = useState(() => {
    const stored = localStorage.getItem('patientSidebarCollapsed') === 'true';
    return stored || window.innerWidth <= 991;
  });

  const [activeTab, setActiveTab] = useState(currentTab);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 991);

  const handleResize = useCallback(() => {
    setIsMobile(window.innerWidth <= 991);
  }, []);

  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  useEffect(() => {
    localStorage.setItem('patientSidebarCollapsed', isCollapsed.toString());
  }, [isCollapsed]);

  useEffect(() => {
    if (currentTab) {
      setActiveTab(currentTab);
    }
  }, [currentTab]);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    if (onTabChange) {
      onTabChange(tab);
    }
  };


  const handleLogout = () => {
    keycloak.logout();
  };

  return (
    <div
      className={`patient-sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobile ? 'mobile' : ''}`}
      role="navigation"
      aria-label="Patient navigation"
    >
      <div className="patient-toggle-button" onClick={toggleSidebar}>
        {isCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
      </div>

      {isCollapsed ? (
        // Collapsed view - only icons
        <div className="patient-icons-container">
          <div className="user-avatar-collapsed">
            <User size={24} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'activities' ? 'active' : ''}`}
            onClick={() => handleTabChange('activities')}
          >
            <Bell size={20} />
            {notificationCount > 0 && <span className="notification-badge">{notificationCount}</span>}
          </div>
          <div
            className={`patient-icon ${activeTab === 'report' ? 'active' : ''}`}
            onClick={() => handleTabChange('report')}
          >
            <AlertCircle size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'conversations' ? 'active' : ''}`}
            onClick={() => handleTabChange('conversations')}
          >
            <MessageSquare size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'live-chat' ? 'active' : ''}`}
            onClick={() => handleTabChange('live-chat')}
          >
            <MessageCircle size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'studies' ? 'active' : ''}`}
            onClick={() => handleTabChange('studies')}
          >
            <BookOpen size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'diary' ? 'active' : ''}`}
            onClick={() => handleTabChange('diary')}
          >
            <Book size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'appointments' ? 'active' : ''}`}
            onClick={() => handleTabChange('appointments')}
          >
            <Calendar size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'resources' ? 'active' : ''}`}
            onClick={() => handleTabChange('resources')}
          >
            <HelpCircle size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'consent' ? 'active' : ''}`}
            onClick={() => handleTabChange('consent')}
          >
            <Signature size={20} />
          </div>
          <div
            className={`patient-icon ${activeTab === 'show-interest' ? 'active' : ''}`}
            onClick={() => handleTabChange('show-interest')}
          >
            <Search size={20} />
          </div>
          <div className="patient-icon logout" onClick={handleLogout}>
            <LogOut size={20} />
          </div>
        </div>
      ) : (
        // Expanded view - full content
        <div className="patient-content-container">
          <div className="user-avatar-expanded">
            <div className="avatar-circle">
              <User size={24} />
            </div>
            <div className="user-info">
              <div className="user-name">{currentUser?.first_name} {currentUser?.last_name}</div>
            </div>
          </div>
          <div
            className={`patient-menu-item ${activeTab === 'activities' ? 'active' : ''}`}
            onClick={() => handleTabChange('activities')}
          >
            <div className="menu-icon-wrapper">
              <Bell size={18} />
              {notificationCount > 0 && <span className="notification-badge">{notificationCount}</span>}
            </div>
            <span>Recommended Activities</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'report' ? 'active' : ''}`}
            onClick={() => handleTabChange('report')}
          >
            <AlertCircle size={18} />
            <span>Report to Clinical Team</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'conversations' ? 'active' : ''}`}
            onClick={() => handleTabChange('conversations')}
          >
            <MessageSquare size={18} />
            <span>My Conversations</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'live-chat' ? 'active' : ''}`}
            onClick={() => handleTabChange('live-chat')}
          >
            <MessageCircle size={18} />
            <span>Chat with Department</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'my-forms' ? 'active' : ''}`}
            onClick={() => handleTabChange('my-forms')}
          >
            <FileText size={18} />
            <span>My Forms</span>
          </div>
          <div
            className={`patient-menu-item ${activeTab === 'studies' ? 'active' : ''}`}
            onClick={() => handleTabChange('studies')}
          >
            <BookOpen size={18} />
            <span>My Studies</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'diary' ? 'active' : ''}`}
            onClick={() => handleTabChange('diary')}
          >
            <Book size={18} />
            <span>My Diary</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'appointments' ? 'active' : ''}`}
            onClick={() => handleTabChange('appointments')}
          >
            <Calendar size={18} />
            <span>My Appointments</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'resources' ? 'active' : ''}`}
            onClick={() => handleTabChange('resources')}
          >
            <HelpCircle size={18} />
            <span>Resources</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'consent' ? 'active' : ''}`}
            onClick={() => handleTabChange('consent')}
          >
            <Signature size={18} />
            <span>Consent</span>
          </div>

          <div
            className={`patient-menu-item ${activeTab === 'show-interest' ? 'active' : ''}`}
            onClick={() => handleTabChange('show-interest')}
          >
            <Search size={18} />
            <span>Show interest in study</span>
          </div>



          <div className="patient-menu-item logout" onClick={handleLogout}>
            <LogOut size={18} />
            <span>Log Out</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default PatientSidebar;
