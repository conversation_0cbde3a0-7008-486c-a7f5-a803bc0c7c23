.ai-helper {
  position: fixed;
  bottom: 90px;
  right: 20px;
  width: 60px;
  height: 60px;
  background-color: #ffffff;
  border-radius: 30px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 999;
}

.ai-helper.open {
  width: 350px;
  height: 500px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
}

.ai-helper-toggle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 60px;
  height: 60px;
  border-radius: 30px;
  background-color: #1a75a6;
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  z-index: 1000;
}

.ai-helper.open .ai-helper-toggle {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 30px;
  height: 30px;
  border-radius: 15px;
  z-index: 1002; /* Higher than other elements */
  background-color: rgba(255, 255, 255, 0.3); /* Semi-transparent background */
  box-shadow: none;
}

.ai-helper-header {
  display: none;
}

.ai-helper.open .ai-helper-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 45px 15px 20px; /* Added right padding to make room for close button */
  background-color: #1a75a6;
  color: white;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

.ai-helper-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.clear-chat-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.clear-chat-button:hover {
  opacity: 1;
}

.ai-helper-messages {
  display: none;
}

.ai-helper.open .ai-helper-messages {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #f7f7f7;
}

.message {
  display: flex;
  margin-bottom: 16px;
  max-width: 85%;
}

.user-message {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.ai-message {
  align-self: flex-start;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 16px;
  background-color: #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
}

.user-message .message-avatar {
  background-color: #1a75a6;
  color: white;
}

.ai-message .message-avatar {
  background-color: #7c504a;
  color: white;
}

.message-bubble {
  padding: 10px 14px;
  border-radius: 16px;
  position: relative;
}

.user-message .message-bubble {
  background-color: #1a75a6;
  color: white;
  border-top-right-radius: 2px;
}

.ai-message .message-bubble {
  background-color: #ffffff;
  color: #333;
  border-top-left-radius: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-text {
  font-size: 14px;
  line-height: 1.4;
  word-break: break-word; /* Make text wrap at max width */
  overflow-wrap: break-word;
  white-space: normal;
}

.message-time {
  font-size: 10px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 5px 0;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  background: #aaa;
  border-radius: 50%;
  display: inline-block;
  animation: typing 1.4s infinite both;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0% {
    transform: translateY(0);
  }
  28% {
    transform: translateY(-5px);
  }
  44% {
    transform: translateY(0);
  }
}

.ai-helper-input {
  display: none;
}

.ai-helper.open .ai-helper-input {
  display: flex;
  padding: 10px 15px;
  background-color: white;
  border-top: 1px solid #e0e0e0;
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}

.ai-helper-input input {
  flex: 1;
  border: none;
  background: none;
  padding: 8px 0;
  font-size: 14px;
  outline: none;
  width: calc(100% - 40px); /* Ensure input doesn't overflow */
}

.ai-helper-input button {
  background: none;
  border: none;
  color: #1a75a6;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.8;
  transition: opacity 0.2s ease;
}

.ai-helper-input button:hover {
  opacity: 1;
}

.ai-helper-input button:disabled {
  color: #ccc;
  cursor: not-allowed;
}

/* Mobile adjustments */
@media (max-width: 768px) {
  .ai-helper {
    bottom: 80px;
    right: 10px;
  }
  
  .ai-helper.open {
    width: calc(100vw - 20px);
    max-width: 350px;
    height: 450px;
  }
}

@media (max-width: 480px) {
  .ai-helper.open {
    height: 400px;
  }
  
  .message {
    max-width: 90%;
  }
}
