import { useQuery, useMutation } from '@tanstack/react-query';
import { contactService, Trial, PatientApplication, <PERSON><PERSON><PERSON><PERSON><PERSON>, DoctorReferralResponse, PatientApplicationResponse, PaginatedResponse } from '@/services/api/contact.service';

export const contactKeys = {
  all: ['contact'] as const,
  trials: {
    all: () => [...contactKeys.all, 'trials'] as const,
    byHospital: (hospitalUuid: string) => [...contactKeys.trials.all(), hospitalUuid] as const,
    active: () => [...contactKeys.trials.all(), 'active'] as const,
  },
  applications: {
    all: () => [...contactKeys.all, 'applications'] as const,
  },
  referrals: {
    all: () => [...contactKeys.all, 'referrals'] as const,
  },
};

export const useTrialsByHospitalQuery = (hospitalUuid: string) => {
  console.log('useTrialsByHospitalQuery called with hospitalUuid:', hospitalUuid);
  return useQuery<Trial[]>({
    queryKey: contactKeys.trials.byHospital(hospitalUuid),
    queryFn: async () => {
      console.log('Fetching trials for hospital:', hospitalUuid);
      const result = await contactService.getTrialsByHospital(hospitalUuid);
      console.log('Trials fetch result:', result);
      return result;
    },
    enabled: !!hospitalUuid,
  });
};

function isAxiosErrorWithMessage(error: unknown): error is { response: { data: { message: string } } } {
  if (
    typeof error === 'object' &&
    error !== null &&
    'response' in error
  ) {
    const response = (error as Record<string, unknown>).response;
    if (
      typeof response === 'object' &&
      response !== null &&
      'data' in response
    ) {
      const data = (response as Record<string, unknown>).data;
      if (
        typeof data === 'object' &&
        data !== null &&
        'message' in data &&
        typeof (data as Record<string, unknown>).message === 'string'
      ) {
        return true;
      }
    }
  }
  return false;
}

export const useSubmitPatientApplication = () => {
  return useMutation({
    mutationFn: (application: PatientApplication) => contactService.submitPatientApplication(application),
    onSuccess: () => {
      alert('Application submitted successfully! We will contact you soon.');
    },
    onError: (error: unknown) => {
      let errorMessage = 'Failed to submit application. Please try again.';
      if (isAxiosErrorWithMessage(error)) {
        errorMessage = error.response.data.message;
      }
      alert(errorMessage);
    },
  });
};

export const useSubmitDoctorReferral = () => {
  return useMutation({
    mutationFn: (referral: DoctorReferral) => contactService.submitDoctorReferral(referral),
    onSuccess: () => {
      alert('Referral submitted successfully! We will contact you soon.');
    },
    onError: (error: unknown) => {
      let errorMessage = 'Failed to submit referral. Please try again.';
      if (isAxiosErrorWithMessage(error)) {
        errorMessage = error.response.data.message;
      }
      alert(errorMessage);
    },
  });
};

export const useActiveTrialsQuery = () => {
  return useQuery<Trial[]>({
    queryKey: contactKeys.trials.active(),
    queryFn: () => contactService.getActiveTrials(),
  });
};

export const useDoctorReferralsQuery = () => {
  return useQuery<PaginatedResponse<DoctorReferralResponse>>({
    queryKey: contactKeys.referrals.all(),
    queryFn: () => contactService.getDoctorReferrals(),
  });
};

export const usePatientApplicationsQuery = () => {
  return useQuery<PaginatedResponse<PatientApplicationResponse>>({
    queryKey: contactKeys.applications.all(),
    queryFn: () => contactService.getPatientApplications(),
  });
}; 