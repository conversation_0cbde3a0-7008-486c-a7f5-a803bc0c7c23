.ask-main-content {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.ask-content-wrapper {
  flex: 1;
  padding: 40px 0;
}

.ask-dashboard__content {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Header Styles */
.ask-header {
  text-align: center;
  margin-bottom: 40px;
}

.ask-title {
  font-size: 2em;
  color: #333;
  margin-bottom: 10px;
}

.ask-subtitle {
  color: #666;
  font-size: 1.1em;
}

/* Cards Grid */
.ask-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
  padding: 20px;
}

/* Card Styles */
.ask-card {
  background: white;
  border: 2px solid #3dc6d6;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  background-color: #f0fcfc;
}

.ask-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(61, 198, 214, 0.2);
}

.ask-card-icon {
  color: #3dc6d6;
  font-size: 2em;
  margin-bottom: 15px;
  text-align: center;
}

.ask-card-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.ask-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ask-card-title {
  font-size: 1.2em;
  color: #333;
  margin: 0;
}

.ask-card-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.9em;
  font-weight: 500;
}

.ask-card-status.pending {
  background-color: #ffd700;
  color: #333;
}

.ask-card-status.inprogress {
  background-color: #3498db;
  color: white;
}

.ask-card-status.completed {
  background-color: #2ecc71;
  color: white;
}

.ask-card-status.rejected {
  background-color: #e74c3c;
  color: white;
}

.ask-card-details {
  font-size: 0.9em;
  color: #666;
}

.ask-card-details p {
  margin: 5px 0;
}

.ask-card-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.ask-card-button {
  flex: 1;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.ask-card-button.view {
  background-color: #e0f7f9;
  color: #3dc6d6;
}

.ask-card-button.respond {
  background-color: #3dc6d6;
  color: white;
}

.ask-card-button:hover {
  opacity: 0.9;
}

/* Loading and Empty States */
.loading-message,
.no-conversations {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 1.1em;
  grid-column: 1 / -1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .ask-dashboard__content {
    padding: 15px;
  }

  .ask-cards-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .ask-title {
    font-size: 1.5em;
  }

  .ask-subtitle {
    font-size: 1em;
  }
}

/* Tabs Styles */
.ask-tabs-wrapper {
  display: flex;
  gap: 2rem;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0;
  margin-bottom: 2rem;
}

.ask-tab-button {
  background: none;
  border: none;
  padding: 0.75rem 0.5rem;
  font-size: 1rem;
  color: #6b7280;
  position: relative;
  cursor: pointer;
  transition: color 0.3s ease;
}

.ask-tab-button:hover {
  color: #374151;
}

.ask-tab-button.active {
  color: #3dc6d6;
}

.ask-tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #3dc6d6;
}

.logs-container {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;
  border: 1px solid #e9ecef;
}

.conversation-logs {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.log-item {
  padding: 0.75rem;
  background-color: white;
  border-radius: 0.375rem;
  border: 1px solid #dee2e6;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.log-field {
  font-weight: 600;
  color: #495057;
}

.log-changes {
  color: #0d6efd;
}

.log-details {
  display: flex;
  justify-content: space-between;
  font-size: 0.875rem;
  color: #6c757d;
}

.log-user {
  font-style: italic;
}

.log-time {
  font-family: monospace;
}

.no-logs {
  text-align: center;
  color: #6c757d;
  font-style: italic;
  padding: 1rem;
}

.ask-card-button.logs {
  background-color: #6c757d;
  color: white;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.ask-card-button.logs:hover {
  background-color: #5a6268;
}
