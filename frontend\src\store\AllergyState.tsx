import { create } from 'zustand';


interface Allergy {
  name: string;
}


interface AllergyStore {
  allergies: Allergy[];
  addAllergy: (name: string) => void;
  removeAllergy: (index: number) => void; 
  clearAllergies: () => void;
}

export const useAllergyStore = create<AllergyStore>((set) => ({
  allergies: [], 

  
  addAllergy: (name) => {
    const newAllergy: Allergy = {
      name

    };
    set((state) => ({
      allergies: [...state.allergies, newAllergy],
    }));
  },


  removeAllergy: (index) => {
    set((state) => ({
      allergies: state.allergies.filter((_, i) => i !== index),
    }));
  },

  clearAllergies: () => {
    set({ allergies: [] });
  },
}));