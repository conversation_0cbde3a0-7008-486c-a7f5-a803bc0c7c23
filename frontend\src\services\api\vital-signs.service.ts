import api from "@/services/api";

// Types for Vital Signs
export interface VitalSign {
  uuid: string;
  patient: {
    uuid: string;
    first_name: string;
    last_name: string;
    nhs_number: string;
  };
  recorded_at: string;
  temperature?: number;
  temp_unit?: 'C' | 'F';
  temp_location?: string;
  heart_rate?: number;
  systolic_bp?: number;
  diastolic_bp?: number;
  respiratory_rate?: number;
  oxygen_saturation?: number;
  consciousness_level?: 'A' | 'V' | 'P' | 'U';
  supplemental_oxygen?: boolean;
  blood_sugar?: number;
  blood_sugar_unit?: 'mg/dL' | 'mmol/L';
  height?: number;
  height_unit?: 'cm' | 'in';
  weight?: number;
  weight_unit?: 'kg' | 'lbs';
  news_score: number;
  news_severity: 'Low' | 'Low-Medium' | 'Medium' | 'Medium-High' | 'High';
  notes?: string;
  created_by: {
    uuid: string;
    email: string;
    first_name: string;
    last_name: string;
  };
  created_at: string;
  updated_at: string;
}

export interface CreateVitalSignData {
  patient_id: string;
  temperature?: number;
  temp_unit?: 'C' | 'F';
  temp_location?: string;
  heart_rate?: number;
  systolic_bp?: number;
  diastolic_bp?: number;
  respiratory_rate?: number;
  oxygen_saturation?: number;
  consciousness_level?: 'A' | 'V' | 'P' | 'U';
  supplemental_oxygen?: boolean;
  blood_sugar?: number;
  blood_sugar_unit?: 'mg/dL' | 'mmol/L';
  height?: number;
  height_unit?: 'cm' | 'in';
  weight?: number;
  weight_unit?: 'kg' | 'lbs';
  notes?: string;
}

export interface UpdateVitalSignData {
  temperature?: number;
  temp_unit?: 'C' | 'F';
  temp_location?: string;
  heart_rate?: number;
  systolic_bp?: number;
  diastolic_bp?: number;
  respiratory_rate?: number;
  oxygen_saturation?: number;
  consciousness_level?: 'A' | 'V' | 'P' | 'U';
  supplemental_oxygen?: boolean;
  blood_sugar?: number;
  blood_sugar_unit?: 'mg/dL' | 'mmol/L';
  height?: number;
  height_unit?: 'cm' | 'in';
  weight?: number;
  weight_unit?: 'kg' | 'lbs';
  notes?: string;
}

export interface VitalSignsSummary {
  patient_id: string;
  total_records: number;
  latest_news_score: number;
  latest_news_severity: 'Low' | 'Low-Medium' | 'Medium' | 'Medium-High' | 'High';
  latest_recorded_at: string;
  latest_temperature?: number;
  latest_temp_unit?: 'C' | 'F';
  latest_temp_location?: string;
  latest_heart_rate?: number;
  latest_systolic_bp?: number;
  latest_diastolic_bp?: number;
  latest_respiratory_rate?: number;
  latest_oxygen_saturation?: number;
  latest_consciousness_level?: 'A' | 'V' | 'P' | 'U';
  latest_blood_sugar?: number;
  latest_blood_sugar_unit?: 'mg/dL' | 'mmol/L';
  latest_height?: number;
  latest_height_unit?: 'cm' | 'in';
  latest_weight?: number;
  latest_weight_unit?: 'kg' | 'lbs';
  temperature_trend: 'increasing' | 'decreasing' | 'stable';
  heart_rate_trend: 'increasing' | 'decreasing' | 'stable';
  blood_pressure_trend: 'increasing' | 'decreasing' | 'stable';
  news_score_trend: 'increasing' | 'decreasing' | 'stable';
}

export interface ChartDataPoint {
  recorded_at: string;
  temperature?: number;
  heart_rate?: number;
  systolic_bp?: number;
  diastolic_bp?: number;
  respiratory_rate?: number;
  oxygen_saturation?: number;
  blood_sugar?: number;
  height?: number;
  weight?: number;
  news_score: number;
  temperature_color: string;
  heart_rate_color: string;
  blood_pressure_color: string;
  respiratory_rate_color: string;
  oxygen_saturation_color: string;
  blood_sugar_color?: string;
  height_color?: string;
  weight_color?: string;
  news_score_color: string;
}

export interface VitalSignsChartData {
  data: ChartDataPoint[];
}

export interface VitalSignsAlert {
  uuid: string;
  patient: {
    uuid: string;
    first_name: string;
    last_name: string;
    nhs_number: string;
  };
  news_score: number;
  news_severity: 'Low' | 'Low-Medium' | 'Medium' | 'Medium-High' | 'High';
  recorded_at: string;
  temperature?: number;
  heart_rate?: number;
  systolic_bp?: number;
  diastolic_bp?: number;
  respiratory_rate?: number;
  oxygen_saturation?: number;
}

export interface VitalSignsStatistics {
  total_records: number;
  average_news_score: number;
  high_risk_patients: number;
  alerts_count: number;
  vital_signs_distribution: {
    temperature: { normal: number; low: number; high: number; critical: number };
    heart_rate: { normal: number; low: number; high: number; critical: number };
    blood_pressure: { normal: number; low: number; high: number; critical: number };
    respiratory_rate: { normal: number; low: number; high: number; critical: number };
    oxygen_saturation: { normal: number; low: number; high: number; critical: number };
  };
}

export interface VitalSignsFilters {
  patient_id?: string;
  start_date?: string;
  end_date?: string;
  news_severity?: 'Low' | 'Low-Medium' | 'Medium' | 'Medium-High' | 'High';
  vital_type?: string;
  limit?: number;
  offset?: number;
  ordering?: string;
}

export interface PaginatedVitalSignsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: VitalSign[];
}

// CRUD Operations
export const getVitalSigns = async (filters: VitalSignsFilters = {}): Promise<PaginatedVitalSignsResponse> => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, value.toString());
    }
  });

  console.log('🔍 Fetching vital signs with filters:', filters);
  const url = `/vitals/vital-signs/?${params.toString()}`;
  console.log('🌐 API URL:', url);
  
  const response = await api.get(url);
  console.log('✅ Vital signs response:', response.data);
  return response.data;
};

export const createVitalSign = async (data: CreateVitalSignData): Promise<VitalSign> => {
  const response = await api.post('/vitals/vital-signs/', data);
  return response.data;
};

export const getVitalSign = async (uuid: string): Promise<VitalSign> => {
  const response = await api.get(`/vitals/vital-signs/${uuid}/`);
  return response.data;
};

export const updateVitalSign = async (uuid: string, data: UpdateVitalSignData): Promise<VitalSign> => {
  const response = await api.patch(`/vitals/vital-signs/${uuid}/`, data);
  return response.data;
};

export const deleteVitalSign = async (uuid: string): Promise<void> => {
  await api.delete(`/vitals/vital-signs/${uuid}/`);
};

// Summary and Analytics
export const getVitalSignsSummary = async (patientId: string): Promise<VitalSignsSummary> => {
  console.log('🔍 Fetching vital signs summary for patient:', patientId);
  const url = `/vitals/vital-signs/summary/?patient_id=${patientId}`;
  console.log('🌐 API URL:', url);
  
  const response = await api.get(url);
  console.log('✅ Vital signs summary response:', response.data);
  return response.data;
};

export const getVitalSignsChartData = async (patientId: string, days: number = 7): Promise<VitalSignsChartData> => {
  console.log('🔍 Fetching chart data for patient:', patientId, 'days:', days);
  const url = `/vitals/vital-signs/chart_data/?patient_id=${patientId}&days=${days}`;
  console.log('🌐 Chart data API URL:', url);
  
  const response = await api.get(url);
  console.log('✅ Chart data response:', response.data);
  return response.data;
};

export const getVitalSignsAlerts = async (threshold: number = 7, limit: number = 50): Promise<VitalSignsAlert[]> => {
  const response = await api.get(`/vitals/vital-signs/alerts/?threshold=${threshold}&limit=${limit}`);
  return response.data;
};

export const getVitalSignsStatistics = async (patientId?: string, days: number = 30): Promise<VitalSignsStatistics> => {
  const params = new URLSearchParams();
  if (patientId) params.append('patient_id', patientId);
  params.append('days', days.toString());
  
  const response = await api.get(`/vitals/vital-signs/statistics/?${params.toString()}`);
  return response.data;
};

// History and Audit
export const getVitalSignHistory = async (uuid: string): Promise<any[]> => {
  const response = await api.get(`/vitals/vital-signs/${uuid}/history/`);
  return response.data;
};

export const getPatientVitalSignsHistory = async (patientId: string, filters: {
  field_name?: string;
  changed_by?: string;
  start_date?: string;
  end_date?: string;
} = {}): Promise<any[]> => {
  const params = new URLSearchParams();
  params.append('patient_id', patientId);
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, value.toString());
    }
  });

  const response = await api.get(`/vitals/vital-signs/?${params.toString()}`);
  return response.data;
};

export const getVitalSignsHistory = async (filters: {
  vital_sign?: string;
  field_name?: string;
  changed_by?: string;
} = {}): Promise<any[]> => {
  const params = new URLSearchParams();
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, value.toString());
    }
  });

  const response = await api.get(`/vitals/history/?${params.toString()}`);
  return response.data;
}; 

// New individual vital sign history endpoints
export const getFieldHistory = async (patientId: string, fieldName: string, filters: {
  changed_by?: string;
  start_date?: string;
  end_date?: string;
  ordering?: string;
} = {}): Promise<any[]> => {
  const params = new URLSearchParams();
  params.append('patient_id', patientId);
  params.append('field_name', fieldName);
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, value.toString());
    }
  });

  const response = await api.get(`/vitals/history/?${params.toString()}`);
  return response.data;
};

export const getVitalSignTypeHistory = async (patientId: string, vitalType: string, filters: {
  changed_by?: string;
  start_date?: string;
  end_date?: string;
  ordering?: string;
} = {}): Promise<any[]> => {
  const params = new URLSearchParams();
  params.append('patient_id', patientId);
  params.append('vital_type', vitalType);
  
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      params.append(key, value.toString());
    }
  });

  const response = await api.get(`/vitals/history/?${params.toString()}`);
  return response.data;
}; 