import { useState } from "react";
import { Eye, EyeOff } from "lucide-react";
import "./styles.css";
import "./password-input.css";

interface PasswordInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  placeholder?: string;
  onKeyDown?: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  className?: string;
  disabled?: boolean;
  showRequirements?: boolean;
  showStrengthMeter?: boolean;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const PasswordInput: React.FC<PasswordInputProps> = ({
  label,
  name,
  value,
  onChange,
  onKeyDown,
  placeholder,
  className,
  disabled,
  showRequirements = false,
  showStrengthMeter = false,
  ...rest
}) => {
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [passwordStrength, setPasswordStrength] = useState<number>(0);

  // Password validation
  const validatePassword = (pass: string): boolean => {
    // Check password criteria
    const hasLetter = /[A-Za-z]/.test(pass);
    const hasNumber = /\d/.test(pass);
    const hasSpecialChar = /[@$!%*#?&]/.test(pass);
    const hasMinLength = pass.length >= 8;
    
    // Calculate strength (0-4)
    let strength = 0;
    if (hasLetter) strength++;
    if (hasNumber) strength++;
    if (hasSpecialChar) strength++;
    if (hasMinLength) strength++;
    
    setPasswordStrength(strength);
    
    // Return true if all criteria are met
    return hasLetter && hasNumber && hasSpecialChar && hasMinLength;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPass = e.target.value;
    onChange(e);
    if (showStrengthMeter) {
      validatePassword(newPass);
    }
  };

  return (
    <div className={`password-container ${className || ""}`}>
      {label && <label htmlFor={name}>{label}</label>}
      <div className="password-input-wrapper">
        <input
          type={showPassword ? "text" : "password"}
          id={name}
          name={name}
          value={value}
          onChange={handleChange}
          onKeyDown={onKeyDown}
          placeholder={placeholder || "Enter password"}
          disabled={disabled}
          className="password-input"
          {...rest}
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="password-toggle"
        >
          {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
        </button>
      </div>

      {showStrengthMeter && value && (
        <div className="mb-3">
          <div className="strength-meter">
            {[1, 2, 3, 4].map((level) => (
              <div
                key={level}
                className="strength-segment"
                style={{
                  backgroundColor: passwordStrength >= level 
                    ? level === 4 
                      ? "#10B981" // Strong (green)
                      : level === 3 
                        ? "#22C55E" // Good (light green)
                        : level === 2 
                          ? "#F59E0B" // Fair (orange)
                          : "#EF4444" // Weak (red)
                    : "#E2E8F0", // Empty (gray)
                }}
              />
            ))}
          </div>
          <div className="strength-text" style={{ 
            color: passwordStrength === 4 
              ? "#10B981" 
              : passwordStrength >= 3 
                ? "#22C55E" 
                : passwordStrength >= 2 
                  ? "#F59E0B" 
                  : "#EF4444"
          }}>
            {passwordStrength === 0 && "Enter password"}
            {passwordStrength === 1 && "Weak password"}
            {passwordStrength === 2 && "Fair password"}
            {passwordStrength === 3 && "Good password"}
            {passwordStrength === 4 && "Strong password"}
          </div>
        </div>
      )}
      
      {showRequirements && (
        <div className="password-requirements">
          Password must contain:
          <ul>
            <li style={{ color: value.length >= 8 ? "#10B981" : "#64748b" }}>
              At least 8 characters
            </li>
            <li style={{ color: /[A-Za-z]/.test(value) ? "#10B981" : "#64748b" }}>
              At least one letter
            </li>
            <li style={{ color: /\d/.test(value) ? "#10B981" : "#64748b" }}>
              At least one number
            </li>
            <li style={{ color: /[@$!%*#?&]/.test(value) ? "#10B981" : "#64748b" }}>
              At least one special character (@$!%*#?&)
            </li>
          </ul>
        </div>
      )}
    </div>
  );
};

export default PasswordInput;
