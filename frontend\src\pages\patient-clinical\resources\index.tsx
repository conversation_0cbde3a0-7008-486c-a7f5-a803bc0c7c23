import React from "react";
import { Link } from 'react-router-dom';
import { motion } from "framer-motion";
import { blogArticles } from '@/data/blogData';
import "./resources.css";

// Helper function to create URL-friendly slugs
const createBlogUrl = (article: { id: string, title: string }) => {
  const slug = article.title.toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special chars
    .replace(/\s+/g, '-')     // Replace spaces with hyphens
    .trim();
  return `${article.id}-${slug}`;
};

const ResourcesSection: React.FC = () => {
  return (
    <motion.div
      className="patclin-tab-content"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <h2 className="patclin-section-title">Health Resources</h2>
      
      <div className="resources-blog-container">
        <motion.h3 
          className="resources-blog-title"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          Knowledge <span className="text-highlight">Hub</span>
        </motion.h3>
        
        <motion.p 
          className="resources-blog-subtitle"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          Latest articles and resources to support your health journey
        </motion.p>

        <div className="resources-blog-grid">
          {blogArticles.map((article, index) => (
            <motion.div 
              key={article.id} 
              className="resources-blog-card"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="resources-blog-card-image">
                <img src={article.image} alt={article.title} />
              </div>
              <div className="resources-blog-card-content">
                <div className="resources-blog-card-meta">
                  <span className="resources-blog-card-author">{article.author}</span>
                  <span className="resources-blog-card-date">{article.date}</span>
                </div>
                <h2 className="resources-blog-card-title">{article.title}</h2>
                <p className="resources-blog-card-excerpt">{article.excerpt}</p>
                <Link to={`/blog/${createBlogUrl(article)}`} className="resources-blog-read-more">
                  Read Article
                </Link>
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      <div className="patclin-empty-state" style={{ marginTop: '30px' }}>
        <p>More resources and educational materials will be added soon.</p>
      </div>
    </motion.div>
  );
};

export default ResourcesSection;
