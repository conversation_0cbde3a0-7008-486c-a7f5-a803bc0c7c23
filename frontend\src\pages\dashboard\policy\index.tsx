import "./policy.css";
import { ArrowUpRight, FileText, Pencil, Plus, Search, SquareX, X } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import {
  useDeletePolicyMutation,
  usePoliciesQuery,
} from "@/hooks/policy.query";
import { format } from "date-fns";
import type { Policy } from "@/services/api/types";
import { useCallback, useEffect, useState, useMemo } from "react";
import { usePolicyStore } from "@/store/policyState";
import ConfirmationDeleteModal from "./DeletePolicyModal";
import { debounce } from "lodash";
import Preloader from "@/components/common/Preloader";
import NoResult from "@/components/common/NoResult";
import DataTable, { DataTableSearch } from "@/components/common/DataTable";

// Extended Policy type with formatted date for display
interface ExtendedPolicy extends Policy {
  formattedDate: string;
  departmentName: string;
  hospitalName: string;
  studyName: string | null;
}

export default function Policy() {
  const navigate = useNavigate();
  const { searchTerm, setSearchTerm } = usePolicyStore();
  const [queryTerm, setQueryTerm] = useState(searchTerm);
  const { data, isLoading, isError, refetch } = usePoliciesQuery(queryTerm);
  const { mutate: deletePolicy } = useDeletePolicyMutation();

  const [, setIsDeleting] = useState(false);
  const [isConfirmDeleteModalOpen, setIsConfirmDeleteModalOpen] = useState(false);
  const [selectedPolicyUuid, setSelectedPolicyUuid] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [filteredData, setFilteredData] = useState<ExtendedPolicy[]>([]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((term: string) => {
      setQueryTerm(term);
    }, 500),
    []
  );

  // Prepare data for the DataTable
  const tableData = useMemo<ExtendedPolicy[]>(() => {
    if (!data?.results) return [];

    return data.results.map((policy: Policy): ExtendedPolicy => ({
      ...policy,
      formattedDate: format(new Date(policy.created_at), "dd/MM/yyyy"),
      departmentName: policy.department.name,
      hospitalName: policy.hospital.name,
      studyName: policy.study?.name || null
    }));
  }, [data]);

  useEffect(() => {
    debouncedSearch(searchTerm);
  }, [searchTerm, debouncedSearch]);

  useEffect(() => {
    refetch();
  }, [queryTerm, refetch]);

  // Update the search term when filtered data changes
  useEffect(() => {
    if (filteredData.length === tableData.length) {
      setSearchTerm("");
    }
  }, [filteredData, tableData, setSearchTerm]);

  // Define columns for the EnhancedTable
  const columns = useMemo(() => [
    {
      key: "title" as keyof ExtendedPolicy,
      header: "Policy Name",
      sortable: true,
    },
    {
      key: "formattedDate" as keyof ExtendedPolicy,
      header: "Date Created",
      sortable: true,
    },
    {
      key: "departmentName" as keyof ExtendedPolicy,
      header: "Department",
      sortable: true,
    },
    {
      key: "hospitalName" as keyof ExtendedPolicy,
      header: "Hospital",
      sortable: true,
    },
    {
      key: "studyName" as keyof ExtendedPolicy,
      header: "Study",
      sortable: true,
    },
  ], []);

  // Define actions for the EnhancedTable
  const actions = useMemo(() => [
    {
      icon: <ArrowUpRight size={16} />,
      tooltipText: "Policy details",
      onClick: (policy: ExtendedPolicy) => handlePolicyDetails(policy.uuid),
    },
    {
      icon: <Pencil size={16} />,
      tooltipText: "Edit policy",
      onClick: (policy: ExtendedPolicy) => handleRedirect(policy.uuid),
    },
    {
      icon: <SquareX size={16} />,
      tooltipText: "Delete policy",
      onClick: (policy: ExtendedPolicy) => handleDelete(policy.uuid),
    },
  ], []);

  // Delete policy
  const handleDelete = (uuid: string) => {
    setSelectedPolicyUuid(uuid);
    setIsConfirmDeleteModalOpen(true);
  };

  // Confirm deletion and close modal
  const handleConfirmDeletePolicy = async () => {
    if (selectedPolicyUuid) {
      try {
        setIsDeleting(true);
        await deletePolicy(selectedPolicyUuid);
        setSuccessMessage("Policy deleted successfully");

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSuccessMessage(null);
        }, 3000);
      } catch (error) {
        console.error("Error deleting policy:", error);
      } finally {
        setIsDeleting(false);
      }
    }
    setIsConfirmDeleteModalOpen(false);
    setSelectedPolicyUuid(null);
  };

  // Navigate to update policy page
  const handleRedirect = (policyUuid: string) => {
    navigate(`/org/dashboard/update-policy/${policyUuid}`);
  };
  const handlePolicyDetails = (policyUuid: string) => {
    navigate(`/policy-details/${policyUuid}`);
  };

  return (
    <div className="policy-container">
      {/* Success message */}
      {successMessage && (
        <div style={{
          backgroundColor: "#4CAF50",
          color: "white",
          padding: "10px 20px",
          borderRadius: "5px",
          marginBottom: "20px",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center"
        }}>
          <span>{successMessage}</span>
          <button
            onClick={() => setSuccessMessage(null)}
            style={{ background: "none", border: "none", color: "white", cursor: "pointer" }}
          >
            <X size={18} />
          </button>
        </div>
      )}

      {/* Header */}
      <div className="policy-header">
        <div className="policy-title">
          <h1>
            <FileText size={24} style={{ marginRight: "10px" }} />
            Guidelines
          </h1>
        </div>
        <div className="policy-subtitle">
          <h6>Manage and view all policies in your organization</h6>
        </div>
      </div>

      {/* Search and Add */}
      <div className="policy-search-container">
        <div className="policy-search-box">
          <Search className="search-icon" size={18} />
          <DataTableSearch
            data={tableData}
            onFilter={setFilteredData}
            placeholder="Search policies..."
          />
        </div>
        <Link
          to="/org/dashboard/add-policy"
          className="policy-add-button"
          aria-label="Add new policy"
        >
          Add Policy <Plus size={18} />
        </Link>
      </div>

      {/* Loading state */}
      {isLoading ? (
        <div className="policy-loading">
          <Preloader />
        </div>
      ) : isError ? (
        <div className="policy-empty-state">
          <h3>Error loading policies</h3>
          <p>There was a problem loading the policies. Please try again later.</p>
          <button
            onClick={() => refetch()}
            style={{
              backgroundColor: "#3EC1C9",
              color: "white",
              border: "none",
              padding: "8px 16px",
              borderRadius: "5px",
              cursor: "pointer"
            }}
          >
            Try Again
          </button>
        </div>
      ) : (
        <div style={{ marginTop: "30px" }}>
          {tableData && tableData.length > 0 ? (
            <div className="policy-table-container">
              <DataTable
                data={tableData}
                filteredData={filteredData}
                columns={columns}
                actions={actions}
                noDataMessage="No policies found"
                defaultItemsPerPage={10}
              />
            </div>
          ) : (
            <div className="policy-empty-state">
              <NoResult
                title="Policy"
                description={"There are no policies yet"}
              />
            </div>
          )}
        </div>
      )}
      {/* Delete confirmation modal */}
      <ConfirmationDeleteModal
        isOpen={isConfirmDeleteModalOpen}
        onClose={() => setIsConfirmDeleteModalOpen(false)}
        onConfirm={handleConfirmDeletePolicy}
        title="Confirm Deleting Policy"
        message="Are you sure you want to delete this policy?"
        subMessage="This action is permanent and cannot be undone. All associated data will be lost."
      />
    </div>
  );
}
