/* Duplicate Consent Modal Styles */
.duplicate-consent-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.duplicate-consent-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
}

.duplicate-consent-modal .modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Modal Content */
.duplicate-consent-modal .modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
}

.original-consent-info,
.duplicate-form-section,
.questions-section {
  margin-bottom: 32px;
}

.original-consent-info h3,
.duplicate-form-section h3,
.questions-section h3 {
  margin: 0 0 20px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.info-item label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.info-item span {
  font-size: 14px;
  color: #1f2937;
  font-weight: 500;
}

/* Form Groups */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  outline: none;
  border-color: #37b7c3;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

.form-group input.error,
.form-group textarea.error,
.form-group select.error {
  border-color: #ef4444;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
  display: block;
}

/* Section Description */
.section-description {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 20px;
  line-height: 1.5;
}

/* Questions List */
.questions-list {
  margin-bottom: 24px;
}

.question-card {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.question-number {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.remove-question-btn {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.remove-question-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

.question-content .form-group {
  margin-bottom: 16px;
}

.question-content .form-group:last-child {
  margin-bottom: 0;
}

/* Add Question Form */
.add-question-form {
  background-color: #f0f9ff;
  border: 2px dashed #0ea5e9;
  border-radius: 8px;
  padding: 24px;
  margin-top: 20px;
}

.add-question-form h4 {
  margin: 0 0 16px 0;
  color: #0c4a6e;
  font-size: 1rem;
  font-weight: 600;
}

.add-question-btn {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
  font-size: 14px;
}

.add-question-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
}

/* Checkbox Styles */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
}

.standard-checkbox {
  width: 16px;
  height: 16px;
  accent-color: #37b7c3;
}

.checkbox-text {
  color: #374151;
}

/* Warning Section */
.warning-section {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background-color: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  color: #92400e;
  margin-bottom: 20px;
}

.warning-section strong {
  color: #92400e;
}

.error-alert {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background-color: #fef2f2;
  border: 1px solid #ef4444;
  border-radius: 8px;
  color: #dc2626;
  margin-top: 16px;
}

/* Modal Footer */
.duplicate-consent-modal .modal-footer {
  padding: 24px 32px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
}

.footer-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
  font-size: 14px;
}

.btn-primary {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
  transform: translateY(-1px);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Responsive Design */
@media (max-width: 768px) {
  .duplicate-consent-modal {
    margin: 10px;
    max-height: 95vh;
  }

  .duplicate-consent-modal .modal-header,
  .duplicate-consent-modal .modal-content,
  .duplicate-consent-modal .modal-footer {
    padding: 16px 20px;
  }

  .duplicate-consent-modal .modal-header h2 {
    font-size: 1.25rem;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .footer-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .remove-question-btn {
    align-self: flex-end;
  }
} 