// Color coding based on NEWS severity and vital sign thresholds
export const getNewsSeverityColor = (severity: string): string => {
  switch (severity) {
    case 'Low':
      return '#10b981'; // Green
    case 'Low-Medium':
      return '#3b82f6'; // Blue
    case 'Medium':
      return '#f59e0b'; // Orange
    case 'Medium-High':
      return '#ef4444'; // Red
    case 'High':
      return '#7c2d12'; // Dark red
    default:
      return '#6b7280'; // Gray
  }
};

export const getVitalSignColor = (
  vitalType: string,
  value: number,
  unit?: string
): string => {
  // Convert to Celsius if needed
  let normalizedValue = value;
  if (vitalType === 'temperature' && unit === 'F') {
    normalizedValue = (value - 32) * 5/9;
  }
  if (vitalType === 'weight' && unit === 'lbs') {
    normalizedValue = value * 0.453592;
  }
  if (vitalType === 'height' && unit === 'in') {
    normalizedValue = value * 2.54;
  }
  if (vitalType === 'blood_sugar' && unit === 'mmol/L') {
    normalizedValue = value * 18; // Convert to mg/dL
  }

  switch (vitalType) {
    case 'temperature':
      if (normalizedValue >= 36.1 && normalizedValue <= 38.0) return '#10b981'; // Normal (Green)
      if (normalizedValue < 35.0) return '#3b82f6'; // Low (Blue)
      if (normalizedValue >= 38.1 && normalizedValue <= 39.0) return '#ef4444'; // High (Red)
      if (normalizedValue <= 35.0 || normalizedValue >= 39.1) return '#f59e0b'; // Critical (Orange)
      break;
    
    case 'heart_rate':
      if (value >= 51 && value <= 90) return '#10b981'; // Normal (Green)
      if (value <= 40) return '#3b82f6'; // Low (Blue)
      if (value >= 91 && value <= 130) return '#ef4444'; // High (Red)
      if (value <= 40 || value >= 131) return '#f59e0b'; // Critical (Orange)
      break;
    
    case 'systolic_bp':
      if (value >= 111 && value <= 219) return '#10b981'; // Normal (Green)
      if (value <= 90) return '#3b82f6'; // Low (Blue)
      if (value >= 91 && value <= 110) return '#ef4444'; // High (Red)
      if (value <= 90 || value >= 220) return '#f59e0b'; // Critical (Orange)
      break;
    
    case 'respiratory_rate':
      if (value >= 12 && value <= 20) return '#10b981'; // Normal (Green)
      if (value <= 8) return '#3b82f6'; // Low (Blue)
      if (value >= 21 && value <= 24) return '#ef4444'; // High (Red)
      if (value <= 8 || value >= 25) return '#f59e0b'; // Critical (Orange)
      break;
    
    case 'oxygen_saturation':
      if (value >= 97) return '#10b981'; // Normal (Green)
      if (value >= 94 && value <= 96) return '#3b82f6'; // Low (Blue)
      if (value >= 92 && value <= 93) return '#ef4444'; // High (Red)
      if (value <= 91) return '#f59e0b'; // Critical (Orange)
      break;
    
    case 'blood_sugar':
      // Normal range: 70-140 mg/dL
      if (normalizedValue >= 70 && normalizedValue <= 140) return '#10b981'; // Normal (Green)
      if (normalizedValue < 70) return '#3b82f6'; // Low (Blue)
      if (normalizedValue > 140) return '#ef4444'; // High (Red)
      break;
    
    case 'news_score':
      if (value >= 0 && value <= 4) return '#10b981'; // Low risk (Green)
      if (value >= 5 && value <= 6) return '#3b82f6'; // Low-medium risk (Blue)
      if (value >= 7 && value <= 8) return '#ef4444'; // Medium risk (Red)
      if (value >= 9) return '#f59e0b'; // High risk (Orange)
      break;
  }
  
  return '#6b7280'; // Default gray
};

export const formatVitalSignValue = (
  value: number | undefined,
  type: string,
  unit?: string
): string => {
  if (value === undefined || value === null) return 'N/A';
  
  switch (type) {
    case 'temperature':
      return `${value}${unit === 'F' ? '°F' : '°C'}`;
    case 'heart_rate':
      return `${value} bpm`;
    case 'systolic_bp':
    case 'diastolic_bp':
      return `${value} mmHg`;
    case 'respiratory_rate':
      return `${value}/min`;
    case 'oxygen_saturation':
      return `${value}%`;
    case 'blood_sugar':
      return `${value} ${unit || 'mg/dL'}`;
    case 'height':
      return `${value} ${unit || 'cm'}`;
    case 'weight':
      return `${value} ${unit || 'kg'}`;
    case 'news_score':
      return `${value}`;
    default:
      return `${value}`;
  }
};

export const getTrendIcon = (trend: string): string => {
  switch (trend) {
    case 'increasing':
      return '↗️';
    case 'decreasing':
      return '↘️';
    case 'stable':
      return '→';
    default:
      return '→';
  }
};

export const getTrendColor = (trend: string): string => {
  switch (trend) {
    case 'increasing':
      return '#ef4444'; // Red for increasing (potentially concerning)
    case 'decreasing':
      return '#10b981'; // Green for decreasing (potentially improving)
    case 'stable':
      return '#6b7280'; // Gray for stable
    default:
      return '#6b7280';
  }
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-GB', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export const getConsciousnessLevelText = (level: string): string => {
  switch (level) {
    case 'A':
      return 'Alert';
    case 'V':
      return 'Voice';
    case 'P':
      return 'Pain';
    case 'U':
      return 'Unresponsive';
    default:
      return level;
  }
};

export const getTemperatureLocationText = (location: string): string => {
  switch (location) {
    case 'oral':
      return 'Oral';
    case 'axillary':
      return 'Axillary';
    case 'rectal':
      return 'Rectal';
    case 'tympanic':
      return 'Tympanic';
    case 'temporal':
      return 'Temporal';
    case 'other':
      return 'Other';
    default:
      return location;
  }
}; 