import { CountdownCircleTimer } from "react-countdown-circle-timer";

type CprCardProps = {
    duration?: number;
    text1: string;
    text2: string;
    text3: string;
    buttonText: string;
    buttonIconLeft: JSX.Element;
    buttonIconRight: JSX.Element;
    onButtonClick: () => void;
    secondButtonText?: string;
    secondButtonIconLeft?: JSX.Element;
    secondButtonIconRight?: JSX.Element;
    onSecondButtonClick?: () => void;
};

export default function CprCard({
    duration,
    text1,
    text2,
    text3,
    buttonText,
    buttonIconLeft,
    buttonIconRight,
    onButtonClick,
    secondButtonText,
    secondButtonIconLeft,
    secondButtonIconRight,
    onSecondButtonClick
}: CprCardProps) {
    return (
        <div className="cpr-card">
            {duration && (
                <div className="cpr-timer">
                    <CountdownCircleTimer
                        isPlaying
                        duration={duration}
                        size={80}
                        colors={['#37B7C3', '#2A9D8F', '#264653', '#1B3A4B']}
                        colorsTime={[20, 10, 0]}
                    >
                        {({ remainingTime }) => remainingTime}
                    </CountdownCircleTimer>
                </div>
            )}
            <div className="cpr-card-content">
                <h3>{text1}</h3>
                <p>{text2}</p>
                <p>{text3}</p>
                <div className="button-container">
                    <button className="cpr-button" onClick={onButtonClick}>
                        <span className="icon-left">{buttonIconLeft}</span>
                        <span className="button-text">{buttonText}</span>
                        <span className="icon-right">{buttonIconRight}</span>
                    </button>
                    {secondButtonText && onSecondButtonClick && (
                        <button className="cpr-button cpr-button-secondary" onClick={onSecondButtonClick}>
                            <span className="icon-left">{secondButtonIconLeft}</span>
                            <span className="button-text">{secondButtonText}</span>
                            <span className="icon-right">{secondButtonIconRight}</span>
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
}
