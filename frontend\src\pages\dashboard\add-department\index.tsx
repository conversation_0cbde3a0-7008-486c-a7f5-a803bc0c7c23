import "./adddepartment.css";
import { useNavigate } from "react-router-dom";
import { useEffect, useState } from "react";
import AddDepHospModal from "@/components/modal/AddDepHospModal";
import NurtifyText from "@components/NurtifyText.tsx";
import NurtifyInput from "@components/NurtifyInput.tsx";
import NurtifyTextArea from "@components/NurtifyTextArea.tsx";
import { motion } from "framer-motion";
import { Building, MapPin, CheckCircle } from "lucide-react";
import { useCreateDepartmentMutation } from "@/hooks/department.query";
import { useCurrentUserQuery } from "@/hooks/user.query";

export default function AddDepartment() {
  const { data: currentUser } = useCurrentUserQuery();
  const hospitalUuid = currentUser?.hospital?.uuid || "";
  const hospitalName = currentUser?.hospital?.name || "";
  const createDepartmentMutation = useCreateDepartmentMutation();
  const [currentStep, setCurrentStep] = useState(1);
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [newDepartmentUuid, setNewDepartmentUuid] = useState<string | null>(null);
  const navigate = useNavigate();
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState({
    name: "",
    phone_number: "",
    extension: "",
    hospital_uuid: "",
    primary_address: "",
    secondary_address: "",
    postcode: "",
    country: "",
  });

  useEffect(() => {
    if (hospitalUuid) {
      setFormData(prev => ({
        ...prev,
        hospital_uuid: hospitalUuid,
      }));
    }
  }, [hospitalUuid]);

  const validateField = (name: string, value: string) => {
    const maxLengths: Record<string, number> = {
      name: 50,
      phone_number: 20,
      extension: 20,
      primary_address: 100, // Assuming max length for address
      secondary_address: 100, // Assuming max length for address
      postcode: 20,
      country: 50, // Increased max length for country name
    };

    // Fields that are required
    const requiredFields = [
      "name",
      "phone_number",
      "extension",
      "primary_address",
      "secondary_address",
      "postcode",
      "country",
    ];

    if (requiredFields.includes(name) && !value.trim()) {
      return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} is required`;
    }

    if (maxLengths[name] && value.length > maxLengths[name]) {
      return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} must not exceed ${maxLengths[name]} characters`;
    }
    return '';
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));

    // Validate on change
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const newErrors: Record<string, string> = {};
    const fieldsToValidate: (keyof typeof formData)[] = [
        "name", "phone_number", "extension", 
        "primary_address", "secondary_address", "postcode", "country"
    ];

    fieldsToValidate.forEach(key => {
      const error = validateField(key, formData[key]);
      if (error) newErrors[key] = error;
    });
    
    // Check for hospital_uuid (though it's auto-filled, good to be robust)
    if (!formData.hospital_uuid.trim()) {
        newErrors["hospital_uuid"] = "Hospital association is missing. Please refresh or contact support.";
        // This error is systemic, might not fit a specific step, but crucial.
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      try {
        const formattedData = {
          ...formData,
          name: formData.name.trim().replace(/\s+/g, '_') 
        };
        
        const response = await createDepartmentMutation.mutateAsync(formattedData);
        const departmentUuid = response.uuid;
        setNewDepartmentUuid(departmentUuid);
        setShowPopup(true);
      } catch (error) {
        console.error("Error creating department:", error);
        // Potentially set a general error message here
      }
    } else {
      // Move to the step with errors
      const step1Fields: (keyof typeof formData)[] = ['name', 'phone_number', 'extension'];
      const hasStep1Errors = step1Fields.some(field => newErrors[field]);
      setCurrentStep(hasStep1Errors ? 1 : 2);
    }
  };

  const nextStep = () => {
    if (currentStep < 2) {
      // Validate current step's fields before proceeding
      const newErrors: Record<string, string> = {};
      let fieldsToValidateOnNext: (keyof typeof formData)[] = [];

      if (currentStep === 1) {
        fieldsToValidateOnNext = ['name', 'phone_number', 'extension'];
      }
      // Add Step 2 fields if you want to validate them before hitting "Add Department" on step 2
      // else if (currentStep === 2) { /* ... */ }

      fieldsToValidateOnNext.forEach(key => {
        const error = validateField(key, formData[key]);
        if (error) newErrors[key] = error;
      });
      
      setErrors(prev => ({...prev, ...newErrors}));

      if (Object.keys(newErrors).length === 0) {
        setCurrentStep(currentStep + 1);
      }
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePopupYes = () => {
    if (newDepartmentUuid) {
      navigate(`/org/dashboard/department-details/${newDepartmentUuid}/departmentAdmins`);
    }
    setShowPopup(false);
  };

  const handlePopupNo = () => {
    navigate("/org/dashboard/department", { replace: true });
    setShowPopup(false);
  };

  // Define maxLengths for direct use in input components
  const inputMaxLengths = {
    name: 50,
    phone_number: 20,
    extension: 20,
    primary_address: 100,
    secondary_address: 100,
    postcode: 20,
    country: 50,
  };

  return (
    <div className="add-department-container">
      <div className="add-department-header">
        <div className="add-department-title">
          <h1>
            <Building size={24} style={{ marginRight: "10px" }} />
            Add New Department
          </h1>
        </div>
        <div className="add-department-subtitle">
          <h6>Create and manage department information in your network</h6>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="department-progress">
        <div
          className={`department-step ${currentStep >= 1 ? "active" : ""} ${
            currentStep > 1 ? "completed" : ""
          }`}
        >
          <div className="department-step-indicator">
            {currentStep > 1 ? <CheckCircle size={16} /> : 1}
          </div>
          <div className="department-step-label">Department Info</div>
        </div>
        <div
          className={`department-step ${currentStep >= 2 ? "active" : ""}`}
        >
          <div className="department-step-indicator">2</div>
          <div className="department-step-label">Address Details</div>
        </div>
      </div>

      <form className="add-department-form" onSubmit={handleSubmit}>
        {/* Step 1: Department Information */}
        {currentStep === 1 && (
          <motion.div
            className="department-form-section"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="department-form-section-title">
              <Building size={18} style={{ marginRight: "8px", verticalAlign: "middle" }} />
              Department Information
            </h3>
            <div className="row y-gap-30">
              <div className="col-md-6">
                <NurtifyText label="Department Name*" />
                <NurtifyInput
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter Department Name here"
                  maxLength={inputMaxLengths.name}
                />
                {errors.name && <div className="field-error">{errors.name}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Hospital Name*" />
                <NurtifyInput
                  type="text"
                  name="hospital_name_display" // Use a different name to avoid confusion with formData
                  value={hospitalName}
                  disabled
                  placeholder="Hospital Name"
                />
                {/* No error display needed for a disabled field based on auto-fetched data */}
              </div>
            </div>
            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Phone*" />
                <NurtifyInput
                  type="text" // Changed from "number" for better UX with phone numbers
                  name="phone_number"
                  value={formData.phone_number}
                  onChange={handleChange}
                  placeholder="Enter Phone Number here"
                  maxLength={inputMaxLengths.phone_number}
                />
                {errors.phone_number && <div className="field-error">{errors.phone_number}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Extension*" />
                <NurtifyInput
                  type="text" // Changed from "number" for better UX
                  name="extension"
                  value={formData.extension}
                  onChange={handleChange}
                  placeholder="Enter Extension here"
                  maxLength={inputMaxLengths.extension}
                />
                {errors.extension && <div className="field-error">{errors.extension}</div>}
              </div>
            </div>

            <div className="department-form-actions">
              <button
                type="button"
                className="department-btn-submit"
                onClick={nextStep}
              >
                Next
              </button>
            </div>
          </motion.div>
        )}

        {/* Step 2: Address Information */}
        {currentStep === 2 && (
          <motion.div
            className="department-form-section"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="department-form-section-title">
              <MapPin size={18} style={{ marginRight: "8px", verticalAlign: "middle" }} />
              Address Information
            </h3>
            <div className="row y-gap-30">
              <div className="col-md-6">
                <NurtifyText label="Address Line 1*" />
                <NurtifyTextArea // Using TextArea for addresses usually makes sense
                  name="primary_address"
                  value={formData.primary_address}
                  onChange={handleChange}
                  placeholder="Enter Address Line 1"
                  maxLength={inputMaxLengths.primary_address}
                />
                {errors.primary_address && <div className="field-error">{errors.primary_address}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Address Line 2*" />
                 <NurtifyTextArea // Using TextArea
                  name="secondary_address"
                  value={formData.secondary_address}
                  onChange={handleChange}
                  placeholder="Enter Address Line 2"
                  maxLength={inputMaxLengths.secondary_address}
                />
                {errors.secondary_address && <div className="field-error">{errors.secondary_address}</div>}
              </div>
            </div>

            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Postcode*" />
                <NurtifyInput
                  type="text"
                  name="postcode"
                  value={formData.postcode}
                  onChange={handleChange}
                  placeholder="Enter Postcode here"
                  maxLength={inputMaxLengths.postcode}
                />
                {errors.postcode && <div className="field-error">{errors.postcode}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Country*" />
                <NurtifyInput
                  type="text"
                  name="country"
                  value={formData.country}
                  onChange={handleChange}
                  placeholder="Enter country here"
                  maxLength={inputMaxLengths.country}
                />
                {errors.country && <div className="field-error">{errors.country}</div>}
              </div>
            </div>

            <div className="department-form-actions">
              <button
                type="button"
                className="department-btn-cancel"
                onClick={prevStep}
              >
                Back
              </button>
              <button
                type="submit"
                className="department-btn-submit"
                disabled={createDepartmentMutation.isPending}
              >
                {createDepartmentMutation.isPending ? "Submitting..." : "Add Department"}
              </button>
            </div>
          </motion.div>
        )}
      </form>
      {showPopup && (
        <AddDepHospModal
          type="Department"
          isOpen={showPopup}
          onNo={handlePopupNo}
          onYes={handlePopupYes}
        />
      )}
    </div>
  );
}