import { useState } from "react";
import { motion } from "framer-motion";
import {
  FileText,
  User,
  Info,
  CheckCircle,
  AlertCircle,
  Paperclip,
  FileUp,
} from "lucide-react";

import NurtifyText from "@components/NurtifyText";
import NurtifyInput from "@components/NurtifyInput";
import NurtifySelect from "@components/NurtifySelect";
import NurtifyTextArea from "@components/NurtifyTextArea";
import NurtifyAttachFileBox from "@/components/common/NurtifyAttachFileBox";
import { useCreatePolicyMutation } from "@/hooks/policy.query";
import { useStudiesQuery } from "@/hooks/study.query";

import "./addPolicy.css";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { useNavigate } from "react-router-dom";

interface FormData {
  title: string;
  category: string;
  author_name: string;
  job_title: string;
  description: string;
  attach_content: File | null;
  department_uuid: string;
  study_uuid: string | null;
}

interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  subMessage?: string;
}

interface Study {
  uuid: string;
  name: string;
}

// Define policy categories to match backend model
const POLICY_CATEGORIES = [
  { label: "Clinical Trial Protocol", value: "CLINICAL_TRIAL_PROTOCOL" },
  { label: "Lab Manual", value: "LAB_MANUAL" },
  { label: "Pharmacy Manual", value: "PHARMACY_MANUAL" },
  { label: "Imaging Manual", value: "IMAGING_MANUAL" },
  { label: "ECG or Cardiac Monitoring Manual", value: "ECG_MANUAL" },
  { label: "Randomization and Unblinding Procedures", value: "RANDOMIZATION_PROCEDURES" },
  { label: "Patient Information Sheet", value: "PATIENT_INFO_SHEET" },
  { label: "Patient Education Brochures", value: "PATIENT_EDUCATION" },
  { label: "Safety Management Plan", value: "SAFETY_MANAGEMENT" },
  { label: "Site Visit Schedule", value: "SITE_VISIT_SCHEDULE" },
  { label: "Other", value: "OTHER" },
] as const;

function ConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  subMessage,
}: ConfirmationModalProps) {
  if (!isOpen) return null;

  return (
    <div className="confirmation-modal-overlay">
      <motion.div
        className="confirmation-modal"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
      >
        <div className="modal-title">{title}</div>
        <div className="modal-message">{message}</div>
        {subMessage && <div className="modal-submessage">{subMessage}</div>}
        <div className="modal-actions">
          <button className="modal-btn-cancel" onClick={onClose}>
            Cancel
          </button>
          <button className="modal-btn-confirm" onClick={onConfirm}>
            Proceed
          </button>
        </div>
      </motion.div>
    </div>
  );
}

export default function AddPolicy() {
  const createPolicyMutation = useCreatePolicyMutation();
  const { data: studiesData } = useStudiesQuery();
  const [currentStep, setCurrentStep] = useState(1);
  const currentUser = useCurrentUserQuery();
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FormData>({
    title: "",
    category: "",
    author_name: "",
    job_title: "",
    description: "",
    attach_content: null,
    department_uuid: currentUser.data?.department?.uuid || "",
    study_uuid: null,
  });
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  // Form validation state
  const [formErrors, setFormErrors] = useState({
    title: false,
    category: false,
    author_name: false,
    job_title: false,
    description: false,
    attach_content: false,
  });

  // Check if form is valid
  const isFormValid = () => {
    const errors = {
      title: !formData.title,
      category: !formData.category,
      author_name: !formData.author_name,
      job_title: !formData.job_title,
      description: !formData.description,
      attach_content: !formData.attach_content,
    };

    setFormErrors(errors);
    return !Object.values(errors).some((error) => error);
  };

  // Convert studies data to options for the select component
  const studyOptions = studiesData?.map((study: Study) => ({
    value: study.uuid,
    label: study.name,
  })) || [];

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData((prev) => ({
      ...prev,
      category: e.target.value,
    }));

    // Clear error for this field when user selects
    if (formErrors.category) {
      setFormErrors((prev) => ({
        ...prev,
        category: false,
      }));
    }
  };

  const handleStudyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFormData((prev) => ({
      ...prev,
      study_uuid: e.target.value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (isFormValid()) {
      setIsConfirmModalOpen(true);
    }
  };

  const handleConfirmSubmit = async () => {
    const submitData = new FormData();
    submitData.append("title", formData.title);
    submitData.append("category", formData.category);
    submitData.append("author_name", formData.author_name);
    submitData.append("job_title", formData.job_title);
    submitData.append("description", formData.description);
    submitData.append("department_uuid", formData.department_uuid);

    if (formData.study_uuid) {
      submitData.append("study_uuid", formData.study_uuid);
    }

    if (formData.attach_content) {
      submitData.append(
        "attach_content",
        formData.attach_content,
        formData.attach_content.name
      );
    }

    try {
      await createPolicyMutation.mutateAsync(submitData);
      navigate("/org/dashboard/policy");
    } catch (error) {
      console.error("Error creating policy:", error);
      setIsConfirmModalOpen(false);
    }
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field when user types
    if (formErrors[name as keyof typeof formErrors]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: false,
      }));
    }
  };

  const nextStep = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Get selected study name for summary
  const selectedStudy = studiesData?.find((study: Study) => study.uuid === formData.study_uuid);

  return (
    <div className="add-policy-container">
      <div className="add-policy-header">
        <div className="add-policy-title">
          <h1>
            <FileUp size={24} style={{ marginRight: "10px" }} />
            Adding New Policy
          </h1>
        </div>
        <div className="add-sub-title">
          <h6>Create and share important guidelines with your team</h6>
        </div>
      </div>

      {/* Progress Indicator */}
      <div className="form-progress">
            <div
              className={`progress-step ${currentStep >= 1 ? "active" : ""} ${
                currentStep > 1 ? "completed" : ""
              }`}
            >
              <div className="step-indicator">
                {currentStep > 1 ? <CheckCircle size={16} /> : 1}
              </div>
              <div className="step-label">Basic Info</div>
            </div>
            <div
              className={`progress-step ${currentStep >= 2 ? "active" : ""} ${
                currentStep > 2 ? "completed" : ""
              }`}
            >
              <div className="step-indicator">
                {currentStep > 2 ? <CheckCircle size={16} /> : 2}
              </div>
              <div className="step-label">Details</div>
            </div>
            <div
              className={`progress-step ${currentStep >= 3 ? "active" : ""}`}
            >
              <div className="step-indicator">3</div>
              <div className="step-label">Attachments</div>
            </div>
          </div>

          {/* Form */}
          <form className="add-policy-form" onSubmit={handleSubmit}>
            {/* Step 1: Basic Info */}
            {currentStep === 1 && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="form-section">
                  <h3 className="form-section-title">
                    <FileText
                      size={18}
                      style={{ marginRight: "8px", verticalAlign: "middle" }}
                    />
                    Basic Information
                  </h3>

                  <div className="col-md-12">
                    <NurtifyText label="Policy Title*" />
                    <NurtifyInput
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter policy title"
                      required
                    />
                    {formErrors.title && (
                      <div
                        style={{
                          color: "#dc3545",
                          fontSize: "14px",
                          marginTop: "5px",
                        }}
                      >
                        <AlertCircle
                          size={14}
                          style={{
                            marginRight: "5px",
                            verticalAlign: "middle",
                          }}
                        />
                        Policy title is required
                      </div>
                    )}
                  </div>

                  <div className="col-md-12" style={{ marginTop: "20px" }}>
                    <NurtifyText label="Category*" />
                    <NurtifySelect
                      name="category"
                      value={formData.category}
                      onChange={handleCategoryChange}
                      options={[
                        { value: "", label: "Select a category" },
                        ...POLICY_CATEGORIES
                      ]}
                    />
                    {formErrors.category && (
                      <div
                        style={{
                          color: "#dc3545",
                          fontSize: "14px",
                          marginTop: "5px",
                        }}
                      >
                        <AlertCircle
                          size={14}
                          style={{
                            marginRight: "5px",
                            verticalAlign: "middle",
                          }}
                        />
                        Category is required
                      </div>
                    )}
                  </div>

                  <div className="col-md-12" style={{ marginTop: "20px" }}>
                    <NurtifyText label="Study (Optional)" />
                    <NurtifySelect
                      name="study_uuid"
                      value={formData.study_uuid || ""}
                      onChange={handleStudyChange}
                      options={[
                        { value: "", label: "Select a study (optional)" },
                        ...studyOptions
                      ]}
                    />
                  </div>
                </div>

                <div className="form-actions">
                  <button
                    type="button"
                    className="btn-submit"
                    onClick={nextStep}
                  >
                    Next
                  </button>
                </div>
              </motion.div>
            )}

            {/* Step 2: Author Details */}
            {currentStep === 2 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="form-section">
                  <h3 className="form-section-title">
                    <User
                      size={18}
                      style={{ marginRight: "8px", verticalAlign: "middle" }}
                    />
                    Author Information
                  </h3>

                  <div className="form-row">
                    <div className="form-col">
                      <NurtifyText label="Author Name*" />
                      <NurtifyInput
                        type="text"
                        name="author_name"
                        value={formData.author_name}
                        onChange={handleInputChange}
                        placeholder="Enter author name"
                        required
                      />
                      {formErrors.author_name && (
                        <div
                          style={{
                            color: "#dc3545",
                            fontSize: "14px",
                            marginTop: "5px",
                          }}
                        >
                          <AlertCircle
                            size={14}
                            style={{
                              marginRight: "5px",
                              verticalAlign: "middle",
                            }}
                          />
                          Author name is required
                        </div>
                      )}
                    </div>
                    <div className="form-col">
                      <NurtifyText label="Job Title*" />
                      <NurtifyInput
                        type="text"
                        name="job_title"
                        value={formData.job_title}
                        onChange={handleInputChange}
                        placeholder="Enter job title"
                        required
                      />
                      {formErrors.job_title && (
                        <div
                          style={{
                            color: "#dc3545",
                            fontSize: "14px",
                            marginTop: "5px",
                          }}
                        >
                          <AlertCircle
                            size={14}
                            style={{
                              marginRight: "5px",
                              verticalAlign: "middle",
                            }}
                          />
                          Job title is required
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="col-md-12" style={{ marginTop: "20px" }}>
                    <NurtifyText label="Short Description*" />
                    <NurtifyTextArea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Enter a brief description of the policy"
                    />
                    {formErrors.description && (
                      <div
                        style={{
                          color: "#dc3545",
                          fontSize: "14px",
                          marginTop: "5px",
                        }}
                      >
                        <AlertCircle
                          size={14}
                          style={{
                            marginRight: "5px",
                            verticalAlign: "middle",
                          }}
                        />
                        Description is required
                      </div>
                    )}
                  </div>
                </div>

                <div className="form-actions">
                  <button
                    type="button"
                    className="btn-cancel"
                    onClick={prevStep}
                  >
                    Back
                  </button>
                  <button
                    type="button"
                    className="btn-submit"
                    onClick={nextStep}
                  >
                    Next
                  </button>
                </div>
              </motion.div>
            )}

            {/* Step 3: Attachments */}
            {currentStep === 3 && (
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
              >
                <div className="form-section">
                  <h3 className="form-section-title">
                    <Paperclip
                      size={18}
                      style={{ marginRight: "8px", verticalAlign: "middle" }}
                    />
                    Attachments
                  </h3>

                  <div className="col-md-12">
                    <NurtifyText label="File Attachment*" />
                    <NurtifyAttachFileBox
                      onChange={(file) => {
                        setFormData((prev) => ({
                          ...prev,
                          attach_content: file,
                        }));
                        // Clear error when file is selected
                        if (formErrors.attach_content) {
                          setFormErrors((prev) => ({
                            ...prev,
                            attach_content: false,
                          }));
                        }
                      }}
                    />
                    {formErrors.attach_content && (
                      <div
                        style={{
                          color: "#dc3545",
                          fontSize: "14px",
                          marginTop: "5px",
                        }}
                      >
                        <AlertCircle
                          size={14}
                          style={{
                            marginRight: "5px",
                            verticalAlign: "middle",
                          }}
                        />
                        File attachment is required
                      </div>
                    )}
                    <div
                      style={{
                        fontSize: "14px",
                        color: "#4F547B",
                        marginTop: "10px",
                      }}
                    >
                      <Info
                        size={14}
                        style={{ marginRight: "5px", verticalAlign: "middle" }}
                      />
                      Supported file types: PDF, DOC, DOCX, PNG, JPG, JPEG
                    </div>
                  </div>

                  <div className="col-md-12" style={{ marginTop: "30px" }}>
                    <div className="form-summary">
                      <h4 style={{ fontSize: "18px", marginBottom: "15px" }}>
                        Summary
                      </h4>
                      <div className="summary-item">
                        <span className="summary-label">Policy Title:</span>
                        <span className="summary-value">{formData.title}</span>
                      </div>
                      <div className="summary-item">
                        <span className="summary-label">Category:</span>
                        <span className="summary-value">
                          {POLICY_CATEGORIES.find(cat => cat.value === formData.category)?.label || "No category selected"}
                        </span>
                      </div>
                      <div className="summary-item">
                        <span className="summary-label">Study:</span>
                        <span className="summary-value">
                          {selectedStudy ? selectedStudy.name : "No study selected"}
                        </span>
                      </div>
                      <div className="summary-item">
                        <span className="summary-label">Author:</span>
                        <span className="summary-value">
                          {formData.author_name}
                        </span>
                      </div>
                      <div className="summary-item">
                        <span className="summary-label">Job Title:</span>
                        <span className="summary-value">
                          {formData.job_title}
                        </span>
                      </div>
                      <div className="summary-item">
                        <span className="summary-label">Attachment:</span>
                        <span className="summary-value">
                          {formData.attach_content
                            ? formData.attach_content.name
                            : "No file attached"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="form-actions">
                  <button
                    type="button"
                    className="btn-cancel"
                    onClick={prevStep}
                  >
                    Back
                  </button>
                  <button
                    type="submit"
                    className="btn-submit"
                    disabled={createPolicyMutation.isPending}
                  >
                    {createPolicyMutation.isPending
                      ? "Submitting..."
                      : "Submit Policy"}
                  </button>
                </div>
              </motion.div>
            )}
          </form>
      <ConfirmationModal
        isOpen={isConfirmModalOpen}
        onClose={() => setIsConfirmModalOpen(false)}
        onConfirm={handleConfirmSubmit}
        title="Confirm Policy Submission"
        message="Are you sure you want to submit this policy?"
        subMessage="Please review all information before proceeding."
      />
    </div>
  );
}
