/* Page Layout */
.dashboard__content {
  /* padding: 20px; */ /* Removed padding, will be added to the scrollable content area */
  padding: 0;
  /* min-height: calc(100vh - 80px); */
  height: calc(100vh - 50px); /* Full height below the 70px header */
  display: flex;
  flex-direction: column; /* To make .container-fluid stretch */
  overflow: hidden; /* Prevent .dashboard__content itself from scrolling */
  transition: all 0.3s ease;
}

.dashboard__content > .container-fluid {
  flex-grow: 1;
  display: flex; /* To make .row stretch */
  flex-direction: column; /* .row is the main child that should stretch */
  overflow: hidden;
  /* px-0 is already applied via className in index.tsx, removing horizontal padding */
}

/* Main content adjustments */
/* This is the .row that contains sidebar and main content area */
.dashboard__content .row { /* This selector might be too general if other .row exist directly under .dashboard__content. Assuming it targets the primary layout row. */
  display: flex; /* Already present, but ensure it's the primary layout mechanism */
  flex-grow: 1; /* Make the row take all available vertical space in .container-fluid */
  height: 100%; 
  overflow: hidden; /* This row should not scroll */
  display: flex;
  transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Column adjustments with smoother transitions */
.dashboard__content .col-xl-3,
.dashboard__content .col-lg-4,
.dashboard__content .col-xl-9,
.dashboard__content .col-lg-8 {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Styling for the main content column that contains <Outlet /> */
/* Targeting <div className="col-xl-10 col-lg-9"> from dashboard/index.tsx */
.dashboard__content > .container-fluid > .row > .col-xl-10.col-lg-9 {
  height: 100%; /* Fill the height of the parent row */
  overflow-y: auto; /* Enable vertical scrolling ONLY for this content area */
  padding: 20px; /* Add internal padding for the content */
}

/* Ensure the sidebar column does not get unexpected overflow */
/* Targeting <div className="col-xl-2 col-lg-3"> from dashboard/index.tsx */
.dashboard__content > .container-fluid > .row > .col-xl-2.col-lg-3 {
  height: 100%; /* Sidebar should also fill the row's height */
  overflow: hidden !important; /* The sidebar itself should not scroll; its .menuList does */
}

/* When sidebar is collapsed */
.dashboard__content .row:has(.sidebar-dsbh.collapsed) .col-xl-3,
.dashboard__content .row:has(.sidebar-dsbh.collapsed) .col-lg-4 {
  width: 80px;
  flex: 0 0 80px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dashboard__content .row:has(.sidebar-dsbh.collapsed) .col-xl-9,
.dashboard__content .row:has(.sidebar-dsbh.collapsed) .col-lg-8 {
  width: calc(100% - 80px);
  flex: 0 0 calc(100% - 80px);
  max-width: calc(100% - 80px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Mobile adjustments with smoother transitions */
@media (max-width: 991px) {
  /* Hide the original container of the sidebar on mobile,
     as the mobile sidebar itself is position:fixed and manages its own display. */
  .dashboard__content > .container-fluid > .row > .col-xl-2.col-lg-3 {
    display: none;
  }

  /* Targeting the content column in mobile view */
  .dashboard__content .row:has(.sidebar-dsbh.mobile) .col-lg-9,
  .dashboard__content .row:has(.sidebar-dsbh.mobile) .col-xl-10 {
    width: 100%;
    flex: 0 0 100%;
    max-width: 100%;
    /* padding-left: 100px; */ /* This might be related to mobile sidebar interaction */
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    /* Ensure scrolling and padding for mobile content area */
    height: 100%; 
    overflow-y: auto;
    padding: 15px; /* Adjusted padding for mobile content */
  }
}



/* Sidebar Styles */
.sidebar.-dashboard {
  position: sticky;
  top: 20px;
  background: #ffffff;
  border-radius: 0; /* Removed border-radius */
  box-shadow: none; /* Removed box-shadow */
  transition: all 0.3s ease;
}

.sidebar__item {
  padding: 24px;
}

/* Sidebar Header */
.sidebar__header {
  margin-bottom: 24px;
}

.sidebar__title {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
}

.sidebar__subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 24px 0 16px 0;
}

/* Search Field */
.sidebar__search {
  margin-bottom: 20px;
}

.search-field {
  position: relative;
  margin-top: 16px;
}

.search-input {
  width: 100%;
  padding: 14px 18px;
  padding-left: 44px;
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 0; /* Removed border-radius */
  font-size: 14px;
  transition: all 0.2s ease;
  background-color: rgba(55, 183, 195, 0.03);
}

.search-input:focus {
  border-color: #37B7C3;
  box-shadow: none; /* Removed box-shadow */
  background-color: #fff;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 18px;
}

/* Checkbox Container */
.sidebar-checkbox {
  max-height: calc(100vh - 250px);
  overflow-y: auto;
  padding: 4px;
}

/* Scrollbar Styling */
.sidebar-checkbox::-webkit-scrollbar {
  width: 6px;
}

.sidebar-checkbox::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 0; /* Removed border-radius */
}

.sidebar-checkbox::-webkit-scrollbar-thumb {
  background: #37B7C3;
  border-radius: 0; /* Removed border-radius */
}

.sidebar-checkbox::-webkit-scrollbar-thumb:hover {
  background: #2d919a;
}

/* Checkbox Styling */
.form-checkbox {
  position: relative;
  display: flex;
  align-items: center;
  padding: 10px 14px;
  margin-bottom: 8px;
  border-radius: 0; /* Removed border-radius */
  transition: all 0.2s ease;
  background-color: #fff;
  border: 1px solid rgba(55, 183, 195, 0.08);
}

.form-checkbox {
  position: relative;
  display: flex;
  align-items: center;
  padding: 10px 14px;
  margin-bottom: 8px;
  border-radius: 0; /* Changed */
  transition: all 0.2s ease;
  background-color: #fff;
  border: 1px solid rgba(55, 183, 195, 0.08);
}

.form-checkbox:hover {
  background-color: rgba(55, 183, 195, 0.04);
  border-color: rgba(55, 183, 195, 0.2);
  /* transform: translateY(-1px); */ /* Removed */
  box-shadow: none; /* Changed */
}

.form-checkbox__input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.form-checkbox__label {
  position: relative;
  padding-left: 32px;
  cursor: pointer;
  font-size: 15px;
  line-height: 1.5;
  color: #333;
  user-select: none;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  font-weight: 400;
}

.form-checkbox__label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 55%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(55, 183, 195, 0.4);
  border-radius: 0; /* Removed border-radius */
  background-color: #fff;
  transition: all 0.2s ease;
  box-shadow: none; /* Removed box-shadow */
}

.form-checkbox__input:checked + .form-checkbox__label:before {
  background-color: #37B7C3;
  border-color: #37B7C3;
  box-shadow: none; /* Removed box-shadow */
}

.form-checkbox__input:checked + .form-checkbox__label:after {
  content: '';
  position: absolute;
  left: 7px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
}

.form-checkbox__count {
  font-size: 13px;
  color: #37B7C3;
  background: rgba(55, 183, 195, 0.08);
  padding: 4px 10px;
  border-radius: 0; /* Removed border-radius */
  font-weight: 500;
  transition: all 0.2s ease;
}

.form-checkbox:hover .form-checkbox__count {
  background: rgba(55, 183, 195, 0.12);
}

/* Tags Styling */
.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  display: inline-block;
  padding: 8px 14px;
  background: rgba(55, 183, 195, 0.08);
  color: #37B7C3;
  border-radius: 0; /* Removed border-radius */
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(55, 183, 195, 0.12);
}

.tag-item {
  display: inline-block;
  padding: 8px 14px;
  background: rgba(55, 183, 195, 0.08);
  color: #37B7C3;
  border-radius: 0; /* Changed */
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(55, 183, 195, 0.12);
}

.tag-item:hover {
  background: rgba(55, 183, 195, 0.12);
  /* transform: translateY(-1px); */ /* Removed */
  box-shadow: none; /* Changed */
}

/* Form Cards Grid */
.forms-grid {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(4, 1fr);
  margin-bottom: 30px;
}

/* Responsive Adjustments */
@media (max-width: 1400px) {
  .forms-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 991px) {
  .forms-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 767px) {
  .forms-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

/* Form Card Animation */
.forms-grid > * {
  opacity: 1;
  transform: none;
  animation: none;
}

/* Transitions and Hover Effects */
.hover-shadow-2 {
  transition: all 0.3s ease;
}

.hover-shadow-2:hover {
  /* transform: translateY(-5px); */ /* Removed */
  box-shadow: none !important; /* Changed */
}

/* Utility Classes */
.shadow-1 {
  box-shadow: none; /* Changed */
}

.border-light {
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.bg-light-4 {
  background-color: #f5f7fa;
}

/* Loading States */
.form-checkbox__input:disabled + .form-checkbox__label {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Add spacing between cards */
/* This rule for .row.y-gap-30 might be too general. 
   If it's causing issues for the main layout row (sidebar + content), 
   consider removing the y-gap-30 class from that specific row in index.tsx 
   or making this selector more specific to rows *within* the content area.
   For now, leaving it as is, but it's a potential source of "too much gaps".
*/
.row.y-gap-30 {
  row-gap: 20px !important;
}

@media (max-width: 991px) {
  .row.y-gap-30 {
    row-gap: 15px !important;
  }
}

@media (max-width: 480px) {
  .dashboard__content {
    padding: 10px;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 20px;
  border-radius: 0; /* Removed border-radius */
  box-shadow: none; /* Removed box-shadow */
  max-width: 500px;
  width: 100%;
}

/* Corporate style overrides for content within the Outlet */
.dashboard__content > .container-fluid > .row > .col-xl-10.col-lg-9 [class*="card"],
.dashboard__content > .container-fluid > .row > .col-xl-10.col-lg-9 .bg-white,
.dashboard__content > .container-fluid > .row > .col-xl-10.col-lg-9 > div[class*="bg-"], /* Covers bg-light, bg-white etc. if used on direct children */
.dashboard__content > .container-fluid > .row > .col-xl-10.col-lg-9 > section[class*="bg-"] {
  border-radius: 0 !important;
  box-shadow: none !important;
}

/* Ensure specific components like forms inside the outlet also get the corporate look if they didn't match above */
.dashboard__content > .container-fluid > .row > .col-xl-10.col-lg-9 .form-control,
.dashboard__content > .container-fluid > .row > .col-xl-10.col-lg-9 .form-select {
  border-radius: 0;
}
