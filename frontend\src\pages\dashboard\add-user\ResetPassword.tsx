import { useParams } from "react-router-dom";
import ResetPasswordModal from "./ResetPasswordModal";
import "./reset-password.css";

export default function ResetPassword() {
    const params = useParams<Record<string, string>>();
    const uid = params.uid ?? "";
    const token = params.token ?? "";

    if (!uid || !token) {
        return (
            <div className="main-content">
                <div className="content-wrapper">
                    <div className="row justify-content-center pt-5" style={{height: "100vh"}}>
                        <div className="col-md-6 col-lg-5 col-xl-4">
                            <div className="reset-password-card bg-white mb-5 mt-5 border-0">
                                <div className="card-body p-5 text-center">
                                    <h3 className="mb-3">Error</h3>
                                    <p className="text-muted mb-4">Invalid password reset link. Please request a new password reset link.</p>
                                    <a href="/login" className="btn btn-nurtify w-100">
                                        Return to Login
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return <ResetPasswordModal uid={uid} token={token} />;
}
