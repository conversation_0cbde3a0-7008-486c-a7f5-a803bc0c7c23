.add-patient-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.add-patient-form {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.add-new-patient-form-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #333;
  text-align: center;
}

.tabs-container {
  display: flex;
  margin-bottom: 24px;
  border-bottom: 1px solid #e0e0e0;
  position: relative;
}

.tab {
  padding: 12px 16px;
  cursor: pointer;
  font-weight: 500;
  color: #666;
  display: flex;
  align-items: center;
  gap: 8px;
  border-bottom: 3px solid transparent;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
}

.tab.active {
  color: #37b7c3;
  border-bottom-color: #37b7c3;
  background-color: rgba(37, 99, 235, 0.05);
}

.tab.complete:not(.active) {
  color: #10b981;
  border-bottom-color: #10b981;
}

.tab.incomplete {
  color: #ef4444;
  border-bottom-color: #ef4444;
}

.tab-icon {
  font-size: 18px;
}

.tab-warning-icon {
  color: #ef4444;
}

.tab-complete-icon {
  color: #10b981;
}

.tab-content {
  margin-bottom: 24px;
}

.form-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;
}

.form-column {
  flex: 1;
  min-width: 250px;
}

.wide-column {
  flex-basis: 100%;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #d1d5db;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.form-group input:focus,
.form-group select:focus {
  border-color: #37b7c3;
  outline: none;
  box-shadow: 0 0 0 2px rgba(55, 183, 195, 0.2);
}

.form-group.invalid input,
.form-group.invalid select {
  border-color: #ef4444;
}

.field-error {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

.validation-error-message {
  color: #ef4444;
  font-size: 14px;
  margin-bottom: 16px;
  text-align: center;
  padding: 8px;
  background-color: rgba(239, 68, 68, 0.1);
  border-radius: 4px;
}

.button-container {
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.cancel-button,
.submit-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.cancel-button {
  background-color: #f3f4f6;
  color: #4b5563;
}

.cancel-button:hover {
  background-color: #e5e7eb;
}

.submit-button {
  background-color: #37b7c3;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.submit-button:hover {
  background-color: #1c8e98;
}

.submit-button:disabled {
  background-color: #93c5fd;
  cursor: not-allowed;
}

.recheck-title {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #333;
}

.confirmation-message {
  margin-top: 16px;
  padding: 12px;
  background-color: #f0fdf4;
  border-left: 4px solid #10b981;
  border-radius: 4px;
}

.confirmation-message p {
  margin: 0;
  color: #065f46;
}

/* Search tab styles */
.search-tab-content {
  padding: 10px 0;
}

.search-tab-footer {
  margin-top: 20px;
  padding: 15px;
  background-color: #f9fafb;
  border-radius: 6px;
  text-align: center;
}

.search-tab-footer p {
  margin-bottom: 15px;
  color: #4b5563;
}

.continue-button {
  background-color: #37b7c3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 10px 20px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.continue-button:hover {
  background-color: #1c8e98;
}

/* Design-specific styles */
.form-group.design-1 input {
  border-left: 4px solid #37b7c3;
}

.form-group.design-1.invalid input {
  border-left: 4px solid #ef4444;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .form-grid {
    flex-direction: column;
  }

  .tab span {
    font-size: 14px;
  }
}
