.patient-search-container {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.search-title {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: #333;
}

.search-icon {
  margin-right: 10px;
  color: #37b7c3;
}

.search-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.search-form-row {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.search-form-field {
  flex: 1;
  min-width: 250px;
}

.search-form-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.search-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.clear-button {
  background-color: #f0f0f0;
  color: #555;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.clear-button:hover {
  background-color: #e0e0e0;
}

.search-button {
  background-color: #37b7c3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s;
}

.search-button:hover {
  background-color: #1c8e98;
}

.search-loading {
  text-align: center;
  padding: 20px;
  color: #555;
  font-style: italic;
}

.search-error {
  background-color: #fff2f2;
  border-left: 4px solid #ff4d4f;
  padding: 10px 15px;
  margin: 15px 0;
  color: #cf1322;
}

.search-results {
  margin-top: 30px;
}

.search-results h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: #333;
}

.no-results {
  padding: 20px;
  text-align: center;
  background-color: #f9f9f9;
  border-radius: 4px;
  color: #666;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.patient-result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.patient-result-item:hover {
  background-color: #f0f0f0;
}

.patient-info {
  flex: 1;
}

.patient-name {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 5px;
  color: #333;
}

.patient-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  color: #666;
  font-size: 0.9rem;
}

.select-patient-btn {
  background-color: #37b7c3;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.select-patient-btn:hover {
  background-color: #3a5ce5;
}

.pagination {
  margin-top: 15px;
  text-align: center;
  color: #666;
  font-style: italic;
}

@media (max-width: 768px) {
  .search-form-row {
    flex-direction: column;
    gap: 10px;
  }

  .search-form-field {
    width: 100%;
  }

  .patient-details {
    flex-direction: column;
    gap: 5px;
  }
}
