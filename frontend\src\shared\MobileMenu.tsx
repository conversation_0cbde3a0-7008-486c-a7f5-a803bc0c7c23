import MobileFooter from "./MobileFooter";
import { Link } from "react-router-dom";
import { useEffect, useState, FC, useMemo } from "react";
import { useLocation } from "react-router-dom";
import { X } from "lucide-react";
//import { useCurrentUserQuery } from "@/hooks/user.query";


interface MenuItem {
  title: string;
  disabled?: boolean;
  links?: {
    title: string;
    href: string;
    links?: {
      title: string;
      href: string;
    }[];
  }[];
}

interface MobileMenuProps {
  setActiveMobileMenu: (active: boolean) => void;
  activeMobileMenu: boolean;
}

const MobileMenu: FC<MobileMenuProps> = ({ setActiveMobileMenu, activeMobileMenu }) => {
//const { data: currentUser } = useCurrentUserQuery();
const menuList: MenuItem[] = useMemo(() => [
    { title: "Dashboard" },
    { title: "Home" },
    ...(import.meta.env.VITE_APP_MODE === "development" ? [{ title: "Resources", disabled: true }] : []),
    { title: "Contact Us" },
    ...(import.meta.env.VITE_APP_MODE === "development" ? [{ title: "Switch View" }] : []),
    { title: "Login" },
    { title: "Logout" },
  ], []);

  const [showMenu, setShowMenu] = useState<boolean>(false);
  const [, setMenuNesting] = useState<string[]>([]);
  const [menuItem, setMenuItem] = useState<string>("");
  const [, setSubmenu] = useState<string>("");
  const { pathname } = useLocation();

  const handleMobileMenuItemClick = (item: MenuItem): void => {
    if (item.disabled) {
      return; // Do nothing for disabled items
    }
    
    const basePath = "/org/dashboard";
    const title = item.title;
    
    setActiveMobileMenu(false); // Close mobile menu after click
    
    // Use direct navigation with window.location for a more reliable approach
    setTimeout(() => {
      if (title === "Dashboard") {
        window.location.href = basePath;
      } else if (title === "Logout") {
        // For logout, use full page refresh to ensure clean state
        localStorage.removeItem("authToken");
        window.location.href = "/login";
      } else if (title === "Login") {
        window.location.href = "/login";
      } else if (title === "Resources") {
        window.location.href = "/sdk";
      } else if (title === "Contact Us") {
        window.location.href = "/contact-us";
      } else if (title === "Switch View") {
        window.location.href = "/selection";
      } else if (title === "Home") {
        window.location.href = "/home";
      }
    }, 10);
  };

  useEffect(() => {
    menuList.forEach((elm) => {
      elm.links?.forEach((elm2) => {
        if (elm2.href?.split("/")[1] === pathname?.split("/")[1]) {
          setMenuItem(elm.title);
        } else {
          elm2.links?.forEach((elm3) => {
            if (elm3.href?.split("/")[1] === pathname?.split("/")[1]) {
              setMenuItem(elm.title);
              setSubmenu(elm2.title);
            }
          });
        }
      });
    });
  }, [pathname, menuList]);

  useEffect(() => {
    setShowMenu(true);
  }, []);

  return (
    <div
      className={`header-menu js-mobile-menu-toggle ${
        activeMobileMenu ? "-is-el-visible" : ""
      }`}
    >
      <div className="header-menu__content">
        <div className="mobile-bg js-mobile-bg"></div>

        <div className="d-none xl:d-flex items-center px-20 py-20 border-bottom-light">
          <Link
            to="/login"
            className={`button text-white btn-nurtify mr-10 px-4 py-2 ${
              pathname === "/login" ? "" : ""
            } `}
          >
            Log in
          </Link>
          <Link
            to="/login"
            className={`button btn-nurtify-light px-4 py-2 ${
              pathname === "/login" ? "" : ""
            } `}
          >
            Sign Up
          </Link>
        </div>

        {showMenu && activeMobileMenu && (
          <div className="mobileMenu text-dark-1">
            {menuList.map((elm, i) => {
              if (elm.title) {
                return (
                  <div
                    key={i}
                    className="submenuOne"
                    onClick={() => handleMobileMenuItemClick(elm)}
                  >
                    <div
                      className="title"
                      onClick={() =>
                        setMenuNesting((pre) =>
                          pre[0] === elm.title ? [] : [elm.title]
                        )
                      }
                    >
                      <span
                        className={
                          elm.title === menuItem ? "activeMenu" : "inActiveMenu"
                        }
                        style={elm.disabled ? {
                          opacity: 0.5,
                          cursor: 'not-allowed',
                          textDecoration: 'none'
                        } : {}}
                      >
                        {elm.title}
                      </span>
                    </div>
                  </div>
                );
              }
              return null;
            })}
          </div>
        )}

        {/* mobile footer start */}
        <MobileFooter />
        {/* mobile footer end */}
      </div>

      <div
        className="header-menu-close"
        onClick={() => {
          setActiveMobileMenu(false);
        }}
        data-el-toggle=".js-mobile-menu-toggle"
      >
        <div className="size-40 d-flex items-center justify-center rounded-full bg-white">
        <X size={20} style={{color: "var(--color-dark-1)", strokeWidth: 2, fill: "var(--color-dark-1)", backgroundColor: "transparent"}} />
        </div>
      </div>

      <div
        className="header-menu-bg"
        onClick={() => setActiveMobileMenu(false)}
      ></div>
    </div>
  );
};

export default MobileMenu;
