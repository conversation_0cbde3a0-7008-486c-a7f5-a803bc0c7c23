import React, { useState } from "react";
import { motion } from "framer-motion";
import { usePatientConversations, useUpdateReimbursementStatus } from "@/hooks/conversation.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { formatDate } from "@/utils/date";
import { Navigate } from "react-router-dom";
import DataTable, { Column } from "@/components/common/DataTable";
import { Conversation } from "@/services/api/types";
import "./conversations.css";

interface EditModalProps {
  isOpen: boolean;
  onClose: () => void;
  conversation: Conversation;
  onUpdate: (status: "received" | "not_received") => Promise<void>;
  isUpdating: boolean;
}

const EditModal: React.FC<EditModalProps> = ({ isOpen, onClose, conversation, onUpdate, isUpdating }) => {
  const [status, setStatus] = useState<"received" | "not_received">(
    conversation.reimbursement_status || "not_received"
  );

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Update Reimbursement Status</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        <form onSubmit={async (e) => {
          e.preventDefault();
          await onUpdate(status);
        }}>
          <div className="form-group">
            <label htmlFor="status">Reimbursement Status</label>
            <select
              className="form-control"
              id="status"
              value={status}
              onChange={(e) => setStatus(e.target.value as "received" | "not_received")}
              required
            >
              <option value="received">Received</option>
              <option value="not_received">Not Received</option>
            </select>
          </div>
          <div className="modal-footer">
            <button type="submit" className="button -md btn-nurtify text-white me-2" disabled={isUpdating}>
              {isUpdating ? "Saving..." : "Save"}
            </button>
            <button type="button" className="button -md btn-nurtify-lighter" onClick={onClose} disabled={isUpdating}>
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const ConversationsSection: React.FC = () => {
  const { data: currentUser } = useCurrentUserQuery();
  const [editingConversation, setEditingConversation] = useState<Conversation | null>(null);
  
  const patientUuid = (currentUser && typeof currentUser === 'object' && 'patient_uuid' in currentUser)
    ? (currentUser as { patient_uuid: string }).patient_uuid
    : "";

  const { data: conversations, isLoading } = usePatientConversations(patientUuid);
  const updateReimbursementStatus = useUpdateReimbursementStatus(editingConversation?.uuid || "");

  const formatStatus = (status: string) => {
    if (status === "inprogress") {
      return "In Progress";
    }
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const handleEdit = (conversation: Conversation) => {
    setEditingConversation(conversation);
  };

  const handleCloseModal = () => {
    setEditingConversation(null);
  };

  const handleUpdateStatus = async (status: "received" | "not_received") => {
    if (!editingConversation) return;
    
    try {
      await updateReimbursementStatus.mutateAsync(status);
      handleCloseModal();
    } catch (error) {
      console.error("Error updating reimbursement status:", error);
    }
  };

  if (!patientUuid) {
    return <Navigate to="/patient" replace />;
  }

  const conversationColumns: Column<Conversation>[] = [
    { 
      key: "treated_by_details", 
      header: "Treated By", 
      sortable: true,
      render: (value: unknown, row?: Conversation) => {
        if (row?.status !== "reimbursed") {
          return "Not treated yet";
        }
        if (value && typeof value === 'object' && 'first_name' in value && 'last_name' in value) {
          return `${value.first_name} ${value.last_name}`;
        }
        return '';
      }
    },
    { 
      key: "status", 
      header: "Status", 
      sortable: true,
      render: (value: unknown) => {
        if (typeof value === 'string') {
          return formatStatus(value);
        }
        return '';
      }
    },
    { 
      key: "reimbursement_status", 
      header: "Reimbursement Status", 
      sortable: true,
      render: (value: unknown) => {
        if (typeof value === 'string') {
          return formatStatus(value);
        }
        return 'Not Set';
      }
    },
    { 
      key: "comments", 
      header: "Comments", 
      sortable: true 
    },
    { 
      key: "created_at", 
      header: "Submitted Date", 
      sortable: true,
      render: (value: unknown) => {
        if (typeof value === 'string') {
          return formatDate(value);
        }
        return '';
      }
    },
    {
      key: "actions" as keyof Conversation,
      header: "Actions",
      render: (_: unknown, row?: Conversation) => row && (
        <button
          className="patclin-conversation-button"
          onClick={() => handleEdit(row)}
          disabled={row.reimbursement_status === "received" || row.status !== "reimbursed"}
        >
          Edit
        </button>
      ),
    }
  ];

  return (
    <motion.div
      className="patclin-tab-content"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <h2 className="patclin-section-title">My Conversations</h2>
      {isLoading ? (
        <div className="text-center">Loading conversations...</div>
      ) : conversations && conversations.length > 0 ? (
        <DataTable
          data={conversations}
          columns={conversationColumns}
          noDataMessage="No conversations found"
        />
      ) : (
        <div className="patclin-empty-state">
          <p>You have no active conversations at this time.</p>
        </div>
      )}

      {editingConversation && (
        <EditModal
          isOpen={!!editingConversation}
          onClose={handleCloseModal}
          conversation={editingConversation}
          onUpdate={handleUpdateStatus}
          isUpdating={updateReimbursementStatus.isPending}
        />
      )}
    </motion.div>
  );
};

export default ConversationsSection;
