/* Page Layout */
.department-details__content {
    padding: 30px;
    min-height: calc(100vh - 80px); /* Adjust based on your header height */
    background-color: #f5f7fa; /* Match dashboard background */
}

.department-card {
    background-color: #ffffff;
    border-radius: 0; /* Removed border-radius */
    box-shadow: none; /* Removed box-shadow */
    padding: 30px;
    margin-bottom: 30px;
}

.department-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.department-title {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
}

/* Search Field */
.department-search-field {
    position: relative;
    margin-top: 16px;
}

.department-search-input {
    width: 100%;
    padding: 14px 18px;
    padding-left: 44px;
    border: 1px solid rgba(55, 183, 195, 0.15);
    border-radius: 0; /* Removed border-radius */
    font-size: 14px;
    transition: all 0.2s ease;
    background-color: rgba(55, 183, 195, 0.03);
}

.department-search-input:focus {
    border-color: #37B7C3;
    box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.1);
    background-color: #fff;
}

.department-search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    font-size: 18px;
}

/* Tags Styling */
.department-tags-wrapper {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.department-tag-item {
    display: inline-block;
    padding: 8px 14px;
    background: rgba(55, 183, 195, 0.08);
    color: #37B7C3;
    border-radius: 0; /* Removed border-radius */
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid rgba(55, 183, 195, 0.12);
}

.department-tag-item:hover {
    background: rgba(55, 183, 195, 0.12);
    transform: translateY(-1px);
    box-shadow: none; /* Removed box-shadow */
}

/* Form Cards Grid */
.department-forms-grid {
    display: grid;
    gap: 30px;
    grid-template-columns: repeat(4, 1fr);
    margin-bottom: 30px;
}

.department-details-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.department-details-title {
    margin-bottom: 10px;
}

.department-details-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.editableDepartment-container {
    display: flex;
    justify-content: flex-end;
}






/* Responsive Adjustments */
@media (max-width: 1400px) {
    .department-forms-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 991px) {
    .department-forms-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .editableDepartment-container {
        flex-direction: column;
        width: 100%;
    }
}

@media (max-width: 767px) {
    .department-forms-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    .department-details-title h1 {
        font-size: 20px;
    }
}

/* Form Card Animation */
.department-forms-grid > * {
    animation: department-fadeIn 0.3s ease-out forwards;
}

@keyframes department-fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Transitions and Hover Effects */
.department-hover-shadow-2 {
    transition: all 0.3s ease;
}

.department-hover-shadow-2:hover {
    transform: translateY(-5px);
    box-shadow: none !important; /* Removed box-shadow */
}

/* Utility Classes */
.department-shadow-1 {
    box-shadow: none; /* Removed box-shadow */
}

.department-border-light {
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.department-bg-light-4 {
    background-color: #f5f7fa;
}

/* Loading States */
.department-form-checkbox__input:disabled + .department-form-checkbox__label {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Tabs Styling */
.department-tabs-wrapper {
    display: flex;
    gap: 1rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0;
    margin-bottom: 30px;
}

.department-tab-button {
    background: none;
    border: none;
    padding: 0.75rem 1.25rem;
    font-size: 1rem;
    color: #6b7280;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    border-radius: 8px 8px 0 0;
}

.department-tab-button:hover {
    color: #374151;
    background-color: rgba(55, 183, 195, 0.05);
}

.department-tab-button.active {
    color: #37B7C3;
    background-color: rgba(55, 183, 195, 0.08);
}

.department-tab-button.active::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #37B7C3;
}

/* Form Styling */
.department-form-section {
    margin-bottom: 30px;
}

.department-form-row {
    margin-bottom: 20px;
}

/* Button Styles */
.department-btn-custom {
    min-width: 150px;
    height: 50px;
    border-radius: 0; /* Removed border-radius */
    background-color: #37B7C3;
    color: #fff;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 25px;
    margin-left: 10px;
    cursor: pointer;
    border: none;
    transition: all 0.3s ease;
    font-weight: 500;
    box-shadow: none; /* Removed box-shadow */
}

.department-btn-custom:hover {
    background-color: #2d919a;
    transform: translateY(-2px);
    box-shadow: none; /* Removed box-shadow */
}

.department-btn-secondary {
    background-color: #f5f7fa;
    color: #4f547b;
    border: 1px solid #e5e7eb;
    box-shadow: none;
}

.department-btn-secondary:hover {
    background-color: #e5e7eb;
    color: #1a1a1a;
    box-shadow: none; /* Removed box-shadow */
}

.department-btn-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    gap: 10px;
}

/* Disable border for password input */
.department-no-border {
    border: none !important;
    background-color: transparent !important;
    box-shadow: none !important;
}

.department-page-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #f5f7fa;
}

.department-layout {
    display: flex;
    height: 100%;
}

.department-tab-button {
    cursor: pointer;
    padding: 0.75rem;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.department-tab-button.active {
    color: #37b7c3;
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .department-details__content {
        padding: 20px;
    }
    
    .department-card {
        padding: 20px;
    }

    .department-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .department-btn-container {
        flex-direction: column;
        width: 100%;
    }

    .department-btn-custom {
        width: 100%;
        margin-left: 0;
        margin-top: 10px;
    }

    .department-tabs-wrapper {
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .department-tab-button {
        flex: 1 0 auto;
        min-width: 120px;
        text-align: center;
    }
}

@media (max-width: 767px) {
    .department-details__content {
        padding: 15px;
    }
    
    .department-card {
        padding: 15px;
    }
    
    .department-tabs-wrapper {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .department-tab-button {
        width: 100%;
        text-align: left;
        border-radius: 8px;
    }
    
    .department-tab-button.active::after {
        display: none;
    }
    
    .department-tab-button.active {
        background-color: rgba(55, 183, 195, 0.15);
    }
}


.department-detail-input {
    display: flex;
    flex-direction: column;
    justify-content: start;
    background:none;
    border: 1px solid #37B7C3;
    border-radius: 0; /* Removed border-radius */
    width: auto;
    padding: 10px;
    color: black;
  }

  .department-detail-input[type="text"],
.department-detail-input input[type="number"],
.department-detail-input input[type="password"] {
  border: none;
  outline: none;
  background-color: transparent;
  padding: 0px;
}

.dep-details-btn-custom {
    width: 90px;
    height: 40px;
    border-radius: 0; /* Removed border-radius */
    background-color: #37B7C3;
    color: #fff;
    display: inline-flex;
    align-items: center;
    align-self: flex-end;
    justify-content: center;
    text-align: center;
    line-height: 50px;
    margin-left: 10px;
    cursor: pointer;
    border: none;
    transition: background-color 0.3s ease;
}

.dep-details-btn-custom:hover {
    background-color: #2d919a;
}
