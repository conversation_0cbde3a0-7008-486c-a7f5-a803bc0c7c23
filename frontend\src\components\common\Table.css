.table-container {
    width: 100%;
    max-height: 400px; /* Adjust height as needed */
   
  }
  
  
  table {
    width: 100%;
    border-collapse: collapse;
    
    
  }
  
  thead {
    position: sticky;
    color: #A3AED0;
    text-align: start;
    top: 0;
    background-color: #ffffff;
    z-index: 10;
    box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05);
   
  }

  tbody {
    overflow-y: auto;
  }

 
  
  th, td {   
    text-align: start;
    font-weight: bold;
    font-size: small;
    
  }
  
  td {
    padding: 12px;
    font-weight: bold;
  }

  th {
    padding: 8px;
    border-bottom: 1px solid #A3AED0;
  }

  
  
  /* Custom scrollbar */
  .table-container::-webkit-scrollbar {
    width: 8px;
    height: 3px;

  }
  
  .table-container::-webkit-scrollbar-track {
    background: #ffffff;
    margin-top: 40px; /* Reduces the scrollbar height */

  }
  
  .table-container::-webkit-scrollbar-thumb {
    background: #37B7C3;
    border-radius: 4px;
   

  }
  
  .table-container::-webkit-scrollbar-thumb:hover {
    background: #2a8b94;
  }


.iconButton {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    width: auto;
    height: auto;
    padding: 0.5rem;
    border-radius: 8px;
    border: none;
    transition: all 0.2s ease;
    background-color: #37B7C3;
    border-radius: 10px;
    border-color: black;
    color: white;
  }

  .button-wrapper {
    position: relative;
    display: inline-block;
  }
  
  .tooltip {
    visibility: hidden;
    background-color: #333;
    color: #fff;
    text-align: center;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: small;
    
    /* Position the tooltip below the button */
    position: absolute;
    z-index: 1000;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    
    /* Fade transition */
    opacity: 0;
    transition: opacity 0.2s ease;
    
    /* Prevent text wrapping */
    white-space: nowrap;
  }
  
  /* Arrow styles */
  .tooltip::before {
    content: '';
    position: absolute;
    bottom: 100%; /* Places arrow at top of tooltip */
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-bottom-color: #333; /* Matches tooltip background */
  }
  
  /* Show tooltip on hover */
  .button-wrapper:hover .tooltip {
    visibility: visible;
    opacity: 1;
  }