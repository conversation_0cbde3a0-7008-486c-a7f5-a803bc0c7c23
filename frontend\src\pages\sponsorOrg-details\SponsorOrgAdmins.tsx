import { useEffect, useState } from "react";
import "./sponsorOrg-admin.css";
import { 
  useCreateSponsorMutation, 
  useSponsorListByOrgQuery, 
  useResendActivationEmailMutation, 
  useDeleteAdminMutation 
} from "@/hooks/user.query.ts";
import { Admin } from "@/types/types";
import { Trash2 } from "lucide-react";
import DeleteAdminModal from "@/components/modal/DeleteAdminModal";

interface SponsorOrgAdminsProps {
  uuid: string;
}

const ROLE_CHOICES = [
  { value: "Admin", label: "Admin" },
  { value: "Manager", label: "Manager" },
  { value: "Coordinator", label: "Coordinator" },
  { value: "Reviewer", label: "Reviewer" },
  { value: "Other", label: "Other, please specify" },
];

const SponsorOrgAdmins: React.FC<SponsorOrgAdminsProps> = ({ uuid }) => {
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [showAddSection, setShowAddSection] = useState(false);
  const [emailInput, setEmailInput] = useState("");
  const [role, setRole] = useState("");

  const createAdminMutation = useCreateSponsorMutation();
  const resendActivationMutation = useResendActivationEmailMutation();
  const { data: adminData } = useSponsorListByOrgQuery(uuid);
  const deleteAdminMutation = useDeleteAdminMutation();

  useEffect(() => {
    if (adminData) {
      setAdmins(adminData);
    }
  }, [adminData]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [deletableIdentifier, setDeletableIdentifier] = useState("");

  const handleSubmitModal = async () => {
    try {
      await deleteAdminMutation.mutateAsync({ identifier: deletableIdentifier });
      setIsModalOpen(false);
      setAdmins(admins.filter(admin => admin.identifier !== deletableIdentifier));
    } catch (error) {
      console.error("Error deleting sponsor org admin:", error);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleDeleteAdmin = (identifier: string) => {
    setDeletableIdentifier(identifier);
    setIsModalOpen(true);
  };

  const handleResendActivation = (identifier: string) => {
    resendActivationMutation.mutate({ identifier }, {
      onSuccess: () => {
        console.log(`Activation email resent to ${identifier}`);
      },
      onError: (error) => {
        console.error(`Failed to resend activation email to ${identifier}:`, error);
      },
    });
  };

  const handleAddNewAdmin = () => {
    if (emailInput && role) {
      const formData = new FormData();
      formData.append("email", emailInput);
      formData.append("sponsorOrg_uuid", uuid);
      formData.append("role", role);

      createAdminMutation.mutate(formData, {
        onSuccess: (newAdmin) => {
          const newAdminData: Admin = {
            identifier: newAdmin?.identifier || "1",
            is_active: false,
            first_name: newAdmin?.first_name || "",
            last_name: newAdmin?.last_name || "",
            email: emailInput,
            phone_number: newAdmin?.phone_number || "",
          };
          setAdmins([...admins, newAdminData]);
          setEmailInput("");
          setRole("");
          setShowAddSection(false);
        },
        onError: (error) => {
          console.error("Failed to create admin:", error);
        },
      });
    }
  };

  const getInitials = (first_name: string, last_name: string) => {
    if (!first_name && !last_name) {
      return "NA";
    }
    return `${first_name.charAt(0)}${last_name.charAt(0)}`.toUpperCase();
  };

  const getStatus = (is_active: boolean) => {
    return is_active ? "Active" : "Invited";
  };

  return (
    <>
      <div className="header">
        <div>
          <p>Invite or manage your sponsor organization's admins.</p>
        </div>
        <button
          className={`add-member-btn ${showAddSection ? "active" : ""}`}
          onClick={() => {
            setShowAddSection(!showAddSection);
            if (showAddSection) {
              setEmailInput("");
              setRole("");
            }
          }}
        >
          {showAddSection ? "Cancel" : "+ Add New Admin"}
        </button>
      </div>

      {showAddSection && (
        <div className="add-member-section">
          <div className="new-admin-form">
            <input
              type="email"
              value={emailInput}
              onChange={(e) => setEmailInput(e.target.value)}
              placeholder="Enter email address"
              className="enhanced-input"
            />
            <select
              value={role}
              onChange={(e) => setRole(e.target.value)}
              className="enhanced-input"
              required
            >
              <option value="" disabled>
                Select role
              </option>
              {ROLE_CHOICES.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <button
              onClick={handleAddNewAdmin}
              disabled={!emailInput || !role || createAdminMutation.isPending}
              className="action-btn primary"
            >
              {createAdminMutation.isPending ? "Inviting..." : "Invite"}
            </button>
          </div>
        </div>
      )}

      <div className="users-list">
        {admins.length > 0 ? (
          admins.map((admin) => (
            <div key={admin.identifier} className="user-row">
              <div className="user-info">
                <div className="user-initials">
                  {getInitials(admin.first_name ? admin.first_name : "", admin.last_name ? admin.last_name : "")}
                </div>
                <div>
                  <p className="user-name">
                    {admin.first_name && admin.last_name
                      ? `${admin.first_name} ${admin.last_name}`
                      : "New Admin"}
                  </p>
                  <p className="user-email">{admin.email}</p>
                </div>
              </div>
              <div className="user-status">
                <span className={`status ${getStatus(admin.is_active || false).toLowerCase()}`}>
                  {getStatus(admin.is_active || false)}
                </span>
              </div>
              <div className="user-actions">
                {!admin.is_active && (
                  <button
                    className="resend-btn"
                    onClick={() => handleResendActivation(admin.identifier || '')}
                    disabled={resendActivationMutation.isPending || admin.is_active || resendActivationMutation.isSuccess}
                  >
                    {resendActivationMutation.isPending ? "Resending..." : "Resend Activation"}
                  </button>
                )}
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    handleDeleteAdmin(admin.identifier || '');
                  }}
                  style={{ transition: 'transform 0.2s ease' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'scale(1.2)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                >
                  <Trash2 size={20} color="#E53935" style={{ cursor: 'pointer' }} />
                </div>
              </div>
            </div>
          ))
        ) : (
          <p>No admins assigned to this sponsor organization.</p>
        )}

        {isModalOpen && (
          <DeleteAdminModal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            onDelete={handleSubmitModal}
          />
        )}
      </div>
    </>
  );
};

export default SponsorOrgAdmins;