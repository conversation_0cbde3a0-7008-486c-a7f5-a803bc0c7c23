/* Patient Vital Signs - Enhanced Modern Design */

.vital-signs-container {
  padding: 2rem;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
}

/* Header Styles */
.vital-signs-header {
  margin-bottom: 2.5rem;
  text-align: center;
  padding: 2rem;
  background: white;
  border-radius: 20px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.vital-signs-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 0.5rem;
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.vital-signs-header p {
  font-size: 1.125rem;
  color: #64748b;
  font-weight: 400;
  margin-bottom: 1rem;
}

.summary-info {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
  flex-wrap: wrap;
}

.summary-info span {
  padding: 0.75rem 1.25rem;
  background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
  border-radius: 12px;
  color: #475569;
  font-weight: 500;
  font-size: 0.875rem;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Content Container */
.vital-signs-content {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
}

/* Vital Signs Grid */
.vital-signs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.vital-sign-card {
  background: white;
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.vital-sign-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.vital-sign-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.vital-sign-card:hover::before {
  opacity: 1;
}

.vital-sign-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.vital-sign-header h3,
.vital-sign-card h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #374151;
  margin: 0;
}

.history-btn {
  background: #f3f4f6;
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-btn:hover {
  background: #e5e7eb;
  transform: scale(1.1);
}

.vital-sign-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #1f2937;
}

.vital-sign-trend {
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

/* NEWS Score Card */
.news-score-card {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border-left: 4px solid #f59e0b !important;
}

.news-severity {
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-top: 0.25rem;
}

/* Chart Section */
.chart-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.chart-fallback-notice {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  color: #92400e;
  font-size: 0.875rem;
}

.chart-no-data {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
}

.chart-no-data h3 {
  color: #374151;
  margin-bottom: 1rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.chart-no-data p {
  margin-bottom: 1rem;
  font-size: 1rem;
}

.chart-no-data ul {
  text-align: left;
  max-width: 400px;
  margin: 1rem auto;
}

.chart-no-data li {
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

/* Filter Bar */
.vital-signs-filter-bar {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.vital-signs-filter-bar label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.vital-signs-filter-bar select,
.vital-signs-filter-bar input[type="number"],
.vital-signs-filter-bar input[type="date"] {
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  background: white;
  transition: border-color 0.2s ease;
}

.vital-signs-filter-bar select:focus,
.vital-signs-filter-bar input[type="number"]:focus,
.vital-signs-filter-bar input[type="date"]:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.vital-signs-filter-bar input[type="checkbox"] {
  width: 1rem;
  height: 1rem;
  accent-color: #3b82f6;
}

/* Recent History Table */
.recent-history-table-wrapper {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.recent-history-table-wrapper h3 {
  color: #374151;
  margin-bottom: 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
}

.vital-signs-history-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.vital-signs-history-table th,
.vital-signs-history-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.vital-signs-history-table th {
  background: #EBF4F6;
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.vital-signs-history-table td {
  font-size: 0.875rem;
  color: #6b7280;
}

.vital-signs-history-table tr:hover {
  background: #f9fafb;
}

.vital-signs-history-table .group-header td {
  background: #eef2ff;
  font-weight: 600;
  color: #3730a3;
  border-top: 2px solid #3b82f6;
}

/* All Records Section */
.all-records-section {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h3 {
  color: #374151;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
}

.all-records-list {
  background: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
}

.records-table-wrapper {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background: white;
}

.records-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px;
}

.records-table th,
.records-table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.records-table th {
  background: #EBF4F6;
  font-weight: 600;
  color: #374151;
  position: sticky;
  top: 0;
  z-index: 1;
}

.records-table td {
  color: #6b7280;
}

.records-table tr:hover {
  background: #f9fafb;
}

/* Actions Section */
.vital-signs-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

/* Button Styles */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: #f7fafc;
  color: #4a5568;
  border: 2px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-secondary:active {
  transform: translateY(0);
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  border-radius: 10px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  font-weight: 600;
}

.btn-small:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.btn-small:active {
  transform: translateY(0);
}

/* Loading and Error States */
.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  color: #6b7280;
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  margin: 0;
  font-size: 1rem;
}

.error-message {
  text-align: center;
  padding: 4rem;
  color: #dc2626;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #fecaca;
}

.error-message p {
  margin: 0 0 1rem 0;
  font-size: 1rem;
}

.no-data {
  text-align: center;
  padding: 3rem;
  color: #6b7280;
  background: #f9fafb;
  border-radius: 12px;
  border: 2px dashed #e5e7eb;
}

.no-data p {
  font-size: 1rem;
  margin: 0;
}

.no-patient-selected {
  text-align: center;
  padding: 4rem 2rem;
  color: #6b7280;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.no-patient-selected p {
  font-size: 1.125rem;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .vital-signs-container {
    padding: 1rem;
  }
  
  .vital-signs-header {
    padding: 1.5rem;
  }
  
  .vital-signs-header h1 {
    font-size: 2rem;
  }
  
  .vital-signs-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .vital-signs-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 300px;
  }
  
  .summary-info {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .vital-signs-filter-bar {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .records-table-wrapper {
    margin: 0 -1rem;
  }
}

@media (max-width: 480px) {
  .vital-signs-header h1 {
    font-size: 1.75rem;
  }
  
  .vital-sign-card {
    padding: 1.25rem;
  }
  
  .vital-sign-value {
    font-size: 1.75rem;
  }
  
  .recent-history-table-wrapper,
  .all-records-section,
  .chart-section {
    padding: 1.25rem;
  }
}

/* Icon Styles for Lucide Icons */
.icon {
  width: 1.5rem;
  height: 1.5rem;
}

.icon.heart-rate {
  color: #ef4444;
}

.icon.temperature {
  color: #f97316;
}

.icon.blood-pressure {
  color: #8b5cf6;
}

.icon.oxygen-saturation {
  color: #06b6d4;
}

.icon.respiratory-rate {
  color: #10b981;
}

.icon.weight {
  color: #6366f1;
}

.icon.height {
  color: #84cc16;
}

.plus-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.sort-icon {
  width: 0.875rem;
  height: 0.875rem;
  opacity: 0.6;
  margin-left: 0.25rem;
}
