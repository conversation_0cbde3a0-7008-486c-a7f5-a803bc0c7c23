import socialImage from "./static/images/added/social.png";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyRadio from "@/components/NurtifyRadio";

const SocialHx: React.FC = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  const { social_hx } = assessment;

  const eliminationOptions = [
    { value: "Continent", label: "Continent" },
    { value: "Double Incontinent", label: "Double Incontinent" },
    { value: "Urine Incontinent", label: "Urine Incontinent" },
    { value: "Bowel Incontinent", label: "Bowel Incontinent" },
    { value: "Other Elimination", label: "Other Elimination" },
  ];

  const accomodationOptions = [
    { value: "House", label: "House" },
    { value: "Flat", label: "Flat" },
    { value: "Sheltered Accomodation", label: "Sheltered Accomodation" },
    { value: "Nursing Home", label: "Nursing Home" },
    { value: "Visitor", label: "Visitor" },
    { value: "Homeless", label: "Homeless" },
    { value: "Other Accomodation", label: "Other Accomodation" },
  ];

  const livesWithOptions = [
    { value: "Alone", label: "Alone" },
    { value: "Family", label: "Family" },
    { value: "Carer", label: "Carer" },
    { value: "Other", label: "Other" },
  ];

  const mobilityOptions = [
    { value: "Independent", label: "Independent" },
    { value: "Walks with sticks", label: "Walks with sticks" },
    { value: "Zimmer Frame", label: "Zimmer Frame" },
    { value: "Walks with wheelchair", label: "Walks with wheelchair" },
    { value: "Bed Bound", label: "Bed Bound" },
    { value: "Walks with something else", label: "Walks with something else" },
  ];

  const packageOfCareOptions = [
    { value: "No Package of Care", label: "No Package of Care" },
    { value: "Once Daily", label: "Once Daily" },
    { value: "Twice a day", label: "Twice a day" },
    { value: "3 Times a day", label: "3 Times a day" },
    { value: "4 Times a day", label: "4 Times a day" },
    { value: "24 Hours care", label: "24 Hours care" },
    { value: "Other Care Package", label: "Other Care Package" },
  ];

  const smokingHistoryOptions = [
    { value: "Never Smoked", label: "Never Smoked" },
    { value: "Ex-Smoker", label: "Ex-Smoker" },
    { value: "Currently Smoker", label: "Currently Smoker" },
  ];

  const drinkingHistoryOptions = [
    { value: "No Alcohol Consumption", label: "No Alcohol Consumption" },
    { value: "Occasionally", label: "Occasionally" },
    { value: "Never Drink", label: "Never Drink" },
  ];

  const onRadioBoxChange =
    (fieldName: keyof typeof social_hx) => (value: string) => {
      setAssessment({
        ...assessment,
        social_hx: {
          ...assessment.social_hx,
          [fieldName]: value,
        },
      });
    };

  return (
    <div className="block align-items-*-start flex-column flex-md-row p-4 gap-3">
      <div id="division-31" className="mb-2">
        <div className="inlineBlock headinqQuestion">
          <img src={socialImage} className="imageEtiquette" alt="House" />
          <span className="mb-2 py-2 fs-2 text-start etiquetteHeadingForms">
            Social History
          </span>
        </div>
        <div className="d-flex flex-wrap align-items-*-start flex-column flex-md-row py-md-3">
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Elimination</span>
            {eliminationOptions.map((option) => (
              <NurtifyRadio
                key={option.value}
                name="elimination"
                label={option.label}
                value={option.value}
                checked={social_hx.elimination === option.value}
                onChange={() => onRadioBoxChange("elimination")(option.value)}
              />
            ))}
          </div>
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Accomodation</span>
            {accomodationOptions.map((option) => (
              <NurtifyRadio
                key={option.value}
                name="accomodation"
                label={option.label}
                value={option.value}
                checked={social_hx.accomodation === option.value}
                onChange={() => onRadioBoxChange("accomodation")(option.value)}
              />
            ))}
          </div>
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Lives with</span>
            {livesWithOptions.map((option) => (
              <NurtifyRadio
                key={option.value}
                name="livesWith"
                label={option.label}
                value={option.value}
                checked={social_hx.livesWith === option.value}
                onChange={() => onRadioBoxChange("livesWith")(option.value)}
              />
            ))}
          </div>
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Mobility</span>
            {mobilityOptions.map((option) => (
              <NurtifyRadio
                key={option.value}
                name="mobility"
                label={option.label}
                value={option.value}
                checked={social_hx.mobility === option.value}
                onChange={() => onRadioBoxChange("mobility")(option.value)}
              />
            ))}
          </div>
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Package of Care</span>
            {packageOfCareOptions.map((option) => (
              <NurtifyRadio
                key={option.value}
                name="packageOfCare"
                label={option.label}
                value={option.value}
                checked={social_hx.packageOfCare === option.value}
                onChange={() => onRadioBoxChange("packageOfCare")(option.value)}
              />
            ))}
          </div>
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Smoking</span>
            {smokingHistoryOptions.map((option) => (
              <NurtifyRadio
                key={option.value}
                name="smokingHistory"
                label={option.label}
                value={option.value}
                checked={social_hx.smokingHistory === option.value}
                onChange={() =>
                  onRadioBoxChange("smokingHistory")(option.value)
                }
              />
            ))}
          </div>
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">Drinking</span>
            {drinkingHistoryOptions.map((option) => (
              <NurtifyRadio
                key={option.value}
                name="drinkingHistory"
                label={option.label}
                value={option.value}
                checked={social_hx.drinkingHistory === option.value}
                onChange={() =>
                  onRadioBoxChange("drinkingHistory")(option.value)
                }
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SocialHx;
