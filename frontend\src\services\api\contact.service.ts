import api from '@/services/api';

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

export interface Trial {
  uuid: string;
  name: string;
  description: string;
  hospital: {
    uuid: string;
    name: string;
  };
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Hospital {
  uuid: string;
  name: string;
  phone_number: string;
  extension: string;
  point_of_contact_Person: string;
  primary_address: string;
  secondary_address: string;
  postcode: string;
  country: string;
  department_count: number;
}

// Common fields between doctor referrals and patient applications
export interface BaseApplication {
  uuid: string;
  trial: Trial;
  status: "pending" | "approved" | "rejected" | "in_progress";
  created_at: string;
  updated_at: string;
  
  // Personal Information
  first_name: string;
  last_name: string;
  date_of_birth: string;
  ethnicity: string;
  postcode: string;
  email: string;
  phone_number: string;
  medical_history: string;
  current_medications: string;
  
  // Consent
  willing_to_travel: boolean;
  data_sharing_consent: boolean;
}

export interface DoctorReferralResponse extends BaseApplication {
  // Doctor Information
  doctor_name: string;
  nhs_email: string;
  gmc_number: string;
  organization_name: string;
  contact_phone: string;
  
  // Patient Information
  patient_first_name: string;
  patient_last_name: string;
  patient_dob: string;
  nhs_number: string;
  reason_for_referral: string;
  suitability_notes: string;
  attached_documents: string | null;
  phone: string;
}

export interface PatientApplicationResponse extends BaseApplication {
  // Additional Personal Information
  gender: string;
  allergies: string;
  smoking_status: string;
  alcohol_consumption: string;
  physical_activity_level: string;
  pregnancy_status: boolean;
  nhs_number: string;
  
  // Hospital Information
  hospital: Hospital;
}

export interface PatientApplication extends Omit<PatientApplicationResponse, 'uuid' | 'created_at' | 'updated_at' | 'hospital'> {
  hospital_uuid: string;
}

export interface DoctorReferral extends Omit<DoctorReferralResponse, 'uuid' | 'created_at' | 'updated_at' | 'attached_documents'> {
  attached_documents?: File;
}

export interface StatusUpdateRequest {
  status: "Pending" | "In_Progress" | "Approved" | "Rejected";
  notes: string;
}

export interface StatusLogUser {
  identifier: string;
  email: string;
  first_name: string;
  last_name: string;
  phone_number: string;
  gender: string;
  country: string;
  registration_body: string;
  is_active: boolean;
  is_staff: boolean;
  created_at: string;
  updated_at: string;
}

export interface StatusLog {
  uuid: string;
  content_type: string;
  record_uuid: string;
  old_status: string;
  new_status: string;
  changed_by: StatusLogUser;
  changed_at: string;
  notes: string;
}

export interface StatusLogResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: StatusLog[];
}

export interface ContactService {
  getTrialsByHospital: (hospitalUuid: string) => Promise<Trial[]>;
  submitPatientApplication: (application: PatientApplication) => Promise<PatientApplicationResponse>;
  submitDoctorReferral: (referral: DoctorReferral) => Promise<DoctorReferralResponse>;
  getActiveTrials: () => Promise<Trial[]>;
  getDoctorReferrals: () => Promise<PaginatedResponse<DoctorReferralResponse>>;
  getPatientApplications: () => Promise<PaginatedResponse<PatientApplicationResponse>>;
}

class ContactServiceImpl implements ContactService {
  async getTrialsByHospital(hospitalUuid: string): Promise<Trial[]> {
    const response = await api.get(`/clinical-trial/trials/by_hospital/?hospital_uuid=${hospitalUuid}`);
    return response.data;
  }

  async submitPatientApplication(application: PatientApplication): Promise<PatientApplicationResponse> {
    const response = await api.post('/clinical-trial/patient-applications/', application);
    return response.data;
  }

  async submitDoctorReferral(referral: DoctorReferral): Promise<DoctorReferralResponse> {
    const formData = new FormData();
    
    // Append all fields to FormData
    Object.entries(referral).forEach(([key, value]) => {
      if (key === 'attached_documents' && value instanceof File) {
        formData.append(key, value);
      } else if (value !== undefined) {
        formData.append(key, String(value));
      }
    });

    const response = await api.post('/clinical-trial/doctor-referrals/', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async getActiveTrials(): Promise<Trial[]> {
    const response = await api.get('/clinical-trial/trials/active/');
    return response.data;
  }

  async getDoctorReferrals(): Promise<PaginatedResponse<DoctorReferralResponse>> {
    const response = await api.get('/clinical-trial/doctor-referrals/');
    return response.data;
  }

  async getPatientApplications(): Promise<PaginatedResponse<PatientApplicationResponse>> {
    const response = await api.get('/clinical-trial/patient-applications/');
    return response.data;
  }
}

export const contactService = new ContactServiceImpl();

export const updateDoctorReferralStatus = async (uuid: string, data: StatusUpdateRequest): Promise<void> => {
  const response = await api.patch(`/clinical-trial/doctor-referrals/${uuid}/update_status/`, data);
  return response.data;
};

export const updatePatientApplicationStatus = async (uuid: string, data: StatusUpdateRequest): Promise<void> => {
  const response = await api.patch(`/clinical-trial/patient-applications/${uuid}/update_status/`, data);
  return response.data;
};

export const getDoctorReferralStatusLogs = async (uuid: string): Promise<StatusLogResponse> => {
  const response = await api.get(`/clinical-trial/status-logs/?content_type=doctor_referral&record_uuid=${uuid}`);
  return response.data;
};

export const getPatientApplicationStatusLogs = async (uuid: string): Promise<StatusLogResponse> => {
  const response = await api.get(`/clinical-trial/status-logs/?content_type=patient_application&record_uuid=${uuid}`);
  return response.data;
}; 