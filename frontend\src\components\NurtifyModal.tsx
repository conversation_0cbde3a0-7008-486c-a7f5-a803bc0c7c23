import React, { useEffect } from 'react'; // Add useEffect back
import ReactDOM from 'react-dom'; // Import ReactDOM
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes } from '@fortawesome/free-solid-svg-icons';
import './modal.css';

interface NurtifyModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  className?: string; // Optional class for the modal content container
  size?: 'sm' | 'md' | 'lg' | 'xl'; // Optional size prop
}

const NurtifyModal: React.FC<NurtifyModalProps> = ({
  isOpen,
  onClose,
  title,
  children,
  className = '',
  size = 'md',
}) => {
  // Effect to handle body scroll lock (fully uncommented)
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add('modal-open');
    } else {
      document.body.classList.remove('modal-open');
    }
    // Cleanup function to remove class if component unmounts while open
    return () => {
      document.body.classList.remove('modal-open');
    };
  }, [isOpen]); // Dependency array ensures this runs when isOpen changes

  if (!isOpen) return null;

  const sizeClasses: Record<string, string> = {
    sm: 'nurtify-modal-sm',
    md: 'nurtify-modal-md',
    lg: 'nurtify-modal-lg',
    xl: 'nurtify-modal-xl',
  };

  // Define the modal content JSX
  const modalContent = (
    <div className="nurtify-modal-overlay" onClick={onClose}>
      <div
        className={`nurtify-modal-content ${sizeClasses[size]} ${className}`}
        onClick={(e) => e.stopPropagation()} // Prevent closing when clicking inside modal
      >
        <div className="nurtify-modal-header">
          {title && <h2 className="nurtify-modal-title">{title}</h2>}
          <button
            className="nurtify-modal-close-button"
            onClick={onClose}
            aria-label="Close modal"
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
        <div className="nurtify-modal-body">
          {children}
        </div>
      </div>
    </div>
  );

  // Use ReactDOM.createPortal to render the modal into the body
  return ReactDOM.createPortal(modalContent, document.body);
};

export default NurtifyModal;
