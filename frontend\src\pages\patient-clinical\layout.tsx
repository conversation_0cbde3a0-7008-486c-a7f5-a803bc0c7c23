import React from "react";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import PatientSidebar from "@/components/common/PatientSidebar";
import AIHelper from "@/components/AIHelper";
import LightFooter from "@/shared/LightFooter";
import "./patient-clinical.css";

const PatientClinicalLayout: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Get the current tab from the URL path
  const getTabFromPath = (path: string) => {
    const parts = path.split('/');
    return parts[parts.length - 1] || 'report'; // Default to 'report' if no tab specified
  };
  
  const currentTab = getTabFromPath(location.pathname);

  const handleTabChange = (tab: string) => {
    navigate(`/patient/${tab}`);
  };


  return (
    <div className="patclin-dashboard">
      <PatientSidebar 
        onTabChange={handleTabChange} 
        currentTab={currentTab} 
        notificationCount={0} 
      />
      
      <main className="patclin-content">
        <div className="patclin-container">
          <Outlet />
        </div>
        <LightFooter />
      </main>
      
      <AIHelper />
    </div>
  );
};

export default PatientClinicalLayout;