import { useQuery, useMutation } from "@tanstack/react-query";
import {
  getStaffDocuments,
  getAllStaffDocuments,
  downloadStaffDocument,
  type StaffDocument,
  type StaffDocumentsResponse,
} from "@/services/api/staffDocument.service";

// Staff document query keys
export const STAFF_DOCUMENT_KEYS = {
  GET_BY_STAFF: "staff-document/getByStaff",
  GET_ALL: "staff-document/getAll",
} as const;

// Get documents for a specific staff member
export const useStaffDocumentsQuery = (staffUuid: string) => {
  return useQuery<StaffDocument[], Error>({
    queryKey: [STAFF_DOCUMENT_KEYS.GET_BY_STAFF, staffUuid],
    queryFn: () => getStaffDocuments(staffUuid),
    enabled: !!staffUuid,
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Get all staff documents for sponsor's studies
export const useAllStaffDocumentsQuery = () => {
  return useQuery<StaffDocumentsResponse, Error>({
    queryKey: [STAFF_DOCUMENT_KEYS.GET_ALL],
    queryFn: getAllStaffDocuments,
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

// Download staff document mutation
export const useDownloadStaffDocumentMutation = () => {
  return useMutation({
    mutationFn: (documentUuid: string) => downloadStaffDocument(documentUuid),
    onError: (error) => {
      console.error("Error downloading staff document:", error);
    },
  });
}; 