import "./sponsor-org.css";
import { useState, useRef, useEffect } from "react";
import { Pencil, PencilOff } from "lucide-react";
import NurtifyText from "@components/NurtifyText.tsx";
import NurtifyInput from "@components/NurtifyInput.tsx";
import { motion } from "framer-motion";
import DeleteSponsorOrgModal from "@/components/modal/DeleteSponsorOrgModal";
import { SponsorOrg as SponsorOrgData } from "@/services/api/types.ts";
import {
  useSponsorOrgQuery,
  useUpdateSponsorOrgMutation,
  useDeleteSponsorOrgMutation,
} from "@/hooks/sponsorOrg.query";
import { useNavigate } from "react-router-dom";

const SPECIALITY_CHOICES = [
  { value: "pharma", label: "Pharmaceutical Company" },
  { value: "research", label: "Research Institute" },
  { value: "government", label: "Government Body" },
  { value: "private", label: "Private Organization" },
  { value: "other", label: "Other" } 
];

interface SponsorOrgDetailsProps {
  uuid: string;
}

const SponsorOrgDetails: React.FC<SponsorOrgDetailsProps> = ({ uuid }) => {
  const navigate = useNavigate();
  const { data: sponsorOrgData, isLoading } = useSponsorOrgQuery(uuid!);
  const updateSponsorOrgMutation = useUpdateSponsorOrgMutation();
  const deleteSponsorOrgMutation = useDeleteSponsorOrgMutation();

  const [editableSponsorOrg, setEditableSponsorOrg] = useState<SponsorOrgData | null>(null);
  const [changedFields, setChangedFields] = useState<Partial<SponsorOrgData>>({});
  const [isEditing, setIsEditing] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUpdateEnabled, setIsUpdateEnabled] = useState(false);

  const targetRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (sponsorOrgData) {
      setEditableSponsorOrg(sponsorOrgData);
    }
  }, [sponsorOrgData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (!editableSponsorOrg || !sponsorOrgData) return;

    const { id, value } = e.target;

    setEditableSponsorOrg((prev) => ({
      ...prev!,
      [id]: value,
    }));

    setChangedFields((prev) => {
      const updatedChanges = { ...prev };
      const currentValue = sponsorOrgData[id as keyof SponsorOrgData];
      if (currentValue !== value) {
        updatedChanges[id as keyof SponsorOrgData] = value as any;
      } else {
        delete updatedChanges[id as keyof SponsorOrgData];
      }
      setIsUpdateEnabled(Object.keys(updatedChanges).length > 0);
      return updatedChanges;
    });
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEditableSponsorOrg(prev => ({
      ...prev!,
      [name]: value
    }));
  };

  const handleDeleteClick = () => {
    setIsEditing(false);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsEditing(true);
    setIsModalOpen(false);
  };

  const handleSubmitModal = async () => {
    if (!uuid) return;

    try {
      await deleteSponsorOrgMutation.mutateAsync({ uuid });
      navigate("/org/dashboard/sponsorOrg", { replace: true });
    } catch (error) {
      console.error("Error deleting sponsor org:", error);
    }
  };

  const handleScroll = () => {
    if (targetRef.current) {
      targetRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleUpdateClick = () => {
    setIsEditing(true);
    handleScroll();
  };

  const handleCancelClick = () => {
    setEditableSponsorOrg(sponsorOrgData || null);
    setChangedFields({});
    setIsEditing(false);
    setIsUpdateEnabled(false);
  };

  const handleSaveClick = async () => {
    if (!uuid || !isUpdateEnabled || Object.keys(changedFields).length === 0) {
      console.error("UUID is undefined or no changes to update.");
      return;
    }

    try {
      await updateSponsorOrgMutation.mutateAsync({
        uuid,
        data: changedFields,
      });
      navigate("/org/dashboard/sponsorOrg", { replace: true });
    } catch (error) {
      console.error("Error updating sponsor org:", error);
    }
  };

  const loadingMessage = isLoading ? <p>is loading ...</p> : null;

  return (
    <>
      {loadingMessage}
      {!isLoading && !editableSponsorOrg && <p>not found!</p>}
      <div className="sponsorOrg-container">
        {!isLoading && editableSponsorOrg && (
          <div className="custom-button">
            {!isEditing && (
              <button
                className="sponsor-details-btn-custom gap-2"
                onClick={handleUpdateClick}
              >
                <Pencil size={18} /> Edit
              </button>
            )}
            {isEditing && (
              <button
                className="sponsor-details-btn-custom gap-2"
                onClick={handleCancelClick}
              >
                <PencilOff size={18} /> Cancel
              </button>
            )}
          </div>
        )}
      </div>
      <form className="add-policy-form" onSubmit={(e) => e.preventDefault()}>
        <motion.div
          className="mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          <div className="row y-gap-30">
            <div className="col-md-6" style={{ marginTop: "10px" }}>
              <NurtifyText label="Sponsor Organization Name*" />
              <NurtifyInput
                type="text"
                name="name"
                value={editableSponsorOrg?.name}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Sponsor Organization Name here"
                required
              />
            </div>
            <div className="col-md-6">
                <NurtifyText label="Organization Type*" />
                <select
                  name="organization_type"
                  value={editableSponsorOrg?.organization_type}
                  onChange={handleSelectChange}
                  className="nurtify-select" // Added class for styling
                >
                  <option value="">Select Organization Type</option>
                  {SPECIALITY_CHOICES.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
              </div>
          </div>
          <div className="row y-gap-30" style={{ marginTop: "10px" }}>
            <div className="col-md-6">
              <NurtifyText label="Phone*" />
              <NurtifyInput
                type="number"
                name="phone_number"
                value={editableSponsorOrg?.phone_number}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Phone Number here"
                required
              />
            </div>
            <div className="col-md-6">
              <NurtifyText label="Extension*" />
              <NurtifyInput
                type="number"
                name="extension"
                value={editableSponsorOrg?.extension}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Extension here"
                required
              />
            </div>
          </div>
          <div className="row y-gap-30">
            <div className="col-md-6" style={{ marginTop: "10px" }}>
              <NurtifyText label="Point of contact Person*" />
              <NurtifyInput
                type="text"
                name="point_of_contact_Person"
                value={editableSponsorOrg?.point_of_contact_Person}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Point of Contact Person here"
                required
              />
            </div>
          </div>
          <div className="row y-gap-30" style={{ marginTop: "10px" }}>
            <div className="col-md-6">
              <NurtifyText label="Address Line 1*" />
              <div className="sponsor-detail-input">
                <textarea
                  id="primary_address"
                  name="primary_address"
                  value={editableSponsorOrg?.primary_address || ""}
                  onChange={handleChange}
                  placeholder="Enter Address Line 1"
                  disabled={!isEditing}
                  className={`${!isEditing ? "text-muted" : ""}`}
                  style={{
                    border: "none",
                    outline: "none",
                    backgroundColor: "transparent",
                    padding: "0px",
                  }}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <NurtifyText label="Address Line 2*" />
              <div className="sponsor-detail-input">
                <textarea
                  id="secondary_address"
                  name="secondary_address"
                  value={editableSponsorOrg?.secondary_address || ""}
                  onChange={handleChange}
                  placeholder="Enter Address Line 2"
                  disabled={!isEditing}
                  className={`${!isEditing ? "text-muted" : ""}`}
                  style={{
                    border: "none",
                    outline: "none",
                    backgroundColor: "transparent",
                    padding: "0px",
                  }}
                  required
                />
              </div>
            </div>
          </div>
          <div className="row y-gap-30" style={{ marginTop: "10px" }}>
            <div className="col-md-6">
              <NurtifyText label="Postcode*" />
              <NurtifyInput
                type="text"
                name="postcode"
                value={editableSponsorOrg?.postcode}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Postcode here"
                required
              />
            </div>
            <div className="col-md-6">
              <NurtifyText label="Country*" />
              <NurtifyInput
                type="text"
                name="country"
                value={editableSponsorOrg?.country}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter country here"
                required
              />
            </div>
          </div>
          <div className="mt-3" style={{ textAlign: "right" }}>
            {isEditing && (
              <>
                <button
                  className="sponsor-details-form-btn-custom Delete"
                  onClick={handleDeleteClick}
                >
                  Delete Sponsor Org
                </button>
                <button
                  className="sponsor-details-form-btn-custom Update"
                  disabled={!isUpdateEnabled}
                  onClick={handleSaveClick}
                >
                  {updateSponsorOrgMutation.isPending ? "Updating..." : "Update Sponsor Org"}
                </button>
              </>
            )}
          </div>
        </motion.div>
        {isModalOpen && (
          <DeleteSponsorOrgModal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            onDelete={handleSubmitModal}
          />
        )}
      </form>
    </>
  );
};

export default SponsorOrgDetails;