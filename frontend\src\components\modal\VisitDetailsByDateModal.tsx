import React from 'react';
import { useVisitsByDateQuery } from '@/hooks/patient.query';
import { PatientVisitToday } from '@/services/api/types';
import './VisitDetailsByDateModal.css';

interface VisitDetailsByDateModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDate: Date | null;
}

const VisitDetailsByDateModal: React.FC<VisitDetailsByDateModalProps> = ({
  isOpen,
  onClose,
  selectedDate,
}) => {
  // Format date to YYYY-MM-DD for the API, avoiding timezone issues
  const dateStr = selectedDate
    ? `${selectedDate.getFullYear()}-${String(selectedDate.getMonth() + 1).padStart(2, '0')}-${String(selectedDate.getDate()).padStart(2, '0')}`
    : (() => {
        const today = new Date();
        return `${today.getFullYear()}-${String(today.getMonth() + 1).padStart(2, '0')}-${String(today.getDate()).padStart(2, '0')}`;
      })();

  // Fetch visits for the selected date - hook must be called unconditionally
  const { data: visits, isLoading, error } = useVisitsByDateQuery(dateStr);

  // Early return after hook call
  if (!isOpen || !selectedDate) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Visits for {selectedDate.toLocaleDateString()}</h2>
          <button className="close-button" onClick={onClose}>&times;</button>
        </div>
        <div className="modal-body">
          {isLoading ? (
            <p>Loading visit details...</p>
          ) : error ? (
            <p className="error-message">Error loading visits: {error.message}</p>
          ) : visits && visits.length > 0 ? (
            <div className="visits-list">
              {visits.map((visit: PatientVisitToday) => (
                <div key={visit.uuid} className="visit-item">
                  <div className="visit-header">
                    <h3>{visit.patient_details.first_name} {visit.patient_details.last_name}</h3>
                    <span className="nhs-number">NHS: {visit.patient_details.nhs_number}</span>
                  </div>
                  <div className="visit-details">
                    {visit.visit_details.map((detail, index) => (
                      <div key={index} className="visit-detail">
                        <div className="visit-name">{detail.name}</div>
                        <div className="visit-time">{detail.time}</div>
                        <div className="visit-status">{detail.visit_status}</div>
                        {detail.location && (
                          <div className="visit-location">Location: {detail.location}</div>
                        )}
                        {detail.activities && detail.activities.length > 0 && (
                          <div className="visit-activities">
                            Activities: {detail.activities.join(', ')}
                          </div>
                        )}
                        {detail.comments && (
                          <div className="visit-comments">
                            Comments: {detail.comments}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p>No visits scheduled for this date.</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default VisitDetailsByDateModal;
