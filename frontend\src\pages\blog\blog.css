/* Blog List Page Styles */
.blog-list-container {
  padding: 80px 0;
  background-color: #f8f9fa;
  min-height: calc(100vh - 200px);
}

.blog-list-title {
  font-size: 2.5rem;
  margin-bottom: 10px;
  text-align: center;
  color: #333;
}

.blog-list-subtitle {
  font-size: 1.2rem;
  margin-bottom: 50px;
  text-align: center;
  color: #666;
}

.text-highlight {
  color: #00a3c8;
}

.blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.blog-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.blog-card-image {
  height: 200px;
  overflow: hidden;
}

.blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.blog-card:hover .blog-card-image img {
  transform: scale(1.05);
}

.blog-card-content {
  padding: 20px;
}

.blog-card-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: #666;
}

.blog-card-title {
  font-size: 1.4rem;
  margin-bottom: 10px;
  color: #333;
  line-height: 1.3;
}

.blog-card-excerpt {
  color: #555;
  margin-bottom: 20px;
  line-height: 1.5;
}

.blog-read-more {
  display: inline-block;
  padding: 8px 16px;
  background-color: #00a3c8;
  color: white;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.blog-read-more:hover {
  background-color: #0082a0;
}

/* Blog Detail Page Styles */
.blog-detail-container {
  padding: 60px 0;
  background-color: #f8f9fa;
  min-height: calc(100vh - 200px);
}

.back-to-blogs {
  display: inline-block;
  margin-bottom: 30px;
  color: #00a3c8;
  text-decoration: none;
  font-weight: 500;
}

.back-to-blogs:hover {
  text-decoration: underline;
}

.blog-detail-image {
  width: 100%;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 30px;
}

.blog-detail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.blog-detail-title {
  font-size: 2.5rem;
  margin-bottom: 20px;
  color: #333;
  line-height: 1.2;
}

.blog-detail-meta {
  display: flex;
  gap: 20px;
  margin-bottom: 40px;
  font-size: 1rem;
  color: #666;
}

.blog-detail-content {
  background-color: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.blog-detail-section {
  margin-bottom: 40px;
}

.blog-detail-section-title {
  font-size: 1.8rem;
  margin-bottom: 20px;
  color: #333;
}

.blog-detail-section-content {
  color: #444;
  line-height: 1.7;
  font-size: 1.1rem;
}

.blog-detail-section-content p {
  margin-bottom: 1.5rem;
}

.blog-detail-references {
  margin-top: 60px;
  padding-top: 30px;
  border-top: 1px solid #eee;
}

.blog-references-title {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: #333;
}

.blog-references-list {
  list-style-type: none;
  padding: 0;
}

.blog-reference-item {
  margin-bottom: 10px;
  font-size: 1rem;
  color: #555;
}

.blog-reference-item a {
  color: #00a3c8;
  text-decoration: none;
}

.blog-reference-item a:hover {
  text-decoration: underline;
}

.blog-detail-footer {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

@media (max-width: 768px) {
  .blog-grid {
    grid-template-columns: 1fr;
  }
  
  .blog-detail-image {
    height: 250px;
  }
  
  .blog-detail-title {
    font-size: 2rem;
  }
  
  .blog-detail-content {
    padding: 20px;
  }
}
