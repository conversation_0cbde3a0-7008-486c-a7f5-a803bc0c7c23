import React from 'react';
import { Link, Outlet, useLocation } from 'react-router-dom';
import LightFooter from '@/shared/LightFooter';
import './terms-and-conditions.css';

const TermsAndConditionsPage: React.FC = () => {
  const location = useLocation();
  const isPrivacyPage = location.pathname === '/privacy';
  const isTermsPage = location.pathname === '/terms-and-conditions';
  const isCookiesPage = location.pathname === '/cookies';

  return (
    <div className="main-content">
      <main className="content-wrapper">
        <div className="terms-container">
          <div className="terms-sidebar">
            <h3 className="sidebar-title">Legal Documents</h3>
            <ul className="sidebar-nav">
              <li className={isTermsPage ? 'active' : ''}>
                <Link to="/terms-and-conditions">Terms of Use</Link>
              </li>
              <li className={isPrivacyPage ? 'active' : ''}>
                <Link to="/privacy">Privacy Policy</Link>
              </li>
              <li className={isCookiesPage ? 'active' : ''}>
                <Link to="/cookies">Cookie Policy</Link>
              </li>
            </ul>
          </div>
          <div className="terms-content">
            <Outlet />
            {(isTermsPage || isPrivacyPage || isCookiesPage) ? null : (
              <div className="terms-redirect">
                <h2>Legal Documents</h2>
                <p>Please select a document from the sidebar to view our legal information.</p>
                <div className="terms-buttons">
                  <Link to="/terms-and-conditions" className="terms-button">
                    Terms of Use
                  </Link>
                  <Link to="/privacy" className="terms-button">
                    Privacy Policy
                  </Link>
                  <Link to="/cookies" className="terms-button">
                    Cookie Policy
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
      <LightFooter />
    </div>
  );
};

export default TermsAndConditionsPage;
