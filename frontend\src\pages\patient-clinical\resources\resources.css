/* Resources Blog Section Styles */
.resources-blog-container {
  padding: 20px 0;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.resources-blog-title {
  font-size: 2rem;
  margin-bottom: 10px;
  text-align: center;
  color: #333;
}

.resources-blog-subtitle {
  font-size: 1.1rem;
  margin-bottom: 30px;
  text-align: center;
  color: #666;
}

.text-highlight {
  color: #00a3c8;
}

.resources-blog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.resources-blog-card {
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.resources-blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.resources-blog-card-image {
  height: 180px;
  overflow: hidden;
}

.resources-blog-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.resources-blog-card:hover .resources-blog-card-image img {
  transform: scale(1.05);
}

.resources-blog-card-content {
  padding: 20px;
}

.resources-blog-card-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 0.9rem;
  color: #666;
}

.resources-blog-card-title {
  font-size: 1.3rem;
  margin-bottom: 10px;
  color: #333;
  line-height: 1.3;
}

.resources-blog-card-excerpt {
  color: #555;
  margin-bottom: 20px;
  line-height: 1.5;
}

.resources-blog-read-more {
  display: inline-block;
  padding: 8px 16px;
  background-color: #00a3c8;
  color: white;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.resources-blog-read-more:hover {
  background-color: #0082a0;
}

@media (max-width: 768px) {
  .resources-blog-grid {
    grid-template-columns: 1fr;
  }
}
