import { useState, forwardRef } from 'react';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import "./styles.css";

interface NurtifyDateInputProps {
  label?: string;
  name?: string;
  value?: string;
  onChange?: (event: { target: { name: string; value: string } }) => void;
  className?: string;
  defaultValue?: string;
  placeholder?: string;
}

interface CustomInputProps {
  value?: string;
  onClick?: () => void;
  className?: string;
}

const NurtifyDateInput: React.FC<NurtifyDateInputProps> = ({ label, name, value, onChange, className }) => {
  const [selectedDate, setSelectedDate] = useState<Date | null>(value ? new Date(value) : null);

  const CustomInput = forwardRef<HTMLInputElement, CustomInputProps>(({ value, onClick }, ref) => (
    <input
      type="date"
      className="nurtify-date-control"
      onClick={onClick}
      value={value}
      ref={ref}
      readOnly
      placeholder="Select date"
    />
  ));

  CustomInput.displayName = 'CustomInput';

  const handleDateChange = (date: Date | null) => {
    setSelectedDate(date);
    if (onChange) {
      const formattedDate = date ? date.toISOString().split('T')[0] : '';
      onChange({
        target: {
          name: name || '',
          value: formattedDate
        }
      });
    }
  };

  return (
    <div className={`nurtify-input ${className}`}>
      <label htmlFor={name}>{label}</label>
      <DatePicker
        selected={value ? new Date(value) : selectedDate}
        onChange={handleDateChange}
        customInput={<CustomInput />}
        dateFormat="yyyy-MM-dd"
        isClearable
        showYearDropdown
        scrollableYearDropdown
        yearDropdownItemNumber={100}
        calendarClassName="nurtify-datepicker-theme" // Add custom class name
      />
    </div>
  );
};

export default NurtifyDateInput;
