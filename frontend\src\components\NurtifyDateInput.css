.nurtify-date-input {
  position: relative;
}

.nurtify-date-input .react-datepicker-wrapper {
  width: 100%;
}

.nurtify-date-input .form-control {
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.nurtify-date-input .form-control:focus {
  border-color: #86b7fe;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.nurtify-date-input .form-control:disabled {
  background-color: #e9ecef;
  opacity: 1;
}

.nurtify-date-input .react-datepicker {
  font-family: inherit;
  border-radius: 0.5rem;
  border: 1px solid #ced4da;
}

.nurtify-date-input .react-datepicker__header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ced4da;
}

.nurtify-date-input .react-datepicker__day--selected {
  background-color: #0d6efd;
  color: white;
}

.nurtify-date-input .react-datepicker__day:hover {
  background-color: #e9ecef;
}

.nurtify-input, .nurtify-select {
    margin-bottom: 1rem;
}

.error {
    border-color: #dc3545 !important;
}

.error-message {
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.required-asterisk {
    color: #dc3545;
    margin-left: 4px;
} 