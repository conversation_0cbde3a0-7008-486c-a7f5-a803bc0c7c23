import { ReactNode, useState, useEffect, useMemo } from "react";
import "./DataTable.css";
import { ChevronDown, ChevronLeft, ChevronRight } from "lucide-react";

// Export the Column type
export type Column<T> = {
  key: keyof T;
  header: string;
  render?: (value: T[keyof T], row?: T) => ReactNode;
  hidden?: boolean;
  sortable?: boolean;
  maxLength?: number; // Add maxLength property for text truncation
};

// Remove the unexported interface Action<T> above this line

// Export the Action type
export interface Action<T> {
  icon?: React.ReactNode;
  label?: string;
  path?: string;
  onClick?: (row: T) => void;
  tooltipText?: string | ((row: T) => string);
  className?: string | ((row: T) => string);
  disabled?: boolean | ((row: T) => boolean);
}

// Remove the duplicate unexported interface Action<T> { ... } - This was the error source

type SortDirection = "asc" | "desc" | null;

type DataTableProps<T> = {
  data: T[];
  columns: Column<T>[];
  actions?: Action<T>[];
  itemsPerPageOptions?: number[];
  defaultItemsPerPage?: number;
  noDataMessage?: string;
  filteredData?: T[]; // Optional filtered data from external search
};

// Utility function to truncate text
const truncateText = (text: string, maxLength: number = 20): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

export default function DataTable<T extends { uuid?: number | string }>({
  data,
  columns,
  actions = [],
  itemsPerPageOptions = [5, 10, 25, 50],
  defaultItemsPerPage = 10,
  noDataMessage = "No data available",
  filteredData
}: DataTableProps<T>) {
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(defaultItemsPerPage);
  const [sortConfig, setSortConfig] = useState<{
    key: keyof T | null;
    direction: SortDirection;
  }>({
    key: null,
    direction: null,
  });

  // Reset to first page when data changes
  useEffect(() => {
    setCurrentPage(1);
  }, [filteredData]);

  // Sort data based on sort config
  const sortedData = useMemo(() => {
    const dataToSort = filteredData || data;
    if (!sortConfig.key || !sortConfig.direction) return dataToSort;

    return [...dataToSort].sort((a, b) => {
      const aValue = a[sortConfig.key as keyof T];
      const bValue = b[sortConfig.key as keyof T];

      if (aValue === bValue) return 0;
      
      // Handle null/undefined values
      if (aValue === null || aValue === undefined) return sortConfig.direction === "asc" ? -1 : 1;
      if (bValue === null || bValue === undefined) return sortConfig.direction === "asc" ? 1 : -1;
      
      // Compare based on type
      if (typeof aValue === "string" && typeof bValue === "string") {
        return sortConfig.direction === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }
      
      // Fallback to basic comparison
      return sortConfig.direction === "asc"
        ? aValue < bValue ? -1 : 1
        : aValue < bValue ? 1 : -1;
    });
  }, [filteredData, data, sortConfig]);

  // Paginate data
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    return sortedData.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedData, currentPage, itemsPerPage]);

  // Calculate total pages
  const totalPages = Math.ceil(sortedData.length / itemsPerPage);

  // Handle sort
  const handleSort = (key: keyof T) => {
    const column = columns.find(col => col.key === key);
    if (!column?.sortable) return;

    setSortConfig((prevConfig) => {
      if (prevConfig.key === key) {
        // Toggle direction if same key
        if (prevConfig.direction === "asc") {
          return { key, direction: "desc" };
        } else if (prevConfig.direction === "desc") {
          return { key: null, direction: null };
        }
      }
      // Default to ascending for new sort key
      return { key, direction: "asc" };
    });
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setItemsPerPage(Number(e.target.value));
    setCurrentPage(1); // Reset to first page
  };

  // Render sort indicator
  const renderSortIndicator = (key: keyof T) => {
    const column = columns.find(col => col.key === key);
    if (!column?.sortable) return null;

    if (sortConfig.key !== key) {
      return <ChevronDown size={16} className="sort-icon" />;
    }

    return sortConfig.direction === "asc" ? (
      <ChevronDown size={16} className="sort-icon active asc" />
    ) : (
      <ChevronDown size={16} className="sort-icon active desc" />
    );
  };

  // Pagination component
  const Pagination = () => (
    <div className="data-table-pagination">
      <div className="pagination-info">
        Showing {sortedData.length > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0} to{" "}
        {Math.min(currentPage * itemsPerPage, sortedData.length)} of{" "}
        {sortedData.length} entries
        {filteredData && filteredData !== data && ` (filtered from ${data.length} total entries)`}
      </div>
      <div className="pagination-controls">
        <button
          className="pagination-button"
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1}
          aria-label="First page"
        >
          First
        </button>
        <button
          className="pagination-button"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          aria-label="Previous page"
        >
          <ChevronLeft size={16} />
        </button>
        <div className="pagination-pages">
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNum;
            if (totalPages <= 5) {
              pageNum = i + 1;
            } else if (currentPage <= 3) {
              pageNum = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = currentPage - 2 + i;
            }
            return (
              <button
                key={pageNum}
                className={`pagination-page ${currentPage === pageNum ? "active" : ""}`}
                onClick={() => handlePageChange(pageNum)}
              >
                {pageNum}
              </button>
            );
          })}
        </div>
        <button
          className="pagination-button"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages || totalPages === 0}
          aria-label="Next page"
        >
          <ChevronRight size={16} />
        </button>
        <button
          className="pagination-button"
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages || totalPages === 0}
          aria-label="Last page"
        >
          Last
        </button>
      </div>
    </div>
  );

  // Items per page selector
  const ItemsPerPageSelector = () => (
    <div className="data-table-items-per-page">
      <label htmlFor="itemsPerPage">Show entries:</label>
      <select
        id="itemsPerPage"
        value={itemsPerPage}
        onChange={handleItemsPerPageChange}
        className="data-table-select"
      >
        {itemsPerPageOptions.map((option) => (
          <option key={option} value={option}>
            {option}
          </option>
        ))}
      </select>
    </div>
  );

  return (
    <div className="data-table-container">
      <div className="data-table-top-controls">
        <ItemsPerPageSelector />
        {/* Empty div for spacing when search is not provided */}
        <div></div>
      </div>

      <div className="data-table-wrapper">
        <table className="data-table">
          <thead>
            <tr>
              {columns
                .filter((column) => !column.hidden)
                .map((column) => (
                  <th
                    key={`header-${String(column.key)}`}
                    onClick={() => handleSort(column.key)}
                    className={column.sortable ? "sortable" : ""}
                  >
                    <div className="th-content">
                      <span>{column.header}</span>
                      {column.sortable && renderSortIndicator(column.key)}
                    </div>
                  </th>
                ))}
              {actions.length > 0 && <th className="actions-column">Actions</th>}
            </tr>
          </thead>
        </table>
        
        <div className="table-body-container">
          <table className="data-table">
            <tbody>
              {paginatedData.length > 0 ? (
                paginatedData.map((row) => (
                  <tr key={`row-${row.uuid?.toString() || Math.random().toString()}`}>
                      {columns
                        .filter((column) => !column.hidden)
                        .map((column) => {
                          const cellValue = column.render
                            ? column.render(row[column.key], row)
                            : row[column.key] !== null && row[column.key] !== undefined
                              ? String(row[column.key])
                              : "-";
                          
                          const displayValue = typeof cellValue === 'string' && column.maxLength
                            ? truncateText(cellValue, column.maxLength)
                            : cellValue;
                          
                          const fullValue = typeof cellValue === 'string' ? cellValue : '';
                          
                          return (
                            <td 
                              key={String(column.key)}
                              title={typeof cellValue === 'string' && cellValue.length > (column.maxLength || 20) ? fullValue : undefined}
                            >
                              {displayValue}
                            </td>
                          );
                        })}
                    {actions.length > 0 && (
                      <td>
                        <div className="actions-cell">
                        {actions.map((action, index) => (
                          <div key={index} className="action-button-wrapper">
                            <button
                              className={`action-button ${typeof action.className === 'function' ? action.className(row) : action.className || ''}`}
                              onClick={() => {
                                const isDisabled = typeof action.disabled === 'function' ? action.disabled(row) : action.disabled;
                                if (!isDisabled && action.onClick) {
                                  action.onClick(row);
                                }
                              }}
                              title={typeof action.tooltipText === 'function' ? action.tooltipText(row) : action.tooltipText}
                              disabled={typeof action.disabled === 'function' ? action.disabled(row) : action.disabled}
                            >
                              {action.icon && <span className="action-icon">{action.icon}</span>}
                              {action.label && <span className="action-label">{action.label}</span>}
                            </button>
                            {action.tooltipText && (
                              <span className="tooltip">
                                {typeof action.tooltipText === 'function' ? action.tooltipText(row) : action.tooltipText}
                              </span>
                            )}
                          </div>
                        ))}
                        </div>
                      </td>
                    )}
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={columns.filter(col => !col.hidden).length + (actions.length > 0 ? 1 : 0)} className="no-data">
                    {noDataMessage}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {sortedData.length > 0 && <Pagination />}
    </div>
  );
}

// Search component to be used with DataTable
export function DataTableSearch<T>({
  data,
  onFilter,
  placeholder = "Search...",
}: {
  data: T[];
  onFilter: (filtered: T[]) => void;
  placeholder?: string;
  searchFields?: (keyof T)[];
}) {
  const [searchTerm, setSearchTerm] = useState("");

  // Filter data based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      onFilter(data);
      return;
    }

    const filtered = data.filter((item) => {
      // Convert item to a record for type safety
      const record = item as Record<string, unknown>;
      
      return Object.keys(record).some(key => {
        const value = record[key];
        if (value === null || value === undefined) return false;
        
        // Handle nested objects
        if (typeof value === 'object' && value !== null) {
          const nestedRecord = value as Record<string, unknown>;
          return Object.keys(nestedRecord).some(nestedKey => {
            const nestedValue = nestedRecord[nestedKey];
            return nestedValue !== null && 
                   nestedValue !== undefined && 
                   String(nestedValue).toLowerCase().includes(searchTerm.toLowerCase());
          });
        }
        
        return String(value).toLowerCase().includes(searchTerm.toLowerCase());
      });
    });

    onFilter(filtered);
  }, [searchTerm, data, onFilter]);

  return (
    <div className="data-table-search">
      <input
        type="text"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        placeholder={placeholder}
        className="data-table-search-input"
      />
    </div>
  );
}
