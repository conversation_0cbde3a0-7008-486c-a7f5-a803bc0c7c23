import "./department-details.css";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import DepartmentDetails from "./DepartmentDetails";
import DepartmentAdmins from "./DepartmentAdmins";

import { User } from "lucide-react";

type TabName = "departmentDetails" | "departmentAdmins";

export default function DepartmentDetailsAndAdmins() {
  const { uuid, tab } = useParams<{ uuid: string; tab?: TabName }>();
  const [activeTab, setActiveTab] = useState<TabName>(
    tab || "departmentDetails"
  );

  useEffect(() => {
    if (tab && (tab === "departmentDetails" || tab === "departmentAdmins")) {
      setActiveTab(tab);
    }
  }, [tab]);


  return (
    <div className="department-details__content">
      <div className="department-card">
        <div className="department-details-header">
          <div className="department-details-title">
            <h1>
              <User size={24} style={{ marginRight: "10px" }} />
              Department Details and Admins
            </h1>
          </div>
        </div> 
        <div className="department-tabs-wrapper">
          <button
            className={`department-tab-button ${
              activeTab === "departmentDetails" ? "active" : ""
            }`}
            onClick={() => setActiveTab("departmentDetails")}
          >
            Department Details
          </button>
          <button
            className={`department-tab-button ${
              activeTab === "departmentAdmins" ? "active" : ""
            }`}
            onClick={() => setActiveTab("departmentAdmins")}
          >
            Department Administrators
          </button>
        </div>
        
        <div className="department-form-section">
            {activeTab === "departmentDetails" && (
               <DepartmentDetails uuid={uuid!} />
            )}

            {activeTab === "departmentAdmins" && (
              <DepartmentAdmins uuid={uuid!} />
              
            )}
        </div>
      </div>
    </div>
  );
}
