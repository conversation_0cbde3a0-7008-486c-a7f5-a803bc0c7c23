import { FileText } from 'lucide-react';
import { useGenerateDynamicFormPDF } from '@/hooks/form.query';

interface GeneratePdfButtonProps {
  uuid: string;
  isSubmission?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const GeneratePdfButton: React.FC<GeneratePdfButtonProps> = ({
  uuid,
  isSubmission = false,
  className,
  style,
}) => {
  const {
    mutate: generatePDF,
    isPending: generatePdfIsPending,
  } = useGenerateDynamicFormPDF();

  const handleClick = () => {
    if (!uuid) return;
    console.log("Form uuid:",uuid)

    generatePDF(
      { uuid, isSubmission },
      {
        onSuccess: (pdfResponse) => {
          const blob = pdfResponse.data;
          const url = window.URL.createObjectURL(blob);
          window.open(url, '_blank');
          setTimeout(() => window.URL.revokeObjectURL(url), 5000);
        },
        onError: (error: Error) => {
          console.error('PDF generation failed:', error.message);
        },
      }
    );
  };

  return (
    <button
      onClick={handleClick}
      disabled={generatePdfIsPending}
      className={className || 'btn-edit-form'}
      style={{ width: '120px', ...style }}
    >
      {generatePdfIsPending ? (
        <svg
          className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 
            5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 
            5.824 3 7.938l3-2.647z"
          />
        </svg>
      ) : (
        <>
          <FileText size={16} />
          <span style={{ fontSize: '11px' }} className="ml-1">Generate PDF</span>
        </>
      )}
    </button>
  );
};
