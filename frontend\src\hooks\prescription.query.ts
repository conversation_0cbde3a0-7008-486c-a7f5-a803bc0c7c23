import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAllPrescriptions, getAllPrescriptionsByPatient, createPrescription ,updatePrescription } from "@/services/api/prescription.service";
import { Prescription_KEYS } from './keys';
import { PrescriptionWithDetails } from "@/services/api/types";

export const usePrescriptionsQuery = () => {
    return useQuery<PrescriptionWithDetails[], Error>({
        queryKey: [Prescription_KEYS.GET_ALL],
        queryFn: getAllPrescriptions,
        refetchOnWindowFocus: false,
    });
};

export const usePrescriptionsByPatientQuery = (uuid: string) => {
    return useQuery<PrescriptionWithDetails[], Error>({
        queryKey: [Prescription_KEYS.GET_ALL_By_Patient],
        queryFn: () => getAllPrescriptionsByPatient(uuid),
        refetchOnWindowFocus: false,
        staleTime: 1000 * 60 * 5,
    });
};

export const useCreatePrescriptionMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: createPrescription,
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: [Prescription_KEYS.CREATE] });
        },
    });
};

export const useUpdatePrescriptionMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: ({ uuid, data }: { uuid: string; data: Partial<PrescriptionWithDetails> }) => 
            updatePrescription(uuid, data),
        onSuccess: (_, { uuid }) => {
          
            queryClient.invalidateQueries({ queryKey: [Prescription_KEYS.UPDATE, uuid] });
        },
    });
};
