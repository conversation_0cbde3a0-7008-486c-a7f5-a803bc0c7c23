.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f9fafb;
  border-radius: 8px 8px 0 0;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.modal-body {
  padding: 24px;
}

.visits-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.visit-item {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  background-color: #ffffff;
  transition: box-shadow 0.2s;
}

.visit-item:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.visit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f4f6;
}

.visit-header h3 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
}

.nhs-number {
  font-size: 0.875rem;
  color: #6b7280;
  background-color: #f3f4f6;
  padding: 4px 8px;
  border-radius: 4px;
}

.visit-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.visit-detail {
  background-color: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.visit-name {
  font-weight: 600;
  color: #111827;
  margin-bottom: 4px;
}

.visit-time {
  font-size: 0.875rem;
  color: #374151;
  margin-bottom: 4px;
}

.visit-status {
  font-size: 0.875rem;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
  margin-bottom: 4px;
}

.visit-status {
  background-color: #dbeafe;
  color: #1e40af;
}

.visit-location {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 4px;
}

.visit-activities {
  font-size: 0.875rem;
  color: #374151;
  margin-bottom: 4px;
}

.visit-comments {
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
  background-color: #ffffff;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e5e7eb;
}

.error-message {
  color: #dc2626;
  background-color: #fef2f2;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #fecaca;
}

/* Loading state */
.modal-body p {
  text-align: center;
  color: #6b7280;
  font-size: 1rem;
  margin: 20px 0;
}

/* Responsive design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    max-height: 90vh;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-header h2 {
    font-size: 1.25rem;
  }

  .modal-body {
    padding: 20px;
  }

  .visit-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .visit-header h3 {
    font-size: 1.125rem;
  }
}
