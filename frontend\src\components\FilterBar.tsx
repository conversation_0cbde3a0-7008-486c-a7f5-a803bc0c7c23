import React, { useState } from 'react';
import { Search, Filter, X } from 'lucide-react';
import './filterbar.css';

interface Tag {
  id: string;
  name: string;
}

interface Study {
  id: string;
  name: string;
}

interface FilterBarProps {
  totalItems: number;
  onSearch: (searchTerm: string) => void;
  tags?: Tag[];
  selectedTags: string[];
  onTagToggle: (tagId: string) => void;
  studies?: Study[];
  selectedStudies: string[];
  onStudyToggle: (studyId: string) => void;
}

const FilterBar: React.FC<FilterBarProps> = ({
  totalItems,
  onSearch,
  tags = [],
  selectedTags,
  onTagToggle,
  studies = [],
  selectedStudies,
  onStudyToggle
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    onSearch(value);
  };

  const handleClearSearch = () => {
    setSearchTerm('');
    onSearch('');
  };

  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };

  return (
    <div className="filter-bar">
      <div className="filter-bar__main">
        <div className="filter-bar__search">
          <Search size={18} className="search-icon" />
          <input
            type="text"
            placeholder="Search forms..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="search-input"
          />
          {searchTerm && (
            <button className="clear-search" onClick={handleClearSearch}>
              <X size={16} />
            </button>
          )}
        </div>
        
        <div className="filter-bar__actions">
          <div className="filter-bar__count">
            {totalItems} {totalItems === 1 ? 'form' : 'forms'} found
          </div>
          
          <button 
            className={`filter-toggle ${showFilters ? 'active' : ''}`} 
            onClick={toggleFilters}
          >
            <Filter size={18} />
            <span>Filters</span>
            {(selectedTags.length > 0 || selectedStudies.length > 0) && (
              <span className="filter-badge">{selectedTags.length + selectedStudies.length}</span>
            )}
          </button>
        </div>
      </div>
      
      {showFilters && (
        <div className="filter-bar__filters">
          {/* Tags */}
          {tags.length > 0 && (
            <div className="filter-bar__tags">
              <h6 className="filter-section-title">Categories</h6>
              <div className="tags-wrapper">
                {tags.map(tag => (
                  <button
                    key={tag.id}
                    className={`tag-item ${selectedTags.includes(tag.id) ? 'active' : ''}`}
                    onClick={() => onTagToggle(tag.id)}
                  >
                    {tag.name}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Studies */}
          {studies.length > 0 && (
            <div className="filter-bar__tags">
              <h6 className="filter-section-title">Studies</h6>
              <div className="tags-wrapper">
                {studies.map(study => (
                  <button
                    key={study.id}
                    className={`tag-item ${selectedStudies.includes(study.id) ? 'active' : ''}`}
                    onClick={() => onStudyToggle(study.id)}
                  >
                    {study.name}
                  </button>
                ))}
              </div>
            </div>
          )}
          
          {(selectedTags.length > 0 || selectedStudies.length > 0) && (
            <button 
              className="tag-item clear-all"
              onClick={() => {
                selectedTags.forEach(tagId => onTagToggle(tagId));
                selectedStudies.forEach(studyId => onStudyToggle(studyId));
              }}
            >
              Clear All
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default FilterBar;
