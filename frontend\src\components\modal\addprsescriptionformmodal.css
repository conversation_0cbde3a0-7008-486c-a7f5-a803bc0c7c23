.add-prsescription-form-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba(217, 217, 217, 0.89);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 20px;
    box-sizing: border-box;
  }
  
  .form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
  }
  .add-prsescription-form {
    background-color: #fff;
    border-radius: 8px;
    width: 90%; 
    max-width: 1023px;
    max-height: 80vh;
    padding: 20px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid black;
    overflow-y: auto;
  }
  
  .confirm-modal-content {
    background-color: #fff;
    border-radius: 8px;
    width: 440px;
    max-height: 80vh;
    padding: 20px;
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid black;
    overflow-y: auto;
  }
  
  .add-new-prsescription-form-title {
    font-size: 28px; 
    font-weight: bold;
    text-align: center;
    margin-bottom: 30px; 
    line-height: 1.5;
    color: #000000;
  }
  
  .form-grid {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 800;
    line-height: 24px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
    flex-wrap: wrap; 
  }
  
  .form-column {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }
  
  
  
  .form-column:first-child input,
  .form-column:first-child select {
    width: 100%; 
    max-width: 251px;
    border: 1px solid #37b7c3;
    border-radius: 8px;
    padding: 0.8rem;
    font-size: 1rem;
  }
  
  .form-column:last-child input,
  .form-column:last-child select {
    width: 100%; 
    max-width: 443px;
    border: 1px solid #37b7c3;
    border-radius: 8px;
    padding: 0.8rem;
    font-size: 1rem;
  }
  
  .button-container {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    flex-wrap: wrap; 
  }
  
  label {
    font-size: 16px;
    font-weight: 800;
    line-height: 24px;
    text-align: left;
    text-underline-position: from-font;
    text-decoration-skip-ink: none;
  }
  
  .cancel-button {
    background-color: #ff4d4d;
    color: white;
    border: none;
    width: 100%; 
    max-width: 108px;
    height: 36px;
    border-radius: 6px;
    cursor: pointer;
  }
  
  .submit-button {
    background-color: #00a6c9;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    width: 100%; 
    max-width: 108px;
    height: 36px;
  }
  
  .submit-button:hover {
    background-color: #0088a7;
  }
  
  .cancel-button:hover {
    background-color: #d13a3a;
  }
  
  
  @media (max-width: 768px) {
    .add-new-prsescription-form-title {
      font-size: 24px;
      margin-bottom: 20px;
    }
  
    .form-grid {
      flex-direction: column; 
      gap: 15px; 
    }
  
    .form-column {
      padding: 10px; 
    }
  
    .form-column input,
    .form-column select {
      width: 100%; 
    }
  
    .button-container {
      justify-content: center; 
    }
  
    .cancel-button,
    .submit-button {
      width: 100%;
      max-width: none;
    }
  }
  
  @media (max-width: 480px) {
    .add-new-prsescription-form-title {
      font-size: 20px;
      margin-bottom: 15px;
    }
  
    .form-column {
      padding: 5px; 
    }
  
    label {
      font-size: 14px;
    }
  
    .cancel-button,
    .submit-button {
      height: 32px; 
    }
  }
  