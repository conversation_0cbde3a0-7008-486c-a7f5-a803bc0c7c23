.analytics-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    padding: 15px 30px 30px 30px; /* Changed top padding */
    margin-bottom: 30px;
    width: 100%;
}

@media (max-width: 768px) {
    .analytics-container {
        /* padding-bottom: 80px; */ /* Removed */
    }
}

@media (max-width: 480px) {
    .analytics-container {
        /* padding-bottom: 60px; */ /* Removed */
    }
}

.analytics-content-wrapper {
    padding: 20px;
}

.analytics-dashboard {
    display: flex;
    flex-direction: column;
}

.analytics-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.analytics-title {
    margin-bottom: 10px;
}

.analytics-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.analytics-subtitle {
    color: #4F547B;
    font-size: 16px;
    font-weight: 400;
    margin-top: 5px;
}

.analytics-subtitle h6 {
    margin: 0;
    font-weight: 400;
}

.analytics-search-container {
    display: flex;
    gap: 50px;
    align-items: center;
}

.analytics-search-box {
    position: relative;
    width: 550px;
}

.analytics-search-input {
    width: 100%;
    padding: 10px 50px 10px 15px;
    border: 1px solid #EDEDED !important;
    border-radius: 0; /* Changed */
    outline: none;
}


.analytics-search-button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #3EC1C9;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 0; /* Changed */
    cursor: pointer;
}

.analytics-add-button {
    background-color: #37B7C3;
    color: white !important;
    border: none;
    padding: 10px 15px;
    border-radius: 0; /* Changed */
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    text-decoration: none;
}

.analytics-add-button:hover {
    background-color: #2ea4a9;
}



.analytics-table-container {
    border: 1px solid #A3AED0;
    border-radius: 0; /* Changed */
    max-height: 600px;
    overflow-y: auto;
    margin-top: 30px;
}

.analytics-chart-container {
    border: 1px solid #E0E0E0;
    border-radius: 0; /* Changed */
    padding: 20px;
    margin-top: 30px;
    background-color: #ffffff;
    box-shadow: none; /* Changed */
    transition: all 0.3s ease;
}

.chart-card {
    position: relative;
    overflow: hidden;
}

.chart-card:hover {
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;
}

.chart-summary {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 0 10px;
}

.summary-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.summary-value {
    font-size: 18px;
    font-weight: 600;
    color: #2B3674;
}

.chart-title {
    color: #2B3674;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.chart-wrapper {
    width: 100%;
    height: 350px;
    padding: 10px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.chart-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 30px;
    width: 100%;
    align-items: stretch;
}

/* Custom tooltip styles */
.recharts-tooltip-wrapper {
    border-radius: 0 !important; /* Changed */
    box-shadow: none !important; /* Changed */
}

.recharts-default-tooltip {
    background-color: rgba(255, 255, 255, 0.95) !important;
    border: none !important;
    border-radius: 0 !important; /* Changed */
    padding: 10px 15px !important;
    box-shadow: none !important; /* Changed */
}

/* Legend styles */
.recharts-legend-item-text {
    font-size: 14px !important;
    color: #2B3674 !important;
}

/* Axis styles */
.recharts-cartesian-axis-tick-value {
    font-size: 12px;
    fill: #666;
}

/* Animation for charts */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.analytics-chart-container {
    animation: fadeIn 0.6s ease-out forwards;
}

.analytics-table {
    width: 100%;
    border-collapse: collapse;
}

.analytics-table thead {
    position: sticky;
    top: -3px;
    color: #A3AED0;
    background: #ffffff;
    z-index: 10;
}

.analytics-table th, .analytics-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ffffff;
}

.analytics-table th {
    color: #A3AED0;
}

.analytics-table tr:nth-child(even) {
    background-color: #ffffff;
}

.analytics-table tr:hover {
    background-color: #ffffff;
}

.analytics-action-button {
    background: none;
    border: none;
    cursor: pointer;
    margin-right: 8px;
}

.analytics-action-button:hover {
    color: #007bff;
}


.ArrowUp{
    background-color: #37B7C3;
    border-radius: 0; /* Changed */
    border-color: black;
    color: white;
}

.iconButton {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 32px;
    height: 32px;
    border-radius: 0; /* Changed */
    border: none;
    transition: all 0.2s ease;
  }

  .editIcon {
    background-color: #37b7c3;
    color: white;
  }

  /* Keeping table-grid for backward compatibility */
  .table-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 30px;
    width: 100%;
    align-items: stretch;
  }

  .card-grid {
    display: grid;
    
   
    align-items: stretch;
  }

  .kpi-cards-container {
    position: relative;
    display: flex;
    align-items: center;
    margin: 20px 0;
  }

  .carrousel-KPIcard {
    overflow-x: auto;
    scroll-behavior: smooth;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
    padding: 10px 0;
    gap: 16px;
  }

  .carrousel-KPIcard::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  .scroll-button {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 2;
    box-shadow: none; /* Changed */
    transition: all 0.2s ease;
  }

  .scroll-button:hover {
    background: #f5f5f5;
    box-shadow: none; /* Changed */
  }

  .scroll-button.left {
    left: -20px;
  }

  .scroll-button.right {
    right: -20px;
  }

  .title-Analitics {
    color: #2B3674;
    border-bottom-style: solid;
    border-bottom-color: #E0E0E0;
    border-bottom-width: 2px;
    display: inline-block;
    margin: 0;
    padding: 4px 0px;
   }


   
   /* From Uiverse.io by satyamchaudharydev */ 
.spinner {
    --clr: rgb(0, 113, 128);
    --gap: 6px;
      /* gap between each circle */
    width: 100px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--gap);
   }
   
   .spinner span {
    width: 20px;
    height: 20px;
    border-radius: 100%;
    background-color: var(--clr);
    opacity: 0;
   }
   
   .spinner span:nth-child(1) {
    animation: fade 1s ease-in-out infinite;
   }
   
   .spinner span:nth-child(2) {
    animation: fade 1s ease-in-out 0.33s infinite;
   }
   
   .spinner span:nth-child(3) {
    animation: fade 1s ease-in-out 0.66s infinite;
   }
   
   @keyframes fade {
    0%, 100% {
     opacity: 1;
    }
   
    60% {
     opacity: 0;
    }
   }
   

   .Reload-button {
    background-color: #37B7C3;
    color: white !important;
    border: none;
    padding: 10px 15px;
    border-radius: 0; /* Changed */
    gap: 5px;
    cursor: pointer;
    text-decoration: none;
}

.Reload-button:hover {
    background-color: #2ea4a9;
}
