import { useQuery } from "@tanstack/react-query";
import { getSponsorStaff, SponsorStaffFilters } from "@/services/api/sponsorStaff.service";

export const SPONSOR_STAFF_KEYS = {
  GET_ALL: "sponsor-staff",
  GET_FILTERED: (filters: SponsorStaffFilters) => ["sponsor-staff", filters],
};

export const useSponsorStaffQuery = (filters?: SponsorStaffFilters) => {
  return useQuery({
    queryKey: filters ? SPONSOR_STAFF_KEYS.GET_FILTERED(filters) : [SPONSOR_STAFF_KEYS.GET_ALL],
    queryFn: () => getSponsorStaff(filters),
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}; 