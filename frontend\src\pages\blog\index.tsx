import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { blogArticles } from '../../data/blogData';
import Footer from '../../shared/Footer';
import './blog.css';

// Helper function to create URL-friendly slugs
const createBlogUrl = (article: { id: string, title: string }) => {
  const slug = article.title.toLowerCase()
    .replace(/[^\w\s-]/g, '') // Remove special chars
    .replace(/\s+/g, '-')     // Replace spaces with hyphens
    .trim();
  return `${article.id}-${slug}`;
};

const BlogList: React.FC = () => {
  return (
    <>
      <div className="blog-list-container">
        <div className="container">
          <motion.h1 
            className="blog-list-title"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
          Knowledge <span className="text-highlight">Hub</span>
          </motion.h1>
          
          <motion.p 
            className="blog-list-subtitle"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            Latest articles and resources
          </motion.p>

          <div className="blog-grid">
            {blogArticles.map((article, index) => (
              <motion.div 
                key={article.id} 
                className="blog-card"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="blog-card-image">
                  <img src={article.image} alt={article.title} />
                </div>
                <div className="blog-card-content">
                  <div className="blog-card-meta">
                    <span className="blog-card-author">{article.author}</span>
                    <span className="blog-card-date">{article.date}</span>
                  </div>
                  <h2 className="blog-card-title">{article.title}</h2>
                  <p className="blog-card-excerpt">{article.excerpt}</p>
                  <Link to={`/blog/${createBlogUrl(article)}`} className="blog-read-more">
                    Read Article
                  </Link>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default BlogList;
