import React, { useState, useMemo } from "react";
import { useSponsorStaffQuery } from "@/hooks/sponsorStaff.query";
import { useHospitalsQuery } from "@/hooks/hospital.query";
import { useDepartmentsQuery } from "@/hooks/department.query";
import { Search, Filter, X, FileText } from "lucide-react";
import DataTable from "@/components/common/DataTable";
import StaffDocumentsModal from "@/components/modal/StaffDocumentsModal";
import "./sponsor-staff.css";

interface Hospital {
  uuid: string;
  name: string;
}

interface Department {
  uuid: string;
  name: string;
  hospital: {
    uuid: string;
    name: string;
  };
  phone_number: string;
  extension: string;
  primary_address: string;
  secondary_address: string;
  country: string;
  postcode: string;
}

interface SponsorStaff {
  staff_uuid: string;
  full_name: string;
  role: string;
  associated_hospital: string;
  department_name: string;
  email: string;
  phone_number: string;
  involved_studies: Array<{
    study_uuid: string;
    study_name: string;
    iras: string;
  }>;
  is_admin: boolean;
  is_hospital_admin: boolean;
}

const AllStuff: React.FC = () => {
  const [filters, setFilters] = useState({
    hospital: "",
    department: "",
    role: "",
    search: "",
  });

  // State for document modal
  const [selectedStaff, setSelectedStaff] = useState<{
    uuid: string;
    name: string;
  } | null>(null);
  const [isDocumentsModalOpen, setIsDocumentsModalOpen] = useState(false);

  const { data: staffData, isLoading: isLoadingStaff, error: staffError } = useSponsorStaffQuery(filters);
  const { data: hospitals = [] as Hospital[], isLoading: isLoadingHospitals } = useHospitalsQuery();
  const { data: allDepartments = [] as Department[], isLoading: isLoadingDepartments } = useDepartmentsQuery();

  // Filter departments based on selected hospital
  const departments = useMemo(() => {
    if (!filters.hospital) return [];
    return allDepartments.filter(dept => {
      const hospital = dept.hospital;
      return typeof hospital === 'object' && hospital !== null && 'uuid' in hospital && hospital.uuid === filters.hospital;
    });
  }, [allDepartments, filters.hospital]);

  // Get unique roles from staff data
  const uniqueRoles = useMemo(() => {
    if (!staffData?.results) return [];
    const roles = staffData.results.map(staff => staff.role).filter(Boolean);
    return [...new Set(roles)].sort();
  }, [staffData?.results]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters(prev => ({ ...prev, search: e.target.value }));
  };

  const handleHospitalFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilters(prev => ({ ...prev, hospital: e.target.value, department: "" }));
  };

  const handleDepartmentFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilters(prev => ({ ...prev, department: e.target.value }));
  };

  const handleRoleFilter = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilters(prev => ({ ...prev, role: e.target.value }));
  };

  // Handle opening documents modal
  const handleViewDocuments = (staffUuid: string, staffName: string) => {
    setSelectedStaff({ uuid: staffUuid, name: staffName });
    setIsDocumentsModalOpen(true);
  };

  // Handle closing documents modal
  const handleCloseDocumentsModal = () => {
    setIsDocumentsModalOpen(false);
    setSelectedStaff(null);
  };

  // Define columns for the DataTable
  const columns = [
    {
      key: "full_name" as keyof SponsorStaff,
      header: "Full Name",
      sortable: true,
    },
    {
      key: "role" as keyof SponsorStaff,
      header: "Role",
      sortable: true,
    },
    {
      key: "associated_hospital" as keyof SponsorStaff,
      header: "Hospital",
      sortable: true,
    },
    {
      key: "department_name" as keyof SponsorStaff,
      header: "Department",
      sortable: true,
    },
    {
      key: "email" as keyof SponsorStaff,
      header: "Email",
      sortable: true,
    },
    {
      key: "phone_number" as keyof SponsorStaff,
      header: "Phone",
      sortable: true,
    },
    {
      key: "involved_studies" as keyof SponsorStaff,
      header: "Involved Studies",
      sortable: false,
      render: (value: any) => {
        if (!value || !Array.isArray(value) || value.length === 0) return "No studies";
        return (
          <div>
            {value.map((study: any, index: number) => (
              <div key={study.study_uuid} style={{ marginBottom: index < value.length - 1 ? '4px' : '0' }}>
                <strong>{study.study_name}</strong> ({study.iras})
              </div>
            ))}
          </div>
        );
      },
    },
    {
      key: "documents" as keyof SponsorStaff,
      header: "Documents",
      sortable: false,
      render: (_: any, row?: SponsorStaff) => {
        if (!row) return null;
        return (
          <button
            className="view-documents-btn"
            onClick={() => handleViewDocuments(row.staff_uuid, row.full_name)}
            title="View Documents"
          >
            <FileText size={16} />
            View Documents
          </button>
        );
      },
    },
  ];

  // Only show loading state during initial data fetch
  const isInitialLoading = isLoadingHospitals || isLoadingDepartments || (!staffData && isLoadingStaff);

  if (isInitialLoading) {
    return <div className="sponsor-staff-container">Loading...</div>;
  }

  if (staffError) {
    return <div className="sponsor-staff-container">Error loading staff data</div>;
  }

  // Add uuid to each staff record for DataTable
  const staffWithUuid = staffData?.results.map((staff) => ({
    ...staff,
    uuid: staff.staff_uuid,
  })) || [];

  return (
    <div className="sponsor-staff-container">
      <h1>Sponsor Staff</h1>

      <div className="filters-section">
        <div className="search-filter-container">
          <div className="policy-search-container">
            <div className="policy-search-box">
              <Search className="search-icon" size={20} />
              <input
                type="text"
                placeholder="Search by name, department, hospital, or role"
                className="policy-search-input"
                value={filters.search}
                onChange={handleSearch}
              />
              {filters.search && (
                <button className="clear-search" onClick={() => setFilters(prev => ({ ...prev, search: "" }))}>
                  <X size={18} />
                </button>
              )}
            </div>
          </div>
        </div>

        <div className="filter-dropdowns">
          <div className="filter-group">
            <Filter size={20} />
            <select 
              value={filters.hospital} 
              onChange={handleHospitalFilter}
              disabled={isLoadingHospitals}
            >
              <option value="">All Hospitals</option>
              {hospitals.map((hospital) => (
                <option key={hospital.uuid} value={hospital.uuid}>
                  {hospital.name}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <Filter size={20} />
            <select 
              value={filters.department} 
              onChange={handleDepartmentFilter}
              disabled={isLoadingDepartments || !filters.hospital}
            >
              <option value="">All Departments</option>
              {departments.map((department) => (
                <option key={department.uuid} value={department.uuid}>
                  {department.name}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <Filter size={20} />
            <select 
              value={filters.role} 
              onChange={handleRoleFilter}
            >
              <option value="">All Roles</option>
              {uniqueRoles.map((role) => (
                <option key={role} value={role}>
                  {role}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {staffWithUuid.length > 0 ? (
        <DataTable
          data={staffWithUuid}
          columns={columns}
          defaultItemsPerPage={10}
          itemsPerPageOptions={[5, 10, 25, 50]}
          noDataMessage="No staff found matching your criteria"
        />
      ) : (
        <div className="no-results">
          No staff found matching your criteria
        </div>
      )}

      {/* Staff Documents Modal */}
      {selectedStaff && (
        <StaffDocumentsModal
          isOpen={isDocumentsModalOpen}
          onClose={handleCloseDocumentsModal}
          staffUuid={selectedStaff.uuid}
          staffName={selectedStaff.name}
        />
      )}
    </div>
  );
};

export default AllStuff; 