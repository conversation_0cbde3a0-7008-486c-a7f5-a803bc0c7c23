/* Study Detail Page Styles */

.study-detail-container {
  background-color: #fff;
  border-radius: 0; /* Changed */
  box-shadow: none; /* Changed */
  padding: 15px 28px 28px 28px; /* Changed top padding */
  margin-bottom: 30px;
  transition: all 0.3s ease;
}

.study-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px;
  border-bottom: 1px solid var(--color-light-2);
  padding-bottom: 20px;
}

.study-detail-header h1 {
  font-size: 26px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0;
}

.back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 0; /* Changed */
  font-size: 14px;
  font-weight: 500;
  background-color: var(--color-light-4);
  color: var(--color-dark-3);
  border: 1px solid var(--color-light-2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-btn:hover {
  background-color: var(--color-light-3);
  color: var(--color-dark-1);
}

.study-detail-content {
  display: grid;
  gap: 28px;
}

.study-info-container {
  background-color: var(--color-light-4);
  border-radius: 0; /* Changed */
  padding: 24px;
  border: 1px solid var(--color-light-2);
  transition: all 0.3s ease;
}

.study-info-container h2 {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin-top: 0;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-light-2);
}

.study-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 0; /* Changed */
  box-shadow: none; /* Changed */
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-label {
  font-size: 0.875rem;
  color: var(--color-dark-2);
  font-weight: 500;
}

.info-value {
  font-size: 1rem;
  color: var(--color-dark-1);
}

.study-description {
  font-size: 1rem;
  color: var(--color-dark-1);
  line-height: 1.5;
  white-space: pre-wrap;
}

.departments-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.department-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.75rem;
  background: var(--color-light-4);
  border-radius: 0; /* Changed */
}

.department-name {
  font-size: 1rem;
  color: var(--color-dark-1);
  font-weight: 500;
}

.hospital-name {
  font-size: 0.875rem;
  color: var(--color-dark-2);
}

.study-patients {
  margin-top: 28px;
}

/* Patient Status Chart Styles */
.patient-status-chart {
  margin-top: 28px;
  background-color: white;
  border-radius: 0; /* Changed */
  box-shadow: none; /* Changed */
  padding: 24px;
  border: 1px solid var(--color-light-2);
  transition: all 0.3s ease;
}

.patient-status-chart h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--color-light-2);
}

.chart-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 280px;
  padding: 10px;
}

.visits-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.visits-header h2 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
  padding: 0;
  border: none;
}

.enroll-patient-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 0; /* Changed */
  font-size: 14px;
  font-weight: 500;
  background-color: var(--color-purple-1);
  color: white;
  border: 1px solid var(--color-purple-1);
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: none; /* Changed */
}

.enroll-patient-btn:hover {
  background-color: #2da1ac;
  box-shadow: none; /* Changed */
}

.loading-patients,
.no-patients {
  padding: 18px;
  text-align: center;
  color: var(--color-dark-3);
  background-color: var(--color-light-4);
  border: 1px dashed var(--color-light-2);
  border-radius: 0; /* Changed */
  font-size: 14px;
  margin-top: 16px;
}

.patients-list {
  display: flex;
  flex-direction: column;
  gap: 14px;
  margin-top: 16px;
}

.patient-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #fff;
  border: 1px solid var(--color-light-2);
  border-radius: 0; /* Changed */
  transition: all 0.2s ease;
  box-shadow: none; /* Changed */
}

.patient-item:hover {
  box-shadow: none; /* Changed */
  border-color: var(--color-light-8);
}

.patient-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.patient-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-dark-1);
}

.patient-id {
  font-size: 14px;
  color: var(--color-dark-3);
}

.patient-actions {
  display: flex;
  gap: 8px;
}

.remove-patient-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 0; /* Changed */
  background-color: var(--color-red-2);
  color: var(--color-red-3);
  border: 1px solid var(--color-red-2);
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-patient-btn:hover {
  background-color: var(--color-red-3);
  color: white;
}

.study-visits-container {
  background-color: #f9fafb;
  border-radius: 0; /* Changed */
  padding: 20px;
  border: 1px solid #e5e7eb;
}

.visits-actions {
  display: flex;
  gap: 8px;
}

.add-visit-btn,
.add-unscheduled-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 0; /* Changed */
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-visit-btn {
  background-color: #10b981;
  color: white;
  border: 1px solid #059669;
}

.add-visit-btn:hover {
  background-color: #059669;
}

.add-unscheduled-btn {
  background-color: #f59e0b;
  color: white;
  border: 1px solid #d97706;
}

.add-unscheduled-btn:hover {
  background-color: #d97706;
}

/* View toggle styles */
.view-toggle-container {
  display: flex;
  gap: 4px;
}

.view-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 0; /* Changed */
  background-color: #f3f4f6;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-toggle-btn:hover {
  background-color: #e5e7eb;
}

.view-toggle-btn.active {
  background-color: #36b6c2;
  color: white;
  border-color: #2da1ac;
}

.search-bar {
  position: relative;
  margin-bottom: 16px;
}

.search-bar input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 0; /* Changed */
  font-size: 14px;
}

.search-bar svg {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.filter-container {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.filter-select {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 0; /* Changed */
  font-size: 14px;
  background-color: white;
}

.loading-visits,
.no-visits {
  padding: 24px;
  text-align: center;
  color: #6b7280;
  background-color: #fff;
  border: 1px dashed #e5e7eb;
  border-radius: 0; /* Changed */
  font-size: 14px;
}

/* Table view styles */
.visits-table-container {
  overflow-x: auto;
  margin-bottom: 20px;
}

.visits-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.visits-table th,
.visits-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
}

.visits-table th {
  font-weight: 600;
  color: #4b5563;
  background-color: #f9fafb;
}

.visits-table tr:hover {
  background-color: #f9fafb;
}

.visit-name-cell {
  font-weight: 500;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.visit-actions-cell {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.visits-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.visit-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 0; /* Changed */
}

.visit-info {
  flex: 1;
}

.visit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.visit-name {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.unscheduled-badge {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 0; /* Changed */
  background-color: #f59e0b;
  color: white;
}

.status-badge {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 0; /* Changed */
}

.status-badge.pending {
  background-color: #36b6c2;
  color: white;
}

.status-badge.completed {
  background-color: #10b981;
  color: white;
}

.status-badge.not-completed {
  background-color: #ef4444;
  color: white;
}

.visit-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 8px;
}

.visit-date,
.visit-patient,
.visit-activities {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
}

.visit-actions {
  display: flex;
  gap: 8px;
}

.status-select {
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 0; /* Changed */
  font-size: 14px;
  background-color: white;
}

.edit-btn,
.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 0; /* Changed */
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-btn {
  background-color: #f59e0b;
  color: white;
  border: 1px solid #d97706;
}

.edit-btn:hover {
  background-color: #d97706;
}

.delete-btn {
  background-color: #ef4444;
  color: white;
  border: 1px solid #dc2626;
}

.delete-btn:hover {
  background-color: #dc2626;
}

/* Visit Modal Styles */
.visit-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.visit-modal {
  background-color: white;
  border-radius: 0; /* Changed */
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: none; /* Changed */
}

.visit-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e5e7eb;
}

.visit-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.visit-modal-close {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  padding: 4px;
}

.visit-modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 6px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  /* border: 1px solid #d1d5db; */
  border-radius: 0; /* Changed */
  font-size: 14px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.cancel-btn,
.save-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 0; /* Changed */
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn {
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
}

.cancel-btn:hover {
  background-color: #e5e7eb;
}

.save-btn {
  background-color: #36b6c2;
  color: white;
  border: 1px solid #2da1ac;
}

.save-btn:hover {
  background-color: #2da1ac;
}

/* Responsive styles */
@media (max-width: 768px) {
  .study-detail-content {
    grid-template-columns: 1fr;
  }
  
  .filter-container {
    flex-direction: column;
  }
  
  .visit-item {
    flex-direction: column;
  }
  
  .visit-actions {
    margin-top: 12px;
    width: 100%;
    justify-content: flex-end;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .patient-status-chart {
    padding: 12px;
  }
  
  .chart-container {
    height: 220px;
  }
}

@media (max-width: 480px) {
  .patient-status-chart h3 {
    font-size: 14px;
    margin-bottom: 12px;
  }
  
  .chart-container {
    height: 200px;
  }
}
