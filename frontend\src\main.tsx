import "./bootstrap";
import "bootstrap/dist/css/bootstrap.min.css";
import "./utils/console-production"; // Hide console logs in production
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.tsx";
import { ReactKeycloakProvider } from "@react-keycloak/web";
import { keycloak } from "./keycloack.ts";

const root = createRoot(document.getElementById("root")!);
root.render(
  <ReactKeycloakProvider
    authClient={keycloak}
    initOptions={{ onLoad: 'check-sso' }}
    onEvent={(event, error) => {
      console.log('[Keycloak event]', event);
      if (error) console.error('[Keycloak error]', error);
    }}
  >
    <App />
  </ReactKeycloakProvider>
);
