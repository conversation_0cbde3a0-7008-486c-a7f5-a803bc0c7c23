 
import useHolisticFormStore from "../../../../store/holisticFormState";
import woundImage from "./static/images/added/wound.png";
import { useState } from "react";
import NurtifyRadio from "../../../../components/NurtifyRadio";
import NurtifyInput from "../../../../components/NurtifyInput";
import NurtifyTextArea from "../../../../components/NurtifyTextArea";

// CSS classes for consistent styling
const sectionHeadingClass = "subheading mb-3 mt-4";
const radioGroupClass = "d-flex flex-wrap gap-2 mb-3";
const radioOptionClass = "labelSBARForm cursorPointer rounded-3";
const inputGroupClass = "input-group my-3 gap-2";
const inputLabelClass = "inlineInput";

const Wound: React.FC = () => {
  const holisticForm = useHolisticFormStore();
  const { assessment } = holisticForm;
  
  const setWound = (woundData: any) => {
    holisticForm.setAssessment({
      ...holisticForm.assessment,
      wound: {
        ...holisticForm.assessment.wound,
        ...woundData
      },
    });
  };

  const setTypeOfWound = (value: any) => {
    setWound({
      ...assessment.wound,
      typeOfWound: value,
    });
  };

  const [typeOfWoundOther, setTypeOfWoundOther] = useState<string | null>(assessment.wound.typeOfWound === "" ? null : assessment.wound.typeOfWound);
  const [typeOfWoundOtherActive, setTypeOfWoundOtherActive] = useState(assessment.wound.typeOfWound === typeOfWoundOther ? true : false);
 
  
  const handleTypeOfWoundOther = () => {
    
    setTypeOfWoundOtherActive(true);
      setWound({
        ...assessment.wound,
        typeOfWound: typeOfWoundOther,
      });   
  };

  const [colorOfWoundOther, setColorOfWoundOther] = useState<string | null>(assessment.wound.colorOfWound === "" ? null : assessment.wound.colorOfWound);
  const [colorOfWoundOtherActive, setColorOfWoundOtherActive] = useState(assessment.wound.colorOfWound === colorOfWoundOther ? true : false);

  const handleColorOfWoundOther = () => {
    
    setColorOfWoundOtherActive(true);
      setWound({
        ...assessment.wound,
        colorOfWound: colorOfWoundOther,
      });   
  };
 
  
  const setColorOfWound = (value: any) => {
    setWound({
      ...assessment.wound,
      colorOfWound: value,
    });
  };

  
  
  const setSurroundingSkinOfWound = (value: any) => {
    setWound({
      ...assessment.wound,
      surroundingSkinOfWound: value,
    });
  };

  const setExudatesOfWound = (value: any) => {
    setWound({
      ...assessment.wound,
      exudatesOfWound: value,
    });
  };

  const setDescriptionOfExudateOfWound = (value: any) => {
    setWound({
      ...assessment.wound,
      descriptionOfExudateOfWound: value,
    });
  };

  const [descriptionOfExudateOfWoundOther, setDescriptionOfExudateOfWoundOther] = useState<string | null>(assessment.wound.descriptionOfExudateOfWound === "" ? null : assessment.wound.descriptionOfExudateOfWound);
  const [descriptionOfExudateOfWoundOtherActive, setDescriptionOfExudateOfWoundOtherActive] = useState(assessment.wound.descriptionOfExudateOfWound === descriptionOfExudateOfWoundOther ? true : false);

  const handleDescriptionOfExudateOfWoundOther = () => {
    setDescriptionOfExudateOfWoundOtherActive(true);
    setWound({
      ...assessment.wound,
      descriptionOfExudateOfWound: descriptionOfExudateOfWoundOther,
    });
  };

  return (
    <form className="container-fluid p-4">
      <div className="row">
        <div className="col-12">
          {/* Header Section */}
          <div className="d-flex align-items-center mb-4 headinqQuestion">
            <img
              src={woundImage}
              className="imageEtiquette me-3"
              alt="Wound assessment icon"
            />
            <h2 className="fs-2 text-start etiquetteHeadingForms m-0">
              Wound Assessment
            </h2>
          </div>

          {/* Initial Question */}
          <div className="bg-light rounded p-4 mb-4">
            <div className="row">
              <div className="col-md-8 col-lg-6">
                <h4 className="headinqQuestion mb-3">
                  Any wound noticed during assessment?
                </h4>

                <div>
                  <div>
                    <NurtifyRadio
                      label="Yes"
                      name="anyWound"
                      value="true"
                      checked={assessment?.wound?.anyWound === true}
                      onChange={() =>
                        setWound({
                          ...assessment.wound,
                          anyWound: true,
                        })
                      }
                    />
                  </div>
                  <div>
                    <NurtifyRadio
                      label="No"
                      name="anyWound"
                      value="false"
                      checked={assessment?.wound?.anyWound === false}
                      onChange={() =>
                        setWound({
                          anyWound: false,
                        })
                      }
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Conditional Wound Assessment Form */}
          {assessment?.wound?.anyWound === true && (
            <div className="bg-light rounded p-4 mb-4">
              <div className="row">
                <div className="col-12">
                  {/* Wound Details Section */}
                  <h3 className={sectionHeadingClass}>
                    Wound Details
                  </h3>
                  
                  <div className="row mb-4">
                    <div className="col-md-6">
                      <div>
                        <label className={inputLabelClass}>
                          Wound Location
                        </label>
                        <NurtifyInput
                          value={assessment?.wound?.locationOfWound || ""}
                          onChange={(e) =>
                            setWound({
                              ...assessment.wound,
                              locationOfWound: e.target.value,
                            })
                          }
                          type="text"
                          name="location_of_wound"
                        />
                      </div>
                    </div>
                    
                    <div className="col-md-6">
                      <div>
                        <label className={inputLabelClass}>
                          Age of wound
                        </label>
                        <NurtifyInput
                          value={assessment?.wound?.ageOfWound || ""}
                          onChange={(e) =>
                            setWound({
                              ...assessment.wound,
                              ageOfWound: e.target.value,
                            })
                          }
                          type="text"
                          name="age_of_wound"
                        />
                      </div>
                    </div>
                    
                    <div className="col-md-6">
                      <div>
                        <label 
                          className={inputLabelClass}
                          title="(e.g. pressure, shear, trauma, shoes, etc.)"
                        >
                          Cause of wound
                        </label>
                        <NurtifyInput
                          value={assessment?.wound?.causeOfWound || ""}
                          onChange={(e) =>
                            setWound({
                              ...assessment.wound,
                              causeOfWound: e.target.value,
                            })
                          }
                          name="cause_of_wound"
                          type="text"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Type of Wound Section */}
                  <h3 className={sectionHeadingClass}>
                    Type of wound
                  </h3>
                  
                  <div className="row mb-4">
                    <div className="col-12">
                      <div className={radioGroupClass}>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Pressure Ulcer"
                            name="type_of_wound"
                            value="Pressure Ulcer"
                            checked={assessment?.wound?.typeOfWound === "Pressure Ulcer"}
                            onChange={(e) => setTypeOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Diabetic Ulcer"
                            name="type_of_wound"
                            value="Diabetic Ulcer"
                            checked={assessment?.wound?.typeOfWound === "Diabetic Ulcer"}
                            onChange={(e) => setTypeOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Penetrating wound"
                            name="type_of_wound"
                            value="Penetrating wound"
                            checked={assessment?.wound?.typeOfWound === "Penetrating wound"}
                            onChange={(e) => setTypeOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Laceration"
                            name="type_of_wound"
                            value="Laceration"
                            checked={assessment?.wound?.typeOfWound === "Laceration"}
                            onChange={(e) => setTypeOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Burn"
                            name="type_of_wound"
                            value="Burn"
                            checked={assessment?.wound?.typeOfWound === "Burn"}
                            onChange={(e) => setTypeOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Surgical Wound"
                            name="type_of_wound"
                            value="Surgical Wound"
                            checked={assessment?.wound?.typeOfWound === "Surgical Wound"}
                            onChange={(e) => setTypeOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Fungating Lesion"
                            name="type_of_wound"
                            value="Fungating Lesion"
                            checked={assessment?.wound?.typeOfWound === "Fungating Lesion"}
                            onChange={(e) => setTypeOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Abraision"
                            name="type_of_wound"
                            value="Abraision"
                            checked={assessment?.wound?.typeOfWound === "Abraision"}
                            onChange={(e) => setTypeOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Skin Tear"
                            name="type_of_wound"
                            value="Skin Tear Wound"
                            checked={assessment?.wound?.typeOfWound === "Skin Tear Wound"}
                            onChange={(e) => setTypeOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Other"
                            name="type_of_wound"
                            value={typeOfWoundOther || ""}
                            checked={typeOfWoundOtherActive === true}
                            onChange={handleTypeOfWoundOther}
                            
                          />
                        </div>
                      </div>
                      
                      {typeOfWoundOtherActive  && assessment.wound.typeOfWound === typeOfWoundOther && (
                        <div className="col-md-8 col-lg-6 my-3">
                          <NurtifyTextArea
                            placeholder="Type here for type of wound..."
                            name="type_of_wound"
                            value={typeOfWoundOther}
                            onChange={(e) => {
                              setTypeOfWoundOther(e.target.value);
                              if (e.target.value !== null) {
                                setWound({
                                  ...assessment.wound,
                                  typeOfWound: e.target.value,
                                });
                              }
                              
                            }}
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Wound Measurement Section */}
                  <h3 className={sectionHeadingClass}>
                    Wound measurement
                  </h3>
                  
                  <div className="row mb-4">
                    <div className="col-md-4">
                      <div className={inputGroupClass}>
                        <label className={inputLabelClass}>
                          Wound Length (cm)
                        </label>
                        <NurtifyInput
                          type="text"
                          value={assessment?.wound?.lengthOfWound || ""}
                          onChange={(e) =>
                            setWound({
                              ...assessment.wound,
                              lengthOfWound: e.target.value,
                            })
                          }
                          name="wound_length"
                        />
                      </div>
                    </div>
                    
                    <div className="col-md-4">
                      <div className={inputGroupClass}>
                        <label className={inputLabelClass}>
                          Wound Width (cm)
                        </label>
                        <NurtifyInput
                          type="text"
                          value={assessment?.wound?.widthOfWound || ""}
                          onChange={(e) =>
                            setWound({
                              ...assessment.wound,
                              widthOfWound: e.target.value,
                            })
                          }
                          name="wound_width"
                        />
                      </div>
                    </div>
                    
                    <div className="col-md-4">
                      <div className={inputGroupClass}>
                        <label className={inputLabelClass}>
                          Wound Depth (cm)
                        </label>
                        <NurtifyInput
                          type="text"
                          value={assessment?.wound?.depthOfWound || ""}
                          onChange={(e) => {
                            setWound({
                              ...assessment.wound,
                              depthOfWound: e.target.value,
                            });
                          }}
                          name="wound_depth"
                        />
                      </div>
                    </div>
                  </div>

                  {/* Wound Color Section */}
                  <h3 className={sectionHeadingClass}>
                    Wound Color
                  </h3>
                  
                  <div className="row mb-4">
                    <div className="col-12">
                      <div className={radioGroupClass}>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Yellow"
                            name="wound_color"
                            value="Yellow"
                            checked={assessment?.wound?.colorOfWound === "Yellow"}
                            onChange={(e) => setColorOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Black"
                            name="wound_color"
                            value="Black"
                            checked={assessment?.wound?.colorOfWound === "Black"}
                            onChange={(e) => setColorOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Green"
                            name="wound_color"
                            value="Green"
                            checked={assessment?.wound?.colorOfWound === "Green"}
                            onChange={(e) => setColorOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Red"
                            name="wound_color"
                            value="Red"
                            checked={assessment?.wound?.colorOfWound === "Red"}
                            onChange={(e) => setColorOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Pink"
                            name="wound_color"
                            value="Pink"
                            checked={assessment?.wound?.colorOfWound === "Pink"}
                            onChange={(e) => setColorOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Other"
                            name="wound_color"
                            value={colorOfWoundOther || ""}
                            checked={colorOfWoundOtherActive === true}
                            onChange={handleColorOfWoundOther}
                          />
                        </div>
                      </div>
                      
                      {colorOfWoundOtherActive && assessment?.wound?.colorOfWound === colorOfWoundOther && (
                        <div className="col-md-8 col-lg-6 my-3">
                          <NurtifyTextArea
                            name="wound_color"
                            value={colorOfWoundOther}
                            onChange={(e) => {
                              setColorOfWoundOther(e.target.value);
                              if (e.target.value !== null) {
                              setWound({
                                ...assessment.wound,
                                colorOfWound: e.target.value,
                              });
                            }
                            }}
                            placeholder="Specify wound color..."
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Surrounding Skin Section */}
                  <h3 className={sectionHeadingClass}>
                    Surrounding Skin
                  </h3>
                  
                  <div className="row mb-4">
                    <div className="col-12">
                      <div className={radioGroupClass}>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Healthy/intact"
                            name="wound_surrounding_skin"
                            value="Healthy/intact"
                            checked={assessment?.wound?.surroundingSkinOfWound === "Healthy/intact"}
                            onChange={(e) => setSurroundingSkinOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Oedema"
                            name="wound_surrounding_skin"
                            value="Oedema"
                            checked={assessment?.wound?.surroundingSkinOfWound === "Oedema"}
                            onChange={(e) => setSurroundingSkinOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Erythematic"
                            name="wound_surrounding_skin"
                            value="Erythematic"
                            checked={assessment?.wound?.surroundingSkinOfWound === "Erythematic"}
                            onChange={(e) => setSurroundingSkinOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Macerated"
                            name="wound_surrounding_skin"
                            value="Macerated"
                            checked={assessment?.wound?.surroundingSkinOfWound === "Macerated"}
                            onChange={(e) => setSurroundingSkinOfWound(e.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Wound Exudates Section */}
                  <h3 className={sectionHeadingClass}>
                    Wound Exudates
                  </h3>
                  
                  <div className="row mb-4">
                    <div className="col-12">
                      <div className={radioGroupClass}>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="None"
                            name="wound_exudates"
                            value="No exudate"
                            checked={assessment?.wound?.exudatesOfWound === "No exudate"}
                            onChange={(e) => setExudatesOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Low"
                            name="wound_exudates"
                            value="low"
                            checked={assessment?.wound?.exudatesOfWound === "low"}
                            onChange={(e) => setExudatesOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Moderate"
                            name="wound_exudates"
                            value="moderate"
                            checked={assessment?.wound?.exudatesOfWound === "moderate"}
                            onChange={(e) => setExudatesOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="High"
                            name="wound_exudates"
                            value="high"
                            checked={assessment?.wound?.exudatesOfWound === "high"}
                            onChange={(e) => setExudatesOfWound(e.target.value)}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Describe Exudate Section */}
                  <h3 className={sectionHeadingClass}>
                    Describe Exudate
                  </h3>
                  
                  <div className="row mb-4">
                    <div className="col-12">
                      <div className={radioGroupClass}>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Not Applicable"
                            name="wound_exudate_desc"
                            value="Not Applicable"
                            checked={assessment?.wound?.descriptionOfExudateOfWound === "Not Applicable"}
                            onChange={(e) => setDescriptionOfExudateOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Serous"
                            name="wound_exudate_desc"
                            value="serous"
                            checked={assessment?.wound?.descriptionOfExudateOfWound === "serous"}
                            onChange={(e) => setDescriptionOfExudateOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Hemoserous"
                            name="wound_exudate_desc"
                            value="hemoserous"
                            checked={assessment?.wound?.descriptionOfExudateOfWound === "hemoserous"}
                            onChange={(e) => setDescriptionOfExudateOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Purulent"
                            name="wound_exudate_desc"
                            value="purulant"
                            checked={assessment?.wound?.descriptionOfExudateOfWound === "purulant"}
                            onChange={(e) => setDescriptionOfExudateOfWound(e.target.value)}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Other"
                            name="wound_exudate_desc"
                            value={descriptionOfExudateOfWoundOther || ""}
                            checked={descriptionOfExudateOfWoundOtherActive === true}
                            onChange={handleDescriptionOfExudateOfWoundOther}
                            
                          />
                        </div>
                      </div>
                      
                      {descriptionOfExudateOfWoundOtherActive && assessment?.wound?.descriptionOfExudateOfWound === descriptionOfExudateOfWoundOther && (
                        <div className="col-md-8 col-lg-6 my-3">
                          <NurtifyTextArea
                            name="wound_exudate_desc"
                            value={descriptionOfExudateOfWoundOther}
                            onChange={(e) => {
                              setDescriptionOfExudateOfWoundOther(e.target.value);
                              if (e.target.value !== null) {
                              setWound({
                                ...assessment.wound,
                                descriptionOfExudateOfWound: e.target.value,
                              });
                            }
                            }}
                            placeholder="Describe exudate..."
                          />
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Wound Odor Section */}
                  <h3 className={sectionHeadingClass}>
                    Wound Odor
                  </h3>
                  
                  <div className="row mb-4">
                    <div className="col-12">
                      <div className={radioGroupClass}>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Yes"
                            name="wound_odor"
                            value="Yes"
                            checked={assessment?.wound?.odorOfWound === "Yes"}
                            onChange={(e) => {
                              setWound({
                                ...assessment.wound,
                                odorOfWound: e.target.value,
                              });
                            }}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="No"
                            name="wound_odor"
                            value="No"
                            checked={assessment?.wound?.odorOfWound === "No"}
                            onChange={(e) => {
                              setWound({
                                ...assessment.wound,
                                odorOfWound: e.target.value,
                              });
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Pain on Wound Site Section */}
                  <h3 className={sectionHeadingClass}>
                    Pain on the wound site
                  </h3>
                  
                  <div className="row mb-4">
                    <div className="col-12">
                      <div className={radioGroupClass}>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="Yes"
                            name="wound_pain"
                            value="Yes"
                            checked={assessment?.wound?.painOfWound === "Yes"}
                            onChange={(e) => {
                              setWound({
                                ...assessment.wound,
                                painOfWound: e.target.value,
                              });
                            }}
                          />
                        </div>
                        <div className={radioOptionClass}>
                          <NurtifyRadio
                            label="No"
                            name="wound_pain"
                            value="No"
                            checked={assessment?.wound?.painOfWound === "No"}
                            onChange={(e) => {
                              setWound({
                                ...assessment.wound,
                                painOfWound: e.target.value,
                              });
                            }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Other Wound Details Section */}
                  <h3 className={sectionHeadingClass}>
                    Other Wound details
                  </h3>
                  
                  <div className="row mb-4">
                    <div className="col-md-6">
                      <div>
                        <label className={inputLabelClass}>
                          Healing/Closure
                        </label>
                        <NurtifyInput
                          type="text"
                          value={assessment?.wound?.healingOrClosure || ""}
                          onChange={(e) => {
                            setWound({
                              ...assessment.wound,
                              healingOrClosure: e.target.value,
                            });
                          }}
                          name="healing_or_closure"
                        />
                      </div>
                    </div>
                    
                    <div className="col-md-6">
                      <div>
                        <label className={inputLabelClass}>
                          Condition of the Wound Floor
                        </label>
                        <NurtifyInput
                          type="text"
                          value={assessment?.wound?.conditionOfWound || ""}
                          onChange={(e) => {
                            setWound({
                              ...assessment.wound,
                              conditionOfWound: e.target.value,
                            });
                          }}
                          name="condition_of_wound"
                        />
                      </div>
                    </div>
                    
                    <div className="col-md-6">
                      <div>
                        <label 
                          className={inputLabelClass}
                          title="(such as swab, dressing and referral to the medical team ...)"
                        >
                          Further actions taken
                        </label>
                        <NurtifyInput
                          type="text"
                          value={assessment?.wound?.otherTakenActionsOfWound || ""}
                          onChange={(e) =>
                            setWound({
                              ...assessment.wound,
                              otherTakenActionsOfWound: e.target.value,
                            })
                          }
                          name="other_taken_actions"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </form>
  );
};

export default Wound;
