/* Enhanced Modal Overlay with backdrop blur */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Enhanced Modal Content */
.modal-content {
  background: var(--color-white);
  border-radius: 16px;
  padding: 0;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  position: relative;
  box-shadow: 0px 25px 70px rgba(1, 33, 58, 0.15), 0px 10px 24px rgba(1, 33, 58, 0.1);
  animation: slideIn 0.3s ease-out;
  font-family: var(--font-primary);
}

/* Enhanced Modal Header */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
}

.modal-header h2 {
  margin: 0;
  font-size: var(--text-20);
  font-weight: 600;
  color: var(--color-dark-1);
  display: flex;
  align-items: center;
  gap: 12px;
}

.modal-header h2::before {
  content: "";
  width: 4px;
  height: 24px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 2px;
}

/* Enhanced Close Button */
.close-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-light-1);
  border-radius: 8px;
  transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  width: 40px;
  height: 40px;
}

/* Profile Picture Modal Styles */
.profile-picture-upload-container {
  display: flex;
  gap: 32px;
  margin-bottom: 24px;
}

.current-profile-picture,
.new-profile-picture {
  flex: 1;
}

.current-profile-picture h3,
.new-profile-picture h3 {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0 0 16px 0;
}

.profile-picture-display {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid var(--color-light-2);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-light-4);
  position: relative;
}

.profile-picture-display img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-picture-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-light-1);
}

.profile-picture-placeholder.hidden {
  display: none;
}

.upload-area {
  width: 120px;
  height: 120px;
  border: 2px dashed var(--color-light-2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--color-light-4);
  position: relative;
  overflow: hidden;
}

.upload-area:hover {
  border-color: var(--color-blue-1);
  background: var(--color-light-6);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: var(--color-light-1);
  text-align: center;
}

.upload-placeholder p {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
}

.upload-hint {
  font-size: 12px;
  color: var(--color-light-1);
  opacity: 0.7;
}

.preview-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.2s ease;
  border-radius: 50%;
}

.preview-container:hover .preview-overlay {
  opacity: 1;
}

.preview-overlay span {
  font-size: 12px;
  margin-top: 4px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 24px;
  border-top: 1px solid var(--color-light-2);
}

.modal-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.modal-button.cancel {
  background: var(--color-light-4);
  color: var(--color-dark-1);
}

.modal-button.cancel:hover {
  background: var(--color-light-2);
}

.modal-button.primary {
  background: var(--color-blue-1);
  color: white;
}

.modal-button.primary:hover {
  background: var(--color-blue-2);
}

.modal-button.primary:disabled {
  background: var(--color-light-2);
  color: var(--color-light-1);
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-picture-upload-container {
    flex-direction: column;
    gap: 24px;
  }
  
  .current-profile-picture,
  .new-profile-picture {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .modal-button {
    width: 100%;
  }
}

.close-button:hover {
  color: var(--color-dark-1);
  background-color: var(--color-light-3);
  transform: scale(1.05);
}

.close-button:active {
  transform: scale(0.95);
}

/* Enhanced Modal Body */
.modal-body {
  padding: 32px;
  max-height: calc(90vh - 200px);
  overflow-y: auto;
}

/* Custom scrollbar for modal body */
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: var(--color-light-3);
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: var(--color-light-1);
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: var(--color-dark-3);
}

/* Enhanced Department Selection */
.department-selection {
  margin-top: 0;
}

.form-label {
  display: block;
  margin-bottom: 16px;
  font-weight: 600;
  color: var(--color-dark-1);
  font-size: var(--text-16);
}

/* Enhanced Department List */
.department-list {
  max-height: 320px;
  overflow-y: auto;
  border: 2px solid var(--color-light-2);
  border-radius: 12px;
  padding: 8px;
  background: var(--color-light-4);
  transition: border-color 0.2s ease;
}

.department-list:focus-within {
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

/* Custom scrollbar for department list */
.department-list::-webkit-scrollbar {
  width: 6px;
}

.department-list::-webkit-scrollbar-track {
  background: var(--color-light-3);
  border-radius: 3px;
}

.department-list::-webkit-scrollbar-thumb {
  background: var(--color-purple-1);
  border-radius: 3px;
}

.department-list::-webkit-scrollbar-thumb:hover {
  background: var(--color-dark-1);
}

/* Enhanced Department Items */
.department-item {
  padding: 12px 16px;
  border-bottom: 1px solid var(--color-light-2);
  border-radius: 8px;
  margin-bottom: 4px;
  transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  background: var(--color-white);
}

.department-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.department-item:hover {
  background: var(--color-light-6);
  transform: translateX(4px);
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.1);
}

/* Enhanced Checkbox Label */
.checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  font-size: var(--text-15);
  font-weight: 500;
  color: var(--color-dark-1);
  transition: color 0.2s ease;
}

.checkbox-label:hover {
  color: var(--color-purple-1);
}

/* Enhanced Custom Checkbox */
.checkbox-label input[type="checkbox"] {
  width: 20px;
  height: 20px;
  cursor: pointer;
  appearance: none;
  border: 2px solid var(--color-light-1);
  border-radius: 4px;
  background: var(--color-white);
  transition: all 0.2s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
}

.checkbox-label input[type="checkbox"]:hover {
  border-color: var(--color-purple-1);
  transform: scale(1.05);
}

.checkbox-label input[type="checkbox"]:checked {
  background: var(--color-purple-1);
  border-color: var(--color-purple-1);
}

.checkbox-label input[type="checkbox"]:checked::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--color-white);
  font-size: 12px;
  font-weight: bold;
}

/* Enhanced Modal Footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 24px 32px;
  background: var(--color-light-4);
  border-top: 1px solid var(--color-light-2);
}

/* Enhanced Buttons using project's button system */
.cancel-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  background: var(--color-white);
  border: 2px solid var(--color-light-2);
  color: var(--color-light-1);
  font-size: var(--text-15);
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-button:hover {
  background: var(--color-light-3);
  border-color: var(--color-light-1);
  color: var(--color-dark-1);
  transform: translateY(-1px);
}

.cancel-button:active {
  transform: translateY(0);
}

.submit-button {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border: 2px solid var(--color-purple-1);
  color: var(--color-white);
  font-size: var(--text-15);
  min-width: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.submit-button:hover {
  background: linear-gradient(135deg, var(--color-blue-1) 0%, var(--color-purple-1) 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(55, 183, 195, 0.4);
}

.submit-button:active {
  transform: translateY(-1px);
}

.submit-button:disabled {
  background: var(--color-light-1);
  border-color: var(--color-light-1);
  color: var(--color-white);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.6;
}

.submit-button:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Enhanced Loading State */
.loading {
  text-align: center;
  padding: 40px 20px;
  color: var(--color-light-1);
  font-size: var(--text-16);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading::before {
  content: "";
  width: 32px;
  height: 32px;
  border: 3px solid var(--color-light-2);
  border-top: 3px solid var(--color-purple-1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    max-width: none;
    margin: 20px;
    max-height: calc(100vh - 40px);
  }
  
  .modal-header {
    padding: 20px 24px;
  }
  
  .modal-header h2 {
    font-size: var(--text-18);
  }
  
  .modal-body {
    padding: 24px;
    max-height: calc(100vh - 180px);
  }
  
  .modal-footer {
    padding: 20px 24px;
    flex-direction: column;
    gap: 12px;
  }
  
  .cancel-button,
  .submit-button {
    width: 100%;
    min-width: auto;
  }
  
  .department-list {
    max-height: 240px;
  }
}

@media (max-width: 480px) {
  .modal-content {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
    margin: 0;
  }
  
  .modal-header {
    padding: 16px 20px;
  }
  
  .modal-body {
    padding: 20px;
    max-height: calc(100vh - 140px);
  }
  
  .modal-footer {
    padding: 16px 20px;
  }
}
