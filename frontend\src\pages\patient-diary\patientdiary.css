/* Patient Diary Specific Styles */
.patclin-tab-content {
  padding: 20px;
}

.patclin-section-title {
  color: #1e293b;
  font-size: 28px;
  font-weight: 600;
  margin: 0;
}

.patclin-empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
  font-style: italic;
  background-color: #f8fafc;
  border-radius: 12px;
  border: 2px dashed #e2e8f0;
}

/* Enhanced Actions Cell Styling */
.data-table .actions-cell {
  display: flex !important;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px 16px;
  min-width: 140px;
  white-space: nowrap;
}

/* Action Button Wrapper */
.data-table .action-button-wrapper {
  position: relative;
  display: inline-block;
}

/* Enhanced Action Button Styling */
.data-table .action-button {
  background: #ffffff;
  border: 1.5px solid #e2e8f0;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  color: #475569;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: all 0.2s ease;
  font-size: 13px;
  font-weight: 500;
  min-height: 36px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-table .action-button:hover {
  background-color: #36B6C2;
  border-color: #36B6C2;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(54, 182, 194, 0.3);
}

.data-table .action-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(54, 182, 194, 0.2);
}

/* Action Icon and Label */
.data-table .action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.data-table .action-label {
  font-weight: 500;
  letter-spacing: 0.3px;
}

/* Enhanced Tooltip */
.data-table .action-button-wrapper .tooltip {
  position: absolute;
  bottom: calc(100% + 8px);
  left: 50%;
  transform: translateX(-50%);
  background-color: #1e293b;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  pointer-events: none;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.data-table .action-button-wrapper .tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: #1e293b transparent transparent transparent;
}

.data-table .action-button-wrapper:hover .tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .data-table .actions-cell {
    flex-direction: column;
    gap: 8px;
    min-width: 100px;
    padding: 8px;
  }
  
  .data-table .action-button {
    padding: 6px 10px;
    font-size: 12px;
    min-height: 32px;
  }
  
  .data-table .action-label {
    display: none;
  }
  
  .patclin-section-title {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .data-table .actions-cell {
    gap: 6px;
  }
  
  .data-table .action-button {
    padding: 4px 8px;
    min-height: 28px;
  }
  
  .data-table .action-icon {
    font-size: 12px;
  }
}

/* Ensure proper table column sizing */
.data-table th:last-child,
.data-table td:last-child {
  width: 140px;
  min-width: 140px;
  text-align: center;
}

/* Override any conflicting Bootstrap or other styles */
.data-table .actions-cell button {
  margin: 0 !important;
}

.data-table .actions-cell .action-button:focus {
  outline: 2px solid #36B6C2;
  outline-offset: 2px;
}
