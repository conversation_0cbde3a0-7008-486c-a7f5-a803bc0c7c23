import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import './clinicalcard2.css'

type ClinicalCard2Props = {
    title: string;
    description?: string;
    link?: string;
    icon: string | React.ReactNode;
};

function ClinicalCard2({ title, description, link, icon }: ClinicalCard2Props): JSX.Element {
    const CardContent = () => (
        <>
            <motion.div 
                className="tier2-box"
            >
                {typeof icon === 'string' ? (
                    <motion.img
                        src={icon}
                        alt={title}
                        className="box-icon2"
                        initial={{ scale: 0.8 }}
                        whileInView={{ scale: 1 }}
                        transition={{ duration: 0.4, delay: 0.2 }}
                    />
                ) : (
                    <motion.div
                        className="box-icon2-container"
                        initial={{ scale: 0.8 }}
                        whileInView={{ scale: 1 }}
                        transition={{ duration: 0.4, delay: 0.2 }}
                    >
                        {icon}
                    </motion.div>
                )}
            </motion.div>
            <motion.div 
                className="all-titles"
                initial={{ opacity: 0 }}
                whileInView={{ opacity: 1 }}
                transition={{ duration: 0.4, delay: 0.3 }}
            >
                <h5 className="box-title2">{title}</h5>
                {description && (
                    <motion.p 
                        className="box-sub2"
                        initial={{ opacity: 0 }}
                        whileInView={{ opacity: 1 }}
                        transition={{ duration: 0.4, delay: 0.4 }}
                    >
                        {description}
                    </motion.p>
                )}
            </motion.div>
        </>
    );

    return (
        <motion.div 
            className="main-box2"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true, amount: 0.5 }}
            whileHover={{
                scale: 1.05,
                cursor: "pointer",
            }}
        >
            {link && link !== "#" ? (
                <Link to={link} style={{ textDecoration: 'none', display: 'contents' }}>
                    <CardContent />
                </Link>
            ) : (
                <CardContent />
            )}
        </motion.div>
    );
}

export default ClinicalCard2;
