/**
 * Utility functions for handling file operations
 * 
 * This module provides utilities for:
 * - Extracting file extensions from filenames and URLs
 * - Creating proper filenames with extensions
 * - Downloading files with correct filenames
 * - Formatting file sizes
 * - Getting MIME types from extensions
 * 
 * Examples:
 * - getFileExtension("document.pdf", "https://example.com/files/document.pdf") // returns "pdf"
 * - createFileNameWithExtension("my document", "https://example.com/files/doc.docx") // returns "my document.docx"
 * - formatFileSize(1024) // returns "1 KB"
 */

/**
 * Get file extension from display name or file URL
 * @param displayName - The display name of the file
 * @param fileUrl - The file URL (fallback for extension extraction)
 * @returns The file extension in lowercase, or empty string if not found
 * 
 * @example
 * getFileExtension("document.pdf") // returns "pdf"
 * getFileExtension("document", "https://example.com/file.docx") // returns "docx"
 * getFileExtension("document.txt.backup") // returns "backup"
 */
export const getFileExtension = (displayName: string, fileUrl?: string): string => {
  // First try to get extension from display name
  const displayNameParts = displayName.split('.');
  if (displayNameParts.length > 1) {
    const extension = displayNameParts[displayNameParts.length - 1].toLowerCase();
    // Validate that it's a reasonable extension (2-4 characters, alphanumeric)
    if (/^[a-z0-9]{2,4}$/.test(extension)) {
      return extension;
    }
  }
  
  // Fallback to extracting from file URL
  if (fileUrl) {
    try {
      const url = new URL(fileUrl);
      const pathname = url.pathname;
      const urlParts = pathname.split('.');
      if (urlParts.length > 1) {
        const extension = urlParts[urlParts.length - 1].toLowerCase();
        // Validate that it's a reasonable extension
        if (/^[a-z0-9]{2,4}$/.test(extension)) {
          return extension;
        }
      }
    } catch {
      // If URL parsing fails, try simple string split
      const urlParts = fileUrl.split('.');
      if (urlParts.length > 1) {
        const extension = urlParts[urlParts.length - 1].toLowerCase();
        if (/^[a-z0-9]{2,4}$/.test(extension)) {
          return extension;
        }
      }
    }
  }
  
  // Default to no extension
  return '';
};

/**
 * Get clean filename without extension
 * @param displayName - The display name of the file
 * @returns The filename without extension
 * 
 * @example
 * getCleanFileName("document.pdf") // returns "document"
 * getCleanFileName("my file.docx") // returns "my file"
 * getCleanFileName("document") // returns "document"
 */
export const getCleanFileName = (displayName: string): string => {
  const parts = displayName.split('.');
  if (parts.length > 1) {
    // Check if the last part is a valid extension
    const lastPart = parts[parts.length - 1].toLowerCase();
    if (/^[a-z0-9]{2,4}$/.test(lastPart)) {
      return parts.slice(0, -1).join('.');
    }
  }
  return displayName;
};

/**
 * Create proper filename with extension
 * @param displayName - The display name of the file
 * @param fileUrl - The file URL (optional, for fallback extension extraction)
 * @returns The filename with proper extension
 * 
 * @example
 * createFileNameWithExtension("my document", "https://example.com/file.docx") // returns "my document.docx"
 * createFileNameWithExtension("report.pdf") // returns "report.pdf"
 * createFileNameWithExtension("file with spaces") // returns "file with spaces"
 */
export const createFileNameWithExtension = (displayName: string, fileUrl?: string): string => {
  const extension = getFileExtension(displayName, fileUrl);
  const cleanFileName = getCleanFileName(displayName);
  
  // Sanitize filename to remove invalid characters
  const sanitizedFileName = cleanFileName.replace(/[<>:"/\\|?*]/g, '_');
  
  return extension ? `${sanitizedFileName}.${extension}` : sanitizedFileName;
};

/**
 * Download a blob with proper filename
 * @param blob - The blob to download
 * @param filename - The filename to use for download
 * 
 * @example
 * downloadBlob(pdfBlob, "document.pdf")
 */
export const downloadBlob = (blob: Blob, filename: string): void => {
  const url = window.URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  window.URL.revokeObjectURL(url);
};

/**
 * Format file size in human readable format
 * @param bytes - File size in bytes
 * @returns Formatted file size string
 * 
 * @example
 * formatFileSize(1024) // returns "1 KB"
 * formatFileSize(1048576) // returns "1 MB"
 * formatFileSize(1536) // returns "1.5 KB"
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

/**
 * Get MIME type from file extension
 * @param extension - File extension (without dot)
 * @returns MIME type string
 * 
 * @example
 * getMimeTypeFromExtension("pdf") // returns "application/pdf"
 * getMimeTypeFromExtension("docx") // returns "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
 */
export const getMimeTypeFromExtension = (extension: string): string => {
  const mimeTypes: Record<string, string> = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'png': 'image/png',
    'gif': 'image/gif',
    'txt': 'text/plain',
    'csv': 'text/csv',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'xls': 'application/vnd.ms-excel',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  };
  
  return mimeTypes[extension.toLowerCase()] || 'application/octet-stream';
}; 