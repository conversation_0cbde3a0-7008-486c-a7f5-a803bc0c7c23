import React, { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { usePatientAllergiesQuery } from "@/hooks/patient.query";
import { usePatientWarnings } from "@/hooks/usePatientWarnings";
import { usePatientMedicalHistoryQuery } from "@/hooks/patient.query";
import { usePatientConcomitantMedicationsQuery } from "@/hooks/patient.query";
import { useGetSubmissionsByPatient } from "@/hooks/form.query";
import { useSymptomsQuery } from "@/hooks/symptom.query";
import { usePrescriptionsByPatientQuery } from "@/hooks/prescription.query";
import { useStudyEnrollmentsByPatientQuery, useVisitsByEnrollmentQuery } from "@/hooks/study.query";
import { useSponsorPatientsQuery } from "@/hooks/sponsorPatients.query";
import { useGetFormSubmissionQueriesByPatient, useGetQueryResponses, useCreateQueryResponse, useResolveFormSubmissionQuery } from "@/hooks/form.query";
import { useSponsorPatientConsentQuery } from "@/hooks/consent.query";
import { AlertTriangle, Pill, FileText, Calendar, Activity, ArrowLeft, Eye, MessageSquare, Send, CheckCircle, HelpCircle, Shield } from "lucide-react";
import FormSubmissionPreview from "@/pages/patient-form/FormSubmissionPreview";
import { getFormByUuid, getSubmissionByUuid } from "@/services/api/form.service";
import "./patient-details.css";

const PatientDetailsPage: React.FC = () => {
  const { patientUuid } = useParams<{ patientUuid: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'allergies' | 'warnings' | 'medical-history' | 'medications' | 'forms' | 'diary' | 'prescriptions' | 'visits' | 'queries' | 'consents'>('allergies');
  const [selectedSubmission, setSelectedSubmission] = useState<any>(null);
  const [showSubmissionPreview, setShowSubmissionPreview] = useState(false);
  const [formNames, setFormNames] = useState<Record<string, string>>({});
  const [isLoadingFormNames, setIsLoadingFormNames] = useState(false);
  const [selectedQuery, setSelectedQuery] = useState<any>(null);
  const [queryActiveTab, setQueryActiveTab] = useState<'active' | 'resolved'>('active');
  const [newResponse, setNewResponse] = useState('');
  const [isClarification, setIsClarification] = useState(false);
  const [resolutionNotes, setResolutionNotes] = useState('');
  const [showResolveModal, setShowResolveModal] = useState(false);
  const [previewSubmission, setPreviewSubmission] = useState<any>(null);

  // Fetch patient data
  const { data: patientsResponse } = useSponsorPatientsQuery();
  const patients = patientsResponse?.results || [];
  
  // Find the patient by actual patient_uuid
  const patient = patients.find((p: any) => p.patient_uuid === patientUuid) || null;
  
  // Use the actual patient UUID from the API response
  const actualPatientUuid = patient?.patient_uuid || "";

  // Fetch patient details using the actual patient UUID
  const { data: allergies, isLoading: isLoadingAllergies } = usePatientAllergiesQuery(patient?.nhs_number || "");
  const { warnings, isLoading: isLoadingWarnings } = usePatientWarnings(actualPatientUuid);
  const { data: medicalHistory, isLoading: isLoadingMedicalHistory } = usePatientMedicalHistoryQuery(actualPatientUuid);
  const { data: medications, isLoading: isLoadingMedications } = usePatientConcomitantMedicationsQuery(actualPatientUuid);
  const { data: formSubmissions, isLoading: isLoadingFormSubmissions } = useGetSubmissionsByPatient(actualPatientUuid);
  const { data: symptoms, isLoading: isLoadingSymptoms } = useSymptomsQuery(actualPatientUuid);
  const { data: prescriptions, isLoading: isLoadingPrescriptions } = usePrescriptionsByPatientQuery(actualPatientUuid);
  const { data: enrollments, isLoading: isLoadingEnrollments } = useStudyEnrollmentsByPatientQuery(actualPatientUuid);
  
  // Get visits for the first enrollment
  const firstEnrollment = enrollments?.[0];
  const { data: visits, isLoading: isLoadingVisits } = useVisitsByEnrollmentQuery(
    firstEnrollment?.uuid || "",
    { enabled: !!firstEnrollment?.uuid }
  );

  // Fetch form queries for the patient
  const { data: queriesData, isLoading: isLoadingQueries } = useGetFormSubmissionQueriesByPatient(actualPatientUuid, {
    is_resolved: queryActiveTab === 'resolved'
  });
  const queries = ((queriesData as any)?.results || []);

  // Get responses for the selected query
  const { data: responsesData, isLoading: isLoadingResponses } = useGetQueryResponses(
    selectedQuery ? { query_uuid: selectedQuery.uuid } : undefined
  );
  const responses = ((responsesData as any)?.results || []);

  // Fetch patient consent data
  const { data: consentData, isLoading: isLoadingConsents } = useSponsorPatientConsentQuery(actualPatientUuid);

  // Mutations
  const { mutate: createResponse, isPending: isSubmitting } = useCreateQueryResponse();
  const { mutate: resolveQuery, isPending: isResolving } = useResolveFormSubmissionQuery(selectedQuery?.uuid || '');

  // Fetch form names for submissions that have form as UUID string
  useEffect(() => {
    if (formSubmissions) {
      const formUuids = formSubmissions
        .filter((submission: any) => typeof submission.form === 'string')
        .map((submission: any) => submission.form);

      // Only fetch if we have form UUIDs that aren't already cached
      const uncachedUuids = formUuids.filter(uuid => !formNames[uuid]);
      
      if (uncachedUuids.length > 0) {
        setIsLoadingFormNames(true);
        
        // Fetch form details for each UUID using the proper API service
        const fetchFormNames = async () => {
          for (const formUuid of uncachedUuids) {
            try {
              const formData = await getFormByUuid(formUuid);
              setFormNames(prev => ({
                ...prev,
                [formUuid]: formData.name || 'Unknown Form'
              }));
            } catch (error) {
              console.error(`Error fetching form ${formUuid}:`, error);
              setFormNames(prev => ({
                ...prev,
                [formUuid]: 'Unknown Form'
              }));
            }
          }
          setIsLoadingFormNames(false);
        };

        fetchFormNames();
      }
    }
  }, [formSubmissions, formNames]);

  const tabs = [
    { id: 'allergies', label: 'Allergies', icon: <AlertTriangle size={16} /> },
    { id: 'warnings', label: 'Active Warnings', icon: <AlertTriangle size={16} /> },
    { id: 'medical-history', label: 'Past Medical History', icon: <FileText size={16} /> },
    { id: 'medications', label: 'Concomitant Medication', icon: <Pill size={16} /> },
    { id: 'forms', label: 'Submitted Forms', icon: <FileText size={16} /> },
    { id: 'diary', label: 'Patient Diary', icon: <Activity size={16} /> },
    { id: 'prescriptions', label: 'Prescriptions', icon: <Pill size={16} /> },
    { id: 'visits', label: 'Study Visits', icon: <Calendar size={16} /> },
    { id: 'queries', label: 'Form Queries', icon: <MessageSquare size={16} /> },
    { id: 'consents', label: 'Patient Consents', icon: <Shield size={16} /> },
  ];

  const handleViewSubmission = async (submission: any) => {
    setSelectedSubmission(submission);
    setShowSubmissionPreview(true);
  };

  const handleCloseSubmissionPreview = () => {
    setShowSubmissionPreview(false);
    setSelectedSubmission(null);
  };

  const handlePreviewForm = async (formSubmissionUuid: string) => {
    try {
      const response = await getSubmissionByUuid(formSubmissionUuid);
      setPreviewSubmission(response);
      setShowSubmissionPreview(true);
    } catch (error) {
      console.error('Error fetching form submission:', error);
    }
  };

  const handleClosePreview = () => {
    setPreviewSubmission(null);
    setShowSubmissionPreview(false);
  };

  const handleSubmitResponse = () => {
    if (!newResponse.trim() || !selectedQuery) return;

    createResponse({
      query: selectedQuery.uuid,
      message: newResponse,
      is_clarification: isClarification
    }, {
      onSuccess: () => {
        setNewResponse('');
        setIsClarification(false);
        // The responses will be automatically refetched due to query invalidation
      }
    });
  };

  const handleResolveQuery = () => {
    if (!selectedQuery) return;

    resolveQuery({
      resolution_notes: resolutionNotes
    }, {
      onSuccess: () => {
        setShowResolveModal(false);
        setResolutionNotes('');
        setSelectedQuery(null);
        // The queries will be automatically refetched due to query invalidation
      }
    });
  };

  const renderResolveModal = () => {
    if (!showResolveModal) return null;

    return (
      <div className="modal-overlay">
        <div className="modal-content">
          <h3>Resolve Query</h3>
          <p>Please provide resolution notes for this query:</p>
          <textarea
            value={resolutionNotes}
            onChange={(e) => setResolutionNotes(e.target.value)}
            placeholder="Enter resolution notes..."
            rows={4}
          />
          <div className="modal-actions">
            <button 
              className="cancel-btn"
              onClick={() => setShowResolveModal(false)}
            >
              Cancel
            </button>
            <button 
              className="resolve-btn"
              onClick={handleResolveQuery}
              disabled={isResolving || !resolutionNotes.trim()}
            >
              {isResolving ? 'Resolving...' : 'Resolve Query'}
            </button>
          </div>
        </div>
      </div>
    );
  };

  const getFormName = (submission: any) => {
    if (typeof submission.form === 'string') {
      // Check if we have the form name cached
      const cachedName = formNames[submission.form];
      if (cachedName) {
        return cachedName;
      }
      // If not cached yet, show loading state
      return 'Loading form name...';
    } else if (submission.form?.name) {
      return submission.form.name;
    } else if (submission.form_name) {
      return submission.form_name;
    } else if (submission.submission?.formDetails?.name) {
      return submission.submission.formDetails.name;
    }
    return 'Unknown Form';
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'allergies':
        return (
          <div className="tab-content">
            <h3>Patient Allergies</h3>
            {isLoadingAllergies ? (
              <div className="loading">Loading allergies...</div>
            ) : allergies && allergies.length > 0 ? (
              <div className="allergies-list">
                {allergies.map((allergy, index) => (
                  <div key={allergy.uuid || index} className="allergy-item">
                    <span className="allergy-name">{allergy.name}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-data">No allergies recorded</div>
            )}
          </div>
        );

      case 'warnings':
        return (
          <div className="tab-content">
            <h3>Active Warnings</h3>
            {isLoadingWarnings ? (
              <div className="loading">Loading warnings...</div>
            ) : warnings && warnings.length > 0 ? (
              <div className="warnings-list">
                {warnings.filter((warning: any) => warning.is_active).map((warning: any) => (
                  <div key={warning.uuid} className="warning-item">
                    <div className="warning-header">
                      <span className="warning-name">{warning.name}</span>
                      <span className={`severity-badge severity-${warning.severity}`}>
                        {warning.severity_display}
                      </span>
                    </div>
                    <p className="warning-description">{warning.description}</p>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-data">No active warnings</div>
            )}
          </div>
        );

      case 'medical-history':
        return (
          <div className="tab-content">
            <h3>Past Medical History</h3>
            {isLoadingMedicalHistory ? (
              <div className="loading">Loading medical history...</div>
            ) : medicalHistory?.results && medicalHistory.results.length > 0 ? (
              <div className="medical-history-list">
                {medicalHistory.results.map((history) => (
                  <div key={history.uuid} className="history-item">
                    {history.conditions.map((condition) => (
                      <div key={condition.uuid} className="condition-item">
                        <div className="condition-header">
                          <span className="condition-name">{condition.condition}</span>
                          <span className={`status-badge ${condition.status}`}>
                            {(condition.status as string) === 'current' ? 'Current' : 
                             (condition.status as string) === 'resolved' ? 'Resolved' : 
                             condition.status}
                          </span>
                        </div>
                        <div className="condition-dates">
                          <span>Start: {new Date(condition.start_date).toLocaleDateString()}</span>
                          {condition.resolved_date && (
                            <span>Resolved: {new Date(condition.resolved_date).toLocaleDateString()}</span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-data">No medical history recorded</div>
            )}
          </div>
        );

      case 'medications':
        return (
          <div className="tab-content">
            <h3>Concomitant Medications</h3>
            {isLoadingMedications ? (
              <div className="loading">Loading medications...</div>
            ) : medications?.results && medications.results.length > 0 ? (
              <div className="medications-list">
                {medications.results.map((medication) => (
                  <div key={medication.uuid} className="medication-item">
                    <div className="medication-header">
                      <span className="medication-name">{medication.medication}</span>
                      <span className={`status-badge ${medication.is_continuing ? 'continuing' : 'discontinued'}`}>
                        {medication.is_continuing ? 'Continuing' : 'Discontinued'}
                      </span>
                    </div>
                    <div className="medication-details">
                      <span>Indication: {medication.indication}</span>
                      <span>Dose: {medication.dose} {medication.dose_units_display}</span>
                      <span>Schedule: {medication.schedule_display}</span>
                      <span>Start: {new Date(medication.start_date).toLocaleDateString()}</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-data">No medications recorded</div>
            )}
          </div>
        );

      case 'forms':
        return (
          <div className="tab-content">
            <h3>Submitted Forms</h3>
            {isLoadingFormSubmissions ? (
              <div className="loading">Loading form submissions...</div>
            ) : formSubmissions && formSubmissions.length > 0 ? (
              <div className="forms-list">
                {isLoadingFormNames && (
                  <div className="loading-note">Loading form names...</div>
                )}
                {formSubmissions.map((submission: any) => (
                  <div key={submission.uuid} className="form-item">
                    <div className="form-header">
                      <span className="form-name">
                        {getFormName(submission)}
                      </span>
                      <div className="form-actions">
                        <button 
                          className="view-submission-btn"
                          onClick={() => handleViewSubmission(submission)}
                          title="View Form Submission"
                        >
                          <Eye size={16} />
                          View
                        </button>
                        <span className={`status-badge ${submission.is_completed ? 'completed' : 'draft'}`}>
                          {submission.is_completed ? 'Completed' : 'Draft'}
                        </span>
                      </div>
                    </div>
                    <div className="form-details">
                      <span>Submitted: {submission.created_at ? new Date(submission.created_at).toLocaleDateString() : 'N/A'}</span>
                      {submission.updated_at && submission.updated_at !== submission.created_at && (
                        <span>Updated: {new Date(submission.updated_at).toLocaleDateString()}</span>
                      )}
                      {submission.final_submission && (
                        <span className="final-submission-badge">Final Submission</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-data">No form submissions found</div>
            )}
          </div>
        );

      case 'diary':
        return (
          <div className="tab-content">
            <h3>Patient Diary (Symptoms)</h3>
            {isLoadingSymptoms ? (
              <div className="loading">Loading symptoms...</div>
            ) : symptoms && symptoms.length > 0 ? (
              <div className="symptoms-list">
                {symptoms.map((symptom: any) => (
                  <div key={symptom.uuid} className="symptom-item">
                    <div className="symptom-header">
                      <span className="symptom-name">{symptom.name || symptom.symptom_name}</span>
                      <span className={`status-badge ${symptom.status}`}>
                        {symptom.status_display || symptom.status}
                      </span>
                    </div>
                    <div className="symptom-details">
                      <span>Severity: {symptom.severity_display || symptom.severity}</span>
                      <span>Start Date: {new Date(symptom.start_date).toLocaleDateString()}</span>
                      {symptom.description && <span>Description: {symptom.description}</span>}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-data">No symptoms recorded</div>
            )}
          </div>
        );

      case 'prescriptions':
        return (
          <div className="tab-content">
            <h3>Prescriptions</h3>
            {isLoadingPrescriptions ? (
              <div className="loading">Loading prescriptions...</div>
            ) : prescriptions && prescriptions.length > 0 ? (
              <div className="prescriptions-list">
                {prescriptions.map((prescription) => (
                  <div key={prescription.uuid} className="prescription-item">
                    <div className="prescription-header">
                      <span className="prescription-name">{prescription.drug_name}</span>
                      <span className="prescription-status">{prescription.status}</span>
                    </div>
                    <div className="prescription-details">
                      <span>Dose: {prescription.dose} {prescription.unit}</span>
                      <span>Route: {prescription.route}</span>
                      <span>Frequency: {prescription.frequency}</span>
                      <span>Prescribed: {prescription.prescribed_at ? new Date(prescription.prescribed_at).toLocaleDateString() : 'N/A'}</span>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-data">No prescriptions found</div>
            )}
          </div>
        );

      case 'visits':
        return (
          <div className="tab-content">
            <h3>Study Visits</h3>
            {isLoadingEnrollments || isLoadingVisits ? (
              <div className="loading">Loading visits...</div>
            ) : visits && visits.length > 0 ? (
              <div className="visits-list">
                {visits.map((visit: any) => (
                  <div key={visit.uuid} className="visit-item">
                    <div className="visit-header">
                      <span className="visit-name">{visit.name}</span>
                      <span className={`status-badge ${visit.visit_status.toLowerCase()}`}>
                        {visit.visit_status}
                      </span>
                    </div>
                    <div className="visit-details">
                      <span>Date: {new Date(visit.date).toLocaleDateString()}</span>
                      <span>Visit Number: {visit.number}</span>
                      {visit.comments && <span>Comments: {visit.comments}</span>}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-data">No visits scheduled</div>
            )}
          </div>
        );

      case 'queries':
        return (
          <div className="tab-content">
            <h3>Form Queries</h3>
            
            {/* Query Tabs */}
            <div className="query-tabs">
              <button 
                className={`query-tab ${queryActiveTab === 'active' ? 'active' : ''}`}
                onClick={() => setQueryActiveTab('active')}
              >
                Active Queries
              </button>
              <button 
                className={`query-tab ${queryActiveTab === 'resolved' ? 'active' : ''}`}
                onClick={() => setQueryActiveTab('resolved')}
              >
                Resolved Queries
              </button>
            </div>

            {isLoadingQueries ? (
              <div className="loading">Loading queries...</div>
            ) : queries && queries.length > 0 ? (
              <div className="queries-table-container">
                <table className="queries-table">
                  <thead>
                    <tr>
                      <th>Question #</th>
                      <th>Form Name</th>
                      <th>Description</th>
                      <th>Priority</th>
                      <th>Created Date</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {queries.map((query: any) => (
                      <tr key={query.uuid} className="query-row">
                        <td>{query.question_number}</td>
                        <td>{query.form_submission?.form_name || 'Unknown Form'}</td>
                        <td>{query.description}</td>
                        <td>
                          <span className={`priority-badge priority-${query.priority}`}>
                            {query.priority}
                          </span>
                        </td>
                        <td>{new Date(query.created_at).toLocaleDateString()}</td>
                        <td>
                          <span className={`status-badge ${query.is_resolved ? 'resolved' : 'pending'}`}>
                            {query.is_resolved ? 'Resolved' : 'Pending'}
                          </span>
                        </td>
                        <td>
                          <div className="query-actions">
                            <button 
                              className="view-query-btn"
                              onClick={() => setSelectedQuery(query)}
                            >
                              <Eye size={16} />
                              View Details
                            </button>
                            {query.form_submission?.uuid && (
                              <button 
                                className="preview-form-btn"
                                onClick={() => handlePreviewForm(query.form_submission.uuid)}
                              >
                                <FileText size={16} />
                                Preview Form
                              </button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="no-data">
                {queryActiveTab === 'active' 
                  ? 'No active queries found for this patient'
                  : 'No resolved queries found for this patient'
                }
              </div>
            )}

            {/* Query Detail View */}
            {selectedQuery && (
              <div className="query-detail-view">
                <div className="query-detail-header">
                  <button 
                    className="back-button"
                    onClick={() => setSelectedQuery(null)}
                  >
                    <ArrowLeft size={16} />
                    Back to Queries
                  </button>
                  <h4>Query Details</h4>
                </div>
                
                <div className="query-card">
                  <div className="query-header">
                    <div className="query-info">
                      <h5 className="query-title">Question #{selectedQuery.question_number}</h5>
                      <p className="query-description">{selectedQuery.description}</p>
                    </div>
                    <div className="query-meta">
                      <span className={`priority-badge priority-${selectedQuery.priority}`}>
                        {selectedQuery.priority} Priority
                      </span>
                      <span className={`status-badge ${selectedQuery.is_resolved ? 'resolved' : 'pending'}`}>
                        {selectedQuery.is_resolved ? 'Resolved' : 'Pending'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="query-details">
                    <div className="detail-item">
                      <span className="label">Form:</span>
                      <span className="value">{selectedQuery.form_submission?.form_name || 'Unknown Form'}</span>
                    </div>
                    <div className="detail-item">
                      <span className="label">Created by:</span>
                      <span className="value">
                        {selectedQuery.created_by?.first_name} {selectedQuery.created_by?.last_name}
                      </span>
                    </div>
                    <div className="detail-item">
                      <span className="label">Created:</span>
                      <span className="value">{new Date(selectedQuery.created_at).toLocaleDateString()}</span>
                    </div>
                    {selectedQuery.resolved_at && (
                      <div className="detail-item">
                        <span className="label">Resolved:</span>
                        <span className="value">{new Date(selectedQuery.resolved_at).toLocaleDateString()}</span>
                      </div>
                    )}
                    {selectedQuery.resolution_notes && (
                      <div className="detail-item">
                        <span className="label">Resolution Notes:</span>
                        <span className="value">{selectedQuery.resolution_notes}</span>
                      </div>
                    )}
                  </div>

                  {/* Responses Section */}
                  <div className="responses-section">
                    <h5>Responses</h5>
                    {isLoadingResponses ? (
                      <div className="loading">Loading responses...</div>
                    ) : responses && responses.length > 0 ? (
                      responses.map((response: any, index: number) => (
                        <div key={response.uuid || index} className="response-item">
                          <div className="response-header">
                            <div className="response-meta">
                              <span className="responder-name">
                                {response.responder?.first_name} {response.responder?.last_name}
                              </span>
                              {response.is_clarification && (
                                <span className="clarification-badge" title="This is a clarification">
                                  <HelpCircle size={14} />
                                  Clarification
                                </span>
                              )}
                            </div>
                            <span className="response-date">
                              {new Date(response.created_at).toLocaleDateString()}
                            </span>
                          </div>
                          <p className="response-message">{response.message}</p>
                        </div>
                      ))
                    ) : (
                      <div className="no-data">No responses yet</div>
                    )}
                  </div>

                  {/* Response Form - Only show if query is not resolved */}
                  {!selectedQuery.is_resolved && (
                    <div className="response-form">
                      <div className="response-input-container">
                        <textarea
                          value={newResponse}
                          onChange={(e) => setNewResponse(e.target.value)}
                          placeholder="Type your response..."
                          rows={3}
                        />
                        <div className="response-options">
                          <label className="clarification-toggle">
                            <input
                              type="checkbox"
                              checked={isClarification}
                              onChange={(e) => setIsClarification(e.target.checked)}
                            />
                            <span className="toggle-label">
                              This is a clarification
                            </span>
                          </label>
                        </div>
                      </div>
                      <button
                        className="submit-response-btn"
                        onClick={handleSubmitResponse}
                        disabled={isSubmitting || !newResponse.trim()}
                      >
                        <Send size={16} />
                        {isSubmitting ? 'Sending...' : 'Send Response'}
                      </button>
                    </div>
                  )}

                  {/* Resolve Button - Only show if query is not resolved */}
                  {!selectedQuery.is_resolved && (
                    <div className="resolve-section">
                      <button
                        className="resolve-query-btn"
                        onClick={() => setShowResolveModal(true)}
                        disabled={isResolving}
                      >
                        <CheckCircle size={16} />
                        {isResolving ? 'Resolving...' : 'Mark as Resolved'}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        );

      case 'consents':
        return (
          <div className="tab-content">
            <h3>Patient Consents</h3>
            {isLoadingConsents ? (
              <div className="loading">Loading consents...</div>
            ) : consentData && consentData.studies && consentData.studies.length > 0 ? (
              <div className="consents-list">
                {consentData.studies.map((study: any) => (
                  <div key={study.study_uuid} className="study-consent-section">
                    <div className="study-header">
                      <h4 className="study-name">{study.study_name}</h4>
                      <span className="study-iras">IRAS: {study.iras}</span>
                    </div>
                    
                    {study.consent_forms && study.consent_forms.length > 0 ? (
                      <div className="consent-forms-list">
                        {study.consent_forms.map((consentForm: any) => (
                          <div key={consentForm.consent_form_uuid} className="consent-form-item">
                            <div className="consent-form-header">
                              <div className="consent-form-info">
                                <h5 className="consent-form-name">{consentForm.consent_form_name}</h5>
                                <span className="consent-form-version">Version: {consentForm.consent_form_version}</span>
                                {consentForm.consent_form_description && (
                                  <p className="consent-form-description">{consentForm.consent_form_description}</p>
                                )}
                              </div>
                              <div className="consent-status-section">
                                <span className={`consent-status-badge ${consentForm.consent_status?.toLowerCase().replace(' ', '-')}`}>
                                  {consentForm.consent_status || 'Not Consented'}
                                </span>
                                {consentForm.signed_at && (
                                  <span className="signed-date">
                                    Signed: {new Date(consentForm.signed_at).toLocaleDateString()}
                                  </span>
                                )}
                              </div>
                            </div>
                            
                            {consentForm.questions && consentForm.questions.length > 0 && (
                              <div className="consent-questions">
                                <h6>Consent Questions:</h6>
                                <div className="questions-list">
                                  {consentForm.questions.map((question: any) => (
                                    <div key={question.question_uuid} className="question-item">
                                      <div className="question-text">
                                        <span className="question-number">{question.sequence}.</span>
                                        {question.question_text}
                                        {question.is_required && <span className="required-indicator">*</span>}
                                      </div>
                                      <div className="question-answer">
                                        <span className={`answer-badge ${question.patient_answer ? 'agreed' : 'disagreed'}`}>
                                          {question.patient_answer ? 'Yes' : 'No'}
                                        </span>
                                        {question.answered_at && (
                                          <span className="answered-date">
                                            {new Date(question.answered_at).toLocaleDateString()}
                                          </span>
                                        )}
                                      </div>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="no-consent-forms">
                        <p>No consent forms found for this study.</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="no-data">No consent data available for this patient</div>
            )}
          </div>
        );

      default:
        return <div className="tab-content">Select a tab to view patient information</div>;
    }
  };

  if (!patient) {
    return (
      <div className="patient-details-page">
        <div className="patient-details-header">
          <button className="back-button" onClick={() => navigate('/sponsor/study-patients')}>
            <ArrowLeft size={20} />
            Back to Study Patients
          </button>
          <h1>Patient Not Found</h1>
        </div>
      </div>
    );
  }

  // Show loading state while searching for actual patient UUID
  if (patient && !actualPatientUuid) {
    return (
      <div className="patient-details-page">
        <div className="patient-details-header">
          <button className="back-button" onClick={() => navigate('/sponsor/study-patients')}>
            <ArrowLeft size={20} />
            Back to Study Patients
          </button>
          <h1>Patient Details - {patient.patient_initials} ({patient.patient_study_id})</h1>
        </div>
        <div className="patient-info-card">
          <h2>Patient Information</h2>
          <div className="info-grid">
            <div className="info-item">
              <span className="label">Study ID:</span>
              <span className="value">{patient.patient_study_id}</span>
            </div>
            <div className="info-item">
              <span className="label">Initials:</span>
              <span className="value">{patient.patient_initials}</span>
            </div>
            <div className="info-item">
              <span className="label">Year of Birth:</span>
              <span className="value">{patient.year_of_birth}</span>
            </div>
            <div className="info-item">
              <span className="label">Site:</span>
              <span className="value">{patient.site_name}</span>
            </div>
            <div className="info-item">
              <span className="label">Hospital:</span>
              <span className="value">{patient.hospital_name}</span>
            </div>
            <div className="info-item">
              <span className="label">Study:</span>
              <span className="value">{patient.study_name}</span>
            </div>
            <div className="info-item">
              <span className="label">Enrollment Status:</span>
              <span className="value">{patient.enrollment_status}</span>
            </div>
            <div className="info-item">
              <span className="label">Enrollment Date:</span>
              <span className="value">{new Date(patient.enrollment_date).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
        <div className="loading-message">
          <p>Loading patient details...</p>
          <p className="note">Note: Patient UUID not found in the system. Some data may not be available.</p>
        </div>
      </div>
    );
  }

  if (showSubmissionPreview && (selectedSubmission || previewSubmission)) {
    return (
      <FormSubmissionPreview
        submission={selectedSubmission || previewSubmission}
        onClose={selectedSubmission ? handleCloseSubmissionPreview : handleClosePreview}
      />
    );
  }

  return (
    <div className="patient-details-page">
      {/* Header */}
      <div className="patient-details-header">
        <button className="back-button" onClick={() => navigate('/sponsor/study-patients')}>
          <ArrowLeft size={20} />
          Back to Study Patients
        </button>
        <h1>Patient Details - {patient.patient_initials} ({patient.patient_study_id})</h1>
      </div>

      {/* Patient Info Card */}
      <div className="patient-info-card">
        <h2>Patient Information</h2>
        <div className="info-grid">
          <div className="info-item">
            <span className="label">Study ID:</span>
            <span className="value">{patient.patient_study_id}</span>
          </div>
          <div className="info-item">
            <span className="label">Initials:</span>
            <span className="value">{patient.patient_initials}</span>
          </div>
          <div className="info-item">
            <span className="label">Year of Birth:</span>
            <span className="value">{patient.year_of_birth}</span>
          </div>
          <div className="info-item">
            <span className="label">Site:</span>
            <span className="value">{patient.site_name}</span>
          </div>
          <div className="info-item">
            <span className="label">Hospital:</span>
            <span className="value">{patient.hospital_name}</span>
          </div>
          <div className="info-item">
            <span className="label">Study:</span>
            <span className="value">{patient.study_name}</span>
          </div>
          <div className="info-item">
            <span className="label">Enrollment Status:</span>
            <span className="value">{patient.enrollment_status}</span>
          </div>
          <div className="info-item">
            <span className="label">Enrollment Date:</span>
            <span className="value">{new Date(patient.enrollment_date).toLocaleDateString()}</span>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="patient-tabs">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
            onClick={() => setActiveTab(tab.id as any)}
          >
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      <div className="tab-content-container">
        {renderTabContent()}
      </div>

      {renderResolveModal()}
    </div>
  );
};

export default PatientDetailsPage; 