import axios, { AxiosError } from 'axios';
import { keycloak } from '../keycloack';

// Create axios instance with default config
const baseURL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';
console.log('API Base URL:', baseURL);

const api = axios.create({
  baseURL,
  timeout: 35000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(

  (config: any) => {
    if (keycloak.authenticated) {
      config.headers = { ...config.headers, Authorization: `Bearer ${keycloak.token}` };
    }
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

export default api;
