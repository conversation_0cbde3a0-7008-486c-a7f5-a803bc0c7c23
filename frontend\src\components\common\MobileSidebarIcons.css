.mobile-sidebar-icons {
  display: none;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 12px 20px;
  box-shadow: 0 -4px 12px rgba(0, 0, 0, 0.08);
  z-index: 1000;
}

.mobile-nav-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0;
}

.mobile-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mobile-nav-item.active {
  color: #37b7c3;
  background-color: #D7F1F3;
}

.mobile-nav-item:hover {
  background-color: #D7F1F3;
}

.mobile-nav-icon {
  color: #4f547b;
  transition: color 0.2s ease;
}

.mobile-nav-item.active .mobile-nav-icon {
  color: #37b7c3;
}

@media (max-width: 991px) {
  .mobile-sidebar-icons {
    display: block;
  }
  
  .sidebar-dsbh {
    display: none;
  }
} 