import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import ClinicalCard from "@/components/ClinicalCard";
import ClinicalCard2 from "@/components/ClinicalCard2";
import PatientSearchModal from "@/components/modal/PatientSearchModal";
import AddPatientFormModal from "@/components/modal/AddPatientFormModal";
import { useCurrentUserQuery } from "@/hooks/user.query";
import "../clinical-documentation/clinical.css";

type CardProps = {
  title: string;
  description: string;
  link: string;
  icon: string;
};

const PatientsAndTemplates: React.FC = () => {
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const [isAddPatientModalOpen, setIsAddPatientModalOpen] = useState(false);
  const [, setIsMobile] = useState<boolean>(window.innerWidth < 768);
  const [activeTab, setActiveTab] = useState<string>("patients");
  const { data: currentUser, isLoading } = useCurrentUserQuery();

  const handleResize = () => {
    setIsMobile(window.innerWidth < 768);
  };

  useEffect(() => {
    window.addEventListener("resize", handleResize);
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const patientCards: CardProps[] = [
    {
      title: "Search/Add Patient",
      description: "Search and add new patient",
      link: "#",
      icon: "/assets/img/clinical-documentation/add-patients.png",
    },
    {
      title: "Patient Visits",
      description: "Access your patients' visit schedule",
      link: "/patients",
      icon: "/assets/img/clinical-documentation/list-patients.png",
    },
  ];

  const templateCards: CardProps[] = [
    {
      title: "Create Worksheet",
      description: "Create a new patient worksheet",
      link: "/survey-forms",
      icon: "/assets/img/clinical-documentation/create-form.png",
    },
    {
      title: "Nursing Care Plan",
      description: "Browse care plan templates",
      link: "/forms",
      icon: "/assets/img/clinical-documentation/nurtify-templates.png",
    },
    {
      title: "My Worksheets",
      description: "Review your created worksheets",
      link: "/my-templates",
      icon: "/assets/img/clinical-documentation/my-templates.png",
    },
    {
      title: "Ask for Assistance",
      description: "Get help with worksheet creation",
      link: "#",
      icon: "/assets/img/clinical-documentation/ask-assistance.png",
    }
  ];

  return (
    <main className="clinical-dashboard" style={{ height: "100vh" }}>
      {/* Header Section */}
      <div className="dashboard-header">
        <div className="welcome-section">
          <motion.div
            className="welcome-text-container"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="welcome-text">
              <span className="greeting">Hello,</span>
              <span className="username">
                {isLoading ? "User" : `${currentUser?.first_name} ${currentUser?.last_name}`}
              </span>
              <span className="wave-emoji" role="img" aria-label="waving hand">👋</span>
            </h1>
            <p className="welcome-subtitle">
              Manage your patients and templates efficiently
            </p>
          </motion.div>

          <motion.div
            className="dashboard-tabs"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <button
              className={`tab-button ${activeTab === 'patients' ? 'active' : ''}`}
              onClick={() => setActiveTab('patients')}
            >
              Patients
            </button>
            <button
              className={`tab-button ${activeTab === 'templates' ? 'active' : ''}`}
              onClick={() => setActiveTab('templates')}
            >
              Templates
            </button>
          </motion.div>
        </div>
      </div>

      {/* Quick Actions Section */}
      {activeTab === 'patients' &&
        <motion.div
          className="quick-actions-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h2 className="section-title">Patient Quick Actions</h2>
          <div className="cards-container">
            {patientCards.map((card, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index + 0.5 }}
              >
                <ClinicalCard
                  {...card}
                  isModalOpen={isSearchModalOpen}
                  setIsModalOpen={setIsSearchModalOpen}
                />
              </motion.div>
            ))}
          </div>
        </motion.div>
      }

      {activeTab === 'templates' &&
        <motion.div
          className="quick-actions-section"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <h2 className="section-title">Template Quick Actions</h2>
          <div className="cards-container">
            {/* First card uses ClinicalCard, the rest use ClinicalCard2 for variety */}
            {templateCards.map((card, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 * index + 0.5 }}
              >
                {index === 0 ?
                  <ClinicalCard
                    {...card}
                    isModalOpen={isSearchModalOpen}
                    setIsModalOpen={setIsSearchModalOpen}
                  /> :
                  <ClinicalCard2 {...card} />
                }
              </motion.div>
            ))}
          </div>
        </motion.div>
      }

      {/* Modals */}
      <PatientSearchModal
        isOpen={isSearchModalOpen}
        onClose={() => setIsSearchModalOpen(false)}
        onOpenAddPatientModal={() => {
          setIsSearchModalOpen(false);
          setIsAddPatientModalOpen(true);
        }}
      />

      <AddPatientFormModal
        isOpen={isAddPatientModalOpen}
        setIsModalOpen={setIsAddPatientModalOpen}
        setSwitchModal={() => { }}
        switchModal={false}
      />
    </main>
  );
};

export default PatientsAndTemplates;
