/* Main container */
.medications-page {
  padding: 24px;
  background-color: #f8f9fa;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Page Content */
.page-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.page-header {
  padding: 24px 24px 0 24px;
}

.page-title {
  margin: 0 0 24px 0;
  font-size: 28px;
  font-weight: 600;
  color: #1a202c;
}

/* Controls Section */
.controls-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px 24px 24px;
  gap: 16px;
  flex-wrap: wrap;
}

.search-container {
  flex: 1;
  max-width: 400px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  right: 12px;
  color: #a0aec0;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 12px 44px 12px 12px;
  border: 2px solid #D2DFE1;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #4fd1c7;
  box-shadow: 0 0 0 3px rgba(79, 209, 199, 0.1);
}

.search-input::placeholder {
  color: #a0aec0;
}

.add-medication-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #37b7c3;
  color: white;
  border: none;
  border-radius: 35px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s, transform 0.1s;
  white-space: nowrap;
}

.add-medication-btn:hover {
  background: #37b7c3;
  transform: translateY(-1px);
}

.add-medication-btn:active {
  transform: translateY(0);
}

/* Table Container */
.table-container {
  overflow-x: auto;
  border-top: 1px solid #e2e8f0;
}

.medications-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.medications-table th {
  background: #f7fafc;
  color: #4a5568;
  font-weight: 600;
  text-align: left;
  padding: 16px;
  border-bottom: 1px solid #e2e8f0;
  white-space: nowrap;
  font-size: 14px;
}

.medications-table td {
  padding: 16px;
  border-bottom: 1px solid #f1f5f9;
  color: #2d3748;
  vertical-align: middle;
}

.medications-table tbody tr:hover {
  background: #f8fafc;
}

.medications-table tbody tr:last-child td {
  border-bottom: none;
}

/* Status styling */
.status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: capitalize;
}

.status.continue {
  background: #c6f6d5;
  color: #22543d;
}

.status.discontinued {
  background: #fed7d7;
  color: #742a2a;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  background: transparent;
}

.edit-btn {
  color: #3182ce;
}

.edit-btn:hover {
  background: #ebf8ff;
  color: #2c5282;
}

.delete-btn {
  color: #e53e3e;
}

.delete-btn:hover {
  background: #fed7d7;
  color: #c53030;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  padding: 24px;
  border-top: 1px solid #e2e8f0;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #4a5568;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.pagination-btn:hover:not(:disabled) {
  background: #f7fafc;
  color: #2d3748;
}

.pagination-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.pagination-btn.active {
  background: #4fd1c7;
  color: white;
  font-weight: 600;
}

.pagination-ellipsis {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  color: #a0aec0;
  font-size: 14px;
}

/* Loading and empty states */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 24px;
}

.no-data {
  text-align: center;
  padding: 60px 24px;
  color: #718096;
  font-style: italic;
}

/* Responsive design */
@media (max-width: 768px) {
  .medications-page {
    padding: 16px;
  }

  .patient-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    padding: 16px;
  }

  .patient-avatar {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .patient-name {
    font-size: 20px;
  }

  .patient-meta {
    flex-direction: column;
    gap: 8px;
  }

  .page-header {
    padding: 16px 16px 0 16px;
  }

  .page-title {
    font-size: 24px;
  }

  .controls-section {
    flex-direction: column;
    align-items: stretch;
    padding: 0 16px 16px 16px;
    gap: 12px;
  }

  .search-container {
    max-width: none;
  }

  .medications-table {
    font-size: 12px;
  }

  .medications-table th,
  .medications-table td {
    padding: 12px 8px;
  }

  .action-buttons {
    gap: 4px;
  }

  .action-btn {
    width: 28px;
    height: 28px;
  }

  .pagination {
    padding: 16px;
  }

  .pagination-btn {
    min-width: 28px;
    height: 28px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .medications-page {
    padding: 12px;
  }

  .patient-header {
    padding: 12px;
  }

  .page-header {
    padding: 12px 12px 0 12px;
  }

  .controls-section {
    padding: 0 12px 12px 12px;
  }

  .medications-table th,
  .medications-table td {
    padding: 8px 6px;
    font-size: 11px;
  }

  .table-container {
    margin: 0 -12px;
  }

  .medications-table {
    margin: 0 12px;
  }
}

/* Print styles */
@media print {
  .medications-page {
    background: white;
    padding: 0;
  }

  .controls-section,
  .pagination {
    display: none;
  }

  .action-buttons {
    display: none;
  }

  .medications-table th:last-child,
  .medications-table td:last-child {
    display: none;
  }

  .page-content {
    box-shadow: none;
    border-radius: 0;
  }

  .patient-header {
    box-shadow: none;
    border: 1px solid #e2e8f0;
  }
}
