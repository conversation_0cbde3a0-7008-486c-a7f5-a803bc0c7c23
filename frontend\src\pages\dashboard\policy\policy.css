
.policy-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    padding: 15px 30px 30px 30px; /* Changed top padding */
    margin-bottom: 30px;
    width: 100%;
}

.policy-content-wrapper {
    padding: 20px;
}

.policy-dashboard {
    display: flex;
    flex-direction: column;
}

.policy-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.policy-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.policy-title {
    margin-bottom: 10px;
}

.policy-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.policy-subtitle {
    color: #4F547B;
    font-size: 16px;
    font-weight: 400;
    margin-top: 5px;
}

.policy-subtitle h6 {
    margin: 0;
    font-weight: 400;
}

.policy-search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    flex-wrap: wrap;
    gap: 20px;
}

.policy-search-box {
    position: relative;
    width: 550px;
    max-width: 100%;
    display: flex;
    align-items: center;
}

.policy-search-box .search-icon {
    position: absolute;
    left: 15px;
    color: #64748b;
    z-index: 1;
}

.policy-search-box .data-table-search {
    width: 100%;
}

.policy-search-box .data-table-search-input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 1px solid #EDEDED !important;
    border-radius: 0; /* Changed */
    outline: none;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: none; /* Changed */
}

.policy-search-box .data-table-search-input:focus {
    border-color: #3EC1C9 !important;
    box-shadow: none; /* Changed */
}

.policy-add-button {
    background-color: #37B7C3;
    color: white !important;
    border: none;
    padding: 12px 20px;
    border-radius: 0; /* Changed */
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: none; /* Changed */
}

.policy-add-button:hover {
    background-color: #2ea4a9;
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

.policy-table-container {
    border: 1px solid #A3AED0;
    border-radius: 0; /* Changed */
    /* max-height: 600px; */
    overflow-y: auto;
    box-shadow: none; /* Changed */
}

.policy-table {
    width: 100%;
    border-collapse: collapse;
}

.policy-table thead {
    position: sticky;
    top: 0;
    background: #3EC1C9;
    z-index: 10;
}

.policy-table th, .policy-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #E2E8F0;
}

.policy-table th {
    color: #ffffff;
    font-weight: 600;
    font-size: 14px;
}

.policy-table th svg {
    margin-left: 5px;
    cursor: pointer;
    vertical-align: middle;
}

.policy-table tbody tr {
    transition: background-color 0.2s ease;
}

.policy-table tr:nth-child(even) {
    background-color: #F8FAFC;
}

.policy-table tr:hover {
    background-color: #EDF2F7;
}

.policy-table td {
    font-size: 14px;
    color: #1B2559;
}

.policy-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.policy-action-button {
    background: none;
    border: none;
    cursor: pointer;
    margin-right: 8px;
    transition: transform 0.2s ease;
}

.policy-action-button:hover {
    color: #007bff;
    transform: scale(1.1);
}

.ArrowUp {
    background-color: #37B7C3;
    border-radius: 0; /* Changed */
    border: none;
    color: white;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.ArrowUp:hover {
    background-color: #2ea4a9;
    transform: none; /* Changed */
}

.SquareX {
    background-color: #d70909;
    border-radius: 0; /* Changed */
    border: none;
    color: white;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.SquareX:hover {
    background-color: #b30707;
    transform: none; /* Changed */
}

/* Empty state styling */
.policy-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #64748B;
}

.policy-empty-state h3 {
    font-size: 18px;
    margin-bottom: 10px;
    color: #1B2559;
}

.policy-empty-state p {
    font-size: 14px;
    margin-bottom: 20px;
}

/* Loading state */
.policy-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .policy-search-container {
        flex-direction: column;
        align-items: stretch;
    }
    
    .policy-search-box {
        width: 100%;
    }
    
    .policy-table-container {
        overflow-x: auto;
    }
    
    .policy-table th, 
    .policy-table td {
        padding: 10px;
    }
}
