import React, { useEffect, useState } from 'react';
import './SponsorDashboardKPI.css';

const mockSponsorKPI = {
    total_enrollments: 120,
    avg_enrollment_time_days: 5.3,
    dropout_rate_percent: 12.5,
    top_sites: [
      { site_name: 'Hospital Charles Nicolle', count: 40 },
      { site_name: 'Hospital La Rabta', count: 35 },
      { site_name: 'Hospital Habib Thameur', count: 20 },
    ],
  };

interface TopSite {
  site_name: string;
  count: number;
}

interface SponsorKPI {
  total_enrollments: number;
  avg_enrollment_time_days: number | null;
  dropout_rate_percent: number;
  top_sites: TopSite[];
}

interface Props {
  globalStudyId?: string;
}

const SponsorDashboardKPI: React.FC<Props> = ({ globalStudyId }) => {
  const [kpi, setKpi] = useState<SponsorKPI | null>(null);

  useEffect(() => {
    // Simulate API call with mock data
    const loadMockData = async () => {
      await new Promise((r) => setTimeout(r, 500)); // Simulate delay
      setKpi(mockSponsorKPI);
    };
    loadMockData();
  }, [globalStudyId]);

  if (!kpi) return <p>Loading mock KPI data...</p>;

  return (
    <div className="kpi-dashboard">
      <h2>
        {globalStudyId ? 'KPIs for Global Study' : 'All Studies – KPIs'}
      </h2>

      <div className="grid">
        <div className="bg-blue-50">
          <h3>Total Enrollments</h3>
          <p className="text-2xl">{kpi.total_enrollments}</p>
        </div>
        <div className="bg-green-50">
          <h3>Avg Enrollment Time (days)</h3>
          <p className="text-2xl">{kpi.avg_enrollment_time_days}</p>
        </div>
        <div className="bg-red-50">
          <h3>Dropout Rate (%)</h3>
          <p className="text-2xl">{kpi.dropout_rate_percent}%</p>
        </div>
      </div>

      <div className="mt-6">
        <h3>Top Performing Sites</h3>
        <ul>
          {kpi.top_sites.map((site, i) => (
            <li key={i}>
              {site.site_name}: <strong>{site.count}</strong> enrollments
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default SponsorDashboardKPI;