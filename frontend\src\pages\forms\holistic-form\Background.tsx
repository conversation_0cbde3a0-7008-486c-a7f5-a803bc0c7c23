import React, { useState, useEffect, useRef, useMemo } from "react";
import NurtifyRadio from "@/components/NurtifyRadio";
import NurtifyInput from "@/components/NurtifyInput";
import useHolisticFormStore from "@/store/holisticFormState";
import useHolisticFormTabStore from "@/store/holisticFormTabState";
import { PAST_MEDICAL_HISTORY_OPTIONS } from "./constants";
import { X } from "lucide-react";
import NurtifyCheckBox from "@/components/NurtifyCheckBox";

export default function Background() {
  const {
    situation,
    background,
    setBackground,
    whenSymptomsStarted,
    setWhenSymptomsStarted,
  } = useHolisticFormStore();

  const { goToPrevious, goToNext } = useHolisticFormTabStore();

  const [modalVisible, setModalVisible] = useState(false);
  const [optionsModalShowing, setOptionsModalShowing] = useState(false);
  const [a6FormShowing, seta6FormShowing] = useState(false);
  const [a8FormShowing, seta8FormShowing] = useState(false);
  const [a10FormShowing, seta10FormShowing] = useState(false);
  //TODO: Add b10FormShowing
  const [b10FormShowing, setb10FormShowing] = useState(false);
  const [b8FormShowing, setb8FormShowing] = useState(false);
  const [cb, setCb] = useState(false); // for triggering re-renders

  const modalRef = useRef<HTMLDivElement>(null);

  // State for managing the input for new past medical history options
  // const [newPMHOption, setNewPMHOption] = useState("");

  // State for managing selected past medical history options
  // const [selectedPMHOptions, setSelectedPMHOptions] = useState<string[]>([]);

  // Add to your existing state hooks
  const [searchInput, setSearchInput] = useState("");
  const [matchedInterventions, setMatchedInterventions] = useState<string[]>(
    []
  );
  
  // Add to your existing state hooks inside the Background component
  // const [searchPMHInput, setSearchPMHInput] = useState("");
  // const [matchedPMHOptions] = useState<string[]>([]);

  // const [matchedPMHOptions, setMatchedPMHOptions] = useState<string[]>([]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        modalRef.current &&
        !modalRef.current.contains(event.target as Node)
      ) {
        setModalVisible(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [modalRef]);

  /*
  const handleNext = () => {
    console.log(background);
  };
*/
  const handleSymptomsOnsetSentence = () => {
    let symptomsStartText = "";
    if (whenSymptomsStarted === "Within the last 24 hours") {
      symptomsStartText = `Symptoms started at ${background?.timeOfOnset} (within 24hrs from the visit).`;
    } else if (
      whenSymptomsStarted === "More than 24 hours" ||
      whenSymptomsStarted === "other-onset"
    ) {
      symptomsStartText = `Symptoms started on ${background?.dateFromOnset}.`;
    }
    setBackground({
      ...background,
      SymptomsOnsetSentence: symptomsStartText,
    });
  };

  const handlePregnencySentence = () => {
    // Extract relevant data from background
    const {
      pregnencyStatus,
      lastMenstruationPeriod,
      prevPregnancies = 0,
      numberOfMisCarriages = 0,
    } = background;

    let pregnancySentence = "";

    // Define Gravida and Para
    const gravida: number = prevPregnancies + 1; // Total pregnancies including current if pregnant
    const para: number = prevPregnancies - numberOfMisCarriages; // Live births

    if (gravida === 1) {
      pregnancySentence = `The patient is primigravida, with LMP ${lastMenstruationPeriod}.`;
    } else if (pregnencyStatus === "Currently Pregnant") {
      pregnancySentence = `The patient is currently pregnant. Gravida ${gravida}, Para ${para}, with LMP ${lastMenstruationPeriod}.`;
    } else if (pregnencyStatus === "Unknown Pregnancy Status") {
      pregnancySentence = `The patient's pregnancy status is unknown, with LMP ${lastMenstruationPeriod}.`;
    } else if (pregnencyStatus === "Not Pregnant") {
      pregnancySentence = "The patient decline pregnancy.";
    }

   // const pregnancySectionSentence = pregnancySentence !== "";

    setBackground({
      ...background,
      pregnancySentence,
    });
};

  const [maxDate, minDate] = useMemo(() => {
    const currentDate = new Date();
    const maxDate = new Date(
      currentDate.getFullYear() - 10,
      currentDate.getMonth(),
      currentDate.getDate()
    );
    const minDate = new Date(
      currentDate.getFullYear() - 60,
      currentDate.getMonth(),
      currentDate.getDate()
    );
    return [
      maxDate.toISOString().split("T")[0],
      minDate.toISOString().split("T")[0],
    ];
  }, []);

  const handlePregnencyStatus = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (situation && situation.sex === "female") {
      setBackground({ ...background, pregnencyStatus: e.target.value });
      
      if (e.target.value === "Currently Pregnant" || e.target.value === "Unknown Pregnancy Status") {
        seta6FormShowing(true);
        setb10FormShowing(false);
        setOptionsModalShowing(true);
      } 
    }
  };

  // const pastMedicalHistoryOptions = PAST_MEDICAL_HISTORY_OPTIONS;

  // const handleOptionClick = (opt: string) => {
  //   if (selectedPMHOptions.includes(opt)) {
  //     return;
  //   }
  //   setSelectedPMHOptions([...selectedPMHOptions, opt]);
  // };

  // const handleRemoveOption = (opt: string) => {
  //   const filteredOptions = selectedPMHOptions.filter((o) => o !== opt);
  //   setSelectedPMHOptions(filteredOptions);
  // };

  const handleSymptomsOnsetChange = (value: string) => {
    setWhenSymptomsStarted(value);
    setModalVisible(true); // Open modal on change
    if (value === "Within the last 24 hours") {
      setBackground({ ...background, timeOfOnset: "" }); // Reset timeOfOnset
    } else if (value === "More than 24 hours" || value === "other-onset") {
      setBackground({ ...background, dateFromOnset: "" }); // Reset dateFromOnset
    }
  };

  // Function to handle adding a new option
  // const handleAddOption = () => {
  //   if (newPMHOption.trim() !== "") {
  //     const updatedOptions = [...selectedPMHOptions, newPMHOption];
  //     setSelectedPMHOptions(updatedOptions);
  //     setNewPMHOption(""); // Reset input after adding
  //   }
  // };

  // Handler for search input change
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchInput(value);
    const matches = PAST_MEDICAL_HISTORY_OPTIONS.filter((history) =>
      history.toLowerCase().includes(value.toLowerCase())
    );
    setMatchedInterventions(matches);
  };


  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    console.log(value);
    setBackground({
      ...background,
      checkedProblems: background?.checkedProblems
        ? background.checkedProblems.includes(value)
          ? background.checkedProblems.filter((item) => item !== value)
          : [...background.checkedProblems, value]
        : [value],
    });
  };

  const handleOtherCheckedProblems = () => {
    if (!background?.otherCheckedProblems) return;
  
    // Extract checked problems with non-empty text
    const newProblems = Object.values(background.otherCheckedProblems)
      .filter((value) => value.checked && value.text.trim() !== "")
      .map((value) => value.text);
  
    // Remove unchecked problems from existing checkedProblems
    const updatedCheckedProblems = (background?.checkedProblems || []).filter(
      (problem) => newProblems.includes(problem)
    );
  
    // Combine new problems with the filtered existing ones
    const mergedProblems = [...new Set([...updatedCheckedProblems, ...newProblems])];
  
    setBackground({
      ...background,
      checkedProblems: mergedProblems,
    });
  };
  
  // Handler to add an intervention to the selected list
  const handleAddIntervention = (intervention: string) => {
    if (!background?.pastMedicalHistorySelectedOptions?.includes(intervention)) {
      setBackground({
        ...background,
        pastMedicalHistorySelectedOptions: [...(background.pastMedicalHistorySelectedOptions || []), intervention],
      });
    }
  };

  
 

  // Handler to remove an intervention from the selected list
  const handleRemoveIntervention = (intervention: string) => {
    setBackground({
      ...background,
      pastMedicalHistorySelectedOptions: background?.pastMedicalHistorySelectedOptions?.filter((item) => item !== intervention) || [],
    });
  };

  
  

  // Handler for search input change for past medical history
  // const handleSearchPMHInputChange = (
  //   e: React.ChangeEvent<HTMLInputElement>
  // ) => {
  //   const value = e.target.value;
  //   setSearchPMHInput(value);
  //   const matches = PAST_MEDICAL_HISTORY_OPTIONS.filter((option) =>
  //     option.toLowerCase().includes(value.toLowerCase())
  //   );
  //   setMatchedPMHOptions(matches);
  // };

  // Handler to add a PMH option to the selected list
  // const handleAddPMHOption = (option: string) => {
  //   if (!selectedPMHOptions.includes(option)) {
  //     setSelectedPMHOptions((prev) => [...prev, option]);
  //   }
  // };

  // Handler to remove a PMH option from the selected list
  // const handleRemovePMHOption = (option: string) => {
  //   setSelectedPMHOptions((prev) => prev.filter((item) => item !== option));
  // };

  return (
    <div className="tabs__pane -tab-item-1">
      <div id="backgoundSection" className="p-4">
        <div className="block">
          <div className="d-flex flex-wrap align-items-start flex-column flex-md-row gap-5">
            {situation?.sex === "female" &&
            situation?.age &&
            parseInt(situation.age) < 60 ? (
              <>
                <div
                  className="list-group"
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    gap: "10px",
                  }}
                >
                  <span className="headinqQuestion">Pregnancy Status</span>
                  {[
                    "Currently Pregnant",
                    "Unknown Pregnancy Status",
                    "Not Pregnant",
                  ].map((status) => (
                    <NurtifyRadio
                      key={status}
                      name="pregnency_status"
                      label={status}
                      value={status}
                      checked={background?.pregnencyStatus === status}
                      onChange={handlePregnencyStatus}
                    />
                  ))}
                </div>
              </>
            ) : null}

            <div className="list-group" style={{ gap: "10px" }}>
              <span className="headinqQuestion">When the symptoms started</span>
              <NurtifyRadio
                name="symptoms_start_time"
                label="Within the last 24 hours"
                value="Within the last 24 hours"
                checked={whenSymptomsStarted === "Within the last 24 hours"}
                onChange={() =>
                  handleSymptomsOnsetChange("Within the last 24 hours")
                }
              />
              <NurtifyRadio
                name="symptoms_start_time"
                label="More than 24 hours"
                value="More than 24 hours"
                checked={whenSymptomsStarted === "More than 24 hours"}
                onChange={() => handleSymptomsOnsetChange("More than 24 hours")}
              />
              <NurtifyRadio
                name="symptoms_start_time"
                label="Other"
                value="other-onset"
                checked={whenSymptomsStarted === "other-onset"}
                onChange={() => handleSymptomsOnsetChange("other-onset")}
              />
            </div>

            <div className="list-group" style={{ gap: "10px" }}>
              <span className="headinqQuestion">
                What is the purpose of your current visit?
              </span>
              {["Free Text", "Selecting From the List"].map((method) => (
                <NurtifyRadio
                  key={method}
                  name="prefered_method"
                  label={method}
                  value={method}
                  checked={background?.preferedMethod === method}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    setBackground({
                      ...background,
                      preferedMethod: e.target.value,
                    });
                    setOptionsModalShowing(true);
                    if (method === "Selecting From the List") {
                      seta10FormShowing(false);
                      setb10FormShowing(true);
                    } else {
                      seta10FormShowing(true);
                      setb10FormShowing(false);
                    }
                  }}
                />
              ))}
            </div>
          </div>

          {/* Start of PMH Section */}
          <div className="mt-4 mb-4">
            <span className="headinqQuestion mb-4">Past Medical History</span>
          </div>

          <div className="gap-2">
            <div className="d-flex flex-wrap gap-2 mb-3">
              {background?.pastMedicalHistorySelectedOptions?.map((history, index) => (
                <div
                  key={index}
                  className="badge"
                  style={{
                    backgroundColor: "#112D4E",
                    display: "flex",
                    justifyContent: "space-between",
                    flexWrap: "wrap",
                    gap: "10px",
                  }}
                >
                  {history}
                  <X
                    size={16}
                    onClick={() => handleRemoveIntervention(history)}
                    style={{ cursor: "pointer" }}
                  />
                </div>
              ))}
            </div>
            <NurtifyInput
              type="text"
              placeholder="Search past medical history..."
              value={searchInput}
              onChange={handleSearchInputChange}
            />
            <div className="search-results mt-3">
              {searchInput.length != 0 &&
                matchedInterventions.map((history, index) => (
                  <div key={index} className="form-check">
                    <input
                      type="checkbox"
                      className="form-check-input"
                      checked={background?.pastMedicalHistorySelectedOptions?.includes(history)}
                      onChange={() => handleAddIntervention(history)}
                    />
                    <label className="form-check-label">{history}</label>
                  </div>
                ))}
            </div>
          </div>
        </div>
      </div>

      {whenSymptomsStarted && modalVisible && (
        <div className="options-modal" ref={modalRef}>
          <div className="center">
            {whenSymptomsStarted === "Within the last 24 hours" && (
              <div className="radio-input">
                <div className="subheading">Time of Onset</div>
                <div className="options">
                  <input
                    onChange={(e) =>
                      setBackground({
                        ...background,
                        timeOfOnset: e.target.value,
                      })
                    }
                    type="time"
                    value={background?.timeOfOnset}
                    placeholder="Time From Onset"
                    className="mt-3 form-control"
                  />
                </div>
                <button
                  className="btn btn-nurtify mt-4"
                  onClick={() => {
                    handleSymptomsOnsetSentence();
                    setModalVisible(false); // Close modal after submitting
                  }}
                >
                  Submit and Close
                </button>
              </div>
            )}
            {(whenSymptomsStarted === "More than 24 hours" ||
              whenSymptomsStarted === "other-onset") && (
              <div className="radio-input">
                <div className="options">
                  <div className="subheading mb-3">Specify Date of Onset</div>
                  <input
                    type="date"
                    max={maxDate}
                    min={minDate}
                    value={background?.dateFromOnset}
                    onChange={(e) =>
                      setBackground({
                        ...background,
                        dateFromOnset: e.target.value,
                      })
                    }
                    className="form-control"
                  />
                </div>
                <button
                  className="btn btn-nurtify mt-4"
                  onClick={() => {
                    handleSymptomsOnsetSentence();
                    setModalVisible(false); // Close modal after submitting
                  }}
                >
                  Submit and Close
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {optionsModalShowing && (
        <div className="options-modal">
             {b10FormShowing && (
            <div
              className="modal-dialog"
              style={{ maxWidth: "100%", height: "70vh" }}
            >
              <div className="modal-content h-100 d-flex flex-column">
                <div className="modal-body overflow-auto" style={{ flex: 1 }}>
                  <div className="container-fluid p-0">
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">Airway & Breathing</h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Difficulty in breathing",
                              "Difficulty of breathing on exertion",
                              "Productive cough",
                              "Coughing up blood (Haemoptysis)",
                              "Dry cough",
                              "Low Saturation level",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other Breathing Problem"]: { checked: (background?.otherCheckedProblems?.["Other Breathing Problem"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other Breathing Problem"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other Breathing Problem"]?.checked}
                                value="Other Breathing Problem"
                                id="Other Breathing Problem"
                                label="Other Breathing Problem"
                              />
                              {background?.otherCheckedProblems?.["Other Breathing Problem"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other Breathing Problem"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other Breathing Problem"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                  
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">Chest/Circulation</h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Chest Pain",
                              "Palpitation",
                              "Chest Tightness",
                              "High Blood Pressure",
                              "Low Blood Pressure",
                              "Low Haemoglobin Level",
                              "High Potassium Level",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other Circulation Problem"]: { checked: (background?.otherCheckedProblems?.["Other Circulation Problem"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other Circulation Problem"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other Circulation Problem"]?.checked}
                                value="Other Circulation Problem"
                                id="Other Circulation Problem"
                                label="Other Circulation Problem"
                              />
                              {background?.otherCheckedProblems?.["Other Circulation Problem"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other Circulation Problem"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other Circulation Problem"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">Abdomen/Gastrology</h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Abdominal pain",
                              "Vomiting",
                              "Diarrhoea",
                              "Blood in the stool",
                              "Vomiting of Blood (Hematemesis)",
                              "Constipation",
                              "Abdominal Distension",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other Abdominal Problem"]: { checked: (background?.otherCheckedProblems?.["Other Abdominal Problem"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other Abdominal Problem"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other Abdominal Problem"]?.checked}
                                value="Other Abdominal Problem"
                                id="Other Abdominal Problem"
                                label="Other Abdominal Problem"
                              />
                              {background?.otherCheckedProblems?.["Other Abdominal Problem"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other Abdominal Problem"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other Abdominal Problem"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">Genitournary System</h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Flank pain",
                              "Blood in the Urine",
                              "Urine Retention",
                              "Blocked Catheter",
                              "Painful Urination",
                              "Testicular pain",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other Urination Problem"]: { checked: (background?.otherCheckedProblems?.["Other Urination Problem"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other Urination Problem"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other Urination Problem"]?.checked}
                                value="Other Urination Problem"
                                id="Other Urination Problem"
                                label="Other Urination Problem"
                              />
                              {background?.otherCheckedProblems?.["Other Urination Problem"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other Urination Problem"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other Urination Problem"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">Neurology</h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Headache",
                              "Numbness",
                              "Slurred Speech",
                              "Facial Drop",
                              "Limb Weakness",
                              "Seizure",
                              "Dizziness",
                              "Neck pain",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other Nueral Problem"]: { checked: (background?.otherCheckedProblems?.["Other Nueral Problem"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other Nueral Problem"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other Nueral Problem"]?.checked}
                                value="Other Nueral Problem"
                                id="Other Nueral Problem"
                                label="Other Nueral Problem"
                              />
                              {background?.otherCheckedProblems?.["Other Nueral Problem"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other Nueral Problem"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other Nueral Problem"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">Skin</h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Redness",
                              "Swelling",
                              "Lump",
                              "Bruises",
                              "Rashes",
                              "Itchiness",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other Skin Problem"]: { checked: (background?.otherCheckedProblems?.["Other Skin Problem"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other Skin Problem"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other Skin Problem"]?.checked}
                                value="Other Skin Problem"
                                id="Other Skin Problem"
                                label="Other Skin Problem"
                              />
                              {background?.otherCheckedProblems?.["Other Skin Problem"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other Skin Problem"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other Skin Problem"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">ENT</h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Ear Pain",
                              "Ear Discharge",
                              "Ear Foreign Body",
                              "Difficulty of Hearing",
                              "Hearing Loss",
                              "Epistaxis",
                              "Nasal Congestion",
                              "Nasal Injury",
                              "Sore Throat",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other Ear Problem"]: { checked: (background?.otherCheckedProblems?.["Other Ear Problem"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other Ear Problem"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other Ear Problem"]?.checked}
                                value="Other Ear Problem"
                                id="Other Ear Problem"
                                label="Other Ear Problem"
                              />
                              {background?.otherCheckedProblems?.["Other Ear Problem"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other Ear Problem"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other Ear Problem"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">Opthalmology</h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Red Eye",
                              "Periorbital Swelling",
                              "Corneal Abrasion (Right)",
                              "Corneal Abrasion (Left)",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other Eye Problem"]: { checked: (background?.otherCheckedProblems?.["Other Eye Problem"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other Eye Problem"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other Eye Problem"]?.checked}
                                value="Other Eye Problem"
                                id="Other Eye Problem"
                                label="Other Eye Problem"
                              />
                              {background?.otherCheckedProblems?.["Other Eye Problem"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other Eye Problem"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other Eye Problem"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">Ob/Gyn</h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Vaginal Bleeding during pregnancy",
                              "Vaginal bleeding not related to pregnancy",
                              "Low Abdo Pain During Pregnancy",
                              "Vaginal Pain",
                              "Amenorrhea",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other Ob/Gyn Problem"]: { checked: (background?.otherCheckedProblems?.["Other Ob/Gyn Problem"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other Ob/Gyn Problem"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other Ob/Gyn Problem"]?.checked}
                                value="Other Ob/Gyn Problem"
                                id="Other Ob/Gyn Problem"
                                label="Other Ob/Gyn Problem"
                              />
                              {background?.otherCheckedProblems?.["Other Ob/Gyn Problem"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other Ob/Gyn Problem"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other Ob/Gyn Problem"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">Mental Health</h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Suicidal Thoughts",
                              "Depression",
                              "Unusual Behaviour",
                              "Aggressive Behaviour",
                              "Drug Misuse",
                              "Alcohol Intoxication",
                              "Hallucination",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other Mental Health Problem"]: { checked: (background?.otherCheckedProblems?.["Other Mental Health Problem"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other Mental Health Problem"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other Mental Health Problem"]?.checked}
                                value="Other Mental Health Problem"
                                id="Other Mental Health Problem"
                                label="Other Mental Health Problem"
                              />
                              {background?.otherCheckedProblems?.["Other Mental Health Problem"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other Mental Health Problem"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other Mental Health Problem"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">
                            Trauman/Injuries/Foreign Bodies
                          </h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Laceration Wound",
                              "Injuries",
                              "Animal Bites / Sting",
                              "Gunshot",
                              "Stab Wound",
                              "Foreign Body",
                              "Needle-stick injury",
                              "Major Trauma",
                              "Multiple Injuries",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other Injuries"]: { checked: (background?.otherCheckedProblems?.["Other Injuries"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other Injuries"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other Injuries"]?.checked}
                                value="Other Injuries"
                                id="Other Injuries"
                                label="Other Injuries"
                              />
                              {background?.otherCheckedProblems?.["Other Injuries"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other Injuries"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other Injuries"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="list-group">
                      <div className="card border-0 shadow-sm">
                        <div className="card-header headinqQuestion text-dark">
                          <h5 className="mb-0">Unwell/Other</h5>
                        </div>
                        <div className="card-body">
                          <div className="row">
                            {[
                              "Feeling Unwell",
                              "Fall",
                              "Fever",
                              "Overdose and poisoning",
                              "Allergic Reaction",
                              "Expected By",
                              "Unprotected Sex",
                              "Sexually acquired infection",
                              "Asking for Prescription",
                            ].map((option) => (
                              <div
                                key={option}
                                className="col-md-12 col-sm-6 mb-2"
                              >
                                <NurtifyCheckBox
                                  onChange={handleCheckboxChange}
                                  checked={background?.checkedProblems?.includes(option)}
                                  value={option}
                                  id={option}
                                  label={option}
                                />
                              </div>
                            ))}
                            <div className="col-md-12 col-sm-6 mb-2">
                              <NurtifyCheckBox
                                onChange={()=>{
                                  setBackground({
                                    ...background,
                                    otherCheckedProblems: {
                                      ...background.otherCheckedProblems,
                                      ["Other"]: { checked: (background?.otherCheckedProblems?.["Other"]?.checked?false:true),text: background?.otherCheckedProblems?.["Other"]?.text||"" },
                                    },
                                  });
                                }}
                                checked={background?.otherCheckedProblems?.["Other"]?.checked}
                                value="Other"
                                id="Other"
                                label="Other"
                              />
                              {background?.otherCheckedProblems?.["Other"]?.checked && (
                                <input
                                  className="form-control my-3"
                                  type="text"
                                  name=""
                                  value={background?.otherCheckedProblems?.["Other"]?.text}
                                  onChange={(e)=>{
                                    setBackground({
                                      ...background,
                                      otherCheckedProblems: {
                                        ...background.otherCheckedProblems,
                                      ["Other"]: { checked: true, text: e.target.value },
                                    },
                                  });
                                }}
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="modal-footer bg-light d-flex justify-content-center mt-4">
                      <button
                        onClick={() => {
                          setOptionsModalShowing(false);
                          setb10FormShowing(false);
                          handleOtherCheckedProblems();
                        }}
                        className="btn btn-nurtify"
                      >
                        Submit and Close
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          {!b10FormShowing && (
            <div className="center">
              {a6FormShowing && (
                <>
                  <div className="radio-input">
                  <div className="options">
                    <div className="subheading">Last Menstruation Date</div>
                    <input
                      max={maxDate}
                      min={minDate}
                      onChange={(e) => {
                        setBackground({
                          ...background,
                          lastMenstruationPeriod: e.target.value,
                        });
                        setCb(!cb);
                      }}
                      type="date"
                      placeholder="Last Menstruation Period:"
                      className="mb-4 form-control"
                    />
                    <div className="subheading">
                      Number of previous pregnancy
                    </div>
                    <input
                      type="number"
                      min={background?.numberOfMisCarriages}
                      max="20"
                      value={background?.prevPregnancies}
                      onChange={(e) => {
                        setBackground({
                          ...background,
                          prevPregnancies: parseInt(e.target.value),
                        });
                        setCb(!cb);
                      }}
                      placeholder="Number of previous pregnancy"
                      className="mb-4 form-control"
                    />
                    <div className="subheading">
                      Number of previous miscarriages
                    </div>
                    <input
                      type="number"
                      min="0"
                      max={background?.prevPregnancies}
                      value={background?.numberOfMisCarriages}
                      onChange={(e) => {
                        setBackground({
                          ...background,
                          numberOfMisCarriages: parseInt(e.target.value),
                        });
                        setCb(!cb);
                      }}
                      placeholder="Number of previous miscarriage"
                      className="mb-4 form-control"
                    />
                  </div>
                  {/* here */}
                  <button
                    className="btn btn-nurtify mt-4"
                    onClick={() => {
                      setOptionsModalShowing(false);
                      seta6FormShowing(false);
                      handlePregnencySentence();
                    }}
                  >
                    Submit and Close
                  </button>
                </div>
              </>
            )}
            {a8FormShowing && (
              <>
                <div className="radio-input">
                  <div className="subheading">Time of Onset</div>
                  <div className="options">
                    <input
                      onChange={(e) =>
                        setBackground({
                          ...background,
                          timeOfOnset: e.target.value,
                        })
                      }
                      type="time"
                      value={background?.timeOfOnset}
                      placeholder="Time From Onset"
                      className="mt-3 form-control"
                    />
                  </div>
                  <button
                    className="my-primary-btn mt-4"
                    onClick={() => {
                      setOptionsModalShowing(false);
                      seta8FormShowing(false);
                      handleSymptomsOnsetSentence();
                    }}
                  >
                    Submit and Close
                  </button>
                </div>
              </>
            )}
            {a10FormShowing && (
              <>
                <div className="radio-input">
                  <div className="options">
                    <div className="subheading mb-3">
                      Reason of current visit
                    </div>
                    <textarea
                      name=""
                      style={{ resize: "none", width: "40rem" }}
                      className="form-control"
                      id=""
                      value={background?.reasonOfCurrentVisit}
                      onChange={(e) =>
                        setBackground({
                          ...background,
                          reasonOfCurrentVisit: e.target.value,
                        })
                      }
                      cols={30}
                      rows={10}
                    ></textarea>
                  </div>
                  <button
                    className="btn btn-nurtify mt-4"
                    onClick={() => {
                      setOptionsModalShowing(false);
                      seta10FormShowing(false);
                    }}
                  >
                    Submit and Close
                  </button>
                </div>
              </>
            )}

            {b8FormShowing && (
              <>
                <div className="radio-input">
                  <div className="options">
                    <div className="subheading mb-3">Specify Date of Onset</div>
                    <input
                      type="date"
                      max={maxDate}
                      min={minDate}
                      value={background?.dateFromOnset}
                      onChange={(e) => {
                        setBackground({
                          ...background,
                          dateFromOnset: e.target.value,
                        });
                      }}
                      className="form-control"
                    />
                  </div>
                  <button
                    className="my-primary-btn mt-4"
                    onClick={() => {
                      setOptionsModalShowing(false);
                      setb8FormShowing(false);
                      handleSymptomsOnsetSentence();
                    }}
                  >
                    Submit and Close
                  </button>
                </div>
              </>
            )}
          </div>
          )}
        </div>
      )}

      <div className="col-12 mt-60">
        <button
          className="button -md btn-nurtify-lighter"
          style={{ width: "100px", float: "left" }}
          onClick={goToPrevious}
        >
          Prev
        </button>
        <button
          className="button -md btn-nurtify text-white"
          style={{ width: "100px", float: "right" }}
          onClick={goToNext}
        >
          Next
        </button>
      </div>
    </div>
  );
}
