:root {
  /* Using variables consistent with LiveChatConversation.css for a unified theme */
  --primary-color: #37B7C3; /* iOS Blue */
  --secondary-color: #F0F0F0; /* Lighter grey for backgrounds */
  --text-color: #000000; 
  --text-secondary: #8A8A8E; /* iOS secondary text color */
  --background-color: #FFFFFF;
  --border-color: #D1D1D6; /* iOS border color */
  --border-radius: 18px; /* Modern, softer radius from conversation view */
  --list-item-hover-bg: #F5F5F7; /* Subtle hover for list items */
  --list-item-selected-bg: #E5E5EA; /* Slightly darker for selection */
  --list-item-selected-border: var(--primary-color);
  --animation-duration: 0.25s;
  
  --dark-mode-bg: #000000;
  --dark-mode-text: #FFFFFF;
  --dark-mode-secondary: #8D8D93;
  --dark-mode-panel-bg: #1C1C1E; /* Background for panels in dark mode */
  --dark-mode-border-color: #38383A;
  --dark-mode-list-item-hover-bg: #2C2C2E;
  --dark-mode-list-item-selected-bg: #3A3A3C;
  --dark-mode-list-item-selected-border: #37B7C3; /* Dark mode primary blue */

  --input-focus-border: var(--primary-color);
  --input-focus-shadow: rgba(0, 122, 255, 0.2); /* Adjusted shadow for primary color */
  --dark-mode-input-focus-border: var(--dark-mode-list-item-selected-border);
  --dark-mode-input-focus-shadow: rgba(10, 132, 255, 0.3);
}

.nurtify-org-livechat-main-layout {
  display: flex;
  height: 100vh;
  background: var(--secondary-color); 
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Text', 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  color: var(--text-color);
  transition: background-color var(--animation-duration) ease-in-out;
}

.dark-mode .nurtify-org-livechat-main-layout {
  background-color: var(--dark-mode-bg);
  color: var(--dark-mode-text);
}

.org-livechat-chat-list-panel {
  width: 340px; /* Slightly wider for more content */
  background: var(--background-color); 
  border-right: 1px solid var(--border-color); 
  display: flex;
  flex-direction: column;
  transition: background-color var(--animation-duration) ease-in-out, border-color var(--animation-duration) ease-in-out;
}

.dark-mode .org-livechat-chat-list-panel {
  background: var(--dark-mode-panel-bg);
  border-right-color: var(--dark-mode-border-color);
}

.org-livechat-chat-list-header {
  padding: 12px 16px; /* Consistent padding */
  border-bottom: 1px solid var(--border-color); 
}
.dark-mode .org-livechat-chat-list-header {
  border-bottom-color: var(--dark-mode-border-color);
}

.org-livechat-chat-search-input {
  width: 100%;
  padding: 9px 14px; /* Adjusted padding */
  border-radius: 10px; /* Slightly less rounded than bubbles for a search field */
  border: 1px solid var(--border-color); 
  font-size: 14px; /* Slightly smaller for search input */
  background: var(--secondary-color); 
  color: var(--text-color);
  transition: border-color var(--animation-duration) ease-in-out, box-shadow var(--animation-duration) ease-in-out, background-color var(--animation-duration) ease-in-out;
}

.dark-mode .org-livechat-chat-search-input {
  background: #2C2C2E; /* Darker input field */
  border-color: var(--dark-mode-border-color);
  color: var(--dark-mode-text);
}

.org-livechat-chat-search-input::placeholder {
  color: var(--text-secondary);
}
.dark-mode .org-livechat-chat-search-input::placeholder {
  color: var(--dark-mode-secondary);
}

.org-livechat-chat-search-input:focus {
  outline: none;
  border-color: var(--input-focus-border);
  box-shadow: 0 0 0 3px var(--input-focus-shadow); /* Subtle glow */
  background-color: var(--background-color);
}
.dark-mode .org-livechat-chat-search-input:focus {
  border-color: var(--dark-mode-input-focus-border);
  box-shadow: 0 0 0 3px var(--dark-mode-input-focus-shadow);
  background-color: var(--dark-mode-panel-bg); /* Keep panel bg on focus */
}


.org-livechat-chat-list-body {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0; /* Add some padding top/bottom */
}

/* Custom Scrollbar for Webkit browsers in chat list */
.org-livechat-chat-list-body::-webkit-scrollbar {
  width: 6px;
}
.org-livechat-chat-list-body::-webkit-scrollbar-track {
  background: transparent;
}
.org-livechat-chat-list-body::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}
.dark-mode .org-livechat-chat-list-body::-webkit-scrollbar-thumb {
  background: var(--dark-mode-border-color);
}


.org-livechat-ai-agent-btn {
  width: calc(100% - 32px); 
  margin: 8px 16px 12px 16px; /* Adjusted margin */
  padding: 10px 0; 
  background: var(--primary-color);
  /* background-image: var(--primary-gradient); */ /* Solid color is cleaner */
  border: none;
  border-radius: 10px; /* Consistent with search input */
  color: white;
  font-weight: 500; 
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color var(--animation-duration) ease-in-out, transform var(--animation-duration) ease-in-out;
}

.dark-mode .org-livechat-ai-agent-btn {
  background: var(--dark-mode-list-item-selected-border); /* Use dark mode primary */
}

.org-livechat-ai-agent-btn:hover {
  background-color: #0069D9; /* Darker shade of primary */
  transform: translateY(-1px);
  /* box-shadow: 0 1px 3px rgba(0,0,0,0.08); */ /* Softer shadow */
}
.dark-mode .org-livechat-ai-agent-btn:hover {
  background-color: #0070E0;
}


.org-livechat-chat-list-item {
  display: flex;
  align-items: center; 
  gap: 12px;
  padding: 10px 16px; 
  cursor: pointer;
  border-left: 4px solid transparent; /* For selection indicator */
  transition: background-color var(--animation-duration) ease-in-out, border-left-color var(--animation-duration) ease-in-out;
}

.org-livechat-chat-list-item:hover {
  background-color: var(--list-item-hover-bg);
}
.dark-mode .org-livechat-chat-list-item:hover {
  background-color: var(--dark-mode-list-item-hover-bg);
}

.org-livechat-chat-list-item.selected { /* Assuming a .selected class is added */
  background: var(--list-item-selected-bg);
  border-left-color: var(--list-item-selected-border); 
}
.dark-mode .org-livechat-chat-list-item.selected {
  background: var(--dark-mode-list-item-selected-bg);
  border-left-color: var(--dark-mode-list-item-selected-border);
}

.org-livechat-chat-list-item-avatar img {
  width: 44px; /* Slightly larger avatar in list */
  height: 44px;
  border-radius: 50%;
  object-fit: cover;
  background: var(--secondary-color); 
}
.dark-mode .org-livechat-chat-list-item-avatar img {
  background-color: var(--dark-mode-secondary);
}

.org-livechat-chat-list-item-info {
  flex: 1;
  min-width: 0; /* Prevents overflow issues with flex children */
}

.org-livechat-chat-list-item-name {
  font-weight: 500; 
  font-size: 15px; 
  color: var(--text-color);
  margin-bottom: 3px; /* Increased spacing */
}
.dark-mode .org-livechat-chat-list-item-name {
  color: var(--dark-mode-text);
}

.org-livechat-chat-list-item-lastmsg {
  font-size: 13px; 
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 4px; 
}
.dark-mode .org-livechat-chat-list-item-lastmsg {
  color: var(--dark-mode-secondary);
}

.org-livechat-chat-list-item-meta {
  display: flex;
  justify-content: space-between;
  align-items: center; /* Align time and dept */
  font-size: 12px;
  color: var(--text-secondary); 
}
.dark-mode .org-livechat-chat-list-item-meta {
  color: var(--dark-mode-secondary);
}

.org-livechat-chat-list-item-time {
  /* color: var(--text-secondary); */ /* Inherits from meta */
}

.org-livechat-chat-list-item-dept {
  color: var(--primary-color); 
  font-weight: 500;
}
.dark-mode .org-livechat-chat-list-item-dept {
  color: var(--dark-mode-list-item-selected-border);
}

/* This is the container for the Outlet, it should take the remaining space */
.org-livechat-outlet-container {
  flex: 1; 
  display: flex; 
  flex-direction: column; 
  position: relative; 
  overflow: hidden; 
  background-color: var(--secondary-color); /* Match main layout bg */
}
.dark-mode .org-livechat-outlet-container {
  background-color: var(--dark-mode-bg); /* Match main dark layout bg */
}

.org-livechat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--secondary-color); /* Use variable */
}

/* Styles for OrgChatDetail.tsx if it has its own header */
/* These styles are for the header WITHIN the Outlet, not the main page header */
.org-livechat-chat-header {
  display: flex;
  align-items: center;
  padding: 10px 16px; /* Consistent with main header */
  background: var(--background-color);
  border-bottom: 1px solid var(--border-color);
  gap: 12px; /* Reduced gap */
}

.dark-mode .org-livechat-chat-header {
  background-color: var(--dark-mode-panel-bg);
  border-bottom-color: var(--dark-mode-border-color);
}

.org-livechat-back-button { /* Assuming this is for navigating within org chat detail, not main page back */
  background: transparent;
  border: none;
  color: var(--primary-color); /* Match main back button */
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color var(--animation-duration) ease-in-out;
}

.org-livechat-back-button:hover {
  background-color: var(--list-item-hover-bg); /* Consistent hover */
}
.dark-mode .org-livechat-back-button {
  color: var(--dark-mode-list-item-selected-border);
}
.dark-mode .org-livechat-back-button:hover {
  background-color: var(--dark-mode-list-item-hover-bg);
}


.org-livechat-chat-info h2 {
  font-size: 16px; 
  font-weight: 600; 
  margin: 0;
  color: var(--text-color);
}
.dark-mode .org-livechat-chat-info h2 {
  color: var(--dark-mode-text);
}

.org-livechat-chat-meta {
  display: flex;
  align-items: center;
  gap: 8px; 
  font-size: 12px; 
  color: var(--text-secondary);
}
.dark-mode .org-livechat-chat-meta {
  color: var(--dark-mode-secondary);
}

.org-livechat-patient-name {
  font-weight: 500;
  color: var(--primary-color); /* Use primary color */
}

.org-livechat-department-name {
  color: var(--text-secondary);
}

.org-livechat-chat-status {
  padding: 2px 8px; 
  border-radius: 6px; /* Smaller radius for status tags */
  font-size: 11px; /* Smaller font for status */
  font-weight: 500;
  background: var(--secondary-color);
  color: var(--text-secondary);
  text-transform: capitalize;
  border: 1px solid var(--border-color);
}
.dark-mode .org-livechat-chat-status {
  background-color: var(--dark-mode-received-bg); /* Consistent dark element bg */
  color: var(--dark-mode-secondary);
  border-color: var(--dark-mode-border-color);
}

.org-livechat-chat-status.open {
  background: rgba(0, 122, 255, 0.1); /* Light primary color */
  color: var(--primary-color);
  border-color: rgba(0, 122, 255, 0.3);
}
.dark-mode .org-livechat-chat-status.open {
  background: rgba(10, 132, 255, 0.15);
  color: var(--dark-mode-list-item-selected-border);
  border-color: rgba(10, 132, 255, 0.3);
}

.org-livechat-chat-status.closed {
  background: #FFE5E5; /* Softer red */
  color: #E60000;    
  border-color: #FFCCCC;
}
.dark-mode .org-livechat-chat-status.closed {
  background: #5C0000;
  color: #FF8080;
  border-color: #800000;
}


.org-livechat-call-btn { 
  margin-left: auto;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px; /* Consistent smaller radius */
  padding: 7px 14px; 
  font-weight: 500;
  font-size: 13px; 
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: background-color var(--animation-duration) ease, transform var(--animation-duration) ease;
}

.org-livechat-call-btn:hover {
  background-color: var(--button-hover-bg);
  transform: translateY(-1px);
}

/* Styles for status dropdown */
.org-livechat-actions {
  position: relative;
  margin-left: auto;
}

.org-livechat-options-btn { /* This button is shown when chat is OPEN */
  background: transparent;
  border: none;
  font-size: 18px; /* Using text ... as button */
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  color: var(--primary-color); /* Match other header actions */
  margin-left: auto; 
  line-height: 1;
  transition: background-color var(--animation-duration) ease-in-out, color var(--animation-duration) ease-in-out;
}

.org-livechat-options-btn:hover {
  /* color: var(--primary-color); */ /* Already primary */
  background-color: var(--list-item-hover-bg); /* Consistent hover */
}
.dark-mode .org-livechat-options-btn {
  color: var(--dark-mode-list-item-selected-border);
}
.dark-mode .org-livechat-options-btn:hover {
  background-color: var(--dark-mode-list-item-hover-bg);
}


.org-livechat-status-dropdown {
  position: absolute;
  top: calc(100% + 6px); 
  right: 0;
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: 10px; /* Consistent medium radius */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); 
  z-index: 10;
  min-width: 150px; 
  overflow: hidden;
  padding: 6px 0; 
}
.dark-mode .org-livechat-status-dropdown {
  background-color: var(--dark-mode-panel-bg);
  border-color: var(--dark-mode-border-color);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.org-livechat-status-dropdown div {
  padding: 9px 16px; 
  cursor: pointer;
  font-size: 14px;
  color: var(--text-color);
  transition: background-color var(--animation-duration) ease-in-out;
}
.dark-mode .org-livechat-status-dropdown div {
  color: var(--dark-mode-text);
}

.org-livechat-status-dropdown div:hover {
  background-color: var(--list-item-hover-bg);
  /* color: var(--primary-color); */ /* Color change on hover can be too much here */
}
.dark-mode .org-livechat-status-dropdown div:hover {
  background-color: var(--dark-mode-list-item-hover-bg);
}

.nurtify-org-livechat-messages-container {
  flex: 1;
  padding: 16px 24px; /* Adjusted padding */
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px; /* Reduced gap for tighter message packing if desired */
  background: var(--secondary-color); 
  align-items: stretch; /* Ensure children stretch if needed */
}
.dark-mode .nurtify-org-livechat-messages-container {
  background-color: var(--dark-mode-bg); /* Match main dark bg */
}

.nurtify-org-livechat-message {
  display: flex;
  align-items: flex-end;
  gap: 0.75rem;
  max-width: 85%; /* Keep max width */
  /* margin-bottom: 1rem; */ /* Using gap on parent now */
  animation: messageSlideIn 0.25s ease-out; /* Match global animation duration */
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nurtify-org-livechat-message.patient {
  align-self: flex-start;
}

.nurtify-org-livechat-message.organization {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.nurtify-org-livechat-message.system {
  align-self: center;
  max-width: 85%;
  justify-content: center;
}

.nurtify-org-message-avatar {
  flex-shrink: 0;
}

.nurtify-org-message-avatar .avatar-circle {
  width: 36px; /* Slightly smaller */
  height: 36px;
  background: var(--secondary-color); /* Consistent with received bubble bg for patient avatar */
  color: var(--text-secondary); /* Character color */
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500; 
  font-size: 15px; 
  border-radius: 50%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); 
}
.dark-mode .nurtify-org-message-avatar .avatar-circle {
  background-color: var(--dark-mode-received-bg);
  color: var(--dark-mode-secondary);
}


.nurtify-org-livechat-message.organization .nurtify-org-message-avatar .avatar-circle {
  background: var(--primary-color); /* Org user (sender) avatar is primary color */
  color: white;
}
.dark-mode .nurtify-org-livechat-message.organization .nurtify-org-message-avatar .avatar-circle {
  background: var(--dark-mode-sent-bg); /* Org user (sender) avatar is dark primary */
  color: var(--dark-mode-text);
}

.nurtify-org-message-content {
  display: flex;
  flex-direction: column;
  max-width: 100%;
}

.nurtify-org-message-bubble {
  position: relative;
  padding: 9px 14px; 
  border-radius: var(--border-radius); 
  /* background-color: var(--background-color); */ /* Will be set by patient/org specific rules */
  box-shadow: 0 1px 0.5px rgba(0, 0, 0, 0.13); /* Consistent with conversation view */
  /* border: 1px solid var(--border-color); */ /* Remove general border, apply specifically if needed */
  max-width: 100%;
  word-wrap: break-word;
  font-size: 15px; 
  line-height: 1.4;
  color: var(--text-color);
}

.nurtify-org-livechat-message.patient .nurtify-org-message-bubble {
  background-color: var(--received-message-bg); /* Patient message (received by org) */
  color: var(--text-color);
  /* border-radius: var(--border-radius) var(--border-radius) var(--border-radius) 4px; */ /* Tail effect can be done with ::before/::after */
}
.dark-mode .nurtify-org-livechat-message.patient .nurtify-org-message-bubble {
  background-color: var(--dark-mode-received-bg);
  color: var(--dark-mode-text);
}

.nurtify-org-livechat-message.organization .nurtify-org-message-bubble {
  background: var(--sent-message-bg); /* Org message (sent by org) */
  color: white;
  /* border-radius: var(--border-radius) var(--border-radius) 4px var(--border-radius); */
  /* border-color: transparent; */ 
}
.dark-mode .nurtify-org-livechat-message.organization .nurtify-org-message-bubble {
  background: var(--dark-mode-sent-bg);
  color: var(--dark-mode-text);
}


.nurtify-org-livechat-message.system .nurtify-org-message-bubble {
  background: var(--secondary-color);
  color: var(--text-secondary);
  font-style: italic;
  text-align: center;
  font-size: 12px; /* Smaller for system messages */
  padding: 6px 10px;
  border-radius: 8px; /* Smaller radius for system messages */
  border: 1px solid var(--border-color);
}
.dark-mode .nurtify-org-livechat-message.system .nurtify-org-message-bubble {
  background: var(--dark-mode-received-bg); /* Use a subtle dark bg */
  color: var(--dark-mode-secondary);
  border-color: var(--dark-mode-border-color);
}

.nurtify-org-livechat-message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  opacity: 0.8;
}

.nurtify-org-livechat-message.patient .nurtify-org-livechat-message-header {
  color: #6c757d;
}

.nurtify-org-livechat-message.organization .nurtify-org-livechat-message-header {
  color: rgba(255, 255, 255, 0.9);
}

.org-livechat-message-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  color: #495057;
}

.nurtify-org-livechat-message.organization .org-livechat-message-header {
  justify-content: flex-end;
}

.org-livechat-message-meta {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #6c757d;
  opacity: 0.8;
}

.nurtify-org-livechat-message.organization .org-livechat-message-meta {
  justify-content: flex-end;
}

.org-livechat-message .sender-name {
  font-weight: 500;
}

.org-livechat-message.organization .sender-name {
  font-weight: 600;
}

.org-livechat-message .message-time {
  opacity: 0.8;
}

.org-livechat-message .message-content {
  white-space: pre-wrap;
}

.org-livechat-message-input {
  display: flex;
  align-items: center;
  padding: 10px 16px; 
  background: var(--background-color);
  border-top: 1px solid var(--border-color);
  gap: 8px; 
  position: sticky; /* Keep input at bottom */
  bottom: 0;
}
.dark-mode .org-livechat-message-input {
  background-color: var(--dark-mode-panel-bg);
  border-top-color: var(--dark-mode-border-color);
}

.org-livechat-message-input input { /* This should be a textarea for multiline, but styling as input for now */
  flex: 1;
  padding: 9px 14px; 
  border-radius: 20px; /* Match conversation view input */
  border: 1px solid var(--border-color);
  font-size: 15px; 
  background: var(--secondary-color);
  color: var(--text-color);
  outline: none;
  transition: border-color var(--animation-duration) ease-in-out, box-shadow var(--animation-duration) ease-in-out, background-color var(--animation-duration) ease-in-out;
}
.dark-mode .org-livechat-message-input input {
  background-color: var(--dark-mode-input-bg); /* From conversation.css */
  border-color: var(--dark-mode-border-color);
  color: var(--dark-mode-text);
}
.dark-mode .org-livechat-message-input input::placeholder {
  color: var(--dark-mode-secondary);
}


.org-livechat-message-input input:focus {
  border-color: var(--input-focus-border);
  box-shadow: 0 0 0 3px var(--input-focus-shadow);
  background-color: var(--background-color); /* White on focus */
}
.dark-mode .org-livechat-message-input input:focus {
  border-color: var(--dark-mode-input-focus-border);
  box-shadow: 0 0 0 3px var(--dark-mode-input-focus-shadow);
  background-color: #2C2C2E; /* Slightly lighter dark for focus */
}


.org-livechat-message-input button {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 36px; 
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px; /* For icon inside */
  transition: background-color var(--animation-duration) ease-in-out, transform var(--animation-duration) ease-in-out;
}
.dark-mode .org-livechat-message-input button {
  background-color: var(--dark-mode-sent-bg); /* Consistent dark mode primary */
}


.org-livechat-message-input button:hover:not(:disabled) {
  background: #0069D9; /* Darker primary */
  transform: scale(1.05);
}
.dark-mode .org-livechat-message-input button:hover:not(:disabled) {
  background-color: #0070E0;
}

.org-livechat-message-input button:disabled {
  background: #D1D1D6; /* iOS disabled grey */
  color: #8A8A8E;
  cursor: not-allowed;
  transform: none;
}
.dark-mode .org-livechat-message-input button:disabled {
  background: #3A3A3C;
  color: #5A5A5E;
}
