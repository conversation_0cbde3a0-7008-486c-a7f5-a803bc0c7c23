import React from "react";
import { FileText, User, BookOpen } from "lucide-react";

interface PolicyInformationsProps {
    authorName: string;
    jobTitle: string;
    description: string;
    hospital: {
        name: string;
    };
    department: {
        name: string;
    };
    study?: {
        name: string;
    } | null;
}

const PolicyInformations: React.FC<PolicyInformationsProps> = ({
    authorName,
    jobTitle,
    description,
    hospital,
    department,
    study,
}) => {
    return (
        <div style={styles.container}>
            <div style={styles.section}>
                <h2 style={styles.title}>{hospital.name}</h2>
                <p style={styles.subtitle}>{department.name}</p>
            </div>
            {study && (
                <div style={styles.section}>
                    <div style={styles.sectionHeader}>
                        <BookOpen size={18} style={{ marginRight: "8px" }} />
                        <h3 style={styles.name}>Study</h3>
                    </div>
                    <p style={styles.subtitle}>{study.name}</p>
                </div>
            )}
            <div style={styles.section}>
                <div style={styles.sectionHeader}>
                    <User size={18} style={{ marginRight: "8px" }} />
                    <h3 style={styles.name}>{authorName}</h3>
                </div>
                <p style={styles.subtitle}>{jobTitle}</p>
            </div>
            <div style={styles.description}>
                <div style={styles.sectionHeader}>
                    <FileText size={18} style={{ marginRight: "8px" }} />
                    <h3 style={styles.name}>Description</h3>
                </div>
                <p>{description}</p>
            </div>
        </div>
    );
};

const styles = {
    container: {
        backgroundColor: "#D9F6F6",
        padding: "16px",
        borderRadius: "8px",
        width: "300px",
    },
    section: {
        marginBottom: "16px",
        paddingBottom: "16px",
        borderBottom: "1px solid rgba(0, 0, 0, 0.1)",
    },
    sectionHeader: {
        display: "flex",
        alignItems: "center",
        marginBottom: "8px",
    },
    title: {
        fontSize: "20px",
        fontWeight: "bold" as const,
        margin: "0 0 4px",
        color: "#1B2559",
    },
    subtitle: {
        fontSize: "16px",
        margin: "0",
        color: "#555",
    },
    name: {
        fontSize: "18px",
        fontWeight: "bold" as const,
        margin: "0",
        color: "#1B2559",
    },
    description: {
        fontSize: "14px",
        color: "#333",
    },
    date: {
        fontSize: "12px",
        color: "#666",
        marginTop: "10px",
    },
};

export default PolicyInformations;
