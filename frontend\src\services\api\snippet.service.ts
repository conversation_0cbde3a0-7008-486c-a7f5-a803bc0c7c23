import api from "@/services/api";
import type { Snippet, SnippetCreateData } from "./types";

export const getAllSnippets = async (): Promise<Snippet[]> => {
  const response = await api.get("snippet/snippets/");
  return response.data.results || response.data;
};

export const getSnippetByUuid = async (uuid: string): Promise<Snippet> => {
  const { data } = await api.get(`snippet/snippets/${uuid}/`);
  return data;
};

export const createSnippet = async (snippetData: SnippetCreateData): Promise<Snippet> => {
  const { data } = await api.post("snippet/snippets/", snippetData);
  return data;
};

export const updateSnippet = async (uuid: string, snippetData: Partial<Snippet>): Promise<Snippet> => {
  const response = await api.put(`snippet/snippets/${uuid}/`, snippetData);
  return response.data;
};

export const partialUpdateSnippet = async (uuid: string, snippetData: Partial<Snippet>): Promise<Snippet> => {
  const response = await api.patch(`snippet/snippets/${uuid}/`, snippetData);
  return response.data;
};

export const deleteSnippetByUuid = async (uuid: string) => {
    const { data } = await api.delete(`snippet/snippets/${uuid}/`);
    return data;
  };