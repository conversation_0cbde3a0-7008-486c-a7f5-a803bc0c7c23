import api from "@/services/api";
import type {
  Report,
  ReportCreateData,
  ReportUpdateData,
  ReviewReport,
  ReviewReportCreateData,
  ReportContent,
  ReportStats,
  ReportTypes
} from "./types";

// Report CRUD operations
export const getAllReports = async (): Promise<Report[]> => {
  const response = await api.get("/report/reports/");
  return response.data.results || response.data;
};

export const getReportByUuid = async (uuid: string): Promise<Report> => {
  const { data } = await api.get(`/report/reports/${uuid}/`);
  return data;
};

export const createReport = async (reportData: ReportCreateData): Promise<Report> => {
  const formData = new FormData();

  // Add text fields
  Object.entries(reportData).forEach(([key, value]) => {
    if (key !== 'attach_content' && value !== undefined && value !== null) {
      formData.append(key, value.toString());
    }
  });

  // Add file if present
  if (reportData.attach_content) {
    formData.append('attach_content', reportData.attach_content);
  }

  const { data } = await api.post("/report/reports/", formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data;
};

export const updateReport = async (
  uuid: string,
  reportData: ReportUpdateData
): Promise<Report> => {
  const formData = new FormData();

  // Add text fields
  Object.entries(reportData).forEach(([key, value]) => {
    if (key !== 'attach_content' && value !== undefined && value !== null) {
      formData.append(key, value.toString());
    }
  });

  // Add file if present
  if (reportData.attach_content) {
    formData.append('attach_content', reportData.attach_content);
  }

  const { data } = await api.patch(`/report/reports/${uuid}/`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data;
};

export const deleteReport = async (uuid: string): Promise<void> => {
  await api.delete(`/report/reports/${uuid}/`);
};

// Report content operations
export const getReportContent = async (uuid: string): Promise<ReportContent> => {
  const { data } = await api.get(`/report/reports/${uuid}/content/`);
  return data;
};

// Report metadata operations
export const getReportTypes = async (): Promise<ReportTypes> => {
  const { data } = await api.get("/report/reports/types/");
  return data;
};

export const getReportStats = async (): Promise<ReportStats> => {
  const { data } = await api.get("/report/reports/stats/");
  return data;
};

export const getSubInvestigators = async (): Promise<any[]> => {
  const { data } = await api.get('/report/reports/sub_investigators/');
  return data;
};

// Get reports by patient UUID
export const getReportsByPatient = async (patientUuid: string, page: number = 1): Promise<{
  reports: Report[];
  count: number;
  patient: {
    uuid: string;
    first_name: string;
    last_name: string;
  };
}> => {
  const { data } = await api.get(`/report/reports/by_patient/?patient_uuid=${patientUuid}&page=${page}`);
  return data;
};

// Review Report CRUD operations
export const getAllReviewReports = async (): Promise<ReviewReport[]> => {
  const response = await api.get("/report/review-reports/");
  return response.data.results || response.data;
};

export const getReviewReportByUuid = async (uuid: string): Promise<ReviewReport> => {
  const { data } = await api.get(`/report/review-reports/${uuid}/`);
  return data;
};

export const createReviewReport = async (reviewData: ReviewReportCreateData): Promise<ReviewReport> => {
  const { data } = await api.post("/report/review-reports/", reviewData);
  return data;
};

export const updateReviewReport = async (
  uuid: string,
  reviewData: Partial<ReviewReportCreateData>
): Promise<ReviewReport> => {
  const { data } = await api.patch(`/report/review-reports/${uuid}/`, reviewData);
  return data;
};

export const deleteReviewReport = async (uuid: string): Promise<void> => {
  await api.delete(`/report/review-reports/${uuid}/`);
};

// Get review reports by report UUID
export const getReviewReportsByReportUuid = async (reportUuid: string): Promise<ReviewReport[]> => {
  const { data } = await api.get(`/report/review-reports/?report=${reportUuid}`);
  return data.results || data;
};

// Download report attachment
export const downloadReportAttachment = async (reportUuid: string): Promise<Blob> => {
  const response = await api.get(`/report/reports/${reportUuid}/download/`, {
    responseType: 'blob',
  });
  return response.data;
};
