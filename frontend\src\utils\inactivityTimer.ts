/**
 * Utility for tracking user inactivity and automatically logging out
 * after a specified period of inactivity.
 */
/**
 * Utility for tracking user inactivity and automatically logging out
 * after a specified period of inactivity.
 */

// Function to handle logout
let handleLogout = () => {};

const DEFAULT_TIMEOUT = import.meta.env.VITE_INACTIVE_TIME;

class InactivityTimer {
  private timeout: number;
  private timer: number | null = null;
  private events: string[] = [
    'mousedown', 'mousemove', 'keypress', 
    'scroll', 'touchstart', 'click', 'keydown'
  ];

  constructor(timeout: number = DEFAULT_TIMEOUT) {
    this.timeout = timeout;
  }

  /**
   * Start tracking user activity
   * @param keycloak - The keycloak instance to use for logout
   */
  public start(keycloak?: any): void {
    if (keycloak) {
      handleLogout = () => {
        keycloak.logout();
      };
    }
    // Set initial timer
    this.resetTimer();

    // Add event listeners for user activity
    this.events.forEach(event => {
      window.addEventListener(event, this.resetTimer.bind(this));
    });
  }

  /**
   * Stop tracking user activity
   */
  public stop(): void {
    // Clear the timer
    if (this.timer !== null) {
      window.clearTimeout(this.timer);
      this.timer = null;
    }

    // Remove event listeners
    this.events.forEach(event => {
      window.removeEventListener(event, this.resetTimer.bind(this));
    });
  }

  /**
   * Reset the inactivity timer
   */
  private resetTimer(): void {
    // Clear existing timer
    if (this.timer !== null) {
      window.clearTimeout(this.timer);
    }

    // Set new timer
    this.timer = window.setTimeout(() => {
      // Log out the user when timer expires
      handleLogout();
    }, this.timeout);
  }
}

// Create and export a singleton instance
const inactivityTimer = new InactivityTimer();
export default inactivityTimer;
