.terms-container {
  display: flex;
  max-width: 1200px;
  margin: 2rem auto;
  min-height: calc(100vh - 300px);
  padding: 0 1rem 3rem 1rem;
}

.terms-sidebar {
  width: 250px;
  background-color: #f8fafc;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 100px;
  height: fit-content;
}

.sidebar-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e2e8f0;
}

.sidebar-nav {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-nav li {
  margin-bottom: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.sidebar-nav li:hover {
  background-color: #f1f5f9;
}

.sidebar-nav li.active {
  background-color: #e0f2fe;
}

.sidebar-nav li.active a {
  color: #0ea5e9;
  font-weight: 600;
}

.sidebar-nav a {
  color: #64748b;
  text-decoration: none;
  font-size: 0.95rem;
  display: block;
  transition: color 0.2s ease;
}

.sidebar-nav a:hover {
  color: #0ea5e9;
}

.terms-content {
  flex: 1;
  padding: 2rem;
  background-color: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-left: 1.5rem;
}

.terms-content h1 {
  font-size: 2rem;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 1.5rem;
}

.terms-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #334155;
  margin-top: 2rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.terms-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #475569;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.terms-content p {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.terms-content ul, .terms-content ol {
  color: #64748b;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
}

.terms-content li {
  margin-bottom: 0.5rem;
}

.terms-content a {
  color: #0ea5e9;
  text-decoration: none;
  transition: color 0.2s ease;
}

.terms-content a:hover {
  text-decoration: underline;
}

.terms-redirect {
  text-align: center;
  padding: 3rem 1rem;
}

.terms-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
}

.terms-button {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background-color: #0ea5e9;
  color: white;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
}

.terms-button:hover {
  background-color: #0284c7;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Cookie table styles */
.cookie-table-container {
  overflow-x: auto;
  margin: 1.5rem 0;
}

.policy-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.policy-table td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
  vertical-align: top;
}

.policy-table td:last-child {
  border-right: none;
}

.policy-table tr:last-child td {
  border-bottom: none;
}

.policy-table tr:first-child td {
  background-color: #f8fafc;
  font-weight: 600;
  color: #1e293b;
  text-align: center;
}

.policy-table tr:nth-child(2) td {
  background-color: #f1f5f9;
  font-weight: 600;
  color: #334155;
}

.policy-table tr:nth-child(n+3) td {
  color: #64748b;
  font-size: 0.9rem;
}

.policy-table a {
  color: #0ea5e9;
  text-decoration: none;
  word-break: break-all;
}

.policy-table a:hover {
  text-decoration: underline;
}

/* Alert box styles */
.alert-box {
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 8px;
  padding: 1rem;
  margin: 1.5rem 0;
}

.alert-box p {
  color: #0c4a6e;
  margin: 0;
  font-weight: 500;
}

/* Contact section styles */
.contact-section {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 2rem 0;
}

.contact-info {
  margin-top: 1rem;
  padding: 1rem;
  background-color: #ffffff;
  border-radius: 6px;
  border-left: 4px solid #0ea5e9;
}

.contact-info p {
  margin: 0.25rem 0;
  color: #475569;
}

/* Last updated styles */
.last-updated {
  text-align: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

.last-updated p {
  color: #94a3b8;
  font-size: 0.9rem;
  font-style: italic;
}

/* Responsive styles */
@media (max-width: 768px) {
  .terms-container {
    flex-direction: column;
  }
  
  .terms-sidebar {
    width: 100%;
    position: static;
    margin-bottom: 1.5rem;
  }
  
  .terms-content {
    margin-left: 0;
  }
  
  .sidebar-nav {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .sidebar-nav li {
    margin-bottom: 0;
  }
}
