import React, { useState } from 'react';
import { MessageSquare, Send, X, User, RefreshCw } from 'lucide-react';
import './AIHelper.css';

interface Message {
  id: number;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

const AIHelper: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: 'Hello! I\'m your AI health assistant. How can I help you today?',
      sender: 'ai',
      timestamp: new Date()
    }
  ]);
  const [isTyping, setIsTyping] = useState(false);

  const toggleChatbox = () => {
    setIsOpen(!isOpen);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!inputValue.trim()) return;

    // Add user message
    const userMessage: Message = {
      id: messages.length + 1,
      text: inputValue,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages([...messages, userMessage]);
    setInputValue('');
    setIsTyping(true);

    // Simulate AI response after a delay
    setTimeout(() => {
      const aiResponses = [
        "I understand your concern. Based on your symptoms, I'd recommend discussing this with your clinical team during your next appointment.",
        "That's a great question about your medication. It's best to follow your doctor's prescribed dosage, but I can help you understand more about it.",
        "Your health history is important. I've noted your symptoms and will update your patient record.",
        "Remember to stay hydrated and take your medication as prescribed. Is there anything specific you'd like to know about your treatment?",
        "I'd be happy to help you prepare for your upcoming appointment. Let me know what specific concerns you'd like to discuss with your doctor."
      ];

      const randomResponse = aiResponses[Math.floor(Math.random() * aiResponses.length)];

      const aiMessage: Message = {
        id: messages.length + 2,
        text: randomResponse,
        sender: 'ai',
        timestamp: new Date()
      };

      setMessages(prevMessages => [...prevMessages, aiMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const clearChat = () => {
    setMessages([
      {
        id: 1,
        text: 'Hello! I\'m your AI health assistant. How can I help you today?',
        sender: 'ai',
        timestamp: new Date()
      }
    ]);
  };

  return (
    <div className={`ai-helper ${isOpen ? 'open' : ''}`}>
      <button className="ai-helper-toggle" onClick={toggleChatbox}>
        {isOpen ? <X size={20} /> : <MessageSquare size={20} />}
      </button>

      <div className="ai-helper-header">
        <h3>AI Health Assistant</h3>
        <button className="clear-chat-button" onClick={clearChat}>
          <RefreshCw size={16} />
        </button>
      </div>

      <div className="ai-helper-messages">
        {messages.map((message) => (
          <div 
            key={message.id} 
            className={`message ${message.sender === 'user' ? 'user-message' : 'ai-message'}`}
          >
            <div className="message-avatar">
              {message.sender === 'user' ? <User size={18} /> : <MessageSquare size={18} />}
            </div>
            <div className="message-bubble">
              <div className="message-text">{message.text}</div>
              <div className="message-time">{formatTime(message.timestamp)}</div>
            </div>
          </div>
        ))}
        {isTyping && (
          <div className="message ai-message">
            <div className="message-avatar">
              <MessageSquare size={18} />
            </div>
            <div className="message-bubble typing">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
      </div>

      <form className="ai-helper-input" onSubmit={handleSubmit}>
        <input
          type="text"
          placeholder="Type your question..."
          value={inputValue}
          onChange={handleInputChange}
        />
        <button type="submit" disabled={!inputValue.trim()}>
          <Send size={18} />
        </button>
      </form>
    </div>
  );
};

export default AIHelper;
