import React, { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  useConversation,
  useSetForwardedToFinance,
  useSetReimbursed,
  useSetRejected,
} from "@/hooks/conversation.query";
import LightFooter from "@/shared/LightFooter";
import { motion } from "framer-motion";
import { ArrowLeft } from "lucide-react";
import "./conversationdetails.css";
import CommentModal from "@/components/CommentModal";

type ModalAction = "reject" | null;

type ConversationStatus = "pending" | "inprogress" | "forwarded_to_finance" | "rejected" | "reimbursed";

interface QuestionAnswer {
  answer: boolean | string;
}

const ConversationDetails: React.FC = () => {
  const { uuid } = useParams<{ uuid: string }>();
  const navigate = useNavigate();
  const { data: conversation, isLoading } = useConversation(uuid || "");
  const setForwardedToFinanceMutation = useSetForwardedToFinance(uuid || "");
  const setReimbursedMutation = useSetReimbursed(uuid || "");
  const setRejectedMutation = useSetRejected(uuid || "");

  const [showCommentModal, setShowCommentModal] = useState(false);
  const [modalAction, setModalAction] = useState<ModalAction>(null);

  const handleBack = () => {
    navigate("/ask-nurtifiers");
  };

  const handleActionClick = async (action: "forward" | "reject" | "reimburse") => {
    if (action === "reject") {
      setModalAction(action);
      setShowCommentModal(true);
    } else {
      // Handle forward and reimburse directly without modal
      try {
        if (action === "forward") {
          await setForwardedToFinanceMutation.mutateAsync({ comment: "" });
        } else if (action === "reimburse") {
          await setReimbursedMutation.mutateAsync({ comment: "" });
        }
        navigate("/ask-nurtifiers");
      } catch (error) {
        console.error(`Error setting conversation to ${action}:`, error);
      }
    }
  };

  const handleCommentSubmit = async (comments: string) => {
    if (!uuid || !modalAction) return;

    try {
      if (modalAction === "reject") {
        await setRejectedMutation.mutateAsync({ comment: comments });
        setShowCommentModal(false);
        setModalAction(null);
        navigate("/ask-nurtifiers");
      }
    } catch (error) {
      console.error(`Error setting conversation to ${modalAction}:`, error);
    }
  };

  const formatStatus = (status: ConversationStatus) => {
    if (status === "inprogress") {
      return "In Progress";
    }
    return status
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  if (isLoading) {
    return <div className="loading">Loading conversation...</div>;
  }

  if (!conversation) {
    return <div className="error">Conversation not found</div>;
  }

  const conversationStatus = conversation.status as ConversationStatus;

  const isRefundForm = Object.keys(conversation.questions || {}).includes("Attach file");

  const renderAttachment = () => {
    if (!isRefundForm || !conversation.attach_content) return null;

    const fileUrl = conversation.attach_content;
    const isImage = /\.(jpg|jpeg|png|gif)$/i.test(fileUrl);
    const isPdf = /\.pdf$/i.test(fileUrl);

    return (
      <div
        className="convdetails-attachment-section"
        style={{
          margin: "10px 0",
          padding: "15px",
          backgroundColor: "#f9f9f9",
          borderRadius: "10px",
        }}
      >
        <h2 style={{ fontWeight: "bold" }}>Attached File</h2>
        {isImage ? (
          <img
            src={fileUrl}
            alt="Attached file"
            style={{ maxWidth: "100%", height: "auto", borderRadius: "5px" }}
          />
        ) : isPdf ? (
          <embed
            src={fileUrl}
            type="application/pdf"
            width="100%"
            height="500px"
            style={{ borderRadius: "5px" }}
          />
        ) : (
          <a href={fileUrl} target="_blank" rel="noopener noreferrer">
            Download Attached File
          </a>
        )}
      </div>
    );
  };

  return (
    <div className="convdetails-main">
      <div className="convdetails-content">
        <motion.div
          className="convdetails-container"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="convdetails-header">
            <button className="convdetails-back-button" onClick={handleBack}>
              <ArrowLeft size={20} />
              Back
            </button>
            <h1>Conversation Details</h1>
          </div>

          <div className="convdetails-card">
            <div className="convdetails-patient-info">
              <h2>Patient Information</h2>
              <div className="convdetails-info-grid">
                <div className="convdetails-info-item">
                  <label>Name:</label>
                  <span>
                    {conversation?.patient_details?.first_name}{" "}
                    {conversation?.patient_details?.last_name}
                  </span>
                </div>
                <div className="convdetails-info-item">
                  <label>Email:</label>
                  <span>{conversation?.patient_details?.email}</span>
                </div>
                {conversation?.treated_by_details && (
                  <div className="convdetails-info-item">
                    <label>Treated By:</label>
                    <span>
                      {conversation.treated_by_details.first_name}{" "}
                      {conversation.treated_by_details.last_name}
                    </span>
                  </div>
                )}
                <div className="convdetails-info-item">
                  <label>Status:</label>
                  <span>{formatStatus(conversationStatus)}</span>
                </div>
                <div className="convdetails-info-item">
                  <label>Last Update:</label>
                  <span>{conversation.last_update_person}</span>
                </div>
                <div className="convdetails-info-item">
                  <label>Created At:</label>
                  <span>
                    {new Date(conversation.created_at).toLocaleDateString()}
                  </span>
                </div>
              </div>
            </div>

            {conversation.comments && (
              <div
                className="convdetails-comments-section"
                style={{
                  backgroundColor: "#e0f7f9",
                  borderRadius: "10px",
                  padding: "15px",
                  margin: "10px 0",
                }}
              >
                <h2 style={{ fontWeight: "bold" }}>
                  {conversation.last_update_person} Comments
                </h2>
                <p className="convdetails-comment">{conversation.comments}</p>
              </div>
            )}

            {renderAttachment()}

            <div className="convdetails-questions-section">
              <h2>Questions</h2>
              {Object.entries(conversation.questions || {}).map(
                ([questionName, value], index) => (
                  <div key={index} className="convdetails-question-item">
                    <div className="convdetails-question-header">
                      <h3>{questionName}</h3>
                    </div>
                    <div className="convdetails-answer">
                      <p className="convdetails-answer-content">
                        {typeof value === "object" && "answer" in value
                          ? (value as QuestionAnswer).answer === true
                            ? "Yes"
                            : (value as QuestionAnswer).answer === false
                            ? "No"
                            : String((value as QuestionAnswer).answer)
                          : String(value)}
                      </p>
                    </div>
                  </div>
                )
              )}
            </div>

            {conversationStatus !== "pending" && (
              <div className="convdetails-action-buttons">
                {conversationStatus === "inprogress" && (
                  <>
                    <button
                      className="convdetails-reject-button"
                      onClick={() => handleActionClick("reject")}
                      disabled={
                        setRejectedMutation.isPending ||
                        (conversationStatus as string) === "rejected" ||
                        (conversationStatus as string) === "forwarded_to_finance" ||
                        (conversationStatus as string) === "reimbursed"
                      }
                    >
                      Reject
                    </button>
                    <button
                      className="convdetails-complete-button"
                      onClick={() => handleActionClick("forward")}
                      disabled={
                        setForwardedToFinanceMutation.isPending ||
                        (conversationStatus as string) === "forwarded_to_finance" ||
                        (conversationStatus as string) === "reimbursed"
                      }
                    >
                      Forward to Finance
                    </button>
                  </>
                )}
                {conversationStatus === "forwarded_to_finance" && (
                  <>
                    <button
                      className="convdetails-reject-button"
                      onClick={() => handleActionClick("reject")}
                      disabled={
                        setRejectedMutation.isPending ||
                        (conversationStatus as string) === "rejected" ||
                        (conversationStatus as string) === "reimbursed"
                      }
                    >
                      Reject
                    </button>
                    <button
                      className="convdetails-complete-button"
                      onClick={() => handleActionClick("reimburse")}
                      disabled={
                        setReimbursedMutation.isPending ||
                        (conversationStatus as string) === "rejected" ||
                        (conversationStatus as string) === "reimbursed"
                      }
                    >
                      Reimburse
                    </button>
                  </>
                )}
              </div>
            )}
          </div>
        </motion.div>
      </div>
      <CommentModal
        isOpen={showCommentModal}
        onClose={() => {
          setShowCommentModal(false);
          setModalAction(null);
        }}
        onSubmit={handleCommentSubmit}
        action="reject"
      />
      <LightFooter />
    </div>
  );
};

export default ConversationDetails;