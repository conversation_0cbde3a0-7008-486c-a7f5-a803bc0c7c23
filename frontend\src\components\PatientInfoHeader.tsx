// components/PatientInfoHeader.tsx
import React from 'react';
import './PatientInfoHeader.css';
import useSelectedPatientStore from '@/store/SelectedPatientState';
import Preloader from './common/Preloader';

function getInitials(first = "", last = "") {
  return `${first[0] ?? ""}${last[0] ?? ""}`.toUpperCase();
}

const PatientInfoHeader: React.FC = () => {
  const { selectedPatient, isLoading, error } = useSelectedPatientStore();

  if (isLoading) return <Preloader />;
  if (error) return <div>Error loading patient information: {error.message}</div>;
  if (!selectedPatient) return <div>No patient data available</div>;

  return (
    <div className='container'>
    <div className="patient-info-header">
      <div className="patient-avatar" title={`${selectedPatient.first_name || ''} ${selectedPatient.last_name || ''}`}>
        {getInitials(selectedPatient.first_name, selectedPatient.last_name)}
      </div>
      <div className="patient-info-fields">
        <div className="patient-info-item">
          <span className="label">Selected Patient</span>
          <span className="value">
            {selectedPatient.first_name || ''} {selectedPatient.last_name || ''}
          </span>
        </div>
        <div className="patient-info-item">
          <span className="label">NHS Number</span>
          <span className="value">{selectedPatient.nhs_number || 'N/A'}</span>
        </div>
        <div className="patient-info-item">
          <span className="label">Date of Birth</span>
          <span className="value">{selectedPatient.date_of_birth || 'N/A'}</span>
        </div>
      </div>
    </div>
    </div>
  );
};

export default PatientInfoHeader;
