 
import { useState } from "react";
import { Plus, Search, X } from "lucide-react";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import DataTable from "@/components/common/DataTable";
import { useStudiesBySponsorQuery, useCreateStudyInvitationMutation } from "@/hooks/study.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import InviteDepartmentModal from "@/components/modal/InviteDepartmentModal";
import "./sponsor-studies-invitation.css";


// Define types
interface Study {
  uuid: string;
  name: string;
  description?: string;
  full_title?: string;
  team_email?: string;
  leading_team?: string;
  created_by?: {
    first_name?: string;
    last_name?: string;
  };
  iras: string;
  created_at: string;
  updated_at: string;
}

export default function SponsorStudiesInvitationSection() {
  const { data: currentUser } = useCurrentUserQuery();
  const { data: studies, isLoading: isLoadingStudies } = useStudiesBySponsorQuery(currentUser?.identifier || "");
  const [searchTerm, setSearchTerm] = useState("");
  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);
  const [selectedStudy, setSelectedStudy] = useState<Study | null>(null);
  const createStudyInvitationMutation = useCreateStudyInvitationMutation();

  // Filter studies based on search term
  const filteredStudies = studies?.filter((study: Study) => {
    const matchesSearch =
      study.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (study.description && study.description.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesSearch;
  });

  const handleInviteDepartments = (study: Study) => {
    setSelectedStudy(study);
    setIsInviteModalOpen(true);
  };

  const handleCloseInviteModal = () => {
    setIsInviteModalOpen(false);
    setSelectedStudy(null);
  };

  const handleSaveInvite = async (selectedDepartments: string[]) => {
    if (!selectedStudy) return;

    try {
      await createStudyInvitationMutation.mutateAsync({
        study: selectedStudy.uuid,
        departments: selectedDepartments
      });
      handleCloseInviteModal();
    } catch (error) {
      console.error("Error sending invitations:", error);
      // You might want to show an error message to the user here
    }
  };

  // Define columns for the DataTable
  const columns = [
    {
      key: "name" as keyof Study,
      header: "Study Name",
      sortable: true,
    },
    {
      key: "description" as keyof Study,
      header: "Description",
      sortable: true,
      render: (value: any) => {
        const description = value as string | undefined;
        return description
          ? description.length > 100
            ? `${description.substring(0, 100)}...`
            : description
          : "No description";
      },
    },
    {
      key: "full_title" as keyof Study,
      header: "Full Title",
      sortable: true,
      render: (value: any) => {
        const fullTitle = value as string | undefined;
        return fullTitle || "No full title";
      },
    },
    {
      key: "iras" as keyof Study,
      header: "IRAS",
      sortable: true,
      render: (value: any) => {
        const iras = value as string | undefined;
        return iras || "No IRAS";
      },
    },
    {
      key: "leading_team" as keyof Study,
      header: "Leading Team",
      sortable: true,
      render: (value: any) => {
        const leadingTeam = value as string | undefined;
        return leadingTeam || "No leading team";
      },
    },
  ];

  // Define actions for the DataTable
  const actions = [
    {
      icon: <Plus size={16} />,
      tooltipText: "Invite to Study",
      onClick: (row: Study) => handleInviteDepartments(row),
    },
  ];

  return (
    <Wrapper>
      <Preloader />
      <div className="content-wrapper js-content-wrapper overflow-hidden" style={{ paddingBottom: "88px" }}>
        <div className="dashboard__content bg-light-4">
          <div className="row">
            <div className="col-12">
              <div className="studies-container">
                <div className="studies-header">
                  <h1>Study Invitations</h1>
                </div>

                <div className="studies-controls">
                  <div className="search-filter-container">
                    <div className="policy-search-container">
                      <div className="policy-search-box">
                        <Search className="search-icon" size={20} />
                        <input
                          type="text"
                          placeholder="Search studies by name or description"
                          className="policy-search-input"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        {searchTerm && (
                          <button className="clear-search" onClick={() => setSearchTerm("")}>
                            <X size={18} />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {isLoadingStudies ? (
                  <div className="loading-studies">Loading studies...</div>
                ) : filteredStudies && filteredStudies.length > 0 ? (
                  <DataTable
                    data={filteredStudies}
                    columns={columns}
                    actions={actions}
                    defaultItemsPerPage={10}
                    itemsPerPageOptions={[5, 10, 25, 50]}
                    noDataMessage="No studies available"
                  />
                ) : (
                  <div className="no-studies">
                    {searchTerm ? "No studies match your search criteria" : "No studies available for invitation."}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {selectedStudy && (
        <InviteDepartmentModal
          isOpen={isInviteModalOpen}
          onClose={handleCloseInviteModal}
          onSave={handleSaveInvite}
          studyName={selectedStudy.name}
          studyUuid={selectedStudy.uuid}
        />
      )}
    </Wrapper>
  );
}
