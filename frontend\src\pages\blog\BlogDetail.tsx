import React from 'react';
import { use<PERSON>ara<PERSON>, <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { blogArticles } from '../../data/blogData';
import Footer from '../../shared/Footer';
import './blog.css';

const BlogDetail: React.FC = () => {
  const { idAndTitle } = useParams<{ idAndTitle: string }>();
  // Extract the ID from the URL parameter (format: "id-title")
  const id = idAndTitle?.split('-')[0];
  const article = blogArticles.find(article => article.id === id);

  if (!article) {
    return (
      <>
        <div className="blog-detail-container">
          <div className="container">
            <h1>Article Not Found</h1>
            <p>Sorry, the article you're looking for doesn't exist.</p>
            <Link to="/blog" className="back-to-blogs">
              Back to Blogs
            </Link>
          </div>
        </div>
        <Footer />
      </>
    );
  }

  return (
    <>
      <div className="blog-detail-container">
        <div className="container">
          <motion.div
            className="blog-detail-header"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Link to="/blog" className="back-to-blogs">
              ← Back to Blogs
            </Link>
            
            <div className="blog-detail-image">
              <img src={article.image} alt={article.title} />
            </div>
            
            <h1 className="blog-detail-title">{article.title}</h1>
            
            <div className="blog-detail-meta">
              <span className="blog-detail-author">By {article.author}</span>
              <span className="blog-detail-date">Published on {article.date}</span>
            </div>
          </motion.div>

          <motion.div
            className="blog-detail-content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            {article.sections.map((section, index) => (
              <motion.div 
                key={index} 
                className="blog-detail-section"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 + (index * 0.1) }}
              >
                <h2 className="blog-detail-section-title">{section.title}</h2>
                <div className="blog-detail-section-content">
                  {section.content.split('\n').map((paragraph, i) => (
                    <p key={i}>{paragraph}</p>
                  ))}
                </div>
              </motion.div>
            ))}

            {article.references && article.references.length > 0 && (
              <motion.div 
                className="blog-detail-references"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 + (article.sections.length * 0.1) }}
              >
                <h2 className="blog-references-title">References</h2>
                <ul className="blog-references-list">
                  {article.references.map((reference, index) => (
                    <li key={index} className="blog-reference-item">
                      {reference.url ? (
                        <a href={reference.url} target="_blank" rel="noopener noreferrer">
                          {reference.text}
                        </a>
                      ) : (
                        reference.text
                      )}
                    </li>
                  ))}
                </ul>
              </motion.div>
            )}
          </motion.div>

          <motion.div 
            className="blog-detail-footer"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <div className="blog-detail-meta">
              <span className="blog-detail-author">By {article.author}</span>
              <span className="blog-detail-date">Published on {article.date}</span>
            </div>
          </motion.div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default BlogDetail;
