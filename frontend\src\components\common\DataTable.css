/* DataTable Container */
.data-table-container {
  width: 100%;
  background-color: #fff;
  border-radius: 0;
  border: none;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Table Top Controls */
.data-table-top-controls {
  display: none;
}

/* Items Per Page */
.data-table-items-per-page {
  display: flex;
  align-items: center;
  gap: 8px;
}

.data-table-items-per-page label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.data-table-select {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background-color: white;
  font-size: 14px;
  color: #374151;
  cursor: pointer;
  transition: border-color 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23374151' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 8px center;
  padding-right: 28px;
}

.data-table-select:hover {
  border-color: #9ca3af;
}

.data-table-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Table Wrapper */
.data-table-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Scrollable tbody container */
.table-body-container {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 600px;
}

.table-body-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-body-container::-webkit-scrollbar-track {
  background: #f9fafb;
}

.table-body-container::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.table-body-container::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* Table */
.data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  margin: 0;
}

.data-table thead {
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table thead th {
  background-color: #f8f9fa;
  color: #374151;
  font-weight: 600;
  text-align: left;
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
  white-space: nowrap;
  max-width: 150px;
  vertical-align: middle;
}

.data-table th.sortable {
  cursor: pointer;
  user-select: none;
}

.data-table th.sortable:hover {
  background-color: #f3f4f6;
}

.data-table th .th-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.data-table th .sort-icon {
  color: #9ca3af;
  transition: color 0.2s ease;
  flex-shrink: 0;
}

.data-table th:hover .sort-icon {
  color: #6b7280;
}

.data-table th .sort-icon.active {
  color: #3b82f6;
}

.data-table th .sort-icon.active.asc {
  transform: rotate(0deg);
}

.data-table th .sort-icon.active.desc {
  transform: rotate(180deg);
}

.data-table tbody tr {
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.15s ease;
}

.data-table tbody tr:hover {
  background-color: #f9fafb;
}

.data-table tbody tr:last-child {
  border-bottom: none;
}

.data-table td {
  padding: 16px;
  color: #374151;
  vertical-align: middle;
  font-size: 14px;
  line-height: 1.5;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.data-table td.no-data {
  text-align: center;
  padding: 40px 16px;
  color: #6b7280;
  font-style: italic;
  background-color: #f9fafb;
}

/* Actions Column */
.data-table th.actions-column {
  text-align: center;
  width: 120px;
  max-width: 120px;
  min-width: 120px;
}

.data-table td .actions-cell {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  max-width: 120px;
  min-width: 120px;
  white-space: nowrap;
  overflow: visible;
}

.action-button-wrapper {
  position: relative;
}

.action-button {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;
  width: 32px;
  height: 32px;
}

.action-button:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.action-button:active {
  background-color: #e5e7eb;
}

.action-button .action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.action-button .action-label {
  margin-left: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* Tooltip */
.action-button-wrapper .tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: #1f2937;
  color: white;
  padding: 6px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
  pointer-events: none;
  margin-bottom: 5px;
  z-index: 100;
}

.action-button-wrapper .tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 4px;
  border-style: solid;
  border-color: #1f2937 transparent transparent transparent;
}

.action-button-wrapper:hover .tooltip {
  opacity: 1;
  visibility: visible;
}

/* Special button styles */
.action-button.edit-btn {
  color: #3b82f6;
}

.action-button.edit-btn:hover {
  background-color: #dbeafe;
  color: #2563eb;
}

.action-button.delete-btn {
  color: #ef4444;
}

.action-button.delete-btn:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

.action-button.continue-btn {
  color: #10b981;
}

.action-button.continue-btn:hover {
  background-color: #d1fae5;
  color: #059669;
}

/* Pagination */
.data-table-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  border-top: 1px solid #e5e7eb;
  background-color: #fff;
}

.pagination-info {
  display: none;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-button {
  background: none;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

.pagination-button:hover:not(:disabled) {
  background-color: #f3f4f6;
  color: #374151;
}

.pagination-button:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  align-items: center;
  gap: 4px;
  margin: 0 8px;
}

.pagination-page {
  background: none;
  border: none;
  border-radius: 4px;
  min-width: 32px;
  height: 32px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination-page:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.pagination-page.active {
  background-color: #4fd1c7;
  color: white;
}

/* Search Component */
.data-table-search {
  position: relative;
  flex: 1;
  max-width: 300px;
}

.data-table-search::before {
  content: "";
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cpath d='M21 21l-4.3-4.3'%3E%3C/path%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  pointer-events: none;
  z-index: 1;
}

.data-table-search-input {
  width: 100%;
  padding: 12px 12px 12px 36px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  transition: border-color 0.2s ease;
  background-color: white;
  height: 48px;
  box-sizing: border-box;
}

.data-table-search-input:hover {
  border-color: #9ca3af;
}

.data-table-search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.data-table-search-input::placeholder {
  color: #9ca3af;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .data-table-top-controls {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .data-table-search {
    max-width: 100%;
  }
  
  .data-table-pagination {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }
  
  .pagination-controls {
    justify-content: center;
  }
  
  .data-table td, 
  .data-table th {
    padding: 8px 12px;
    font-size: 13px;
  }
  
  .pagination-button,
  .pagination-page {
    min-width: 32px;
    height: 32px;
    font-size: 13px;
  }
  
  .actions-cell {
    gap: 4px;
  }
  
  .action-button {
    width: 28px;
    height: 28px;
  }
}

@media (max-width: 480px) {
  .data-table-container {
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .data-table td, 
  .data-table th {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .data-table-top-controls,
  .data-table-pagination {
    padding: 12px 16px;
  }
}
