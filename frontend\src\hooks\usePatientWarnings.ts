import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { patientWarningService } from '../services/api/patient-warning.service';
import { PatientWarningCreate } from '../services/api/types';

export const usePatientWarnings = (patientUuid: string) => {
    const queryClient = useQueryClient();
    const queryKey = ['patient-warnings', patientUuid];

    const { data, isLoading, error } = useQuery({
        queryKey,
        queryFn: () => patientWarningService.getPatientWarnings(patientUuid),
        enabled: !!patientUuid,
    });

    const createWarning = useMutation({
        mutationFn: (data: PatientWarningCreate) => patientWarningService.createWarning(data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey });
        },
    });

    const updateWarning = useMutation({
        mutationFn: ({ uuid, data }: { uuid: string; data: Partial<PatientWarningCreate> }) =>
            patientWarningService.updateWarning(uuid, data),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey });
        },
    });

    const deleteWarning = useMutation({
        mutationFn: (uuid: string) => patientWarningService.deleteWarning(uuid),
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey });
        },
    });

    return {
        warnings: data?.results ?? [],
        isLoading,
        error,
        createWarning: createWarning.mutate,
        updateWarning: updateWarning.mutate,
        deleteWarning: deleteWarning.mutate,
        isCreating: createWarning.isPending,
        isUpdating: updateWarning.isPending,
        isDeleting: deleteWarning.isPending,
};
}; 