.convdetails-main {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f0fcfc;
}

.convdetails-content {
  flex: 1;
  padding: 40px 20px;
}

.convdetails-container {
  max-width: 1000px;
  margin: 0 auto;
}

.convdetails-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
}

.convdetails-back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  background-color: #e0f7f9;
  color: #3dc6d6;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.convdetails-back-button:hover {
  background-color: #d0f0f3;
}

.convdetails-card {
  background: white;
  border: 2px solid #3dc6d6;
  border-radius: 12px;
  padding: 30px;
}

.convdetails-patient-info {
  margin-bottom: 30px;
}

.convdetails-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.convdetails-info-item {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.convdetails-info-item label {
  color: #666;
  font-size: 0.9em;
}

.convdetails-info-item span {
  font-weight: 500;
  color: #333;
}

.convdetails-questions-section {
  margin-top: 30px;
  border-top: 1px solid #eee;
  padding-top: 30px;
}

.convdetails-question-item {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.convdetails-question-item h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.1em;
}

.convdetails-action-buttons {
  display: flex;
  gap: 15px;
  margin-top: 30px;
  justify-content: flex-end;
}

.convdetails-action-buttons button {
  padding: 10px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.convdetails-reject-button {
  background-color: #ffe0e0;
  color: #ff4444;
}

.convdetails-complete-button {
  background-color: #3dc6d6;
  color: white;
}

.convdetails-action-buttons button:hover {
  opacity: 0.9;
}

.convdetails-action-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .convdetails-content {
    padding: 20px 15px;
  }

  .convdetails-info-grid {
    grid-template-columns: 1fr;
  }

  .convdetails-action-buttons {
    flex-direction: column;
  }
}