import { useState } from "react";
import { Plus, Search, X, Calendar, Edit, Trash2, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Edit, <PERSON>lipboardList, FileText, AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import DataTable from "@/components/common/DataTable";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyTextArea from "@/components/NurtifyTextArea";
import VisitModal from "@/components/modal/VisitModal";
import { useStudiesBySponsorQuery, useCreateStudyMutation, useUpdateStudyMutation, useDeleteStudyMutation } from "@/hooks/study.query";
import { useCreateVisitTemplateMutation, useVisitTemplatesByStudyQuery, useDeleteVisitTemplateMutation, useUpdateVisitTemplateMutation } from "@/hooks/study.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { availableTests, VisitTemplate } from "@/store/scheduleEventState";
import { useQueryClient } from "@tanstack/react-query";
import "./sponsor-studies.css";


// Define types
interface Study {
  uuid: string;
  name: string;
  description?: string;
  full_title?: string;
  team_email?: string;
  leading_team?: string;
  created_by?: {
    first_name?: string;
    last_name?: string;
  };
  iras: string;
  created_at: string;
  updated_at: string;
}

interface VisitsListSectionProps {
  study: Study;
  onAddVisit: () => void;
  onEditVisit: (visit: VisitTemplate) => void;
  onDeleteVisit: (visitUuid: string) => void;
}

// VisitsListSection Component
function VisitsListSection({ study, onAddVisit, onEditVisit, onDeleteVisit }: VisitsListSectionProps) {
  const { data: visitTemplates, isLoading: isLoadingVisitTemplates } = useVisitTemplatesByStudyQuery(study.uuid);

  return (
    <div className="visit-templates-section">
      <div className="section-header">
        <h3>Visit Templates for {study.name}</h3>
        <button className="add-visit-btn" onClick={onAddVisit}>
          <Plus size={16} /> Add Visit Template
        </button>
      </div>
      
      {isLoadingVisitTemplates ? (
        <div className="loading-visits">Loading visits...</div>
      ) : visitTemplates && visitTemplates.length > 0 ? (
        <div className="visit-templates-list">
          {visitTemplates.map((visit: VisitTemplate) => (
            <div key={visit.uuid} className="visit-template-item">
              <div className="visit-template-info">
                <h3>{visit.name}</h3>
                <p>Day: {visit.day}</p>
                <p>Visit Number: {visit.visit_number}</p>
                <p>Activities: {visit.activities?.join(", ") || "None"}</p>
              </div>
              <div className="visit-template-actions">
                <button
                  type="button"
                  className="edit-btn"
                  onClick={() => onEditVisit(visit)}
                >
                  <Edit size={16} />
                </button>
                <button
                  type="button"
                  className="delete-btn"
                  onClick={() => onDeleteVisit(visit.uuid || "")}
                >
                  <Trash2 size={16} />
                </button>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="no-visits">
          No visit templates available for this study.
          <button className="add-visit-btn" onClick={onAddVisit} style={{ marginTop: '16px' }}>
            <Plus size={16} /> Add First Visit Template
          </button>
        </div>
      )}
    </div>
  );
}

const SponsorStudiesSection = () => {
  const { data: currentUser } = useCurrentUserQuery();
  const { data: studies, isLoading: isLoadingStudies } = useStudiesBySponsorQuery(currentUser?.identifier || "");
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreating, setIsCreating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentStudy, setCurrentStudy] = useState<Study | null>(null);
  const [isVisitModalOpen, setIsVisitModalOpen] = useState(false);
  const [currentVisit, setCurrentVisit] = useState<VisitTemplate | null>(null);
  const [showVisitsList, setShowVisitsList] = useState<{ [key: string]: boolean }>({});
  const [selectedStudyForVisit, setSelectedStudyForVisit] = useState<Study | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [fieldErrors, setFieldErrors] = useState<{ [key: string]: string[] }>({});
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [studyFormData, setStudyFormData] = useState<Partial<Study>>({
    name: "",
    description: "",
    full_title: "",
    team_email: "",
    leading_team: "",
    iras: "",
  });

  const queryClient = useQueryClient();
  const createStudyMutation = useCreateStudyMutation();
  const updateStudyMutation = useUpdateStudyMutation();
  const deleteStudyMutation = useDeleteStudyMutation();
  const createVisitTemplateMutation = useCreateVisitTemplateMutation();
  const updateVisitTemplateMutation = useUpdateVisitTemplateMutation();
  const deleteVisitTemplateMutation = useDeleteVisitTemplateMutation();

  // Filter studies based on search term
  const filteredStudies = studies?.filter((study: Study) => {
    const matchesSearch =
      study.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (study.description && study.description.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesSearch;
  });

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setStudyFormData((prev) => ({ ...prev, [name]: value }));
  };

  // Clear messages after timeout
  const clearMessages = () => {
    setTimeout(() => {
      setErrorMessage(null);
      setFieldErrors({});
      setSuccessMessage(null);
    }, 5000);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage(null);
    setFieldErrors({});
    setSuccessMessage(null);
    
    if (isEditing && currentStudy) {
      updateStudyMutation.mutate(
        { uuid: currentStudy.uuid, data: studyFormData },
        {
          onSuccess: () => {
            setIsEditing(false);
            setCurrentStudy(null);
            setStudyFormData({
              name: "",
              description: "",
              full_title: "",
              team_email: "",
              leading_team: "",
              iras: "",
            });
            setSuccessMessage("Study updated successfully!");
            clearMessages();
            // Invalidate and refetch studies query
            queryClient.invalidateQueries({ queryKey: ['studiesBySponsor', currentUser?.identifier] });
          },
          onError: (error: any) => {
            if (error?.response?.data) {
              const errorData = error.response.data;
              if (typeof errorData === 'object') {
                setFieldErrors(errorData);
              } else {
                setErrorMessage(errorData.message || "Failed to update study. Please try again.");
              }
            } else {
              setErrorMessage(error?.message || "Failed to update study. Please try again.");
            }
            clearMessages();
          },
        }
      );
    } else {
      createStudyMutation.mutate(studyFormData, {
        onSuccess: () => {
          setIsCreating(false);
          setStudyFormData({
            name: "",
            description: "",
            full_title: "",
            team_email: "",
            leading_team: "",
            iras: "",
          });
          setSuccessMessage("Study created successfully!");
          clearMessages();
          // Invalidate and refetch studies query
          queryClient.invalidateQueries({ queryKey: ['studiesBySponsor', currentUser?.identifier] });
        },
        onError: (error: any) => {
          if (error?.response?.data) {
            const errorData = error.response.data;
            if (typeof errorData === 'object') {
              setFieldErrors(errorData);
            } else {
              setErrorMessage(errorData.message || "Failed to create study. Please try again.");
            }
          } else {
            setErrorMessage(error?.message || "Failed to create study. Please try again.");
          }
          clearMessages();
        },
      });
    }
  };

  // Handle edit study
  const handleEditStudy = (study: Study) => {
    setCurrentStudy(study);
    setStudyFormData({
      name: study.name,
      description: study.description || "",
      full_title: study.full_title || "",
      team_email: study.team_email || "",
      leading_team: study.leading_team || "",
      iras: study.iras || "",
    });
    setIsEditing(true);
    setIsCreating(false);
  };

  // Handle delete study
  const handleDeleteStudy = (study: Study) => {
    if (window.confirm("Are you sure you want to delete this study?")) {
      deleteStudyMutation.mutate(study.uuid, {
        onSuccess: () => {
          // Invalidate and refetch studies query
          queryClient.invalidateQueries({ queryKey: ['studiesBySponsor', currentUser?.identifier] });
        },
      });
    }
  };

  // Handle manage visits - toggle visits list visibility
  const handleManageVisits = (study: Study) => {
    setShowVisitsList(prev => {
      // Create a new object with all visits closed
      const newState = Object.keys(prev).reduce((acc, key) => ({
        ...acc,
        [key]: false
      }), {});
      
      // Toggle the clicked study's visits
      return {
        ...newState,
        [study.uuid]: !prev[study.uuid]
      };
    });
    setSelectedStudyForVisit(study);
  };

  // Handle visit modal
  const handleOpenVisitModal = () => {
    if (!selectedStudyForVisit) return;
    setCurrentVisit(null);
    setIsVisitModalOpen(true);
  };

  const handleCloseVisitModal = () => {
    setIsVisitModalOpen(false);
    setCurrentVisit(null);
  };

  const handleSaveVisit = (visitData: Partial<VisitTemplate>) => {
    if (!selectedStudyForVisit) return;
    
    // Ensure study-related fields are set
    const enhancedVisitData = {
      ...visitData,
      study_uuid: selectedStudyForVisit.uuid,
    };

    if (currentVisit) {
      // Update existing visit template
      updateVisitTemplateMutation.mutate(
        {
          uuid: currentVisit.uuid || "",
          data: enhancedVisitData
        },
        {
          onSuccess: () => {
            handleCloseVisitModal();
            // Invalidate visit templates query to refetch data
            queryClient.invalidateQueries({ 
              queryKey: ['visitTemplatesByStudy', selectedStudyForVisit.uuid] 
            });
          }
        }
      );
    } else {
      // Create new visit template
      createVisitTemplateMutation.mutate(
        enhancedVisitData,
        {
          onSuccess: () => {
            handleCloseVisitModal();
            // Invalidate visit templates query to refetch data
            queryClient.invalidateQueries({ 
              queryKey: ['visitTemplatesByStudy', selectedStudyForVisit.uuid] 
            });
          }
        }
      );
    }
  };

  const handleDeleteVisit = (visitUuid: string) => {
    if (window.confirm("Are you sure you want to delete this visit template?")) {
      deleteVisitTemplateMutation.mutate(
        visitUuid,
        {
          onSuccess: () => {
            // The query invalidation is handled in the mutation hook
          }
        }
      );
    }
  };

  // Define columns for the DataTable
  const columns = [
    {
      key: "name" as keyof Study,
      header: "Study Name",
      sortable: true,
    },
    {
      key: "description" as keyof Study,
      header: "Description",
      sortable: true,
      render: (value: any) => {
        const description = value as string | undefined;
        return description
          ? description.length > 100
            ? `${description.substring(0, 100)}...`
            : description
          : "No description";
      },
    },
    {
      key: "full_title" as keyof Study,
      header: "Full Title",
      sortable: true,
      render: (value: any) => {
        const fullTitle = value as string | undefined;
        return fullTitle || "No full title";
      },
    },
    {
      key: "iras" as keyof Study,
      header: "IRAS",
      sortable: true,
      render: (value: any) => {
        const iras = value as string | undefined;
        return iras || "No IRAS";
      },
    },
    {
      key: "leading_team" as keyof Study,
      header: "Leading Team",
      sortable: true,
      render: (value: any) => {
        const leadingTeam = value as string | undefined;
        return leadingTeam || "No leading team";
      },
    },
  ];

  // Define actions for the DataTable
  const actions = [
    {
      icon: <Calendar size={16} />,
      tooltipText: "Manage Visits",
      onClick: (row: Study) => handleManageVisits(row),
    },
    {
      icon: <Edit size={16} />,
      tooltipText: "Edit Study",
      onClick: (row: Study) => handleEditStudy(row),
    },
    {
      icon: <Trash2 size={16} />,
      tooltipText: "Delete Study",
      onClick: (row: Study) => handleDeleteStudy(row),
    },
  ];

  return (
    <Wrapper>
      <Preloader />
      <div className="content-wrapper js-content-wrapper overflow-hidden" style={{ paddingBottom: "88px" }}>
        <div className="dashboard__content bg-light-4">
          <div className="row">
            <div className="col-12">
              <div className="studies-container">
                <div className="studies-header">
                  <h1>
                    <BarChart3 size={24} />
                    Studies
                  </h1>
                  <button 
                    className="create-study-btn" 
                    onClick={() => setIsCreating(true)}
                    disabled={createStudyMutation.isPending}
                  >
                    {createStudyMutation.isPending ? (
                      <Loader2 size={16} className="animate-spin" />
                    ) : (
                      <Plus size={16} />
                    )}
                    Create New Study
                  </button>
                </div>

                {(isCreating || isEditing) && (
                  <div className="study-info-container">
                    <h2>
                      {isEditing ? (
                        <><FileEdit size={20} /> Edit Study</>
                      ) : (
                        <><FileText size={20} /> Create New Study</>
                      )}
                    </h2>
                    
                    {/* Error Messages */}
                    {errorMessage && (
                      <div className="error-message">
                        <AlertCircle size={16} />
                        {errorMessage}
                      </div>
                    )}
                    
                    {/* Field-specific Error Messages */}
                    {Object.entries(fieldErrors).map(([field, errors]) => (
                      <div key={field} className="error-message">
                        <AlertCircle size={16} />
                        {errors.map((error, index) => (
                          <div key={index}>{error}</div>
                        ))}
                      </div>
                    ))}
                    
                    {/* Success Message */}
                    {successMessage && (
                      <div className="success-message">
                        <CheckCircle size={16} />
                        {successMessage}
                      </div>
                    )}
                    
                    <form onSubmit={handleSubmit} className="schedule-event-form row">
                      <div className="form-group col-12 col-md-6 mb-3">
                        <label htmlFor="name" className="form-label">
                          Study Name <span className="text-danger">*</span>
                        </label>
                        <NurtifyInput
                          type="text"
                          id="name"
                          name="name"
                          value={studyFormData.name}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      <div className="form-group col-12 col-md-6 mb-3">
                        <label htmlFor="iras" className="form-label">
                          IRAS Number <span className="text-danger">*</span>
                        </label>
                        <NurtifyInput
                          type="text"
                          id="iras"
                          name="iras"
                          value={studyFormData.iras}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      <div className="form-group col-12 mb-3">
                        <label htmlFor="description" className="form-label">
                          Description
                        </label>
                        <NurtifyTextArea
                          id="description"
                          name="description"
                          value={studyFormData.description}
                          onChange={handleInputChange}
                          rows={3}
                        />
                      </div>
                      <div className="form-group col-12 mb-3">
                        <label htmlFor="full_title" className="form-label">
                          Full Title
                        </label>
                        <NurtifyInput
                          type="text"
                          id="full_title"
                          name="full_title"
                          value={studyFormData.full_title}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div className="form-group col-12 col-md-6 mb-3">
                        <label htmlFor="team_email" className="form-label">
                          Team Email
                        </label>
                        <NurtifyInput
                          type="email"
                          id="team_email"
                          name="team_email"
                          value={studyFormData.team_email}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div className="form-group col-12 col-md-6 mb-3">
                        <label htmlFor="leading_team" className="form-label">
                          Leading Team
                        </label>
                        <NurtifyInput
                          type="text"
                          id="leading_team"
                          name="leading_team"
                          value={studyFormData.leading_team}
                          onChange={handleInputChange}
                        />
                      </div>
                      <div className="form-actions">
                        <button
                          type="button"
                          className="cancel-btn"
                          onClick={() => {
                            setIsCreating(false);
                            setIsEditing(false);
                            setCurrentStudy(null);
                            setStudyFormData({
                              name: "",
                              description: "",
                              full_title: "",
                              team_email: "",
                              leading_team: "",
                              iras: "",
                            });
                          }}
                        >
                          <X size={16} /> Cancel
                        </button>
                        <button 
                          type="submit" 
                          className="create-study-btn"
                          disabled={createStudyMutation.isPending || updateStudyMutation.isPending}
                        >
                          {(createStudyMutation.isPending || updateStudyMutation.isPending) ? (
                            <Loader2 size={16} className="animate-spin" />
                          ) : isEditing ? (
                            <CheckCircle size={16} />
                          ) : (
                            <Plus size={16} />
                          )}
                          {isEditing ? "Update Study" : "Create Study"}
                        </button>
                      </div>
                    </form>
                  </div>
                )}

                <div className="studies-controls">
                  <div className="search-filter-container">
                    <div className="policy-search-container">
                      <div className="policy-search-box">
                        <Search className="search-icon" size={20} />
                        <input
                          type="text"
                          placeholder="Search studies by name or description"
                          className="policy-search-input"
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                        />
                        {searchTerm && (
                          <button className="clear-search" onClick={() => setSearchTerm("")}>
                            <X size={18} />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {isLoadingStudies ? (
                  <div className="loading-studies">
                    <Loader2 size={32} className="animate-spin" />
                    Loading studies...
                  </div>
                ) : filteredStudies && filteredStudies.length > 0 ? (
                  <DataTable
                    data={filteredStudies}
                    columns={columns}
                    actions={actions}
                    defaultItemsPerPage={10}
                    itemsPerPageOptions={[5, 10, 25, 50]}
                    noDataMessage="No studies available"
                  />
                ) : (
                  <div className="no-studies">
                    <ClipboardList size={48} />
                    {searchTerm ? "No studies match your search criteria" : "No studies available."}
                  </div>
                )}

                {/* Visits List for each study */}
                {filteredStudies && filteredStudies.map((study: Study) => 
                  showVisitsList[study.uuid] && (
                    <VisitsListSection 
                      key={study.uuid} 
                      study={study} 
                      onAddVisit={handleOpenVisitModal}
                      onEditVisit={(visit) => {
                        setCurrentVisit(visit);
                        setIsVisitModalOpen(true);
                      }}
                      onDeleteVisit={handleDeleteVisit}
                    />
                  )
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Visit Modal */}
      <VisitModal
        isOpen={isVisitModalOpen}
        onClose={handleCloseVisitModal}
        onSave={handleSaveVisit}
        initialData={currentVisit || { study_uuid: selectedStudyForVisit?.uuid }}
        availableTests={availableTests}
        isEditing={!!currentVisit}
      />
    </Wrapper>
  );
}

export default SponsorStudiesSection;
