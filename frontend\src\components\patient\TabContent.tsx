import React, { useState } from "react";
import { motion } from "framer-motion";
import ReportSection from "./ReportSection";
import DiarySection from "@/pages/patient-clinical/diary";
import ConsentSection from "@/pages/patient-clinical/consent";
import BporStudyWidget from "@/components/BporStudyWidget";
import "./TabContent.css";

interface TabContentProps {
  activeTab: string;
}

const TabContent: React.FC<TabContentProps> = ({ activeTab }) => {
  // State for study search
  const [studyQuery, setStudyQuery] = useState("");
  const [studyDistance, setStudyDistance] = useState("20");
  const [studyLocation, setStudyLocation] = useState("London");
  const [searchParams, setSearchParams] = useState({
    query: "",
    distance: "20",
    location: "London"
  });

  // Handle study search
  const handleStudySearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchParams({
      query: studyQuery,
      distance: studyDistance,
      location: studyLocation
    });
  };

  // Render content based on active tab
  switch (activeTab) {
    case 'activities':
      return (
        <motion.div
          className="patclin-tab-content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="patclin-section-title">Recommended Activities</h2>
          <div className="patclin-empty-state">
            <p>You have no recommended activities at this time.</p>
          </div>
        </motion.div>
      );
    case 'report':
      return <ReportSection />;
    case 'conversations':
      return (
        <motion.div
          className="patclin-tab-content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="patclin-section-title">My Conversations</h2>
          <div className="patclin-empty-state">
            <p>You have no active conversations at this time.</p>
          </div>
        </motion.div>
      );
    case 'studies':
      return (
        <motion.div
          className="patclin-tab-content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="patclin-section-title">My Studies</h2>
          <div className="patclin-empty-state">
            <p>You are not enrolled in any studies at this time.</p>
          </div>
        </motion.div>
      );
    case 'diary':
      return <DiarySection />;
    case 'appointments':
      return (
        <motion.div
          className="patclin-tab-content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="patclin-section-title">My Appointments</h2>
          <div className="patclin-empty-state">
            <p>You have no upcoming appointments.</p>
          </div>
        </motion.div>
      );
    case 'resources':
      return (
        <motion.div
          className="patclin-tab-content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="patclin-section-title">Resources</h2>
          <div className="patclin-empty-state">
            <p>Browse helpful resources and information about your health.</p>
          </div>
        </motion.div>
      );
    case 'consent':
      return <ConsentSection />;
    case 'show-interest':
      return (
        <motion.div
          className="patclin-tab-content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <h2 className="patclin-section-title">Show Interest in Studies</h2>
          
          <form className="patclin-search-form" onSubmit={handleStudySearch}>
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="studyQuery">Search Studies</label>
                <input
                  type="text"
                  id="studyQuery"
                  value={studyQuery}
                  onChange={(e) => setStudyQuery(e.target.value)}
                  placeholder="Enter keywords..."
                />
              </div>
              <div className="form-group">
                <label htmlFor="studyDistance">Distance (miles)</label>
                <input
                  type="number"
                  id="studyDistance"
                  value={studyDistance}
                  onChange={(e) => setStudyDistance(e.target.value)}
                  min="1"
                  max="100"
                />
              </div>
              <div className="form-group">
                <label htmlFor="studyLocation">Location</label>
                <input
                  type="text"
                  id="studyLocation"
                  value={studyLocation}
                  onChange={(e) => setStudyLocation(e.target.value)}
                  placeholder="Enter city..."
                />
              </div>
            </div>
            <button type="submit" className="patclin-search-button">
              Search Studies
            </button>
          </form>

          <div className="patclin-widget-container">
            <BporStudyWidget 
              distance={searchParams.distance} 
              query={searchParams.query} 
              location={searchParams.location} 
            />
          </div>
        </motion.div>
      );
    default:
      return null;
  }
};

export default TabContent;
