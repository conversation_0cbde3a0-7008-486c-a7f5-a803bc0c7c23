import React, { useEffect, useState } from 'react';
import './PrescritionDescriptionModal.css';
import { CheckCircle, Lock } from 'lucide-react';
import { PrescriptionWithDetails } from "@/services/api/types";
import { format } from 'date-fns';
import { useUpdatePrescriptionMutation } from "@/hooks/prescription.query";

interface PrescriptionDescriptionModalProps {
  isOpen: boolean;
  setIsModalOpen: (value: boolean) => void;
  prescriptionDetails: PrescriptionWithDetails | null;
  initialStatus?: string;
  userIdentifier: string | null;
  setPrescriptionUpdated: (value: boolean) => void;
  role?:string;
}

const statusOrder = ["Prescribed", "Prepared", "Collected", "Administered", "Checked"] as const;
type Status = typeof statusOrder[number];

const PrescriptionDescriptionModal: React.FC<PrescriptionDescriptionModalProps> = ({
  isOpen,
  setIsModalOpen,
  prescriptionDetails,
  userIdentifier,
  setPrescriptionUpdated,
  role,
}) => {
  const [status, setStatus] = useState<Status>("Prescribed");
  const [userActions, setUserActions] = useState<Set<string>>(new Set()); // Track user's actions locally
  const { mutateAsync: updatePrescriptionMutation, data=[], isPending } = useUpdatePrescriptionMutation();

  useEffect(() => {
    if (prescriptionDetails && prescriptionDetails.status) {
      setStatus(
        prescriptionDetails.status.charAt(0).toUpperCase() + prescriptionDetails.status.slice(1).toLowerCase() as Status
      );
      // Initialize user actions based on initial prescriptionDetails
      const actions = new Set<string>();
      if (prescriptionDetails.prescribed_by?.identifier === userIdentifier) actions.add("Prescribed");
      if (prescriptionDetails.prepared_by?.identifier === userIdentifier) actions.add("Prepared");
      if (prescriptionDetails.collected_by?.identifier === userIdentifier) actions.add("Collected");
      if (prescriptionDetails.administered_by?.identifier === userIdentifier) actions.add("Administered");
      if (prescriptionDetails.checked_by?.identifier === userIdentifier) actions.add("Checked");
      setUserActions(actions);
    }
  }, [prescriptionDetails, userIdentifier]);

  const handleUpdateStatus = async () => {
    if (!prescriptionDetails?.uuid || !userIdentifier) {
      console.error("UUID or userIdentifier is missing");
      return;
    }

    const currentIndex = statusOrder.indexOf(status);
    if (currentIndex >= statusOrder.length - 1) return;

    const nextStatus = statusOrder[currentIndex + 1];
    const statusPayload: Record<string, string> = { status: nextStatus.toLowerCase() };

    switch (nextStatus) {
      case 'Prepared':
        statusPayload.preparer_identifier = userIdentifier;
        break;
      case 'Collected':
        statusPayload.collector_identifier = userIdentifier;
        break;
      case 'Administered':
        statusPayload.administrator_identifier = userIdentifier;
        break;
      case 'Checked':
        statusPayload.checker_identifier = userIdentifier;
        break;
      default:
        break;
    }

    try {
      await updatePrescriptionMutation({
        uuid: prescriptionDetails.uuid,
        data: statusPayload,
      });
      setStatus(nextStatus);
      setUserActions((prev) => new Set(prev).add(nextStatus)); // Record the user's action locally
      setPrescriptionUpdated(true);
    } catch (error) {
      console.error("Error updating prescription status:", error);
    }
  };

  const steps = [
    {
      label: 'Prescribed',
      name: data?.prescribed_by 
        ? `${data.prescribed_by.first_name} ${data.prescribed_by.last_name}`.trim()
        : prescriptionDetails?.prescribed_by 
          ? `${prescriptionDetails.prescribed_by.first_name} ${prescriptionDetails.prescribed_by.last_name}`.trim()
          : 'Unknown',
      date: data?.prescribed_at 
        ? format(new Date(data.prescribed_at), 'yyyy-MM-dd')
        : prescriptionDetails?.prescribed_at 
          ? format(new Date(prescriptionDetails.prescribed_at), 'yyyy-MM-dd')
          : 'N/A',
      time: data?.prescribed_at 
        ? format(new Date(data.prescribed_at), 'hh:mm a')
        : prescriptionDetails?.prescribed_at 
          ? format(new Date(prescriptionDetails.prescribed_at), 'hh:mm a')
          : 'N/A',
    },
    {
      label: 'Prepared',
      name: data?.prepared_by 
        ? `${data.prepared_by.first_name} ${data.prepared_by.last_name}`.trim()
        : prescriptionDetails?.prepared_by 
          ? `${prescriptionDetails.prepared_by.first_name} ${prescriptionDetails.prepared_by.last_name}`.trim()
          : 'Pharmacy Team',
      date: data?.prepared_at 
        ? format(new Date(data.prepared_at), 'yyyy-MM-dd')
        : prescriptionDetails?.prepared_at 
          ? format(new Date(prescriptionDetails.prepared_at), 'yyyy-MM-dd')
          : 'N/A',
      time: data?.prepared_at 
        ? format(new Date(data.prepared_at), 'hh:mm a')
        : prescriptionDetails?.prepared_at 
          ? format(new Date(prescriptionDetails.prepared_at), 'hh:mm a')
          : 'N/A',
    },
    {
      label: 'Collected',
      name: data?.collected_by 
        ? `${data.collected_by.first_name} ${data.collected_by.last_name}`.trim()
        : prescriptionDetails?.collected_by 
          ? `${prescriptionDetails.collected_by.first_name} ${prescriptionDetails.collected_by.last_name}`.trim()
          : 'Nurse',
      date: data?.collected_at 
        ? format(new Date(data.collected_at), 'yyyy-MM-dd')
        : prescriptionDetails?.collected_at 
          ? format(new Date(prescriptionDetails.collected_at), 'yyyy-MM-dd')
          : 'N/A',
      time: data?.collected_at 
        ? format(new Date(data.collected_at), 'hh:mm a')
        : prescriptionDetails?.collected_at 
          ? format(new Date(prescriptionDetails.collected_at), 'hh:mm a')
          : 'N/A',
    },
    {
      label: 'Administered',
      name: data?.administered_by 
        ? `${data.administered_by.first_name} ${data.administered_by.last_name}`.trim()
        : prescriptionDetails?.administered_by 
          ? `${prescriptionDetails.administered_by.first_name} ${prescriptionDetails.administered_by.last_name}`.trim()
          : 'Doctor',
      date: data?.administered_at 
        ? format(new Date(data.administered_at), 'yyyy-MM-dd')
        : prescriptionDetails?.administered_at 
          ? format(new Date(prescriptionDetails.administered_at), 'yyyy-MM-dd')
          : 'N/A',
      time: data?.administered_at 
        ? format(new Date(data.administered_at), 'hh:mm a')
        : prescriptionDetails?.administered_at 
          ? format(new Date(prescriptionDetails.administered_at), 'hh:mm a')
          : 'N/A',
    },
    {
      label: 'Checked',
      name: data?.checked_by 
        ? `${data.checked_by.first_name} ${data.checked_by.last_name}`.trim()
        : prescriptionDetails?.checked_by 
          ? `${prescriptionDetails.checked_by.first_name} ${prescriptionDetails.checked_by.last_name}`.trim()
          : 'Inspector',
      date: data?.checked_at 
        ? format(new Date(data.checked_at), 'yyyy-MM-dd')
        : prescriptionDetails?.checked_at 
          ? format(new Date(prescriptionDetails.checked_at), 'yyyy-MM-dd')
          : 'N/A',
      time: data?.checked_at 
        ? format(new Date(data.checked_at), 'hh:mm a')
        : prescriptionDetails?.checked_at 
          ? format(new Date(prescriptionDetails.checked_at), 'hh:mm a')
          : 'N/A',
    },
  ];

 

  if (!isOpen) return null;

  const onClose = () => {
    setIsModalOpen(false);
  };

  const renderStepIcon = (stepLabel: string) => {
    const currentStatusIndex = statusOrder.indexOf(status);
    const stepIndex = statusOrder.indexOf(stepLabel as Status);

    if (stepIndex < currentStatusIndex || status === stepLabel) {
      return <CheckCircle size={18} className="green-check" />;
    } else if (stepIndex === currentStatusIndex + 1) {
      return <div className="loader"></div>;
    } else {
      return <Lock size={18} />;
    }
  };

  const prescriptionDescription = prescriptionDetails
    ? `${prescriptionDetails.drug_name} 
    ${prescriptionDetails.dose} ${prescriptionDetails.unit} 
    via ${prescriptionDetails.route} every ${prescriptionDetails.frequency} 
    for ${prescriptionDetails.patient.first_name} ${prescriptionDetails.patient.last_name} 
    with height ${prescriptionDetails.height}meters, weight ${prescriptionDetails.weight}kg.`
    : 'No prescription details available';

    const renderActionButton = () => {
      if (!prescriptionDetails || !userIdentifier) return null;
    
      const userPrescribed = userActions.has("Prescribed");
      const userPrepared = userActions.has("Prepared");
      const userCollected = userActions.has("Collected");
      const userAdministered = userActions.has("Administered");
    
      // If user is pharmacy, they can only update from Prescribed to Prepared
      if (role === 'pharmacy') {
        if (status === 'Prescribed' && !userPrescribed) {
          return (
            <button
              onClick={handleUpdateStatus}
              disabled={isPending}
            >
              {isPending ? 'Updating...' : 'Mark as Prepared'}
            </button>
          );
        }
        return null;
      }
    
      // Original logic for other roles
      if (status === 'Prescribed') {
        if (!userPrescribed) {
          return (
            <button
              onClick={handleUpdateStatus}
              disabled={isPending}
            >
              {isPending ? 'Updating...' : 'Mark as Prepared'}
            </button>
          );
        }
        return null;
      } else if (status === 'Prepared') {
        if (!userPrescribed && !userPrepared) {
          return (
            <button
              onClick={handleUpdateStatus}
              disabled={isPending}
            >
              {isPending ? 'Updating...' : 'Mark as Collected'}
            </button>
          );
        }
        return null;
      } else if (status === 'Collected') {
        if (!userPrescribed && !userPrepared) { // Allow same user who collected to administer
          return (
            <button
              onClick={handleUpdateStatus}
              disabled={isPending}
            >
              {isPending ? 'Updating...' : 'Mark as Administered'}
            </button>
          );
        }
        return null;
      } else if (status === 'Administered') {
        if (!userPrescribed && !userPrepared && !userCollected && !userAdministered) {
          return (
            <button
              onClick={handleUpdateStatus}
              disabled={isPending}
            >
              {isPending ? 'Updating...' : 'Mark as Checked'}
            </button>
          );
        }
        return null;
      }
      return null;
    };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Prescription Description</h2>
        </div>
        <div className="modal-body">
          <p className="prescription-summary">{prescriptionDescription}</p>
          <div className="prescription-log">
            {steps.map((step, index) => (
              <div key={index} className="log-step">
                <div className="step-icon">{renderStepIcon(step.label)}</div>
                <div className="step-details">
                  {step.label === 'Prescribed' && (
                    <span>
                      Prescribed by {step.name} on {step.date} at {step.time}
                    </span>
                  )}
                  {step.label === 'Prepared' && (
                    <span>
                      {status === 'Prescribed' ? (
                        'Pharmacy Preparing'
                      ) : (
                        `Prepared by ${step.name} on ${step.date} at ${step.time}`
                      )}
                    </span>
                  )}
                  {step.label === 'Collected' && (
                    <span>
                      {status === 'Prescribed' ? (
                        'Collected'
                      ) : status === 'Prepared' ? (
                        'Collecting'
                      ) : (
                        `Collected by ${step.name} on ${step.date} at ${step.time}`
                      )}
                    </span>
                  )}
                  {step.label === 'Administered' && (
                    <span>
                      {['Prescribed', 'Prepared'].includes(status) ? (
                        'Administered'
                      ) : status === 'Collected' ? (
                        'Administering'
                      ) : (
                        `Administered by ${step.name} on ${step.date} at ${step.time}`
                      )}
                    </span>
                  )}
                  {step.label === 'Checked' && (
                    <span>
                      {['Prescribed', 'Prepared', 'Collected'].includes(status) ? (
                        'Checked'
                      ) : status === 'Administered' ? (
                        'Checking'
                      ) : (
                        `Checked by ${step.name} on ${step.date} at ${step.time}`
                      )}
                    </span>
                  )}
                </div>
                {index < steps.length - 1 && <div className="step-line"></div>}
              </div>
            ))}
          </div>
        </div>
        <div className="modal-footer">
          
          {renderActionButton()}
          <button className="btn btn-primary-nurtify" onClick={onClose}>
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default PrescriptionDescriptionModal;