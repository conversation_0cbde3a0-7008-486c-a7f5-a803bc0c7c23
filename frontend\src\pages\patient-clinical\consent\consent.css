/* Patient Consent Management Styles */
.consent-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.consent-header {
  margin-bottom: 32px;
  text-align: center;
}

.consent-header h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 12px;
  color: #1f2937;
}

.consent-description {
  color: #6b7280;
  font-size: 1.1rem;
  line-height: 1.6;
  margin: 0;
}

/* Loading and Error States */
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #37b7c3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state h3,
.empty-state h3 {
  margin: 16px 0 8px 0;
  color: #1f2937;
  font-size: 1.25rem;
}

.error-state p,
.empty-state p {
  color: #6b7280;
  margin: 0;
}

/* Alert Messages */
.consent-alert {
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  animation: slideIn 0.3s ease-out;
}

.consent-alert-error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.consent-alert-success {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #16a34a;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Consent Forms Section */
.consent-forms-section {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.patient-info {
  margin-bottom: 32px;
  text-align: center;
}

.patient-info h3 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.patient-info p {
  margin: 0;
  color: #6b7280;
  font-size: 1.1rem;
}

.consent-forms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.consent-form-card {
  background: #f9fafb;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  transition: all 0.2s ease;
}

.consent-form-card:hover {
  border-color: #37b7c3;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.15);
  transform: translateY(-2px);
}

.consent-form-card.completed {
  border-color: #10b981;
  background: #f0fdf4;
}

.consent-form-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.consent-form-info {
  flex: 1;
}

.consent-form-info h4 {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
}

.consent-form-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-wrap: wrap;
}

.version-badge {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.questions-count {
  background: #f3f4f6;
  color: #6b7280;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.completed {
  background: #10b981;
  color: white;
}

.consent-form-description {
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 20px 0;
  font-size: 0.95rem;
}

.consent-form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.completion-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #10b981;
  font-size: 0.9rem;
  font-weight: 500;
}

.start-consent-btn {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.start-consent-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(55, 183, 195, 0.3);
}

.start-consent-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Consent Questions Flow */
.consent-questions-flow {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  margin-bottom: 24px;
}

.consent-progress {
  margin-bottom: 32px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 12px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 0.9rem;
  color: #6b7280;
  font-weight: 500;
}

/* Question Card */
.question-card {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.question-number {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
}

.required-badge {
  background: #ef4444;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.question-content {
  margin-bottom: 24px;
}

.question-text {
  font-size: 1.1rem;
  color: #1f2937;
  line-height: 1.6;
  margin: 0;
  font-weight: 500;
}

/* Answer Options */
.answer-options {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.answer-option {
  flex: 1;
  min-width: 120px;
}

.answer-option input[type="radio"] {
  display: none;
}

.answer-label {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
  font-weight: 500;
  color: #374151;
}

.answer-label:hover {
  border-color: #37b7c3;
  background: #f0fdf4;
}

.answer-option input[type="radio"]:checked + .answer-label {
  border-color: #37b7c3;
  background: #f0fdf4;
  color: #37b7c3;
}

.answer-option input[type="radio"]:checked + .answer-label svg {
  color: #37b7c3;
}

/* Navigation */
.consent-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.nav-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.prev-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.prev-btn:hover:not(:disabled) {
  background: #e5e7eb;
  color: #374151;
}

.next-btn {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
}

.next-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(55, 183, 195, 0.3);
}

.submit-btn {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.nav-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Consent Completed */
.consent-completed {
  background: white;
  border-radius: 12px;
  padding: 48px 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  text-align: center;
  margin-bottom: 24px;
}

.completed-icon {
  margin-bottom: 24px;
  color: #10b981;
}

.consent-completed h3 {
  margin: 0 0 16px 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
}

.consent-completed p {
  margin: 0 0 32px 0;
  color: #6b7280;
  font-size: 1.1rem;
}

.completion-details {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin-bottom: 32px;
  flex-wrap: wrap;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 0.9rem;
}

.reset-consent-btn {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
  border: none;
  padding: 16px 32px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.reset-consent-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(55, 183, 195, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .consent-container {
    padding: 16px;
  }

  .consent-forms-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .consent-form-card {
    padding: 20px;
  }

  .consent-form-header {
    flex-direction: column;
    gap: 12px;
  }

  .consent-form-meta {
    justify-content: flex-start;
  }

  .answer-options {
    flex-direction: column;
  }

  .answer-option {
    min-width: auto;
  }

  .consent-navigation {
    flex-direction: column;
    gap: 12px;
  }

  .nav-btn {
    width: 100%;
    justify-content: center;
  }

  .completion-details {
    flex-direction: column;
    gap: 16px;
  }

  .consent-questions-flow,
  .consent-completed {
    padding: 20px;
  }

  .question-card {
    padding: 20px;
  }

  .question-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}