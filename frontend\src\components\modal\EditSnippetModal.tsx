import React, { useState, useEffect } from 'react';
import './AddSnippetModal.css';
import { Blocks, Loader2 } from 'lucide-react';

interface EditSnippetModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (title: string, subject: string, description: string) => void;
  snippet: {
    title: string;
    subject: string;
    description: string;
  } | null;
  isLoading?: boolean;
}

const EditSnippetModal: React.FC<EditSnippetModalProps> = ({ 
  isOpen, 
  onClose, 
  onSave, 
  snippet,
  isLoading = false 
}) => {
  const [title, setTitle] = useState('');
  const [subject, setSubject] = useState('');
  const [description, setDescription] = useState('');

  useEffect(() => {
    if (snippet) {
      setTitle(snippet.title);
      setSubject(snippet.subject);
      setDescription(snippet.description);
    }
  }, [snippet]);

  if (!isOpen) return null;

  const handleSave = () => {
    if (!isLoading) {
      onSave(title, subject, description);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="add-snippet-modal-content">
        <h2 className="modal-title">Edit Snippet</h2>
        <input
          type="text"
          placeholder="Snippet Title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          disabled={isLoading}
        />
        <input
          type="text"
          placeholder="Snippet Subject"
          value={subject}
          onChange={(e) => setSubject(e.target.value)}
          disabled={isLoading}
        />
        <textarea
          placeholder="Snippet Description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          disabled={isLoading}
        />
        <div className="modal-actions">
          <button 
            className="cancel-snippet-button" 
            onClick={onClose}
            disabled={isLoading}
            style={{ opacity: isLoading ? 0.7 : 1, cursor: isLoading ? 'not-allowed' : 'pointer' }}
          >
            Cancel
          </button>
          <button 
            className="save-snippet-button" 
            onClick={handleSave}
            disabled={isLoading}
            style={{ 
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              opacity: isLoading ? 0.7 : 1, 
              cursor: isLoading ? 'not-allowed' : 'pointer' 
            }}
          >
            {isLoading ? (
              <>
                <Loader2 size={16} className="animate-spin" style={{ marginRight: "5px" }} />
                Saving...
              </>
            ) : (
              <>
                Save <Blocks size={16} style={{ marginLeft: "5px" }} />
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EditSnippetModal;
