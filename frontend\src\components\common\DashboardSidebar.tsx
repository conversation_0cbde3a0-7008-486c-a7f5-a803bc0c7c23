import React, { useState, useEffect, useCallback } from 'react';
import { ChevronLeft, Menu, X, User } from 'lucide-react';
import { useLocation, useNavigate } from 'react-router-dom';
import useSelectedPatientStore from '@/store/SelectedPatientState';
import { getProfilePictureUrl } from '@/utils/imageUtils';

import './DashboardSidebar.css';

interface MenuItem {
  icon: React.ReactNode;
  label: string;
  path: string;
  onClick?: () => void;
}

interface PatientInfo {
  name: string;
  gender: string;
  age: string;
  nhsNumber: string;
  dob: string;
  mrn: string;
  ethnicity: string;
  email: string;
  phone: string;
  avatar?: string;
}

interface DashboardSidebarProps {
  menuItems: MenuItem[];
  basePath?: string;
  patientInfo?: PatientInfo;
  onProfilePictureClick?: () => void;
}

const DashboardSidebar: React.FC<DashboardSidebarProps> = ({ menuItems, basePath, patientInfo, onProfilePictureClick }) => {
  const [isCollapsed, setIsCollapsed] = useState(() => {
    const stored = localStorage.getItem('sidebarCollapsed') === 'true';
    return stored || window.innerWidth <= 991; // Default to collapsed on mobile
  });
  const [isMobileActive, setIsMobileActive] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 991);
  const [imageError, setImageError] = useState(false);

  const location = useLocation();
  const navigate = useNavigate();
  const { clearSelectedPatient } = useSelectedPatientStore();

  const handleResize = useCallback(() => {
    const mobile = window.innerWidth <= 991;
    setIsMobile(mobile);
    if (!mobile && isMobileActive) {
      setIsMobileActive(false);
    }
  }, [isMobileActive]);

  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  useEffect(() => {
    localStorage.setItem('sidebarCollapsed', isCollapsed.toString());
  }, [isCollapsed]);

  const toggleSidebar = () => {
    if (isMobile) {
      setIsMobileActive(!isMobileActive);
      setIsCollapsed(!isCollapsed); // Toggle collapsed state in mobile view
    } else {
      setIsCollapsed(!isCollapsed);
    }
  };

  const handleItemClick = (item: MenuItem) => {
    if (item.onClick) {
      item.onClick();
    } else {
      const fullPath = `${basePath}${item.path}`;
      navigate(fullPath);
    }
    if (isMobile) {
      setIsMobileActive(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent, item: MenuItem) => {
    if (e.key === 'Enter' || e.key === ' ') {
      handleItemClick(item);
    }
  };

  const handleClosePatientPortal = () => {
    clearSelectedPatient();
    navigate('/patients');
  };

  return (
    <>
      <div
        className={`sidebar-dsbh ${isCollapsed ? 'collapsed' : ''} ${
          isMobileActive ? 'active' : ''
        } ${isMobile ? 'mobile' : ''}`}
        role="navigation"
        aria-label="Main navigation"
      >
        <div className="sidebar-header">
          <button 
            className={`minimize-btn ${isCollapsed ? 'collapsed' : ''}`} 
            onClick={toggleSidebar}
            title={isCollapsed ? 'Expand sidebar' : 'Minimize sidebar'}
          >
            {isCollapsed ? <Menu size={16} /> : <ChevronLeft size={16} />}
            {!isCollapsed && <span className="minimize-text">Minimize</span>}
          </button>
        </div>
        
        {patientInfo && !isCollapsed && (
          <div className="patient-info-section">
            <div className="patient-header">
              <div 
                className="patient-avatar"
                onClick={onProfilePictureClick}
                style={{ cursor: onProfilePictureClick ? 'pointer' : 'default' }}
                title={onProfilePictureClick ? 'Click to change profile picture' : undefined}
              >
                {patientInfo.avatar && !imageError ? (
                  <img 
                    src={getProfilePictureUrl(patientInfo.avatar) || ''}
                    alt={patientInfo.name}
                    onError={() => setImageError(true)}
                  />
                ) : (
                  <div className="avatar-placeholder">
                    <User size={24} />
                  </div>
                )}
              </div>
              <div className="patient-basic-info">
                <h3 className="patient-name">{patientInfo.name}</h3>
                <p className="patient-demographics">{patientInfo.gender} | {patientInfo.age}</p>
              </div>
            </div>
            <div className="patient-details compact-patient-details">
              <div className="detail-row">
                <span className="detail-label">NHS #</span>
                <a className="detail-value nhs-link" href={`#nhs-${patientInfo.nhsNumber}`}>{patientInfo.nhsNumber}</a>
              </div>
              <div className="detail-row">
                <span className="detail-label">DOB:</span>
                <span className="detail-value">{patientInfo.dob}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">MRN:</span>
                <span className="detail-value">{patientInfo.mrn}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Ethnicity:</span>
                <span className="detail-value">{patientInfo.ethnicity}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Email:</span>
                <span className="detail-value">{patientInfo.email}</span>
              </div>
              <div className="detail-row">
                <span className="detail-label">Phone:</span>
                <span className="detail-value">{patientInfo.phone}</span>
              </div>
            </div>
          </div>
        )}
        
        <ul className="menuList">
          {menuItems.map((item, index) => (
            <li
              key={index}
              onClick={() => handleItemClick(item)}
              onKeyPress={(e) => handleKeyPress(e, item)}
              role="button"
              style={{ width: '95%' }}
              tabIndex={0}
              aria-current={location.pathname.includes(item.path) ? 'page' : undefined}
              className={location.pathname.includes(item.path) ? 'active' : ''}
            >
              {item.icon}
              <span className="menu-label">{item.label}</span>
            </li>
          ))}
        </ul>
        <button 
          className="close-patient-portal"
          onClick={handleClosePatientPortal}
          title="Close Patient Portal"
        >
          <X size={16} />
          <span>Close Patient Portal</span>
        </button>
      </div>
      {isMobile && (
        <div
          className={`overlay ${isMobileActive ? 'active' : ''}`}
          onClick={() => setIsMobileActive(false)}
          role="presentation"
        />
      )}
    </>
  );
};

export default DashboardSidebar;
