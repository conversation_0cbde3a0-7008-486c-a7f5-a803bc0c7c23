/* Page Layout */
.dashboard__content {
  padding: 40px;
  min-height: calc(100vh - 80px); /* Adjust based on your header height */
  background-color: var(--color-light-4);
}

@media (max-width: 991px) {
  .dashboard__content {
    padding: 30px 20px;
  }
}

/* Sidebar Styles */
.sidebar.-dashboard {
  position: sticky;
  top: 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  transition: all 0.3s ease;
  border: 1px solid rgba(55, 183, 195, 0.1);
}

.sidebar__item {
  padding: 24px;
}

/* Sidebar Header */
.sidebar__header {
  margin-bottom: 28px;
}

.sidebar__title {
  font-size: 22px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin-bottom: 16px;
  position: relative;
  padding-bottom: 12px;
}

.sidebar__title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--color-purple-1);
  border-radius: 2px;
}

.sidebar__subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 24px 0 16px 0;
}

/* Search Field */
.sidebar__search {
  margin-bottom: 24px;
}

.search-field {
  position: relative;
  margin-top: 16px;
}

.search-input {
  width: 100%;
  padding: 16px 18px;
  padding-left: 48px;
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 12px;
  font-size: 15px;
  transition: all 0.2s ease;
  background-color: rgba(55, 183, 195, 0.03);
  color: var(--color-dark-1);
}

.search-input:focus {
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.1);
  background-color: #fff;
  outline: none;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 18px;
}

/* Checkbox Container */
.sidebar-checkbox {
  max-height: calc(100vh - 250px);
  overflow-y: auto;
  padding: 8px 4px;
}

/* Scrollbar Styling */
.sidebar-checkbox::-webkit-scrollbar {
  width: 6px;
}

.sidebar-checkbox::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.sidebar-checkbox::-webkit-scrollbar-thumb {
  background: #37B7C3;
  border-radius: 3px;
}

.sidebar-checkbox::-webkit-scrollbar-thumb:hover {
  background: #2d919a;
}

/* Checkbox Styling */
.form-checkbox {
  position: relative;
  display: flex;
  align-items: center;
  padding: 12px 16px;
  margin-bottom: 10px;
  border-radius: 12px;
  transition: all 0.2s ease;
  background-color: #fff;
  border: 1px solid rgba(55, 183, 195, 0.08);
}

.form-checkbox:hover {
  background-color: rgba(55, 183, 195, 0.04);
  border-color: rgba(55, 183, 195, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(55, 183, 195, 0.1);
}

.form-checkbox__input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.form-checkbox__label {
  position: relative;
  padding-left: 32px;
  cursor: pointer;
  font-size: 15px;
  line-height: 1.5;
  color: var(--color-dark-1);
  user-select: none;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0;
  font-weight: 500;
}

.form-checkbox__label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: 2px solid rgba(55, 183, 195, 0.4);
  border-radius: 6px;
  background-color: #fff;
  transition: all 0.2s ease;
  box-shadow: inset 0 1px 3px rgba(55, 183, 195, 0.1);
}

.form-checkbox__input:checked + .form-checkbox__label:before {
  background-color: var(--color-purple-1);
  border-color: var(--color-purple-1);
  box-shadow: 0 2px 4px rgba(55, 183, 195, 0.2);
}

.form-checkbox__input:checked + .form-checkbox__label:after {
  content: '';
  position: absolute;
  left: 7px;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
}

.form-checkbox__count {
  font-size: 13px;
  color: var(--color-purple-1);
  background: rgba(55, 183, 195, 0.08);
  padding: 5px 12px;
  border-radius: 20px;
  font-weight: 600;
  transition: all 0.2s ease;
  min-width: 32px;
  text-align: center;
}

.form-checkbox:hover .form-checkbox__count {
  background: rgba(55, 183, 195, 0.15);
}

/* Tags Styling */
.tags-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 16px;
}

.tag-item {
  display: inline-block;
  padding: 10px 16px;
  background: rgba(55, 183, 195, 0.08);
  color: var(--color-purple-1);
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(55, 183, 195, 0.12);
}

.tag-item:hover {
  background: rgba(55, 183, 195, 0.15);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(55, 183, 195, 0.12);
}

/* Form Cards Grid */
.forms-grid {
  display: grid;
  gap: 30px;
  grid-template-columns: repeat(4, 1fr);
  margin-bottom: 40px;
}

/* Responsive Adjustments */
@media (max-width: 1400px) {
  .forms-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 991px) {
  .forms-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 767px) {
  .forms-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

/* Form Card Animation */
.forms-grid > * {
  animation: fadeIn 0.4s ease-out forwards;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Transitions and Hover Effects */
.hover-shadow-2 {
  transition: all 0.3s ease;
}

.hover-shadow-2:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(55, 183, 195, 0.12) !important;
}

/* Utility Classes */
.shadow-1 {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.border-light {
  border: 1px solid rgba(0, 0, 0, 0.08);
}

.bg-light-4 {
  background-color: #f5f7fa;
}

/* Loading States */
.form-checkbox__input:disabled + .form-checkbox__label {
  opacity: 0.5;
  cursor: not-allowed;
}

.tabs-wrapper {
  display: flex;
  gap: 2.5rem;
  border-bottom: 1px solid rgba(55, 183, 195, 0.2);
  padding-bottom: 0;
  margin-bottom: 30px;
}

.tab-button {
  background: none;
  border: none;
  padding: 1rem 0.5rem;
  font-size: 1.1rem;
  font-weight: 500;
  color: var(--color-light-1);
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: var(--color-dark-1);
}

.tab-button.active {
  color: var(--color-purple-1);
  font-weight: 600;
}

.tab-button.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: var(--color-purple-1);
  border-radius: 3px 3px 0 0;
}

.tab-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.open-btn {
  min-width: 120px;
  height: 55px;
  border-radius: 12px;
  background-color: var(--color-purple-1);
  color: #fff;
  border: none;
  font-weight: 500;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.2);
}

.open-btn:hover {
  background-color: #2d919a;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(55, 183, 195, 0.3);
}
