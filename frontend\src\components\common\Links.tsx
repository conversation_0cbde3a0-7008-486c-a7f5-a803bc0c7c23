import { Link } from "react-router-dom";

interface LinksProps {
  allClasses?: string;
}

interface LinkItem {
  id: number;
  href: string;
  label: string;
}

export default function Links({ allClasses }: LinksProps) {
    const links: LinkItem[] = [
        { id: 1, href: "/terms-and-conditions", label: "Help" },
        { id: 2, href: "/privacy", label: "Privacy Policy" },
        { id: 3, href: "/terms-and-conditions", label: "Cookie Notice" },
        { id: 4, href: "/terms-and-conditions", label: "Security" },
        { id: 5, href: "/terms-and-conditions", label: "Terms of Use" },
    ];
      
    return (
        <>
            {links.map((link) => (
                <Link
                    className={allClasses || ""}
                    key={link.id}
                    to={link.href}
                >
                    {link.label}
                </Link>
            ))}
        </>
    );
}
