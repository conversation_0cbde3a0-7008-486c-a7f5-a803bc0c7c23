import "./hospital-details.css";
import { useState, useRef, useEffect } from "react";
import { Pencil, PencilOff } from "lucide-react";
import NurtifyText from "@components/NurtifyText.tsx";
import NurtifyInput from "@components/NurtifyInput.tsx";
import { motion } from "framer-motion";
import DeleteHospitalModal from "@/components/modal/DeleteHospitalModal"; // Adjusted modal
import { Hospital as HospitalData } from "@/services/api/types.ts";
import {
  useHospitalQuery,
  useUpdateHospitalMutation,
  useDeleteHospitalMutation,
} from "@/hooks/hospital.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { getUserRole, can} from "@/services/permission-system";
import { useNavigate } from "react-router-dom";

interface HospitalDetailsProps {
  uuid: string;
}


const HospitalDetails: React.FC<HospitalDetailsProps> = ({ uuid }) => {
  const navigate = useNavigate();
  const { data: hospitalData, isLoading } = useHospitalQuery(uuid!);
  const updateHospitalMutation = useUpdateHospitalMutation();
  const deleteHospitalMutation = useDeleteHospitalMutation();
  const { data: currentUser} = useCurrentUserQuery();
  const role = getUserRole(currentUser);

  const [editableHospital, setEditableHospital] = useState<HospitalData | null>(null);
  const [changedFields, setChangedFields] = useState<Partial<HospitalData>>({});
  const [isEditing, setIsEditing] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUpdateEnabled, setIsUpdateEnabled] = useState(false);

  const targetRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (hospitalData) {
      setEditableHospital(hospitalData);
    }
  }, [hospitalData]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (!editableHospital || !hospitalData) return;

    const { id, value } = e.target;

    setEditableHospital((prev) => ({
      ...prev!,
      [id]: value,
    }));

    setChangedFields((prev) => {
      const updatedChanges = { ...prev };
      const currentValue = hospitalData[id as keyof HospitalData];
      if (currentValue !== value) {
        updatedChanges[id as keyof HospitalData] = value as any;
      } else {
        delete updatedChanges[id as keyof HospitalData];
      }
      setIsUpdateEnabled(Object.keys(updatedChanges).length > 0);
      return updatedChanges;
    });
  };

  const handleDeleteClick = () => {
    setIsEditing(false);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsEditing(true);
    setIsModalOpen(false);
  };

  const handleSubmitModal = async () => {
    if (!uuid) return;

    try {
      await deleteHospitalMutation.mutateAsync({ uuid });
      navigate("/org/dashboard/hospital", { replace: true });
    } catch (error) {
      console.error("Error deleting hospital:", error);
    }
  };

  const handleScroll = () => {
    if (targetRef.current) {
      targetRef.current.scrollIntoView({ behavior: "smooth" });
    }
  };

  const handleUpdateClick = () => {
    setIsEditing(true);
    handleScroll();
  };

  const handleCancelClick = () => {
    setEditableHospital(hospitalData || null);
    setChangedFields({});
    setIsEditing(false);
    setIsUpdateEnabled(false);
  };

  const handleSaveClick = async () => {
    if (!uuid || !isUpdateEnabled || Object.keys(changedFields).length === 0) {
      console.error("UUID is undefined or no changes to update.");
      return;
    }

    try {
      await updateHospitalMutation.mutateAsync({
        uuid,
        data: changedFields,
      });
      navigate("/org/dashboard/hospital", { replace: true });
    } catch (error) {
      console.error("Error updating hospital:", error);
    }
  };

  const loadingMessage = isLoading ? <p>is loading ...</p> : null;

  return (
    <>
      {loadingMessage}
      {!isLoading && !editableHospital && <p>not found!</p>}
      <div className="editableHospital-container">
        {can(role, "Hospital", "update") && !isLoading && editableHospital && (
          <div className="custom-button">
            {!isEditing && (
              <button
                className="hosp-details-btn-custom gap-2"
                onClick={handleUpdateClick}
              >
                <Pencil size={18} /> Edit
              </button>
            )}
            {isEditing && (
              <button
                className="hosp-details-btn-custom gap-2"
                onClick={handleCancelClick}
              >
                <PencilOff size={18} /> Cancel
              </button>
            )}
          </div>
        )}
      </div>
      <form className="add-policy-form" onSubmit={(e) => e.preventDefault()}>
        <motion.div
          className="mb-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          <div className="row y-gap-30">
            <div className="col-md-6" style={{ marginTop: "10px" }}>
              <NurtifyText label="Hospital Name*" />
              <NurtifyInput
                type="text"
                name="name"
                value={editableHospital?.name}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Hospital Name here"
                required
              />
            </div>
          </div>
          <div className="row y-gap-30" style={{ marginTop: "10px" }}>
            <div className="col-md-6">
              <NurtifyText label="Phone*" />
              <NurtifyInput
                type="number"
                name="phone_number"
                value={editableHospital?.phone_number}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Phone Number here"
                required
              />
            </div>
            <div className="col-md-6">
              <NurtifyText label="Extension*" />
              <NurtifyInput
                type="number"
                name="extension"
                value={editableHospital?.extension}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Extension here"
                required
              />
            </div>
          </div>
          <div className="row y-gap-30">
            <div className="col-md-6" style={{ marginTop: "10px" }}>
              <NurtifyText label="Point of contact Person*" />
              <NurtifyInput
                type="text"
                name="name"
                value={editableHospital?.point_of_contact_Person}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Hospital Name here"
                required
              />
            </div>
          </div>
          <div className="row y-gap-30" style={{ marginTop: "10px" }}>
            <div className="col-md-6">
              <NurtifyText label="Address Line 1*" />
              <div className="hospital-detail-input">
                <textarea
                  id="primary_address"
                  name="primary_address"
                  value={editableHospital?.primary_address || ""}
                  onChange={handleChange}
                  placeholder="Enter Address Line 1"
                  disabled={!isEditing}
                  className={`${!isEditing ? "text-muted" : ""}`}
                  style={{
                    border: "none",
                    outline: "none",
                    backgroundColor: "transparent",
                    padding: "0px",
                  }}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <NurtifyText label="Address Line 2*" />
              <div className="hospital-detail-input">
                <textarea
                  id="secondary_address"
                  name="secondary_address"
                  value={editableHospital?.secondary_address || ""}
                  onChange={handleChange}
                  placeholder="Enter Address Line 2"
                  disabled={!isEditing}
                  className={`${!isEditing ? "text-muted" : ""}`}
                  style={{
                    border: "none",
                    outline: "none",
                    backgroundColor: "transparent",
                    padding: "0px",
                  }}
                  required
                />
              </div>
            </div>
          </div>
          <div className="row y-gap-30" style={{ marginTop: "10px" }}>
            <div className="col-md-6">
              <NurtifyText label="Postcode*" />
              <NurtifyInput
                type="text"
                name="postcode"
                value={editableHospital?.postcode}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter Postcode here"
                required
              />
            </div>
            <div className="col-md-6">
              <NurtifyText label="Country*" />
              <NurtifyInput
                type="text"
                name="country"
                value={editableHospital?.country}
                onChange={handleChange}
                disabled={!isEditing}
                placeholder="Enter country here"
                required
              />
            </div>
          </div>
          <div className="mt-3" style={{ textAlign: "right" }}>
            {isEditing && (
              <>
                <button
                  className="hospital-details-form-btn-custom Delete"
                  onClick={handleDeleteClick}
                >
                  Delete Hospital
                </button>
                <button
                  className="hospital-details-form-btn-custom Update"
                  disabled={!isUpdateEnabled}
                  onClick={handleSaveClick}
                >
                  {updateHospitalMutation.isPending ? "Updating..." : "Update Hospital"}
                </button>
              </>
            )}
          </div>
        </motion.div>
        {isModalOpen && (
          <DeleteHospitalModal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            onDelete={handleSubmitModal}
          />
        )}
      </form>
    </>
  );
};

export default HospitalDetails;