// src/store/holisticFormTabState.tsx
import { create } from "zustand";

interface TabState {
  activeTab: number;
  activeAssessmentSubTab: number;
  setActiveTab: (tab: number) => void;
  setActiveAssessmentSubTab: (subTab: number) => void;
  goToPrevious: () => void;
  goToNext: () => void;
  submitForm: () => void;
}

const TOTAL_ASSESSMENT_SUB_TABS = 17; // Total sub-tabs in Assessment

const useHolisticFormTabStore = create<TabState>((set) => ({
  activeTab: 1,
  activeAssessmentSubTab: 1,
  setActiveTab: (tab: number) => set({ activeTab: tab }),
  setActiveAssessmentSubTab: (subTab: number) => set({ activeAssessmentSubTab: subTab }),
  goToPrevious: () =>
    set((state) => {
      if (state.activeTab === 5) {
        // From Recommendation (Tab 5) to last sub-tab of Assessment (Tab 4)
        return {
          activeTab: 4,
          activeAssessmentSubTab: TOTAL_ASSESSMENT_SUB_TABS,
        };
      }
      // Default behavior: go to previous main tab, reset sub-tab
      return {
        activeTab: Math.max(1, state.activeTab - 1),
        activeAssessmentSubTab: 1,
      };
    }),
  goToNext: () =>
    set((state) => ({
      activeTab: Math.min(5, state.activeTab + 1),
      activeAssessmentSubTab: 1,
    })),
  submitForm: () => {
    console.log("Form submitted!");
    // Add submission logic here
  },
}));

export default useHolisticFormTabStore;