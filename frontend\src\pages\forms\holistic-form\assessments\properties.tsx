import { useState } from "react";
import propertyImage from "./static/images/added/property.png";
import useHolisticFormStore from "@/store/holisticFormState"; // Import Zustand store
import NurtifyTextArea from "@/components/NurtifyTextArea";

const Properties = () => {
  const { assessment, setAssessment } = useHolisticFormStore(); // Use Zustand store
  const [valuableProperties, setValuableProperties] = useState(assessment?.properties?.valuableProperties || "");

  // Function to handle changes in valuable properties
  const handleValuablePropertiesChange = (value: string) => {
    setValuableProperties(value);
    setAssessment({
      ...assessment,
      properties: {
        ...assessment.properties,
        valuableProperties: value
      }
    });
  };

  return (
    <form
      id="division-40"
      className="block align-items-*-start flex-column flex-md-row p-4 mt-5"
    >
      <div className="mb-3">
        <div className="inlineBlock mb-4 headinqQuestion">
          <img
            src={propertyImage}
            className="imageEtiquette"
            alt="Image describing mental health section"
          />
          <span className="mb-2 py-2 fs-2 text-start etiquetteHeadingForms">
            Valuable Properties
          </span>
        </div>

        <div className="list-group me-4 mt-3 col-xl-6 col-lg-8 col-md-12">
          <span className="headingQuestion">
            Please list the valuable patient belongings:
          </span>
          <div className="">
            <NurtifyTextArea
              onChange={(e) => handleValuablePropertiesChange(e.target.value)}
              placeholder="Type here..."
              className="form-control mt-2 w-500"
              value={valuableProperties}
              name="patient_belongings"
            />
          </div>
        </div>
      </div>
    </form>
  );
};

export default Properties;
