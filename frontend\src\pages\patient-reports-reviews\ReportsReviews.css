/* Base styles and variables */
:root {
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --success-color: #10b981;
  --success-hover: #059669;
  --success-light: #dcfce7;
  --warning-color: #f59e0b;
  --warning-hover: #d97706;
  --warning-light: #fef3c7;
  --danger-color: #ef4444;
  --danger-hover: #dc2626;
  --danger-light: #fee2e2;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
}

.reports-reviews-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  color: var(--gray-800);
}

.reports-reviews-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 28px;
}

.reports-reviews-title {
  margin-bottom: 0;
  color: var(--gray-900);
  font-size: 1.75rem;
  font-weight: 600;
  letter-spacing: -0.025em;
  display: flex;
  align-items: center;
  gap: 12px;
}

.patient-name {
  color: var(--gray-600);
  font-size: 1.25rem;
  font-weight: 500;
}

.add-report-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background-color: #20b2aa;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 600;
  transition: all var(--transition-fast);
  box-shadow: 0 2px 4px rgba(32, 178, 170, 0.2);
  text-transform: none;
  letter-spacing: 0.025em;
}

.add-report-btn:hover {
  background-color: #1a9b94;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(32, 178, 170, 0.3);
}

.add-report-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(32, 178, 170, 0.2);
}

.reports-table-container {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  border: 1px solid var(--gray-200);
}

.reports-count {
  padding: 16px 24px;
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-600);
  font-size: 0.9rem;
  font-weight: 500;
}

.reports-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.reports-table th,
.reports-table td {
  padding: 14px 18px;
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.reports-table th {
  background-color: var(--gray-50);
  font-weight: 600;
  color: var(--gray-700);
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  position: sticky;
  top: 0;
  z-index: 10;
}

.reports-table tr:nth-child(even) {
  background-color: var(--gray-50);
}

.reports-table tr:hover {
  background-color: var(--gray-100);
  transition: background-color var(--transition-fast);
}

.reports-table td:first-child,
.reports-table th:first-child {
  padding-left: 24px;
}

.reports-table td:last-child,
.reports-table th:last-child {
  padding-right: 24px;
}

.report-row {
  transition: transform var(--transition-fast);
}

.report-row:hover {
  transform: translateY(-1px);
}

.status-badge {
  padding: 5px 10px;
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  gap: 4px;
}

.status-badge.reviewed {
  background-color: var(--success-light);
  color: var(--success-hover);
  border: 1px solid rgba(22, 163, 74, 0.2);
}

.status-badge.not-reviewed {
  background-color: var(--warning-light);
  color: var(--warning-hover);
  border: 1px solid rgba(217, 119, 6, 0.2);
}

.content-type-badge {
  padding: 4px 8px;
  border-radius: var(--radius-md);
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.content-type-badge.pdf {
  background-color: var(--danger-light);
  color: var(--danger-hover);
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.content-type-badge.text {
  background-color: var(--primary-color);
  color: white;
  border: 1px solid var(--primary-color);
}

.view-report-btn {
  padding: 10px 16px;
  background-color: #20b2aa;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.85rem;
  font-weight: 600;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  box-shadow: 0 2px 4px rgba(32, 178, 170, 0.2);
  text-transform: none;
  letter-spacing: 0.025em;
}

.view-report-btn:hover {
  background-color: #1a9b94;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(32, 178, 170, 0.3);
}

.view-report-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(32, 178, 170, 0.2);
}

.reports-reviews-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 60px 40px;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  color: var(--gray-500);
  border: 1px solid var(--gray-200);
}

.reports-reviews-empty svg {
  color: var(--gray-400);
  opacity: 0.8;
}

.reports-reviews-empty p {
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
}

.reports-reviews-loading,
.reports-reviews-error {
  text-align: center;
  padding: 60px 40px;
  color: var(--gray-600);
  font-size: 1.1rem;
  font-weight: 500;
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
}

.reports-reviews-error {
  color: var(--danger-color);
  border-color: var(--danger-light);
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
  animation: fadeIn var(--transition-normal);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modal-content {
  background: white;
  padding: 28px;
  border-radius: var(--radius-lg);
  width: 90%;
  max-width: 550px;
  box-shadow: var(--shadow-lg);
  animation: slideUp var(--transition-normal);
  border: 1px solid var(--gray-200);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-content h3 {
  margin: 0 0 16px 0;
  color: var(--gray-900);
  font-size: 1.25rem;
  font-weight: 600;
}

.modal-content label {
  display: block;
  margin-bottom: 6px;
  color: var(--gray-700);
  font-weight: 500;
  font-size: 0.9rem;
}

.modal-content select,
.modal-content input[type="date"] {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: 0.95rem;
  margin-bottom: 16px;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.modal-content select:focus,
.modal-content input[type="date"]:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

/* Template-style file upload for modal */
.modal-file-upload-container {
  display: flex;
  flex-direction: column;
  justify-content: start;
  border-radius: 4px;
  height: 60px;
  width: 100%;
  padding: 15px;
  color: black;
  background-color: #DFF3F5;
  margin-bottom: 16px;
}

.modal-file-upload-container input[type="file"] {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0,0,0,0);
  border: 0;
}

.modal-file-upload-label {
  display: flex;
  align-items: center;
  gap: 20px;
  cursor: pointer;
  color: #07195273;
  font-weight: 700;
  height: 100%;
}

.modal-file-upload-label svg {
  flex-shrink: 0;
}

.modal-file-upload-label:hover {
  color: #071952;
}

.modal-content textarea {
  width: 100%;
  padding: 14px;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
  font-size: 0.95rem;
  margin-bottom: 16px;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.modal-content textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.form-error {
  color: var(--danger-color);
  font-size: 0.9rem;
  margin-bottom: 16px;
  padding: 8px 12px;
  background-color: var(--danger-light);
  border-radius: var(--radius-md);
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.modal-actions button {
  padding: 10px 18px;
  border-radius: var(--radius-md);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.modal-actions button:first-child {
  background: none;
  border: 1px solid var(--gray-300);
  color: var(--gray-600);
}

.modal-actions button:first-child:hover {
  background-color: var(--gray-100);
  border-color: var(--gray-400);
  color: var(--gray-800);
}

.modal-actions button:last-child {
  background-color: var(--primary-color);
  color: white;
  border: none;
  box-shadow: var(--shadow-sm);
}

.modal-actions button:last-child:hover:not(:disabled) {
  background-color: var(--primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.modal-actions button:last-child:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.modal-actions button:last-child:disabled {
  background-color: var(--gray-300);
  color: var(--gray-500);
  cursor: not-allowed;
  box-shadow: none;
}

/* Enhanced responsive styles */
@media (max-width: 768px) {
  .reports-reviews-container {
    padding: 16px;
  }

  .reports-reviews-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 20px;
  }

  .reports-reviews-title {
    font-size: 1.5rem;
  }

  .reports-table-container {
    overflow-x: auto;
  }

  .reports-table th,
  .reports-table td {
    padding: 12px 14px;
  }

  .modal-content {
    padding: 20px;
    width: 95%;
  }

  .modal-actions button {
    padding: 8px 14px;
    font-size: 0.9rem;
  }
}

/* Additional animation for better UX */
.report-row,
.add-report-btn,
.view-report-btn {
  will-change: transform;
}

/* Focus styles for better accessibility */
button:focus,
textarea:focus,
input:focus,
select:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Improved scrollbar for better UX */
.reports-table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.reports-table-container::-webkit-scrollbar-track {
  background: var(--gray-100);
  border-radius: 4px;
}

.reports-table-container::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 4px;
}

.reports-table-container::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

.reports-pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  padding: 18px 24px 12px 24px;
  background: white;
  border-top: 1px solid var(--gray-200);
}

.pagination-btn {
  padding: 7px 14px;
  background: var(--gray-100);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  color: var(--gray-700);
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.pagination-btn.active,
.pagination-btn:focus {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.pagination-btn:disabled {
  background: var(--gray-200);
  color: var(--gray-400);
  cursor: not-allowed;
}

.report-detail-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 32px 28px;
  margin: 0 auto;
  max-width: 600px;
  border: 1px solid var(--gray-200);
}

.report-detail-fields {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 24px;
  color: var(--gray-800);
  font-size: 1.05rem;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: none;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  color: var(--primary-color);
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 24px;
  transition: all var(--transition-fast);
}

.back-button:hover {
  color: var(--primary-hover);
  background-color: var(--gray-50);
  border-color: var(--gray-300);
  transform: translateX(-2px);
}

.back-button:active {
  transform: translateX(0);
}

/* Report Review Page Styles */
.report-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 32px;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--gray-900);
  margin: 0;
}

.report-info-card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  margin-bottom: 32px;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  border-bottom: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

.report-meta-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  padding: 28px;
}

.meta-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.meta-item label {
  font-weight: 600;
  color: var(--gray-600);
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.meta-item span {
  color: var(--gray-800);
  font-size: 1rem;
  font-weight: 500;
}

.report-content {
  padding: 28px;
  border-top: 1px solid var(--gray-200);
}

.report-content label {
  font-weight: 600;
  color: var(--gray-600);
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: block;
  margin-bottom: 12px;
}

.content-text {
  background-color: var(--gray-50);
  padding: 16px;
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
  color: var(--gray-800);
  line-height: 1.6;
  white-space: pre-wrap;
}

.report-pdf-viewer {
  padding: 28px;
  border-top: 1px solid var(--gray-200);
}

.report-pdf-viewer label {
  font-weight: 600;
  color: var(--gray-600);
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: block;
  margin-bottom: 12px;
}

.pdf-viewer-container {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--gray-50);
  min-height: 600px;
  max-height: 800px;
}

.pdf-viewer-container .pdf-viewer {
  border: none !important;
  height: 600px;
  width: 100%;
}

.report-attachment {
  padding: 28px;
  border-top: 1px solid var(--gray-200);
}

.report-attachment label {
  font-weight: 600;
  color: var(--gray-600);
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: block;
  margin-bottom: 12px;
}

.attachment-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background-color: #20b2aa;
  color: white;
  text-decoration: none;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  box-shadow: 0 2px 4px rgba(32, 178, 170, 0.2);
  font-size: 0.95rem;
  text-transform: none;
  letter-spacing: 0.025em;
}

.attachment-link:hover:not(:disabled) {
  background-color: #1a9b94;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(32, 178, 170, 0.3);
}

.attachment-link:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(32, 178, 170, 0.2);
}

.attachment-link:disabled {
  background-color: var(--gray-300);
  color: var(--gray-500);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

.attachment-link.download-btn:disabled {
  opacity: 0.7;
}

/* Reviews Section */
.reviews-section {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 28px;
  border-bottom: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.add-review-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background-color: #20b2aa;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 600;
  transition: all var(--transition-fast);
  box-shadow: 0 2px 4px rgba(32, 178, 170, 0.2);
  text-transform: none;
  letter-spacing: 0.025em;
}

.add-review-btn:hover:not(:disabled) {
  background-color: #1a9b94;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(32, 178, 170, 0.3);
}

.add-review-btn:disabled {
  background-color: var(--gray-300);
  color: var(--gray-500);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

/* Review Form */
.review-form-card {
  margin: 28px;
  background-color: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.form-header {
  padding: 20px 24px;
  background-color: white;
  border-bottom: 1px solid var(--gray-200);
}

.form-header h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--gray-900);
}

.form-content {
  padding: 24px;
}

.form-group {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  color: var(--gray-700);
  font-weight: 600;
  font-size: 0.9rem;
}

.required {
  color: var(--danger-color);
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: 0.95rem;
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
  background-color: white;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.radio-group {
  display: flex;
  gap: 20px;
  margin-top: 8px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
  color: var(--gray-700);
}

.radio-option input[type="radio"] {
  width: 16px;
  height: 16px;
  accent-color: var(--primary-color);
}

.radio-label {
  font-size: 0.95rem;
}

.form-error {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--danger-color);
  font-size: 0.9rem;
  margin-top: 16px;
  padding: 12px 16px;
  background-color: var(--danger-light);
  border-radius: var(--radius-md);
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.form-actions {
  display: flex !important;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  background-color: white;
  border-top: 1px solid var(--gray-200);
  position: relative;
  z-index: 10;
}

.cancel-btn,
.submit-btn {
  padding: 12px 24px;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex !important;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-width: 120px;
  position: relative;
  z-index: 11;
}

.cancel-btn {
  background: white;
  border: 2px solid var(--gray-300);
  color: var(--gray-600);
}

.cancel-btn:hover:not(:disabled) {
  background-color: var(--gray-100);
  border-color: var(--gray-400);
  color: var(--gray-800);
  transform: translateY(-1px);
}

.submit-btn {
  background-color: #20b2aa !important;
  color: white !important;
  border: 2px solid #20b2aa !important;
  box-shadow: 0 2px 4px rgba(32, 178, 170, 0.2);
  font-weight: 700;
  border-radius: 8px !important;
  letter-spacing: 0.025em;
}

.submit-btn:hover:not(:disabled) {
  background-color: #1a9b94 !important;
  border-color: #1a9b94 !important;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(32, 178, 170, 0.3);
}

.submit-btn:disabled {
  background-color: var(--gray-300) !important;
  color: var(--gray-500) !important;
  border-color: var(--gray-300) !important;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

/* Reviews List */
.reviews-list {
  padding: 28px;
}

.reviews-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 60px;
  color: var(--gray-500);
  font-size: 1.1rem;
  font-weight: 500;
}

.reviews-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 60px 40px;
  color: var(--gray-500);
  text-align: center;
}

.reviews-empty svg {
  color: var(--gray-400);
  opacity: 0.8;
}

.reviews-empty p {
  font-size: 1.1rem;
  font-weight: 500;
  margin: 0;
}

.empty-subtitle {
  font-size: 0.95rem;
  color: var(--gray-400);
}

.reviews-grid {
  display: grid;
  gap: 20px;
}

.review-card {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);
}

.review-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.review-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

.review-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 16px;
}

.review-title h4 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--gray-900);
  flex: 1;
}

.review-badges {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.category-badge {
  padding: 4px 10px;
  border-radius: var(--radius-xl);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.category-badge.normal {
  background-color: var(--success-light);
  color: var(--success-hover);
  border: 1px solid rgba(22, 163, 74, 0.2);
}

.category-badge.abnormal {
  background-color: var(--warning-light);
  color: var(--warning-hover);
  border: 1px solid rgba(217, 119, 6, 0.2);
}

.abnormal-type-badge {
  padding: 4px 8px;
  border-radius: var(--radius-md);
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  white-space: nowrap;
}

.abnormal-type-badge.clinical {
  background-color: var(--danger-light);
  color: var(--danger-hover);
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.abnormal-type-badge.non-clinical {
  background-color: var(--gray-200);
  color: var(--gray-600);
  border: 1px solid var(--gray-300);
}

.review-content {
  padding: 20px 24px;
}

.review-content p {
  margin: 0;
  color: var(--gray-800);
  line-height: 1.6;
  font-size: 0.95rem;
}

.review-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: var(--gray-50);
  border-top: 1px solid var(--gray-200);
  font-size: 0.85rem;
  color: var(--gray-600);
}

.reviewer-info,
.review-date {
  display: flex;
  align-items: center;
  gap: 6px;
}

/* Animations */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .report-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .page-title {
    font-size: 1.75rem;
  }

  .card-header,
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 20px;
  }

  .report-meta-grid {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 20px;
  }

  .radio-group {
    flex-direction: column;
    gap: 12px;
  }

  .review-title {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .review-badges {
    align-self: flex-start;
  }

  .review-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .form-actions {
    flex-direction: column;
    gap: 8px;
  }

  .cancel-btn,
  .submit-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .reports-reviews-container {
    padding: 12px;
  }

  .review-form-card,
  .reviews-list {
    margin: 16px;
    padding: 16px;
  }

  .form-content {
    padding: 16px;
  }
}
