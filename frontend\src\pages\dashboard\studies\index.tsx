import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { 
  Calendar, 
  Search,
  Users,
  FileText,
  X,
  ArrowUpRight,
  LayoutGrid,
  List
} from "lucide-react";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON>Axis, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from "recharts";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import LightFooter from "@/shared/LightFooter";
import DataTable from "@/components/common/DataTable";
import { useStudiesByDepartmentQuery, usePatientsPerStudyQuery } from "@/hooks/study.query";
import { usePatientsQuery } from "@/hooks/patient.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import "./studies.css";

// Define types for our data
interface Study {
  uuid: string;
  name: string;
  description?: string;
  patients_nhs_number?: string[];
  visits?: any[];
  user?: {
    first_name?: string;
    last_name?: string;
  };
  team_email?: string;
  leading_team?: {
    email: string;
    first_name: string;
    identifier: string;
  },
  other?: null;
  full_title?:string;
}

interface Patient {
  nhs_number: string;
  first_name: string;
  last_name: string;
}

interface PatientStudy {
  uuid: string;
  name: string;
  patient_count: number;
}

export default function Studies() {
  const navigate = useNavigate();
  const { data: currentUser = {} as any } = useCurrentUserQuery();
  const { data: studies, isLoading: isLoadingStudies } = useStudiesByDepartmentQuery(currentUser?.department?.uuid || '');
  const { data: patients } = usePatientsQuery();
  const { data: patientsPerStudy } = usePatientsPerStudyQuery();

  const [searchTerm, setSearchTerm] = useState("");
  const [patientFilter] = useState("");
  const [viewMode, setViewMode] = useState<"table" | "card">("table");

  // Set initial view mode based on screen size
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768 && viewMode === "table") {
        setViewMode("card");
      }
    };

    // Set initial view mode
    handleResize();

    // Add event listener
    window.addEventListener("resize", handleResize);

    // Clean up
    return () => window.removeEventListener("resize", handleResize);
  }, [viewMode]);

  // Filter studies based on search term and patient filter
  const filteredStudies = studies?.filter((study: Study) => {
    const matchesSearch = study.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (study.description && study.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesPatient = patientFilter 
      ? study.patients_nhs_number?.includes(patientFilter)
      : true;
    
    return matchesSearch && matchesPatient;
  });

  // Handle viewing a study
  const handleViewStudy = (studyId: string) => {
    navigate(`/org/dashboard/studies/${studyId}`);
  };

  // Get patient name by NHS number
  const getPatientName = (nhsNumber: string) => {
    const patient = patients?.find((p: Patient) => p.nhs_number === nhsNumber);
    return patient ? `${patient.first_name} ${patient.last_name}` : "Unknown Patient";
  };

  // Count enrolled patients in a study
  const countEnrolledPatients = (study: Study) => {
    return study.patients_nhs_number?.length || 0;
  };

  // Format patient names for display in table
  const formatPatientNames = (study: Study) => {
    if (!study.patients_nhs_number || study.patients_nhs_number.length === 0) {
      return "None";
    }
    
    const patientNames = study.patients_nhs_number.map((nhsNumber: string) => getPatientName(nhsNumber));
    
    if (patientNames.length === 1) {
      return patientNames[0];
    } else if (patientNames.length === 2) {
      return `${patientNames[0]}, ${patientNames[1]}`;
    } else {
      return `${patientNames[0]}, ${patientNames[1]} +${patientNames.length - 2} more`;
    }
  };

  // Define columns for the DataTable
  const columns = [
    {
      key: "name" as keyof Study,
      header: "Study Name",
      sortable: true
    },
    {
      key: "description" as keyof Study,
      header: "Description",
      sortable: true,
      render: (value: any) => {
        const description = value as string | undefined;
        return description 
          ? (description.length > 100 
            ? `${description.substring(0, 100)}...` 
            : description)
          : "No description";
      },
    },
    {
      key: "team_email" as keyof Study,
      header: "Team Email",
      sortable: true,
      render: (value: any) => {
        const teamEmail = value as string | undefined;
        return teamEmail || "No team email";
      },
    },
    {
      key: "leading_team" as keyof Study,
      header: "Leading Team",
      sortable: true,
      render: (value: any) => {
        const leadingTeam = value as Study["leading_team"];
        return leadingTeam?.email || "No leading team";
      },
    },
    {
      key: "full_title" as keyof Study,
      header: "Full Title",
      sortable: true,
      render: (value: any) => {
        const fullTitle = value as string | undefined;
        return fullTitle || "No full title";
      },
    },
    // {
    //   key: "visits" as keyof Study,
    //   header: "Visits",
    //   sortable: true,
    //   render: (value: any) => {
    //     const visits = value as any[] | undefined;
    //     return (
    //       <div className="study-visits">
    //         <Calendar size={16} />
    //         <span>{visits?.length || 0}</span>
    //       </div>
    //     );
    //   },
    // },
    {
      key: "user" as keyof Study,
      header: "Created By",
      sortable: true,
      render: (value: any) => {
        const user = value as Study["user"];
        return (
          <div className="study-created-by">
            <FileText size={16} />
            <span>{user?.first_name || "Unknown"}</span>
          </div>
        );
      },
    },
  ];

  // Define actions for the DataTable
  const actions = [
    {
      icon: <ArrowUpRight size={18} />,
      tooltipText: "View Study Details",
      onClick: (row: Study) => handleViewStudy(row.uuid),
    }
  ];

  // Render card view for studies
  const renderCardView = () => {
    return (
      <div className="studies-card-view">
        {filteredStudies?.map((study: Study) => (
          <div key={study.uuid} className="study-card">
            <div className="study-card-header">
              <h3 className="study-card-title">{study.name}</h3>
            </div>
            <p className="study-card-description">
              {study.description || "No description"}
            </p>
            <div className="study-card-meta">
              <div className="study-card-meta-item">
                <Users size={16} />
                <span>{countEnrolledPatients(study)} patients - {formatPatientNames(study)}</span>
              </div>
              <div className="study-card-meta-item">
                <Calendar size={16} />
                <span>{study.visits?.length || 0} visits</span>
              </div>
              <div className="study-card-meta-item">
                <FileText size={16} />
                <span>Created by {study.user?.first_name || "Unknown"}</span>
              </div>
            </div>
            <div className="study-card-actions">
              <button
                onClick={() => handleViewStudy(study.uuid)}
                className="action-button"
                title="View Study Details"
              >
                <ArrowUpRight size={18} />
              </button>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <>
      <Wrapper>
        <Preloader />

        <div className="content-wrapper js-content-wrapper overflow-hidden" style={{paddingBottom: "88px"}}>
          <div className="dashboard__content bg-light-4">
            <div className="row">
              <div className="col-12">
                <div className="studies-container">
                  <div className="studies-header">
                    <h1>Studies</h1>
                  </div>

                  <div className="studies-controls">
                    <div className="search-filter-container">
                      <div className="policy-search-container">
                        <div className="policy-search-box">
                          <Search className="search-icon" size={20} />
                          <input
                            type="text"
                            placeholder="Search studies by name or description"
                            className="policy-search-input"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                          />
                          {searchTerm && (
                            <button className="clear-search" onClick={() => setSearchTerm("")}>
                              <X size={18} />
                            </button>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="view-toggle-container">
                      <button 
                        className={`view-toggle-btn ${viewMode === "table" ? "active" : ""}`}
                        onClick={() => setViewMode("table")}
                        title="Table View"
                      >
                        <List size={16} />
                      </button>
                      <button 
                        className={`view-toggle-btn ${viewMode === "card" ? "active" : ""}`}
                        onClick={() => setViewMode("card")}
                        title="Card View"
                      >
                        <LayoutGrid size={16} />
                      </button>
                    </div>
                  </div>

                  {isLoadingStudies ? (
                    <div className="loading-studies">Loading studies...</div>
                  ) : filteredStudies && filteredStudies.length > 0 ? (
                    <>
                      {viewMode === "table" ? (
                        <DataTable 
                          data={filteredStudies}
                          columns={columns}
                          actions={actions}
                          defaultItemsPerPage={10}
                          itemsPerPageOptions={[5, 10, 25, 50]}
                          noDataMessage="No studies available"
                        />
                      ) : (
                        renderCardView()
                      )}
                      
                      <div className="studies-chart-container">
                        <h3>Studies Patient Distribution</h3>
                        <div className="chart-container">
                          <ResponsiveContainer width="100%" height={300}>
                            <BarChart
                              data={patientsPerStudy?.filter((study: PatientStudy) => 
                                filteredStudies?.some((filteredStudy: Study) => 
                                  filteredStudy.uuid === study.uuid
                                )
                              )}
                              margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                            >
                              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                              <XAxis 
                                dataKey="name" 
                                angle={-45} 
                                textAnchor="end"
                                height={60}
                                interval={0}
                                tick={{ fill: '#4b5563', fontSize: 12 }}
                                tickLine={{ stroke: '#e5e7eb' }}
                                axisLine={{ stroke: '#e5e7eb' }}
                              />
                              <YAxis 
                                label={{ 
                                  value: 'Number of Patients', 
                                  angle: -90, 
                                  position: 'insideLeft',
                                  style: { fill: '#4b5563', fontSize: 12 }
                                }}
                                tick={{ fill: '#4b5563', fontSize: 12 }}
                                tickLine={{ stroke: '#e5e7eb' }}
                                axisLine={{ stroke: '#e5e7eb' }}
                              />
                              <Tooltip 
                                formatter={(value) => [`${value} patients`, 'Enrolled']}
                                contentStyle={{ 
                                  backgroundColor: 'white', 
                                  border: '1px solid #e5e7eb',
                                  borderRadius: '6px',
                                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
                                }}
                                labelStyle={{ fontWeight: 'bold', color: '#1f2937' }}
                              />
                              <Legend 
                                wrapperStyle={{ paddingTop: 10, fontSize: 12, color: '#4b5563' }}
                              />
                              <Bar 
                                dataKey="patient_count" 
                                name="Enrolled Patients" 
                                fill="#36B6C2"
                                radius={[4, 4, 0, 0]}
                                barSize={40}
                                animationDuration={1000}
                              />
                            </BarChart>
                          </ResponsiveContainer>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="no-studies">
                      {searchTerm || patientFilter
                        ? "No studies match your search criteria"
                        : "No studies created yet. Click 'Create New Study' to get started."}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <LightFooter />
        </div>
      </Wrapper>
    </>
  );
}
