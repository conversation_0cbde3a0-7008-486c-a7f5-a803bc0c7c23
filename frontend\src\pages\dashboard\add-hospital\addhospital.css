/* Add Hospital Page Styles */
.add-hospital-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    padding: 15px 30px 30px 30px; /* Changed top padding */
    margin-bottom: 30px;
    width: 100%;
}

/* Progress Indicator */
.hospital-progress {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
    position: relative;
    width: 100%;
    max-width: 800px;
}

.hospital-progress::before {
    content: '';
    position: absolute;
    top: 15px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #E9ECEF;
    z-index: 1;
}

.hospital-step {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 33.33%;
}

.step-indicator {
    width: 34px;
    height: 34px;
    border-radius: 50%;
    background-color: white;
    border: 2px solid #E9ECEF;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    font-weight: 600;
    color: #4F547B;
    transition: all 0.3s ease;
    box-shadow: none; /* Changed */
}

.step-label {
    font-size: 14px;
    color: #4F547B;
    text-align: center;
    font-weight: 500;
}

.hospital-step.active .step-indicator {
    background-color: #37B7C3;
    border-color: #37B7C3;
    color: white;
    box-shadow: none; /* Changed */
}

.hospital-step.active .step-label {
    color: #37B7C3;
    font-weight: 600;
}

.hospital-step.completed .step-indicator {
    background-color: #37B7C3;
    border-color: #37B7C3;
    color: white;
    box-shadow: none; /* Changed */
}

.add-hospital-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.add-hospital-title {
    margin-bottom: 10px;
}

.add-hospital-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.add-hospital-subtitle {
    color: #4F547B;
    font-size: 16px;
    font-weight: 400;
    margin-top: 5px;
}

.add-hospital-subtitle h6 {
    margin: 0;
    font-weight: 400;
}

.add-hospital-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    width: 100%;
    max-width: 800px;
}

.form-section {
    margin-bottom: 30px;
    background-color: #f9fafb;
    border-radius: 0; /* Changed */
    padding: 25px;
    border: 1px solid #e5e7eb;
}

.form-section-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
}

.hospital-form-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
    gap: 15px;
}

.hospital-btn-cancel {
    min-width: 120px;
    height: 48px;
    background-color: #f5f7fa;
    color: #4F547B;
    border: 1px solid #e5e7eb;
    border-radius: 0; /* Changed */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hospital-btn-cancel:hover {
    background-color: #e5e7eb;
    color: #1a1a1a;
    box-shadow: none; /* Changed */
}

.hospital-btn-submit {
    min-width: 120px;
    height: 48px;
    background-color: #37B7C3;
    color: white;
    border: none;
    border-radius: 0; /* Changed */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: none; /* Changed */
}

.hospital-btn-submit:hover {
    background-color: #2A9A9F;
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

.hospital-btn-submit:disabled {
    background-color: #A8D5DA;
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .add-hospital-container {
        padding: 25px;
    }
    
    .form-section {
        padding: 20px;
    }
}

@media (max-width: 768px) {
    .add-hospital-container {
        padding: 20px;
    }
    
    .add-hospital-title h1 {
        font-size: 20px;
    }
    
    .add-hospital-subtitle {
        font-size: 14px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .hospital-form-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .hospital-btn-cancel, .hospital-btn-submit {
        width: 100%;
    }
}

@media (max-width: 576px) {
    .add-hospital-container {
        padding: 15px;
    }
}
