import './StartLiveChatModal.css';
import { useState, useEffect } from 'react';
import { Send, X, MessageSquare, Building, User } from 'lucide-react';
import { useCreateLiveChatMutation } from '@/hooks/livechat.query';
import type { LiveChatCreateData } from '@/services/api/livechat.types';
import { useCurrentUserQuery } from '@/hooks/user.query';

interface StartLiveChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (chatUuid: string) => void;
}

const StartLiveChatModal: React.FC<StartLiveChatModalProps> = ({ isOpen, onClose, onSuccess }) => {
  const [subject, setSubject] = useState('');
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState('');
  const { data: currentUser } = useCurrentUserQuery();
  const createLiveChatMutation = useCreateLiveChatMutation();

  useEffect(() => {
    if (!isOpen) {
      // Reset form when modal closes
      setSubject('');
      setMessage('');
      setSelectedDepartment('');
      setIsLoading(false);
    }
  }, [isOpen]);

  useEffect(() => {
    if (currentUser?.departments && currentUser.departments.length > 0) {
      setSelectedDepartment(currentUser.departments[0].uuid);
    }
  }, [currentUser]);

  if (!isOpen) return null;

  const handleSendMessage = async () => {
    if (!subject || !message) {
      return;
    }

    setIsLoading(true);

    try {
      const chatData: LiveChatCreateData = {
        department_id: selectedDepartment,
        subject: subject,
        initial_message: message
      };

      console.log('Sending chat data:', chatData);
      const newChat = await createLiveChatMutation.mutateAsync(chatData);
      console.log('Chat created successfully:', newChat);
      onSuccess(newChat.uuid);
      onClose();
    } catch (error) {
      console.error('Error creating live chat:', error);
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  return (
    <div className="modal-overlay" onClick={handleClose}>
      <div className="start-livechat-modal-content" onClick={(e) => e.stopPropagation()}>
        <button className="close-button" onClick={handleClose} disabled={isLoading}>
          <X size={24} />
        </button>
        <h2 className="modal-title">
          <MessageSquare size={28} />
          Start New Conversation
        </h2>
        
        <div className="form-container">
          <div className="form-group">
            <label htmlFor="department">
              <Building size={20} />
              Department
            </label>
            <select
              id="department"
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              disabled={isLoading}
            >
              {currentUser?.departments?.map((dept) => (
                <option key={dept.uuid} value={dept.uuid}>
                  {dept.hospital.name} - {dept.name}
                </option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="subject">
              <MessageSquare size={20} />
              Subject
            </label>
            <input
              id="subject"
              type="text"
              placeholder="Enter subject"
              value={subject}
              onChange={(e) => setSubject(e.target.value)}
              disabled={isLoading}
            />
          </div>

          <div className="form-group">
            <label htmlFor="message">
              <User size={20} />
              Message
            </label>
            <textarea
              id="message"
              placeholder="Type your message here..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              disabled={isLoading}
              rows={4}
            />
          </div>
        </div>

        <div className="modal-actions">
          <button
          className="start-conversation-btn"
          onClick={handleSendMessage}
            disabled={!subject || !message || isLoading}
          >
            {isLoading ? 'Sending...' : 'Send Message'}
            <Send size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default StartLiveChatModal;
