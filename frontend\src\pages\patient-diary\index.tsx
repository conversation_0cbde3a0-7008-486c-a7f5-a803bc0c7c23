import React, { useState } from "react";
import { motion } from "framer-motion";
import { Plus, History, Edit } from "lucide-react";
import DataTable, { Column } from "@/components/common/DataTable";
import { type Symptom, type CreateSymptomData } from "@/services/api/symptom.service";
import AddSymptomModal from "@/components/patient-clinical/AddSymptomModal";
import SymptomLogsModal from "@/components/patient-clinical/SymptomLogsModal";
import "@/components/patient-clinical/AddSymptomModal.css";
import "./patientdiary.css";
import { useSymptomsQuery, useUpdateSymptomMutation } from "@/hooks/symptom.query";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import Preloader from "@/components/common/Preloader";

interface EditSymptomModalProps {
  isOpen: boolean;
  onClose: () => void;
  symptom: Symptom | null;
  onSave: (updatedSymptom: Partial<CreateSymptomData>) => Promise<void>;
}

const EditSymptomModal: React.FC<EditSymptomModalProps> = ({
  isOpen,
  onClose,
  symptom,
  onSave,
}) => {
  const [editedSymptom, setEditedSymptom] = useState<Partial<CreateSymptomData>>({});

  React.useEffect(() => {
    if (symptom) {
      setEditedSymptom({
        description: symptom.description,
        start_date: symptom.start_date,
        start_time: symptom.start_time,
        severity: symptom.severity,
        hospitalization_required: symptom.hospitalization_required,
        status: symptom.status,
        patient_comment: symptom.patient_comment,
        resolved_date: symptom.resolved_date || undefined
      });
    }
  }, [symptom]);

  if (!isOpen || !symptom) return null;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    await onSave(editedSymptom);
  };

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Edit Symptom</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="row g-3">
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="description">Description</label>
                <input
                  type="text"
                  className="form-control"
                  id="description"
                  value={editedSymptom.description || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, description: e.target.value }))}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="severity">Severity</label>
                <select
                  className="form-control"
                  id="severity"
                  value={editedSymptom.severity?.toString() || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, severity: parseInt(e.target.value, 10) }))}
                  required
                >
                  <option value="1">Mild</option>
                  <option value="2">Moderate</option>
                  <option value="3">Severe</option>
                </select>
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="start_date">Start Date</label>
                <input
                  type="date"
                  className="form-control"
                  id="start_date"
                  value={editedSymptom.start_date || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, start_date: e.target.value }))}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="start_time">Start Time</label>
                <input
                  type="time"
                  className="form-control"
                  id="start_time"
                  value={editedSymptom.start_time || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, start_time: e.target.value }))}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="status">Status</label>
                <select
                  className="form-control"
                  id="status"
                  value={editedSymptom.status || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, status: e.target.value }))}
                  required
                >
                  <option value="1">Resolved</option>
                  <option value="2">Recovered with sequelae</option>
                  <option value="3">Ongoing / Continuing treatment</option>
                  <option value="4">Condition worsening</option>
                  <option value="5">Unknown</option>
                </select>
              </div>
            </div>
            {editedSymptom.status === "1" && (
              <div className="col-md-6">
                <div className="form-group">
                  <label htmlFor="resolved_date">Resolved Date</label>
                  <input
                    type="date"
                    className="form-control"
                    id="resolved_date"
                    value={editedSymptom.resolved_date || ""}
                    onChange={(e) => setEditedSymptom(prev => ({ ...prev, resolved_date: e.target.value }))}
                    required
                  />
                </div>
              </div>
            )}
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="hospitalization_required">Hospitalization Required</label>
                <select
                  className="form-control"
                  id="hospitalization_required"
                  value={editedSymptom.hospitalization_required ? "true" : "false"}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, hospitalization_required: e.target.value === "true" }))}
                  required
                >
                  <option value="false">No</option>
                  <option value="true">Yes</option>
                </select>
              </div>
            </div>
            <div className="col-12">
              <div className="form-group">
                <label htmlFor="patient_comment">Comment</label>
                <textarea
                  className="form-control"
                  id="patient_comment"
                  value={editedSymptom.patient_comment || ""}
                  onChange={(e) => setEditedSymptom(prev => ({ ...prev, patient_comment: e.target.value }))}
                  rows={3}
                />
              </div>
            </div>
          </div>
          <div className="modal-footer">
            <button type="submit" className="button -md btn-nurtify text-white me-2">
              Save Changes
            </button>
            <button type="button" className="button -md btn-nurtify-lighter" onClick={onClose}>
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const PatientDiary: React.FC = () => {
  const { selectedPatient, isLoading: isPatientLoading } = useSelectedPatientStore();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingSymptom, setEditingSymptom] = useState<Symptom | null>(null);
  const [viewingLogsFor, setViewingLogsFor] = useState<string | null>(null);
  
  const updateSymptomMutation = useUpdateSymptomMutation();
  const { data: symptoms = [], isLoading: isLoadingSymptoms } = useSymptomsQuery(selectedPatient?.uuid || "");

  if (isPatientLoading) {
    return <Preloader />;
  }

  if (!selectedPatient) {
    return <div>No patient selected</div>;
  }

  const handleEditSymptom = (symptom: Symptom) => {
    setEditingSymptom(symptom);
  };

  const handleCancelEdit = () => {
    setEditingSymptom(null);
  };

  const handleSaveEdit = async (updatedSymptom: Partial<CreateSymptomData>) => {
    if (!editingSymptom) return;

    try {
      await updateSymptomMutation.mutateAsync({
        uuid: editingSymptom.uuid,
        data: updatedSymptom
      });
      setEditingSymptom(null);
    } catch (error) {
      console.error("Error updating symptom:", error);
    }
  };

  const handleViewLogs = (symptomUuid: string) => {
    setViewingLogsFor(symptomUuid);
  };

  const handleCloseLogs = () => {
    setViewingLogsFor(null);
  };

  const symptomColumns: Column<Symptom>[] = [
    { key: "description", header: "Description", sortable: true },
    { key: "start_date", header: "Start Date", sortable: true },
    { key: "start_time", header: "Start Time", sortable: true },
    { key: "severity", header: "Severity", sortable: true },
    { 
      key: "hospitalization_required", 
      header: "Hospitalized", 
      sortable: true,
      render: (value: string | number | boolean | null | undefined) => {
        if (typeof value === 'boolean') {
          return value ? "Yes" : "No";
        }
        return String(value ?? "");
      }
    },
    { key: "status_display", header: "Status", sortable: true },
    { key: "patient_comment", header: "Comment", sortable: true },
    {
      key: "actions" as keyof Symptom,
      header: "Actions",
      sortable: false,
      render: (_: string | number | boolean | null | undefined, row?: Symptom) => row && (
        <div className="actions-cell">
          <div className="action-button-wrapper">
            <button
              className="action-button"
              onClick={() => handleViewLogs(row.uuid)}
              title="View history"
            >
              <span className="action-icon">
                <History size={16} />
              </span>
            </button>
            <div className="tooltip">View History</div>
          </div>
          <div className="action-button-wrapper">
            <button
              className="action-button"
              onClick={() => handleEditSymptom(row)}
              title="Edit symptom"
            >
              <span className="action-icon">
                <Edit size={16} />
              </span>
            </button>
            <div className="tooltip">Edit Symptom</div>
          </div>
        </div>
      )
    }
  ];

  const handleAddSymptom = () => {
    if (!selectedPatient?.uuid) {
      console.error('No patient UUID available');
      return;
    }
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  return (
    <motion.div
      className="patclin-tab-content"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="patclin-section-title">Patient Health Diary</h2>
        <button
          className="btn btn-primary d-flex align-items-center gap-2"
          onClick={handleAddSymptom}
        >
          <Plus size={20} />
          Add Symptom
        </button>
      </div>

      {isLoadingSymptoms ? (
        <div className="text-center">Loading symptoms...</div>
      ) : symptoms.length > 0 ? (
        <DataTable
          data={symptoms}
          columns={symptomColumns}
          noDataMessage="No symptoms recorded yet"
        />
      ) : (
        <div className="patclin-empty-state">
          <p>No symptoms recorded for this patient.</p>
        </div>
      )}

      <AddSymptomModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        patientUuid={selectedPatient.uuid}
        onSuccess={handleModalClose}
      />

      <EditSymptomModal
        isOpen={!!editingSymptom}
        onClose={handleCancelEdit}
        symptom={editingSymptom}
        onSave={handleSaveEdit}
      />

      <SymptomLogsModal
        isOpen={!!viewingLogsFor}
        onClose={handleCloseLogs}
        symptomUuid={viewingLogsFor || ""}
      />
    </motion.div>
  );
};

export default PatientDiary;
