import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import { resolve } from 'path'
import fs from 'fs'
import path from 'path'

// Custom plugin to copy bpor-widget files to assets directory
const copyBporWidgetPlugin = () => {
  return {
    name: 'copy-bpor-widget',
    closeBundle: async () => {
      const sourceDir = path.resolve(__dirname, 'node_modules/bpor-dev-resource/dist/bpor-widget');
      const targetDir = path.resolve(__dirname, 'dist/assets/bpor-widget');

      // Create target directory if it doesn't exist
      if (!fs.existsSync(targetDir)) {
        fs.mkdirSync(targetDir, { recursive: true });
      }

      // Copy all files from source to target
      const files = fs.readdirSync(sourceDir);
      for (const file of files) {
        const sourcePath = path.join(sourceDir, file);
        const targetPath = path.join(targetDir, file);

        if (fs.statSync(sourcePath).isDirectory()) {
          // If it's a directory (like Assets), copy it recursively
          fs.mkdirSync(targetPath, { recursive: true });
          const subFiles = fs.readdirSync(sourcePath);
          for (const subFile of subFiles) {
            const subSourcePath = path.join(sourcePath, subFile);
            const subTargetPath = path.join(targetPath, subFile);
            fs.copyFileSync(subSourcePath, subTargetPath);
          }
        } else {
          // If it's a file, copy it directly
          fs.copyFileSync(sourcePath, targetPath);
        }
      }

      console.log('Copied bpor-widget files to assets directory');
    }
  };
};

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    react(),
    copyBporWidgetPlugin()
  ],

  // Resolve aliases for cleaner imports
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
      '@components': resolve(__dirname, './src/components'),
      '@assets': resolve(__dirname, './src/assets'),
    },
  },

  // Build options optimization
  build: {
    target: 'esnext', // Modern browsers for better performance
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true,
      },
    },
    rollupOptions: {
      output: {
        manualChunks: {
          // Split vendor chunks for better caching
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
        },
      },
    },
    // Improve chunk loading strategy
    chunkSizeWarningLimit: 1000,
    sourcemap: true,
  },

  // Server options
  server: {
    port: 5174,
    strictPort: true,
    open: true, // Open browser on server start
    cors: true, // Enable CORS
    hmr: {
      overlay: true, // Enable HMR overlay
    },
    proxy: {
      '/api': 'http://localhost:8000',
    },
  },

  // CSS optimization
  css: {
    devSourcemap: true,
    modules: {
      localsConvention: 'camelCase',
    },
  },

  // Enable preview options
  preview: {
    port: 3000,
    strictPort: true,
    open: true,
  },

  // Optimize deps
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom'],
    exclude: ['your-local-package'], // Add any local packages to exclude
  },
})
