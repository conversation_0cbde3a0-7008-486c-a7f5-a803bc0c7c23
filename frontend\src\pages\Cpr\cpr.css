.main-content {
  background-color: #f5f7fa;
}

.bg-light-4 {
  background-color: #f5f7fa;
}

.content-wrapper {
  width: 100%;
  max-width: 100vw;
  /* overflow-x: hidden; */
}

.dashboard__content {
  padding: 20px;
  min-height: calc(100vh - 80px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.row.y-gap-30 {
  row-gap: 30px;
}

.col-xl-3.col-lg-4 {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar.-dashboard {
  position: sticky;
  top: 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08), 0 2px 4px rgba(55, 183, 195, 0.04);
  transition: all 0.3s ease;
}

.col-xl-9.col-lg-8 {
  padding-left: 30px;
  padding-right: 30px;
  width: 100%;
  max-width: calc(100% - 300px);
  flex: 1;
}

h1 {
  font-size: 32px;
  font-weight: bold;
  color: #071952;
  margin-bottom: 30px;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  width: calc(100% - 30px);
  margin: 30px auto;
  align-items: stretch;
}

.cpr-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08), 0 2px 4px rgba(55, 183, 195, 0.04);
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid black;
  text-align: center;
  width: 100%;
  min-width: 280px;
  max-width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: relative;
  padding-bottom: 80px;
}

.cpr-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(55, 183, 195, 0.12), 0 4px 8px rgba(55, 183, 195, 0.08);
}

.cpr-card .button-container {
  margin-top: 0;
  position: absolute;
  bottom: 20px;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  gap: 10px;
}

.cpr-timer {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  color: #071952;
  margin-bottom: 20px;
}

.cpr-timer-text {
  font-family: 'Digital Numbers', sans-serif;
  font-size: 24px;
  color: #071952;
  text-align: center;
  margin-bottom: 20px;
}

.cpr-card-content {
  text-align: center;
  margin: 8px 0;
  font-size: 16px;
  color: #333;
  line-height: 1.5;
  flex-grow: 1;
}

.cpr-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  background: #37B7C3;
  color: white;
  font-size: 14px;
  font-weight: 500;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: auto;
  width: 90%;
  max-width: 380px;
  position: relative;
  height: 50px;
}

.cpr-button:hover {
  background: #2d919a;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.2);
}

.cpr-button .icon-left {
  position: absolute;
  left: 10px;
}

.cpr-button .icon-right {
  position: absolute;
  right: 10px;
}

.cpr-button .button-text {
  margin: 0 auto;
}

.cpr-button-secondary {
  background: rgba(55, 183, 195, 0.08);
  color: #37B7C3;
  border: 1px solid rgba(55, 183, 195, 0.15);
  height: 50px;
}

.cpr-button-secondary:hover {
  background: rgba(55, 183, 195, 0.12);
  border-color: rgba(55, 183, 195, 0.2);
}

.cpr-card:first-child .button-container,
.cpr-card:nth-child(2) .button-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: auto;
}

.cpr-card:nth-child(3) .button-container {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  gap: 10px;
  width: calc(180px * 2 + 10px);
  height: 50px;
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.cpr-card:nth-child(3) .cpr-button,
.cpr-card:nth-child(3) .cpr-button-secondary {
  width: 180px;
  height: 50px;
}

.rosc-button-container {
  display: flex;
  justify-content: center;
  margin-top: 40px;
}

.rosc-button {
  width: 100%;
  max-width: 1104px;
  height: 104px;
  background-color: rgba(2, 188, 125, 0.23);
  color: #1C8B34;
  font-size: 40px;
  font-weight: bold;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.rosc-button .icon-left {
  position: absolute;
  left: 10px;
}

.rosc-button .icon-right {
  position: absolute;
  right: 10px;
}

.rosc-button .button-text {
  margin: 0 auto;
}

.table-container {
  width: calc(100% - 30px);
  margin: 40px auto 0;
  overflow-x: auto;
  font-size: 10px;
}

.table-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  width: 100%;
}

.custom-table {
  width: 100%;
  min-width: 280px;
  border-collapse: collapse;
  margin-bottom: 20px;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  table-layout: fixed;
}

.custom-table th,
.custom-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
  word-wrap: break-word;
  font-size: 12px;
}

.custom-table th {
  background-color: #37B7C3;
  color: white;
}

.custom-table tr:nth-child(even) {
  background-color: #f2f2f2;
}

.custom-table tr:hover {
  background-color: #ddd;
}

.table-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 80px;
  height: 30px;
}

.table-button:hover {
  background: #2d919a;
}

.table-button span {
  margin-right: 5px;
}

.edit-button {
  background: #DFF3F5;
  color: #37B7C3;
}

.edit-button:hover {
  background: #cce7ea;
}

.delete-button {
  background: #F8CABD;
  color: red;
}

.delete-button:hover {
  background: #f4b3a3;
}

.table-wrapper {
  height: 177px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #071952 transparent;
}

@media (max-width: 1600px) {
  .cpr-card:nth-child(3) .button-container {
    width: 90%;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
  }

  .cpr-card:nth-child(3) .cpr-button,
  .cpr-card:nth-child(3) .cpr-button-secondary {
    width: 48%;
    height: 50px;
    font-size: 14px;
  }

  .cpr-button svg {
    width: 18px;
    height: 18px;
  }

  .cpr-button .button-text {
    font-size: 13px;
  }

  .cpr-button-secondary svg {
    width: 16px;
    height: 16px;
  }

  .cpr-button-secondary .button-text {
    font-size: 13px;
  }
}

@media (max-width: 1200px) {
  .rosc-button {
    height: auto;
    font-size: 30px;
  }
}

@media (max-width: 991px) {
  .col-xl-9.col-lg-8 {
    padding-left: 15px;
    padding-right: 15px;
    max-width: 100% !important;
  }
}

@media (max-width: 768px) {
  .card-grid {
    width: calc(100% - 30px);
    margin: 30px auto;
  }

  .table-row {
    grid-template-columns: 1fr;
  }

  .col-xl-3.col-lg-4 {
    display: none;
  }

  .col-xl-9.col-lg-8 {
    width: 100%;
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
  }

  .rosc-button {
    height: auto;
    font-size: 25px;
  }

  .cpr-button svg {
    width: 24px;
    height: 24px;
  }

  .rosc-button svg {
    width: 30px;
    height: 30px;
  }

  .table-container {
    width: calc(100% - 30px);
    margin: 40px auto 0;
  }
}

@media (max-width: 480px) {
  .card-grid {
    width: calc(100% - 20px);
    margin: 30px auto;
  }

  .cpr-card-content {
    font-size: 14px;
  }

  .rosc-button {
    height: auto;
    font-size: 20px;
  }

  .cpr-button svg {
    width: 20px;
    height: 20px;
  }

  .rosc-button svg {
    width: 24px;
    height: 24px;
  }

  .cpr-card:nth-child(3) .button-container {
    width: calc(140px * 2 + 10px);
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
  }

  .cpr-card:nth-child(3) .cpr-button,
  .cpr-card:nth-child(3) .cpr-button-secondary {
    width: 140px;
    height: 45px;
    font-size: 12px;
  }

  .table-container {
    width: calc(100% - 20px);
    margin: 40px auto 0;
  }
}

.cpr-card:nth-child(3) .cpr-button {
  background-color: rgba(215, 221, 222, 0.85);
  color: #C89021;
}

.cpr-card:nth-child(3) .cpr-button-secondary {
  background-color: #BBDADF;
  color: #071952;
}

.cpr-card:first-child {
  background-color: rgba(55, 183, 195, 0.06);
}

.cpr-card:nth-child(2) {
  background-color: rgba(222, 5, 5, 0.19);
}

.cpr-card:nth-child(3) {
  background-color: rgba(195, 158, 55, 0.06);
  padding-top: 120px;
}

.cpr-card:first-child .cpr-button {
  background-color: #071952;
}

.cpr-card:nth-child(2) .cpr-button {
  background-color: #dc3545;
}

.cpr-card:first-child .cpr-card-content h3,
.cpr-card:nth-child(2) .cpr-card-content h3,
.cpr-card:nth-child(3) .cpr-card-content h3 {
  color: red;
  font-size: 20px;
}

.cpr-card:first-child .cpr-card-content p:nth-child(2),
.cpr-card:first-child .cpr-card-content p:nth-child(3),
.cpr-card:nth-child(2) .cpr-card-content p:nth-child(2),
.cpr-card:nth-child(2) .cpr-card-content p:nth-child(3) {
  text-align: left;
}

.cpr-card:nth-child(3) .cpr-card-content p:nth-child(2),
.cpr-card:nth-child(3) .cpr-card-content p:nth-child(3) {
  text-align: left;
}

.sidebar.-dashboard,
.col-xl-9.col-lg-8,
.card-grid,
.table-row,
.cpr-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
