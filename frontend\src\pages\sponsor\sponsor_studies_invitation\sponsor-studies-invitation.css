/* Sponsor Studies Invitation Page Styles */
.studies-container {
  padding: 30px;
  min-height: calc(100vh - 80px);
  background-color: #f5f7fa;
  font-family: var(--font-primary);
}

/* Page Header */
.studies-header {
  margin-bottom: 30px;
}

.studies-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Filters Section */
.studies-controls {
  background: #ffffff;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 30px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  border: 1px solid rgba(55, 183, 195, 0.1);
  transition: all 0.3s ease;
}

.studies-controls:hover {
  box-shadow: 0 12px 32px rgba(55, 183, 195, 0.12),
              0 4px 8px rgba(55, 183, 195, 0.06);
  transform: translateY(-2px);
}

/* Search and Filter Container */
.search-filter-container {
  margin-bottom: 20px;
}

/* Policy Search Container (reusing existing styles) */
.policy-search-container {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.policy-search-box {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(55, 183, 195, 0.03);
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 12px;
  transition: all 0.3s ease;
  overflow: hidden;
}

.policy-search-box:hover {
  background: rgba(55, 183, 195, 0.05);
  border-color: rgba(55, 183, 195, 0.25);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.08);
}

.policy-search-box:focus-within {
  background: #ffffff;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.1);
  transform: translateY(-1px);
}

.search-icon {
  position: absolute;
  left: 16px;
  color: var(--color-purple-1);
  z-index: 2;
  transition: all 0.3s ease;
}

.policy-search-input {
  width: 100%;
  padding: 16px 20px;
  padding-left: 50px;
  padding-right: 50px;
  border: none;
  background: transparent;
  font-size: 15px;
  color: var(--color-dark-1);
  outline: none;
  transition: all 0.3s ease;
}

.policy-search-input::placeholder {
  color: var(--color-light-1);
  font-style: italic;
}

.clear-search {
  position: absolute;
  right: 16px;
  background: none;
  border: none;
  color: var(--color-light-1);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.3s ease;
  z-index: 2;
}

.clear-search:hover {
  background: rgba(55, 183, 195, 0.1);
  color: var(--color-purple-1);
  transform: scale(1.1);
}

/* Loading State */
.loading-studies {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.05);
  border: 1px solid rgba(55, 183, 195, 0.1);
  color: var(--color-light-1);
  font-size: 16px;
  font-weight: 500;
}

.loading-studies::before {
  content: "";
  width: 24px;
  height: 24px;
  border: 3px solid rgba(55, 183, 195, 0.2);
  border-top: 3px solid var(--color-purple-1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* No Studies Message */
.no-studies {
  background: #ffffff;
  border-radius: 16px;
  padding: 60px 20px;
  text-align: center;
  color: var(--color-light-1);
  font-size: 16px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.05);
  border: 1px solid rgba(55, 183, 195, 0.1);
  margin-top: 20px;
}

/* Enhanced DataTable Integration */
.studies-container .data-table-container {
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  border: 1px solid rgba(55, 183, 195, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.studies-container .data-table-container:hover {
  box-shadow: 0 12px 32px rgba(55, 183, 195, 0.12),
              0 4px 8px rgba(55, 183, 195, 0.06);
  transform: translateY(-2px);
}

/* Custom table header styling */
.studies-container .data-table thead th {
  background: linear-gradient(135deg, rgba(55, 183, 195, 0.08) 0%, rgba(55, 183, 195, 0.12) 100%);
  color: var(--color-dark-1);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
  border-bottom: 2px solid rgba(55, 183, 195, 0.15);
}

.studies-container .data-table tbody tr:hover {
  background: rgba(55, 183, 195, 0.03);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.08);
}

/* Pagination styling */
.studies-container .data-table-pagination {
  background: linear-gradient(135deg, rgba(55, 183, 195, 0.02) 0%, rgba(55, 183, 195, 0.05) 100%);
  border-top: 1px solid rgba(55, 183, 195, 0.15);
}

.studies-container .pagination-page.active {
  background: var(--color-purple-1);
  border-color: var(--color-purple-1);
  color: white;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.studies-container .pagination-button:hover:not(:disabled),
.studies-container .pagination-page:hover:not(.active) {
  background: rgba(55, 183, 195, 0.1);
  border-color: rgba(55, 183, 195, 0.2);
  color: var(--color-purple-1);
}

/* Invite Button Styling */
.invite-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, #2d919a 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.25);
  text-decoration: none;
}

.invite-btn:hover {
  background: linear-gradient(135deg, #2d919a 0%, #237a82 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(55, 183, 195, 0.4);
  color: white;
}

.invite-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.invite-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.3), 0 6px 20px rgba(55, 183, 195, 0.4);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .studies-container {
    padding: 24px;
  }
}

@media (max-width: 991px) {
  .studies-container {
    padding: 20px;
  }
  
  .studies-header h1 {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .studies-controls {
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .policy-search-container {
    max-width: 100%;
  }
}

@media (max-width: 767px) {
  .studies-container {
    padding: 15px;
  }
  
  .studies-header h1 {
    font-size: 22px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .studies-controls {
    padding: 16px;
  }
  
  .search-filter-container {
    margin-bottom: 16px;
  }
  
  .policy-search-input {
    padding: 14px 18px;
    padding-left: 45px;
    padding-right: 45px;
    font-size: 14px;
  }
  
  .invite-btn {
    padding: 6px 12px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .policy-search-box {
    border-radius: 10px;
  }
  
  .invite-btn {
    width: 100%;
    justify-content: center;
  }
}

/* Animation for smooth transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.studies-container > * {
  animation: fadeInUp 0.3s ease-out forwards;
}

.studies-container > *:nth-child(2) {
  animation-delay: 0.1s;
}

.studies-container > *:nth-child(3) {
  animation-delay: 0.2s;
}

/* Focus states for accessibility */
.policy-search-input:focus,
.invite-btn:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .studies-controls {
    border: 2px solid var(--color-dark-1);
  }
  
  .policy-search-box {
    border: 2px solid var(--color-dark-1);
  }
  
  .invite-btn {
    border: 2px solid var(--color-dark-1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .studies-container {
    background: white;
    padding: 0;
  }
  
  .studies-controls {
    display: none;
  }
  
  .invite-btn {
    display: none;
  }
  
  .data-table-pagination {
    display: none;
  }
}
