/* frontend/src/components/modal.css */

/* Prevent body scroll when modal is open */
body.modal-open {
  overflow: hidden;
}

.nurtify-modal-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000; /* Ensure it's above other content */
  padding: 1rem;
  animation: fadeInOverlay 0.3s ease-out;
}

.nurtify-modal-content {
  background-color: white;
  border-radius: 12px; /* Rounded corners */
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); /* Soft shadow */
  max-height: 90vh; /* Limit height */
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent content overflow */
  animation: fadeInModal 0.3s ease-out;
}

/* Size variations */
.nurtify-modal-sm { max-width: 400px; width: 90%; }
.nurtify-modal-md { max-width: 600px; width: 90%; } /* Default */
.nurtify-modal-lg { max-width: 800px; width: 90%; }
.nurtify-modal-xl { max-width: 1140px; width: 90%; }

.nurtify-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb; /* Light border */
  flex-shrink: 0; /* Prevent header from shrinking */
}

.nurtify-modal-title {
  font-size: 1.25rem; /* 20px */
  font-weight: 600;
  color: #1f2937; /* Dark gray */
  margin: 0;
}

.nurtify-modal-close-button {
  background: none;
  border: none;
  color: #6b7280; /* Medium gray */
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  line-height: 1;
  transition: color 0.2s ease;
}

.nurtify-modal-close-button:hover {
  color: #1f2937; /* Darker gray on hover */
}

.nurtify-modal-body {
  padding: 1.5rem;
  overflow-y: auto; /* Allow body content to scroll */
  flex-grow: 1; /* Allow body to take available space */
}

/* Animations */
@keyframes fadeInOverlay {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInModal {
  from { opacity: 0; transform: translateY(-20px) scale(0.95); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}
