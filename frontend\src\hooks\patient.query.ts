import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type { Allergy, Patient, PatientCreateData, PatientVisitToday, RegistrationStatusLog } from "@/services/api/types";
import type {  PatientAccessLog, AddPatientAccessLogData, MedicalHistory, PaginatedResponse, CreateMedicalHistoryData, MedicalHistoryConditionCreate, ConcomitantMedication, CreateConcomitantMedicationData, PatientSearchParams } from "@/services/api/types";
import {
  getAllPatients,
  getPatientByNhsNumber,
  getPatientAllergies,
  createPatient,
  updatePatient,
  partialUpdatePatient,
  deletePatientByNhsNumber,
  createPatientEnrollment,
  getTodayVisits,
  updateVisit,
  getVisitsByDate,
  getVisits,
  getRegistrationStatusLogsByVisit,
  getPatientAccessLogs,
  addPatientAccessLog,
  getPatientAccessLogsByUuid,
  getMyAccessLogs,
  getPatientMedicalHistory,
  createMedicalHistory,
  updateMedicalHistory,
  editMedicalHistoryCondition,
  deleteMedicalHistoryCondition,
  getPatientConcomitantMedications,
  createConcomitantMedication,
  updateConcomitantMedication,
  deleteConcomitantMedication,
  searchPatients,
  updatePatientProfilePicture,
} from "@/services/api/patient.service";
import { PATIENT_KEYS } from "./keys";
import { PatientEnrollment } from "@/store/scheduleEventState";

export const usePatientsQuery = () => {
  return useQuery<Patient[], Error>({
    queryKey: [PATIENT_KEYS.GET_ALL],
    queryFn: getAllPatients,
  });
};

export const usePatientQuery = (nhs_number: string) => {
  return useQuery<Patient, Error>({
    queryKey: [PATIENT_KEYS.GET_BY_NHS_NUMBER, nhs_number],
    queryFn: () => getPatientByNhsNumber(nhs_number),
    enabled: !!nhs_number,
  });
};

export const usePatientAllergiesQuery = (nhs_number: string) => {
  return useQuery<Allergy[], Error>({
    queryKey: [PATIENT_KEYS.GET_Allergies_BY_NHS_NUMBER, nhs_number],
    queryFn: () => getPatientAllergies(nhs_number),
    enabled: !!nhs_number,
  });
};

export const useCreatePatientMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Patient, Error, PatientCreateData>({
    mutationFn: createPatient,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PATIENT_KEYS.GET_ALL] });
    },
  });
};

export const useUpdatePatientMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Patient,
    Error,
    { nhs_number: string; data: Partial<Patient> }
  >({
    mutationFn: ({ nhs_number, data }) => updatePatient(nhs_number, data),
    onSuccess: (_, { nhs_number }) => {
      queryClient.invalidateQueries({ queryKey: [PATIENT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({
        queryKey: [PATIENT_KEYS.GET_BY_NHS_NUMBER, nhs_number],
      });
    },
  });
};

export const usePartialUpdatePatientMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<
    Patient,
    Error,
    { uuid: string; data: Partial<Patient> }
  >({
    mutationFn: ({ uuid, data }) => partialUpdatePatient(uuid, data),
    onSuccess: (updatedPatient) => {
      // Invalidate all patient queries to ensure data consistency
      queryClient.invalidateQueries({ queryKey: [PATIENT_KEYS.GET_ALL] });

      // If the updated patient has an NHS number, invalidate that specific query
      if (updatedPatient.nhs_number) {
        queryClient.invalidateQueries({
          queryKey: [PATIENT_KEYS.GET_BY_NHS_NUMBER, updatedPatient.nhs_number],
        });
      }
    },
  });
};

export const useDeletePatientMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: (nhs_number) => deletePatientByNhsNumber(nhs_number),
    onSuccess: (_, nhs_number) => {
      queryClient.invalidateQueries({ queryKey: [PATIENT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({
        queryKey: [PATIENT_KEYS.GET_BY_NHS_NUMBER, nhs_number],
      });
    },
  });
};

export const usePatientEnrollmentsMutation = () => {
  return useMutation<PatientEnrollment, Error, PatientEnrollment>({
    mutationFn: (enrollment) => createPatientEnrollment(enrollment),
    onSuccess: () => {
      // queryClient.invalidateQueries({ queryKey: [PATIENT_KEYS.GET_ALL] });
    },
  });
};

export const useTodayVisitsQuery = () => {
  return useQuery<PatientVisitToday[], Error>({
    queryKey: [PATIENT_KEYS.GET_TODAY_VISITS],
    queryFn: getTodayVisits,
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

export const useUpdateVisitMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      patientUuid,
      visitUuid,
      data,
    }: {
      patientUuid: string;
      visitUuid: string;
      data: {
        registration_status: "Not Arrived" | "In Hospital" | "Discharged";
        location: string;
        nurse_identifier: string;
      };
    }) => updateVisit(patientUuid, visitUuid, data),
    onSuccess: () => {
      // Invalidate and refetch the visits list
      queryClient.invalidateQueries({ queryKey: [PATIENT_KEYS.GET_TODAY_VISITS] });
      queryClient.invalidateQueries({ queryKey: [PATIENT_KEYS.GET_VISITS_BY_DATE] });
      // Also invalidate the new unified visits query
      queryClient.invalidateQueries({ queryKey: [PATIENT_KEYS.GET_VISITS] });
    },
  });
};

export const useVisitsByDateQuery = (date: string) => {
  return useQuery<PatientVisitToday[], Error>({
    queryKey: [PATIENT_KEYS.GET_VISITS_BY_DATE, date],
    queryFn: () => getVisitsByDate(date),
    enabled: !!date,
  });
};

/**
 * Hook to fetch visits based on the specified time period.
 * @param period Time period (D=day, W=week, M=month)
 * @param referenceDate Optional reference date in YYYY-MM-DD format. Defaults to current date if not provided.
 * @param useLocalDate Whether to use client's local date instead of server date
 * @param options Additional options for the query
 * @returns Query result with patient visits data
 */
export const useVisits = (
  period: "D" | "W" | "M",
  referenceDate?: string,
  useLocalDate = false,
  options?: any
) => {
  // Create a cache key based on the period and date
  const dateKey = referenceDate || (useLocalDate ? "local-today" : "today");

  return useQuery<PatientVisitToday[], Error>({
    queryKey: [PATIENT_KEYS.GET_VISITS, period, dateKey],
    queryFn: () => getVisits(period, referenceDate, useLocalDate),
    enabled: !!period, // Only run if period is provided
  }, options);
};

// Legacy hook - will be removed in future versions
export const useVisitsQuery = (date: string) => {
  console.warn("useVisitsQuery is deprecated. Please use useVisits('D', date) instead.");
  return useVisits("D", date);
};

export const useRegistrationStatusLogsQuery = (visitUuid: string) => {
  return useQuery<RegistrationStatusLog[], Error>({
    queryKey: [PATIENT_KEYS.GET_REGISTRATION_STATUS_LOGS, visitUuid],
    queryFn: () => getRegistrationStatusLogsByVisit(visitUuid),
    enabled: !!visitUuid,
  });
};
export const usePatientAccessLogsQuery = () => {
  return useQuery<PatientAccessLog[], Error>({
    queryKey: [PATIENT_KEYS.GET_ACCESS_LOGS],
    queryFn: getPatientAccessLogs,
  });
};

export const usePatientAccessLogsByUuidQuery = (patientUuid: string) => {
  return useQuery<PatientAccessLog[], Error>({
    queryKey: [PATIENT_KEYS.GET_ACCESS_LOGS_BY_UUID, patientUuid],
    queryFn: () => getPatientAccessLogsByUuid(patientUuid),
    enabled: !!patientUuid,
  });
};

export const useMyAccessLogsQuery = () => {
  return useQuery<PatientAccessLog[], Error>({
    queryKey: [PATIENT_KEYS.GET_MY_ACCESS_LOGS],
    queryFn: getMyAccessLogs,
  });
};

export const useAddPatientAccessLogMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<PatientAccessLog, Error, AddPatientAccessLogData>({
    mutationFn: addPatientAccessLog,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [PATIENT_KEYS.GET_ACCESS_LOGS] });
    },
  });
};

export const usePatientMedicalHistoryQuery = (patientUuid: string) => {
  return useQuery<PaginatedResponse<MedicalHistory>, Error>({
    queryKey: [PATIENT_KEYS.GET_MEDICAL_HISTORY, patientUuid],
    queryFn: () => getPatientMedicalHistory(patientUuid),
    enabled: !!patientUuid,
  });
};

export const useCreateMedicalHistoryMutation = (patientUuid: string) => {
  const queryClient = useQueryClient();
  return useMutation<MedicalHistory, Error, CreateMedicalHistoryData>({
    mutationFn: (data) => createMedicalHistory(patientUuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [PATIENT_KEYS.GET_MEDICAL_HISTORY, patientUuid]
      });
    },
  });
};

export const useUpdateMedicalHistoryMutation = (patientUuid: string, medicalHistoryUuid: string) => {
  const queryClient = useQueryClient();
  return useMutation<MedicalHistory, Error, CreateMedicalHistoryData>({
    mutationFn: (data) => updateMedicalHistory(patientUuid, medicalHistoryUuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [PATIENT_KEYS.GET_MEDICAL_HISTORY, patientUuid]
      });
    },
  });
};

export const useEditMedicalHistoryConditionMutation = (
  patientUuid: string,
  medicalHistoryUuid: string,
  conditionUuid: string
) => {
  const queryClient = useQueryClient();
  return useMutation<MedicalHistory, Error, MedicalHistoryConditionCreate>({
    mutationFn: (data) => editMedicalHistoryCondition(patientUuid, medicalHistoryUuid, conditionUuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [PATIENT_KEYS.GET_MEDICAL_HISTORY, patientUuid]
      });
    },
  });
};

export const useDeleteMedicalHistoryConditionMutation = (
  patientUuid: string,
  medicalHistoryUuid: string,
  conditionUuid: string
) => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, void>({
    mutationFn: () => deleteMedicalHistoryCondition(patientUuid, medicalHistoryUuid, conditionUuid),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [PATIENT_KEYS.GET_MEDICAL_HISTORY, patientUuid]
      });
    },
  });
};

export const usePatientConcomitantMedicationsQuery = (patientUuid: string) => {
  return useQuery<PaginatedResponse<ConcomitantMedication>, Error>({
    queryKey: [PATIENT_KEYS.GET_CONCOMITANT_MEDICATIONS, patientUuid],
    queryFn: () => getPatientConcomitantMedications(patientUuid),
    enabled: !!patientUuid,
  });
};

export const useCreateConcomitantMedicationMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<ConcomitantMedication, Error, CreateConcomitantMedicationData>({
    mutationFn: createConcomitantMedication,
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: [PATIENT_KEYS.GET_CONCOMITANT_MEDICATIONS, data.patient]
      });
    },
  });
};

export const useUpdateConcomitantMedicationMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<ConcomitantMedication, Error, { uuid: string; data: Partial<CreateConcomitantMedicationData> }>({
    mutationFn: ({ uuid, data }) => updateConcomitantMedication(uuid, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({
        queryKey: [PATIENT_KEYS.GET_CONCOMITANT_MEDICATIONS, data.patient]
      });
    },
  });
};

export const useDeleteConcomitantMedicationMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, { uuid: string; patientUuid: string }>({
    mutationFn: ({ uuid }) => deleteConcomitantMedication(uuid),
    onSuccess: (_, { patientUuid }) => {
      queryClient.invalidateQueries({
        queryKey: [PATIENT_KEYS.GET_CONCOMITANT_MEDICATIONS, patientUuid]
      });
    },
  });
};

/**
 * Hook to fetch visits for a specific week
 * @param date Reference date in YYYY-MM-DD format
 * @returns Query result with patient visits data for the week containing the reference date
 */
export const useVisitsByWeekQuery = (date: string, options?: any) => {
  console.warn("useVisitsByWeekQuery is deprecated. Please use useVisits('W', date) instead.");
  return useVisits("W", date, false, options);
};

/**
 * Hook to fetch visits for a specific month
 * @param date Reference date in YYYY-MM-DD format
 * @returns Query result with patient visits data for the month containing the reference date
 */
export const useVisitsByMonthQuery = (date: string, options?: any) => {
  console.warn("useVisitsByMonthQuery is deprecated. Please use useVisits('M', date) instead.");
  return useVisits("M", date, false, options);
};

/**
 * Hook to search for patients using various criteria.
 * All parameters are optional and can be combined for more specific searches.
 * @param params Search parameters (nhs_number, first_name, last_name, full_name, date_of_birth, medical_record_number, gender, study)
 * @param enabled Whether the query should automatically run
 * @returns Query result with matching patients
 */
export const useSearchPatients = (params: PatientSearchParams, enabled = true) => {
  return useQuery<PaginatedResponse<Patient>, Error>({
    queryKey: [PATIENT_KEYS.SEARCH, params],
    queryFn: () => searchPatients(params),
    enabled: enabled && Object.values(params).some(value => !!value), // Only run if at least one parameter has a value
  });
};

export const useUpdatePatientProfilePictureMutation = (patientUuid: string) => {
  const queryClient = useQueryClient();
  return useMutation<
    { uuid: string; profile_picture: string; profile_picture_url: string },
    Error,
    File
  >({
    mutationFn: (profilePicture: File) => updatePatientProfilePicture(patientUuid, profilePicture),
    onSuccess: () => {
      // Invalidate patient queries to refresh the data
      queryClient.invalidateQueries({ queryKey: [PATIENT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [PATIENT_KEYS.GET_BY_NHS_NUMBER, patientUuid] });
    },
  });
};
