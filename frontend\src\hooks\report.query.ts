import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type { Report, ReportCreateData, ReportUpdateData, ReviewReport, ReviewReportCreateData, ReportContent, ReportStats, ReportTypes } from "@/services/api/types";
import {
  getAllReports,
  getReportByUuid,
  createReport,
  updateReport,
  deleteReport,
  getReportContent,
  getReportTypes,
  getReportStats,
  getAllReviewReports,
  getReviewReportByUuid,
  createReviewReport,
  updateReviewReport,
  deleteReviewReport,
  getReviewReportsByReportUuid,
  getSubInvestigators,
  getReportsByPatient,
  downloadReportAttachment,
} from "@/services/api/report.service";
import { REPORT_KEYS } from "./keys";

// Report Queries
export const useReportsQuery = () => {
  return useQuery<Report[], Error>({
    queryKey: [REPORT_KEYS.GET_ALL],
    queryFn: getAllReports,
  });
};

export const useReportQuery = (uuid: string) => {
  return useQuery<Report, Error>({
    queryKey: [REPORT_KEYS.GET_BY_UUID, uuid],
    queryFn: () => getReportByUuid(uuid),
    enabled: !!uuid,
  });
};

export const useCreateReportMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Report, Error, ReportCreateData>({
    mutationFn: createReport,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [REPORT_KEYS.GET_ALL] });
    },
  });
};

export const useUpdateReportMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Report, Error, { uuid: string; data: ReportUpdateData }>({
    mutationFn: ({ uuid, data }) => updateReport(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({ queryKey: [REPORT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [REPORT_KEYS.GET_BY_UUID, uuid] });
    },
  });
};

export const useDeleteReportMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: (uuid) => deleteReport(uuid),
    onSuccess: (_, uuid) => {
      queryClient.invalidateQueries({ queryKey: [REPORT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [REPORT_KEYS.GET_BY_UUID, uuid] });
    },
  });
};

export const useReportContentQuery = (uuid: string) => {
  return useQuery<ReportContent, Error>({
    queryKey: [REPORT_KEYS.GET_CONTENT, uuid],
    queryFn: () => getReportContent(uuid),
    enabled: !!uuid,
  });
};

export const useReportTypesQuery = () => {
  return useQuery<ReportTypes, Error>({
    queryKey: [REPORT_KEYS.GET_TYPES],
    queryFn: getReportTypes,
  });
};

export const useReportStatsQuery = () => {
  return useQuery<ReportStats, Error>({
    queryKey: [REPORT_KEYS.GET_STATS],
    queryFn: getReportStats,
  });
};

export const useReportsByPatientQuery = (patientUuid: string, page: number = 1) => {
  return useQuery<{
    reports: Report[];
    count: number;
    patient: {
      uuid: string;
      first_name: string;
      last_name: string;
    };
  }, Error>({
    queryKey: [REPORT_KEYS.GET_BY_PATIENT, patientUuid, page],
    queryFn: () => getReportsByPatient(patientUuid, page),
    enabled: !!patientUuid && patientUuid !== '',
    refetchOnWindowFocus: true,
    refetchOnMount: true,
  });
};

// Review Report Queries
export const useReviewReportsQuery = () => {
  return useQuery<ReviewReport[], Error>({
    queryKey: [REPORT_KEYS.GET_ALL_REVIEWS],
    queryFn: getAllReviewReports,
  });
};

export const useReviewReportQuery = (uuid: string) => {
  return useQuery<ReviewReport, Error>({
    queryKey: [REPORT_KEYS.GET_BY_UUID_REVIEW, uuid],
    queryFn: () => getReviewReportByUuid(uuid),
    enabled: !!uuid,
  });
};

export const useCreateReviewReportMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<ReviewReport, Error, ReviewReportCreateData>({
    mutationFn: createReviewReport,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [REPORT_KEYS.GET_ALL_REVIEWS] });
    },
  });
};

export const useUpdateReviewReportMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<ReviewReport, Error, { uuid: string; data: Partial<ReviewReportCreateData> }>({
    mutationFn: ({ uuid, data }) => updateReviewReport(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({ queryKey: [REPORT_KEYS.GET_ALL_REVIEWS] });
      queryClient.invalidateQueries({ queryKey: [REPORT_KEYS.GET_BY_UUID_REVIEW, uuid] });
    },
  });
};

export const useDeleteReviewReportMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: (uuid) => deleteReviewReport(uuid),
    onSuccess: (_, uuid) => {
      queryClient.invalidateQueries({ queryKey: [REPORT_KEYS.GET_ALL_REVIEWS] });
      queryClient.invalidateQueries({ queryKey: [REPORT_KEYS.GET_BY_UUID_REVIEW, uuid] });
    },
  });
};

export const useReviewReportsByReportQuery = (reportUuid: string) => {
  return useQuery<ReviewReport[], Error>({
    queryKey: [REPORT_KEYS.GET_BY_REPORT, reportUuid],
    queryFn: () => getReviewReportsByReportUuid(reportUuid),
    enabled: !!reportUuid,
  });
};

export const useSubInvestigatorsQuery = () => {
  return useQuery<any[], Error>({
    queryKey: ["sub_investigators"],
    queryFn: getSubInvestigators,
  });
};

// Download Report Attachment Mutation
export const useDownloadReportAttachmentMutation = () => {
  return useMutation<Blob, Error, string>({
    mutationFn: downloadReportAttachment,
  });
};
