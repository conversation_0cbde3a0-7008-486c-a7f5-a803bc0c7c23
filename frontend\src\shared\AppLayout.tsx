import React, { ReactNode } from 'react';
import { useLocation } from 'react-router-dom';
import LandingHeader from './LandingHeader';
import Header from './Header';

interface AppLayoutProps {
  children: ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const location = useLocation();
  const path = location.pathname;

  // Define routes where header should not be shown
  const noHeaderRoutes = [
    '/',
    '/login',
    '/otp',
    '/patient',
    '/sponsor',
  ];

  // Check if the current path starts with any of these prefixes
  const noHeaderPrefixes = [
    '/account/activate/',
    '/account/reset-password/',
    '/patient/',
    '/sponsor/',
    '/test-new-dashboard'
  ];

  // Prevent potential infinite loop by ensuring usedHeader logic is correct
  // usedHeader is true if path exactly matches any of mainPagesPrefixes
  const mainPagesPrefixes = [
    '/home',
    '/',
    '/contact-us'
  ];

  const usedHeader = mainPagesPrefixes.includes(path);

  // shouldShowHeader follows the original logic
  const shouldShowHeader = !noHeaderRoutes.includes(path) &&
    !noHeaderPrefixes.some(prefix => path.startsWith(prefix));

  return (
    <div className="app-layout">
      {shouldShowHeader && usedHeader && <LandingHeader />}
      {shouldShowHeader && !usedHeader && <Header />}
      <main>
        {children}
      </main>
    </div>
  );
};

export default AppLayout;
