import { useEffect } from "react";
import Links from "@/components/common/Links";
import { Globe } from 'lucide-react';
import "./footer.css";
import { setupFooterScrollListener } from "@/utils/scrollUtils";

export default function LightFooter() {
  useEffect(() => {
    // Setup scroll listener for footer visibility
    const cleanup = setupFooterScrollListener(".modern-footer");

    // Cleanup on component unmount
    return cleanup;
  }, []);

  return (
    <footer
      className="modern-footer"
      style={{
        backgroundColor: "#071952",
        left: 0,
        bottom: 0,
        width: "100%",
        zIndex: 100,
      }}
    >
      <div className="container">
        <div className="modern-footer__content">
          <div className="modern-footer__row">
            <div className="modern-footer__copyright">
              © {new Date().getFullYear()} Nurtify. All Rights Reserved.
            </div>

            <div className="modern-footer__right">
              <div className="modern-footer__links">
                <Links allClasses="modern-footer__link" />
              </div>

              <button className="modern-footer__language">
                <Globe size={16} className="modern-footer__icon" />
                <span>English</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
