 
import { useState } from "react";
import brainImage from "./static/images/added/brain.png";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyRadio from "@/components/NurtifyRadio";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyText from "@/components/NurtifyText";

// Interface for NurtifyRadio component props - used for type checking


const Disability = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  const [disabilityAssesRequired, setDisabilityAssesRequired] = useState("");

  const handleEChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setAssessment({
      ...assessment,
      disability: {
        ...assessment.disability,
        GCS: { ...assessment.disability.GCS, eyeOpening: value },
        AVPU: ""
      }
    });
  };

  const setDisability = (disabilityData: Partial<typeof assessment.disability>) => {
    setAssessment({
      ...assessment,
      disability: {
        ...assessment.disability,
        ...disabilityData
      }
    });
  };

  const setLeftLegMovement = (value: string) => {
    setAssessment({
      ...assessment,
      disability: {
        ...assessment.disability,
        leftLegMovement: value,
        limbMovement: { 
          ...assessment.disability.limbMovement || {}, 
          leftLeg: value 
        },
      }
    });
  }

  const setRightLegMovement = (value: string) => {
    setAssessment({
      ...assessment,
      disability: {
        ...assessment.disability,
        rightLegMovement: value,
        limbMovement: { 
          ...assessment.disability.limbMovement || {}, 
          rightLeg: value 
        },
      }
    });
  }
  
  const setRightArmMovement = (value: string) => {
    setAssessment({
      ...assessment,
      disability: {
        ...assessment.disability,
        rightArmMovement: value,
        limbMovement: { 
          ...assessment.disability.limbMovement || {}, 
          rightArm: value 
        },
      }
    });
  }

  const setLeftArmMovement = (value: string) => {
    setAssessment({
      ...assessment,
      disability: {
        ...assessment.disability,
        leftArmMovement: value,
        limbMovement: { 
          ...assessment.disability.limbMovement || {}, 
          leftArm: value 
        },
      }
    });
  }

  const handleVChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setAssessment({
      ...assessment,
      disability: {
        ...assessment.disability,
        GCS: { ...assessment.disability.GCS, verbalResponse: value },
        AVPU: ""
      }
    });
  };

  const handleMChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setAssessment({
      ...assessment,
      disability: {
        ...assessment.disability,
        GCS: { ...assessment.disability.GCS, motorResponse: value },
        AVPU: ""
      }
    });
  };

  const handleAVPUChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setAssessment({
      ...assessment,
      disability: {
        ...assessment.disability,
        AVPU: value,
        GCS: { eyeOpening: "", verbalResponse: "", motorResponse: "" }
      }
    });
  };

  return (
    <div className="block align-items-*-start flex-column flex-md-row p-4 gap-5">
      <div id="division-26">
        {/* Section Title  */}

          <div className="inlineBlock mb-4 headinqQuestion">
            <img
              src={brainImage}
              className="imageEtiquette"
              alt="patient face image round"
            />
            <span className="mb-2 py-2 fs-2 text-start etiquetteHeadingForms">
              Disability
            </span>
          </div>

          <div className="list-group col-xl-6 col-lg-8 col-md-12">
            <NurtifyText label="Level of consciousness Scale:" className="mb-2 fw-bold" />
            <div onClick={() => setDisabilityAssesRequired("gcs")}>
              <NurtifyRadio
                name="disabilityAssesRequired"
                value="gcs"
                label="GCS"
                checked={disabilityAssesRequired == "gcs"}
                onChange={() => setDisabilityAssesRequired("gcs")}
              />
            </div>

            <div onClick={() => setDisabilityAssesRequired("avpu")}>
              <NurtifyRadio
                name="disabilityAssesRequired"
                value="avpu"
                label="AVPU"
                checked={disabilityAssesRequired == "avpu"}
                onChange={() => setDisabilityAssesRequired("avpu")}
              />
            </div>
          </div>

          {disabilityAssesRequired == "gcs" ? (
            <>
              <div
                className="d-flex flex-wrap align-items-*-start flex-column flex-md-row "
                id="division-26-a"
              >
              <div className="list-group col-sm-12 col-md-6 col-lg-4 col-xl-3 me-4 mt-3">
                  <NurtifyText label="Eye Opening" className="mb-2 fw-bold" />

                  <NurtifyRadio
                    name="eyeOpening"
                    value="4"
                    label="Spontaneously"
                    checked={assessment?.disability?.GCS?.eyeOpening === "4"}
                    onChange={handleEChange}
                  />

                  <NurtifyRadio
                    name="eyeOpening"
                    value="3"
                    label="To Verbal Command"
                    checked={assessment?.disability?.GCS?.eyeOpening === "3"}
                    onChange={handleEChange}
                  />

                  <NurtifyRadio
                    name="eyeOpening"
                    value="2"
                    label="To Pain"
                    checked={assessment?.disability?.GCS?.eyeOpening === "2"}
                    onChange={handleEChange}
                  />

                  <NurtifyRadio
                    name="eyeOpening"
                    value="1"
                    label="No Eye Opening"
                    checked={assessment?.disability?.GCS?.eyeOpening === "1"}
                    onChange={handleEChange}
                  />

                  <NurtifyRadio
                    name="eyeOpening"
                    value="Not Testable E"
                    label="Not Testable"
                    checked={assessment?.disability?.GCS?.eyeOpening === "Not Testable E"}
                    onChange={handleEChange}
                  />
                </div>

                <div className="list-group col-sm-12 col-md-6 col-lg-4 col-xl-3 me-4 mt-3">
                  <NurtifyText label="Verbal Response" className="mb-2 fw-bold" />

                  <NurtifyRadio
                    name="verbalResponse"
                    value="5"
                    label="Oriented"
                    checked={assessment?.disability?.GCS?.verbalResponse === "5"}
                    onChange={handleVChange}
                  />

                  <NurtifyRadio
                    name="verbalResponse"
                    value="4"
                    label="Confused"
                    checked={assessment?.disability?.GCS?.verbalResponse === "4"}
                    onChange={handleVChange}
                  />

                  <NurtifyRadio
                    name="verbalResponse"
                    value="3"
                    label="Inapropriate Words"
                    checked={assessment?.disability?.GCS?.verbalResponse === "3"}
                    onChange={handleVChange}
                  />

                  <NurtifyRadio
                    name="verbalResponse"
                    value="2"
                    label="Uncomprehensive Sounds"
                    checked={assessment?.disability?.GCS?.verbalResponse === "2"}
                    onChange={handleVChange}
                  />

                  <NurtifyRadio
                    name="verbalResponse"
                    value="1"
                    label="No Verbal Response"
                    checked={assessment?.disability?.GCS?.verbalResponse === "1"}
                    onChange={handleVChange}
                  />

                  <NurtifyRadio
                    name="verbalResponse"
                    value="Not Testable V"
                    label="Not Testable"
                    checked={assessment?.disability?.GCS?.verbalResponse === "Not Testable V"}
                    onChange={handleVChange}
                  />
                </div>

                <div className="list-group col-sm-12 col-md-6 col-lg-4 col-xl-3  me-4 mt-3">
                  <NurtifyText label="Motor Response" className="mb-2 fw-bold" />
                  
                  <NurtifyRadio
                    name="motorResponse"
                    value="6"
                    label="Obey Commands"
                    checked={assessment?.disability?.GCS?.motorResponse === "6"}
                    onChange={handleMChange}
                  />
                  
                  <NurtifyRadio
                    name="motorResponse"
                    value="5"
                    label="Localizes Pain"
                    checked={assessment?.disability?.GCS?.motorResponse === "5"}
                    onChange={handleMChange}
                  />
                  
                  <NurtifyRadio
                    name="motorResponse"
                    value="4"
                    label="Withdrawal from pain"
                    checked={assessment?.disability?.GCS?.motorResponse === "4"}
                    onChange={handleMChange}
                  />
                  
                  <NurtifyRadio
                    name="motorResponse"
                    value="3"
                    label="Flexion to pain"
                    checked={assessment?.disability?.GCS?.motorResponse === "3"}
                    onChange={handleMChange}
                  />
                  
                  <NurtifyRadio
                    name="motorResponse"
                    value="2"
                    label="Extension to pain"
                    checked={assessment?.disability?.GCS?.motorResponse === "2"}
                    onChange={handleMChange}
                  />
                  
                  <NurtifyRadio
                    name="motorResponse"
                    value="1"
                    label="No Motor Response"
                    checked={assessment?.disability?.GCS?.motorResponse === "1"}
                    onChange={handleMChange}
                  />
                  
                  <NurtifyRadio
                    name="motorResponse"
                    value="Not Testable M"
                    label="Not Testable"
                    checked={assessment?.disability?.GCS?.motorResponse === "Not Testable M"}
                    onChange={handleMChange}
                  />
                </div>
              </div>
            </>
          ) : null}

          {disabilityAssesRequired == "avpu" ? (
            <>
              <div
                className="list-group col-xl-6 col-lg-8 col-md-12 mt-3"
                id="division-26-b"
              >
                <NurtifyText label="AVPU Score" className="mb-2 fw-bold" />

                <NurtifyRadio
                  name="avpuScore"
                  value="Alert"
                  label="Alert"
                  checked={assessment?.disability?.AVPU === "Alert"}
                  onChange={handleAVPUChange}
                />
                
                <NurtifyRadio
                  name="avpuScore"
                  value="Confused(New Episode)"
                  label="Confused (New Episode)"
                  checked={assessment?.disability?.AVPU === "Confused(New Episode)"}
                  onChange={handleAVPUChange}
                />
                
                <NurtifyRadio
                  name="avpuScore"
                  value="Responding to Painful Stimulation"
                  label="Responding to Painful Stimulation"
                  checked={assessment?.disability?.AVPU === "Responding to Painful Stimulation"}
                  onChange={handleAVPUChange}
                />
                
                <NurtifyRadio
                  name="avpuScore"
                  value="Unresponsive"
                  label="Unresponsive"
                  checked={assessment?.disability?.AVPU === "Unresponsive"}
                  onChange={handleAVPUChange}
                />
              </div>
            </>
          ) : null}

          {/* Blood Glucose */}
          <div className="d-flex align-items-center mb-1 gap-2 col-md-4 mt-4">
            <NurtifyText label="Blood Glucose:" className="me-2" />
            <div style={{ width: "100px" }}>
              <NurtifyInput
                type="number"
                onChange={(e) =>
                  setDisability({
                    bloodGlucose: parseFloat(e.target.value) || 0,
                  })
                }
                value={assessment?.disability?.bloodGlucose || ""}
                min={0.1}
                max={45}
                step={0.1}
              />
            </div>
            <NurtifyText label="mmol/L." className="ms-2" />
          </div>

          {/* Pupil Assessment */}
          <div className="inlineBlock mt-4 justify-content-start gap-4">
            {/* Right Pupil */}
            <div className="d-flex flex-column mb-2 border border-1 badge border border-primary-subtle">
              <NurtifyText label="Right Pupil" className="fw-bold mb-2" />
              {/* Shape */}
              <NurtifyText label="Shape" className="mb-2" />

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                <NurtifyRadio
                  name="rightPupilShape"
                  value="Regular"
                  label="Regular"
                  checked={assessment?.disability?.rightPupil?.shape === "Regular"}
                  onChange={(e) => {
                    setDisability({
                      rightPupil: {
                        ...assessment.disability.rightPupil,
                        shape: e.target.value,
                      },
                    });
                  }}
                />

                <NurtifyRadio
                  name="rightPupilShape"
                  value="Irregular"
                  label="Irregular"
                  checked={assessment?.disability?.rightPupil?.shape === "Irregular"}
                  onChange={(e) => {
                    setDisability({
                      rightPupil: {
                        ...assessment.disability.rightPupil,
                        shape: e.target.value,
                      },
                    });
                  }}
                />

                <NurtifyRadio
                  name="rightPupilShape"
                  value="Unable to access"
                  label="Unable to access"
                  checked={assessment?.disability?.rightPupil?.shape === "Unable to access"}
                  onChange={(e) => {
                    setDisability({
                      rightPupil: {
                        ...assessment.disability.rightPupil,
                        shape: e.target.value,
                      },
                    });
                  }}
                />
              </div>

              {/* Responsiveness to light */}
              <NurtifyText label="Responsiveness to light" className="mb-2" />
              <div className="list-group d-flex flex-row flex-wrap gap-2 me-4 mt-2 mb-4">
                <NurtifyRadio
                  name="rightPupilResponsiveness"
                  value="Brisk"
                  label="Brisk"
                  checked={assessment?.disability?.rightPupil?.responsivenessToLight === "Brisk"}
                  onChange={(e) => {
                    setDisability({
                      rightPupil: {
                        ...assessment.disability.rightPupil,
                        responsivenessToLight: e.target.value,
                      },
                    });
                  }}
                />
                
                <NurtifyRadio
                  name="rightPupilResponsiveness"
                  value="Sluggish"
                  label="Sluggish"
                  checked={assessment?.disability?.rightPupil?.responsivenessToLight === "Sluggish"}
                  onChange={(e) => {
                    setDisability({
                      rightPupil: {
                        ...assessment.disability.rightPupil,
                        responsivenessToLight: e.target.value,
                      },
                    });
                  }}
                />
                
                <NurtifyRadio
                  name="rightPupilResponsiveness"
                  value="Non Reactive"
                  label="Non Reactive"
                  checked={assessment?.disability?.rightPupil?.responsivenessToLight === "Non Reactive"}
                  onChange={(e) => {
                    setDisability({
                      rightPupil: {
                        ...assessment.disability.rightPupil,
                        responsivenessToLight: e.target.value,
                      },
                    });
                  }}
                />
                
                <NurtifyRadio
                  name="rightPupilResponsiveness"
                  value="Unable to access"
                  label="Unable to access"
                  checked={assessment?.disability?.rightPupil?.responsivenessToLight === "Unable to access"}
                  onChange={(e) => {
                    setDisability({
                      rightPupil: {
                        ...assessment.disability.rightPupil,
                        responsivenessToLight: e.target.value,
                      },
                    });
                  }}
                />
              </div>
              {/* Size (in mm) */}
              <NurtifyText label="Size (in mm)" className="mb-2" />
              <div className="d-flex align-items-center mt-2 mb-3">
                <div style={{ width: "220px" }}>
                  <NurtifyInput
                    type="number"
                    value={assessment?.disability?.rightPupil?.size}
                    onChange={(e) => {
                      setDisability({
                        rightPupil: {
                          ...assessment.disability.rightPupil,
                          size: parseInt(e.target.value) || 0,
                        },
                      });
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Left pupil */}

            <div className="d-flex flex-column mb-2 border border-1 badge border border-primary-subtle">
              <NurtifyText label="Left Pupil" className="fw-bold mb-2" />
              {/* Shape */}
              <NurtifyText label="Shape" className="mb-2" />

              <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                <NurtifyRadio
                  name="leftPupilShape"
                  value="Regular"
                  label="Regular"
                  checked={assessment?.disability?.leftPupil?.shape === "Regular"}
                  onChange={(e) => {
                    setDisability({
                      leftPupil: {
                        ...assessment.disability.leftPupil,
                        shape: e.target.value,
                      },
                    });
                  }}
                />

                <NurtifyRadio
                  name="leftPupilShape"
                  value="Irregular"
                  label="Irregular"
                  checked={assessment?.disability?.leftPupil?.shape === "Irregular"}
                  onChange={(e) => {
                    setDisability({
                      leftPupil: {
                        ...assessment.disability.leftPupil,
                        shape: e.target.value,
                      },
                    });
                  }}
                />

                <NurtifyRadio
                  name="leftPupilShape"
                  value="Unable to access"
                  label="Unable to access"
                  checked={assessment?.disability?.leftPupil?.shape === "Unable to access"}
                  onChange={(e) => {
                    setDisability({
                      leftPupil: {
                        ...assessment.disability.leftPupil,
                        shape: e.target.value,
                      },
                    });
                  }}
                />
              </div>

              {/* Responsiveness to light */}
              <NurtifyText label="Responsiveness to light" className="mb-2" />
              <div className="list-group d-flex flex-row flex-wrap gap-2 me-4 mt-2 mb-4">
                <NurtifyRadio
                  name="leftPupilResponsiveness"
                  value="Brisk"
                  label="Brisk"
                  checked={assessment?.disability?.leftPupil?.responsivenessToLight === "Brisk"}
                  onChange={(e) => {
                    setDisability({
                      leftPupil: {
                        ...assessment.disability.leftPupil,
                        responsivenessToLight: e.target.value,
                      },
                    });
                  }}
                />
                
                <NurtifyRadio
                  name="leftPupilResponsiveness"
                  value="Sluggish"
                  label="Sluggish"
                  checked={assessment?.disability?.leftPupil?.responsivenessToLight === "Sluggish"}
                  onChange={(e) => {
                    setDisability({
                      leftPupil: {
                        ...assessment.disability.leftPupil,
                        responsivenessToLight: e.target.value,
                      },
                    });
                  }}
                />
                
                <NurtifyRadio
                  name="leftPupilResponsiveness"
                  value="Non Reactive"
                  label="Non Reactive"
                  checked={assessment?.disability?.leftPupil?.responsivenessToLight === "Non Reactive"}
                  onChange={(e) => {
                    setDisability({
                      leftPupil: {
                        ...assessment.disability.leftPupil,
                        responsivenessToLight: e.target.value,
                      },
                    });
                  }}
                />
                
                <NurtifyRadio
                  name="leftPupilResponsiveness"
                  value="Unable to access"
                  label="Unable to access"
                  checked={assessment?.disability?.leftPupil?.responsivenessToLight === "Unable to access"}
                  onChange={(e) => {
                    setDisability({
                      leftPupil: {
                        ...assessment.disability.leftPupil,
                        responsivenessToLight: e.target.value,
                      },
                    });
                  }}
                />
              </div>
              {/* Size (in mm) */}
              <NurtifyText label="Size (in mm)" className="mb-2" />
              <div className="d-flex align-items-center mt-2 mb-3">
                <div style={{ width: "220px" }}>
                  <NurtifyInput
                    type="number"
                    value={assessment?.disability?.leftPupil?.size}
                    onChange={(e) => {
                      setDisability({
                        leftPupil: {
                          ...assessment.disability.leftPupil,
                          size: parseInt(e.target.value) || 0,
                        },
                      });
                    }}
                  />
                </div>
              </div>
            </div>

            {/* Neuro Exam*/}

            <div className="d-flex flex-column mb-2 border border-1 badge border border-primary-subtle">
              <NurtifyText label="Limb Movement" className="fw-bold mb-2" />
              <NurtifyText label="Right Arm" className="mb-2" />

              {/* Right Arm */}
              <div>
                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                  <div className="inline-block">
                    <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                      <NurtifyRadio
                        name="rightArmMovement"
                        value="Normal Power"
                        label="Normal Power"
                        checked={assessment?.disability?.rightArmMovement === "Normal Power" || assessment?.disability?.limbMovement?.rightArm === "Normal Power"}
                        onChange={(e) => setRightArmMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="rightArmMovement"
                        value="Mild Weakness"
                        label="Mild Weakness"
                        checked={assessment?.disability?.rightArmMovement === "Mild Weakness" || assessment?.disability?.limbMovement?.rightArm === "Mild Weakness"}
                        onChange={(e) => setRightArmMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="rightArmMovement"
                        value="Severe Weakness"
                        label="Severe Weakness"
                        checked={assessment?.disability?.rightArmMovement === "Severe Weakness" || assessment?.disability?.limbMovement?.rightArm === "Severe Weakness"}
                        onChange={(e) => setRightArmMovement(e.target.value)}
                      />
                    </div>
                    <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                      <NurtifyRadio
                        name="rightArmMovement"
                        value="Spastic Flexion"
                        label="Spastic Flexion"
                        checked={assessment?.disability?.rightArmMovement === "Spastic Flexion" || assessment?.disability?.limbMovement?.rightArm === "Spastic Flexion"}
                        onChange={(e) => setRightArmMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="rightArmMovement"
                        value="Extension"
                        label="Extension"
                        checked={assessment?.disability?.rightArmMovement === "Extension" || assessment?.disability?.limbMovement?.rightArm === "Extension"}
                        onChange={(e) => setRightArmMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="rightArmMovement"
                        value="No Right Arm Response"
                        label="No Response"
                        checked={assessment?.disability?.rightArmMovement === "No Right Arm Response" || assessment?.disability?.limbMovement?.rightArm === "No Right Arm Response"}
                        onChange={(e) => setRightArmMovement(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Left Arm */}
              <NurtifyText label="Left Arm" className="mb-2" />

              <div>
                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                  <div className="inline-block">
                    <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                      <NurtifyRadio
                        name="leftArmMovement"
                        value="Normal Power"
                        label="Normal Power"
                        checked={assessment?.disability?.leftArmMovement === "Normal Power" || assessment?.disability?.limbMovement?.leftArm === "Normal Power"}
                        onChange={(e) => setLeftArmMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="leftArmMovement"
                        value="Mild Weakness"
                        label="Mild Weakness"
                        checked={assessment?.disability?.leftArmMovement === "Mild Weakness" || assessment?.disability?.limbMovement?.leftArm === "Mild Weakness"}
                        onChange={(e) => setLeftArmMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="leftArmMovement"
                        value="Severe Weakness"
                        label="Severe Weakness"
                        checked={assessment?.disability?.leftArmMovement === "Severe Weakness" || assessment?.disability?.limbMovement?.leftArm === "Severe Weakness"}
                        onChange={(e) => setLeftArmMovement(e.target.value)}
                      />
                    </div>

                    <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                      <NurtifyRadio
                        name="leftArmMovement"
                        value="Spastic Flexion"
                        label="Spastic Flexion"
                        checked={assessment?.disability?.leftArmMovement === "Spastic Flexion" || assessment?.disability?.limbMovement?.leftArm === "Spastic Flexion"}
                        onChange={(e) => setLeftArmMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="leftArmMovement"
                        value="Extension"
                        label="Extension"
                        checked={assessment?.disability?.leftArmMovement === "Extension" || assessment?.disability?.limbMovement?.leftArm === "Extension"}
                        onChange={(e) => setLeftArmMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="leftArmMovement"
                        value="No Left Arm Response"
                        label="No Response"
                        checked={assessment?.disability?.leftArmMovement === "No Left Arm Response" || assessment?.disability?.limbMovement?.leftArm === "No Left Arm Response"}
                        onChange={(e) => setLeftArmMovement(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Leg */}
              <NurtifyText label="Right Leg" className="mb-2" />

              <div>
                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                  <div className="inline-block">
                    <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                      <NurtifyRadio
                        name="rightLegMovement"
                        value="Normal Power"
                        label="Normal Power"
                        checked={assessment?.disability?.rightLegMovement === "Normal Power" || assessment?.disability?.limbMovement?.rightLeg === "Normal Power"}
                        onChange={(e) => setRightLegMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="rightLegMovement"
                        value="Mild Weakness"
                        label="Mild Weakness"
                        checked={assessment?.disability?.rightLegMovement === "Mild Weakness" || assessment?.disability?.limbMovement?.rightLeg === "Mild Weakness"}
                        onChange={(e) => setRightLegMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="rightLegMovement"
                        value="Severe Weakness"
                        label="Severe Weakness"
                        checked={assessment?.disability?.rightLegMovement === "Severe Weakness" || assessment?.disability?.limbMovement?.rightLeg === "Severe Weakness"}
                        onChange={(e) => setRightLegMovement(e.target.value)}
                      />
                    </div>
                    <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                      <NurtifyRadio
                        name="rightLegMovement"
                        value="Spastic Flexion"
                        label="Spastic Flexion"
                        checked={assessment?.disability?.rightLegMovement === "Spastic Flexion" || assessment?.disability?.limbMovement?.rightLeg === "Spastic Flexion"}
                        onChange={(e) => setRightLegMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="rightLegMovement"
                        value="Extension"
                        label="Extension"
                        checked={assessment?.disability?.rightLegMovement === "Extension" || assessment?.disability?.limbMovement?.rightLeg === "Extension"}
                        onChange={(e) => setRightLegMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="rightLegMovement"
                        value="No Right Leg Response"
                        label="No Response"
                        checked={assessment?.disability?.rightLegMovement === "No Right Leg Response" || assessment?.disability?.limbMovement?.rightLeg === "No Right Leg Response"}
                        onChange={(e) => setRightLegMovement(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Left Leg */}
              <NurtifyText label="Left Leg" className="mb-2" />

              <div>
                <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap mb-4">
                  <div className="inline-block">
                    <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                      <NurtifyRadio
                        name="leftLegMovement"
                        value="Normal Power"
                        label="Normal Power"
                        checked={assessment?.disability?.leftLegMovement === "Normal Power" || assessment?.disability?.limbMovement?.leftLeg === "Normal Power"}
                        onChange={(e) => setLeftLegMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="leftLegMovement"
                        value="Mild Weakness"
                        label="Mild Weakness"
                        checked={assessment?.disability?.leftLegMovement === "Mild Weakness" || assessment?.disability?.limbMovement?.leftLeg === "Mild Weakness"}
                        onChange={(e) => setLeftLegMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="leftLegMovement"
                        value="Severe Weakness"
                        label="Severe Weakness"
                        checked={assessment?.disability?.leftLegMovement === "Severe Weakness" || assessment?.disability?.limbMovement?.leftLeg === "Severe Weakness"}
                        onChange={(e) => setLeftLegMovement(e.target.value)}
                      />
                    </div>

                    <div className="list-group d-flex flex-row gap-2 me-4 mt-2 flex-wrap  mb-4">
                      <NurtifyRadio
                        name="leftLegMovement"
                        value="Spastic Flexion"
                        label="Spastic Flexion"
                        checked={assessment?.disability?.leftLegMovement === "Spastic Flexion" || assessment?.disability?.limbMovement?.leftLeg === "Spastic Flexion"}
                        onChange={(e) => setLeftLegMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="leftLegMovement"
                        value="Extension"
                        label="Extension"
                        checked={assessment?.disability?.leftLegMovement === "Extension" || assessment?.disability?.limbMovement?.leftLeg === "Extension"}
                        onChange={(e) => setLeftLegMovement(e.target.value)}
                      />
                      
                      <NurtifyRadio
                        name="leftLegMovement"
                        value="No Left Leg Response"
                        label="No Response"
                        checked={assessment?.disability?.leftLegMovement === "No Left Leg Response" || assessment?.disability?.limbMovement?.leftLeg === "No Left Leg Response"}
                        onChange={(e) => setLeftLegMovement(e.target.value)}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Neuro Exam END */}
          </div>
        </div>
      </div>
    )
};

export default Disability;
