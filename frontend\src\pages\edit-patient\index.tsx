import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Preloader from "@/components/common/Preloader";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyDateInput from "@/components/NurtifyDateInput";
import NurtifySelect from "@/components/NurtifySelect";
import { usePartialUpdatePatientMutation } from "@/hooks/patient.query";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import { Patient } from "@/services/api/types";
import { usePatientWarnings } from "@/hooks/usePatientWarnings";
import PatientAllergiesTab from "@/components/PatientAllergiesTab";
import "./edit-patient.css";

type WarningSeverity = 'low' | 'medium' | 'high';

interface FormData {
  first_name: string;
  last_name: string;
  medical_record_number: string;
  date_of_birth: string;
  gender: string;
  email: string;
  phone: string;
}

export default function EditPatient() {
  const navigate = useNavigate();
  const { selectedPatient, isLoading, clearSelectedPatient } = useSelectedPatientStore();
  const updatePatientMutation = usePartialUpdatePatientMutation();
  const [activeTab, setActiveTab] = useState<'info' | 'warnings' | 'allergies'>('info');

  const [formData, setFormData] = useState<FormData>({
    first_name: "",
    last_name: "",
    medical_record_number: "",
    date_of_birth: "",
    gender: "",
    email: "",
    phone: "",
  });

  useEffect(() => {
    if (selectedPatient) {
      // Map the gender value from backend format to select option format
      let mappedGender = "";
      if (selectedPatient.gender === "Male") mappedGender = "Male";
      else if (selectedPatient.gender === "Female") mappedGender = "Female";
      else if (selectedPatient.gender === "Prefer not to disclose") mappedGender = "Prefer_not_to_disclose";
      setFormData({
        first_name: selectedPatient.first_name || "",
        last_name: selectedPatient.last_name || "",
        medical_record_number: selectedPatient.medical_record_number || "",
        date_of_birth: selectedPatient.date_of_birth || "",
        gender: mappedGender,
        email: selectedPatient.email || "",
        phone: selectedPatient.phone_number || "",
      });
    }
  }, [selectedPatient]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Gender options - matching the profile page options
  const genderOptions = [
    { value: "Male", label: "Male" },
    { value: "Female", label: "Female" },
    { value: "Prefer_not_to_disclose", label: "Prefer not to disclose" }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedPatient) return;
    
    // Validate gender is not empty
    if (!formData.gender) {
      alert("Please select a gender");
      return;
    }

    // Map the gender value back to backend format
    let backendGender = "";
    if (formData.gender === "Male") backendGender = "Male";
    else if (formData.gender === "Female") backendGender = "Female";
    else if (formData.gender === "Prefer_not_to_disclose") backendGender = "Prefer_not_to_disclose";
    
    // Create a submission object that matches the Patient type
    const submissionData: Partial<Patient> = {
      first_name: formData.first_name,
      last_name: formData.last_name,
      medical_record_number: formData.medical_record_number,
      date_of_birth: formData.date_of_birth,
      gender: backendGender,
      email: formData.email,
      phone_number: formData.phone,
    };
    
    try {
      const updatedPatient = await updatePatientMutation.mutateAsync({
        uuid: selectedPatient.uuid,
        data: submissionData
      });
      
      // Force a refresh of the patient data in the store
      if (updatedPatient) {
        // Create a merged patient object with all the updated fields
        const mergedPatient = {
          ...selectedPatient,
          first_name: updatedPatient.first_name || selectedPatient.first_name,
          last_name: updatedPatient.last_name || selectedPatient.last_name,
          medical_record_number: updatedPatient.medical_record_number || selectedPatient.medical_record_number,
          date_of_birth: updatedPatient.date_of_birth || selectedPatient.date_of_birth,
          gender: updatedPatient.gender || selectedPatient.gender,
          email: updatedPatient.email || selectedPatient.email,
          phone_number: updatedPatient.phone_number || selectedPatient.phone_number
        };
        
        // Update the store with the merged patient data
        useSelectedPatientStore.getState().setSelectedPatient(mergedPatient);
      }
      
      // Show success message
      alert("Patient information updated successfully");
      
      // Navigate to patient details
      navigate(`/org/dashboard/patient-board/patient-details`);
    } catch (err) {
      console.error("Error updating patient:", err);
      alert("Failed to update patient information. Please try again.");
    }
  };

  if (isLoading) {
    return <Preloader />;
  }

  if (!selectedPatient) {
    return <div className="alert alert-danger">No patient selected</div>;
  }

  return (
    <div className="content-wrapper js-content-wrapper">
      <div className="dashboard__content bg-light-4">
        <div className="container-fluid py-6 px-6">
          <div className="edit-patient-container">
            <div className="edit-patient-header">
              <h1 className="page-title">Edit Patient</h1>
              <div className="edit-patient-actions">
                <button
                  type="button"
                  className="button -md btn-nurtify-lighter"
                  onClick={() => {clearSelectedPatient();navigate('/patients')}}
                >
                  Patient List
                </button>
                <button
                  type="button"
                  className="button -md btn-nurtify text-white"
                  onClick={handleSubmit}
                  disabled={updatePatientMutation.isPending}
                >
                  {updatePatientMutation.isPending ? "Updating..." : "Save Changes"}
                </button>
              </div>
            </div>

            {/* Tabs Navigation */}
            <div className="edit-patient-tabs">
              <button
                className={`tab-button ${activeTab === 'info' ? 'active' : ''}`}
                onClick={() => setActiveTab('info')}
              >
                Patient Information
              </button>
              <button
                className={`tab-button ${activeTab === 'warnings' ? 'active' : ''}`}
                onClick={() => setActiveTab('warnings')}
              >
                Patient Warnings
              </button>
              <button
                className={`tab-button ${activeTab === 'allergies' ? 'active' : ''}`}
                onClick={() => setActiveTab('allergies')}
              >
                Allergies
              </button>
            </div>

            {/* Tab Content */}
            <div className="edit-patient-card">
              {activeTab === 'info' ? (
                <form className="edit-patient-form">
                  <div className="row g-5">
                    {/* Left Column */}
                    <div className="col-md-6">
                      <div className="form-section">
                        {/* First Name */}
                        <div className="form-group">
                          <label className="form-label">First Name</label>
                          <NurtifyInput
                            type="text"
                            name="first_name"
                            value={formData.first_name}
                            onChange={(e) => handleInputChange('first_name', e.target.value)}
                            placeholder="Enter first name"
                            className="form-control"
                          />
                        </div>

                        {/* Last Name */}
                        <div className="form-group">
                          <label className="form-label">Last Name</label>
                          <NurtifyInput
                            type="text"
                            name="last_name"
                            value={formData.last_name}
                            onChange={(e) => handleInputChange('last_name', e.target.value)}
                            placeholder="Enter last name"
                            className="form-control"
                          />
                        </div>

                        {/* Medical Record Number */}
                        <div className="form-group">
                          <label className="form-label">Medical Record Number</label>
                          <NurtifyInput
                            type="text"
                            name="medical_record_number"
                            value={formData.medical_record_number}
                            onChange={(e) => handleInputChange('medical_record_number', e.target.value)}
                            placeholder="Enter MRN"
                            className="form-control"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Right Column */}
                    <div className="col-md-6">
                      <div className="form-section">
                        {/* Date of Birth */}
                        <div className="form-group">
                          <label className="form-label">Date of Birth</label>
                          <NurtifyDateInput
                            name="date_of_birth"
                            value={formData.date_of_birth}
                            onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                            className="form-control"
                          />
                        </div>

                        {/* Gender */}
                        <div className="form-group">
                          <label className="form-label">Gender</label>
                          <NurtifySelect
                            options={genderOptions}
                            value={formData.gender}
                            onChange={(e) => handleInputChange('gender', e.target.value)}
                            placeholder="Select gender"
                            required
                          />
                          {!formData.gender && (
                            <div className="text-danger mt-1">Gender is required</div>
                          )}
                        </div>

                        {/* Email */}
                        <div className="form-group">
                          <label className="form-label">Email</label>
                          <NurtifyInput
                            type="email"
                            name="email"
                            value={formData.email}
                            onChange={(e) => handleInputChange('email', e.target.value)}
                            placeholder="Enter email address"
                            className="form-control"
                          />
                        </div>

                        {/* Phone */}
                        <div className="form-group">
                          <label className="form-label">Phone</label>
                          <NurtifyInput
                            type="text"
                            name="phone"
                            value={formData.phone}
                            onChange={(e) => handleInputChange('phone', e.target.value)}
                            placeholder="Enter phone number"
                            className="form-control"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </form>
              ) : activeTab === 'warnings' ? (
                <PatientWarningsTab patientUuid={selectedPatient.uuid} />
              ) : (
                <PatientAllergiesTab
                  patientUuid={selectedPatient?.uuid || ""}
                  nhsNumber={selectedPatient?.nhs_number || ""}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Patient Warnings Tab Component
function PatientWarningsTab({ patientUuid }: { patientUuid: string }) {
  const {
    warnings,
    isLoading,
    error,
    createWarning,
    updateWarning,
    deleteWarning,
    isCreating,
    isUpdating,
    isDeleting
  } = usePatientWarnings(patientUuid);

  const [newWarning, setNewWarning] = useState<{
    name: string;
    description: string;
    severity: WarningSeverity;
    is_active: boolean;
  }>({
    name: '',
    description: '',
    severity: 'low',
    is_active: true
  });

  const [editingWarning, setEditingWarning] = useState<string | null>(null);

  const handleCreateWarning = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      await createWarning({
        patient_uuid: patientUuid,
        ...newWarning
      });
      setNewWarning({
        name: '',
        description: '',
        severity: 'low',
        is_active: true
      });
    } catch (error) {
      console.error('Error creating warning:', error);
      alert('Failed to create warning. Please try again.');
    }
  };

  const handleUpdateWarning = async (uuid: string, data: Partial<typeof newWarning>) => {
    try {
      await updateWarning({ uuid, data });
      setEditingWarning(null);
    } catch (error) {
      console.error('Error updating warning:', error);
      alert('Failed to update warning. Please try again.');
    }
  };

  const handleDeleteWarning = async (uuid: string) => {
    if (window.confirm('Are you sure you want to delete this warning?')) {
      try {
        await deleteWarning(uuid);
      } catch (error) {
        console.error('Error deleting warning:', error);
        alert('Failed to delete warning. Please try again.');
      }
    }
  };

  if (isLoading) return (
    <div className="warnings-loading">
      <Preloader />
    </div>
  );
  
  if (error) return (
    <div className="alert alert-danger">
      <strong>Error loading warnings:</strong> {error.message || 'Unable to load patient warnings'}
    </div>
  );

  return (
    <div className="patient-warnings-tab">
      {/* Add New Warning Form */}
      <div className="add-warning-section">
        <h3>Add New Warning</h3>
        <form onSubmit={handleCreateWarning} className="add-warning-form">
          <div className="row g-3">
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Warning Name</label>
                <NurtifyInput
                  type="text"
                  value={newWarning.name}
                  onChange={(e) => setNewWarning(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter warning name"
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label className="form-label">Severity</label>
                <NurtifySelect
                  options={[
                    { value: 'low', label: 'Low' },
                    { value: 'medium', label: 'Medium' },
                    { value: 'high', label: 'High' }
                  ]}
                  value={newWarning.severity}
                  onChange={(e) => setNewWarning(prev => ({ ...prev, severity: e.target.value as WarningSeverity }))}
                  required
                />
              </div>
            </div>
            <div className="col-12">
              <div className="form-group">
                <label className="form-label">Description</label>
                <textarea
                  className="form-control"
                  value={newWarning.description}
                  onChange={(e) => setNewWarning(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Enter warning description"
                  required
                  rows={3}
                />
              </div>
            </div>
            <div className="col-12">
              <div className="form-check">
                <input
                  type="checkbox"
                  className="form-check-input"
                  id="isActive"
                  checked={newWarning.is_active}
                  onChange={(e) => setNewWarning(prev => ({ ...prev, is_active: e.target.checked }))}
                />
                <label className="form-check-label" htmlFor="isActive">
                  Active
                </label>
              </div>
            </div>
          </div>
          <div className="mt-3">
            <button
              type="submit"
              className="button -md btn-nurtify text-white"
              disabled={isCreating}
            >
              {isCreating ? 'Adding...' : 'Add Warning'}
            </button>
          </div>
        </form>
      </div>

      {/* Warnings List */}
      <div className="warnings-list">
        <h3>Existing Warnings</h3>
        {warnings.length === 0 ? (
          <div className="warnings-empty">
            <h4>No Warnings Found</h4>
            <p>This patient currently has no active warnings. Use the form above to add new warnings when needed.</p>
          </div>
        ) : (
          <div className="warnings-grid">
            {warnings.map((warning: any) => (
              <div key={warning.uuid} className="warning-card">
                {editingWarning === warning.uuid ? (
                  <form
                    onSubmit={(e) => {
                      e.preventDefault();
                      const formData = new FormData(e.currentTarget);
                      handleUpdateWarning(warning.uuid, {
                        name: formData.get('name') as string,
                        description: formData.get('description') as string,
                        severity: (formData.get('severity') as WarningSeverity) || 'low',
                        is_active: !!formData.get('is_active')
                      });
                    }}
                  >
                    <div className="form-group">
                      <label>Name</label>
                      <input
                        type="text"
                        name="name"
                        defaultValue={warning.name}
                        className="form-control"
                        required
                      />
                    </div>
                    <div className="form-group">
                      <label>Description</label>
                      <textarea
                        name="description"
                        defaultValue={warning.description}
                        className="form-control"
                        required
                        rows={3}
                      />
                    </div>
                    <div className="form-group">
                      <label>Severity</label>
                      <select
                        name="severity"
                        defaultValue={warning.severity}
                        className="form-control"
                        required
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                      </select>
                    </div>
                    <div className="form-check">
                      <input
                        type="checkbox"
                        name="is_active"
                        defaultChecked={warning.is_active}
                        className="form-check-input"
                      />
                      <label className="form-check-label">Active</label>
                    </div>
                    <div className="warning-actions mt-3">
                      <button
                        type="submit"
                        className="button -sm btn-nurtify text-white me-2"
                        disabled={isUpdating}
                      >
                        {isUpdating ? 'Saving...' : 'Save'}
                      </button>
                      <button
                        type="button"
                        className="button -sm btn-nurtify-lighter"
                        onClick={() => setEditingWarning(null)}
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                ) : (
                  <>
                    <div className="warning-header">
                      <h4>{warning.name}</h4>
                      <span className={`severity-badge severity-${warning.severity}`}>
                        {warning.severity_display}
                      </span>
                    </div>
                    <p className="warning-description">{warning.description}</p>
                    <div className="warning-meta">
                      <span className={`status-badge ${warning.is_active ? 'active' : 'inactive'}`}>
                        {warning.is_active ? 'Active' : 'Inactive'}
                      </span>
                      <small className="text-muted">
                        Created by {warning.created_by_name} on{' '}
                        {new Date(warning.created_at).toLocaleDateString()}
                      </small>
                    </div>
                    <div className="warning-actions mt-3">
                      <button
                        className="button -sm btn-nurtify-lighter me-2"
                        onClick={() => setEditingWarning(warning.uuid)}
                      >
                        Edit
                      </button>
                      <button
                        className="button -sm btn-danger"
                        onClick={() => handleDeleteWarning(warning.uuid)}
                        disabled={isDeleting}
                      >
                        {isDeleting ? 'Deleting...' : 'Delete'}
                      </button>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
