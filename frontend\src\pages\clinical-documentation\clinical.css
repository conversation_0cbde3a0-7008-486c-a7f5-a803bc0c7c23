/* Clinical Dashboard Styles */
.clinical-dashboard {
  padding: 40px 5%;
  max-width: 1400px;
  margin: 0 auto;
  padding-bottom: 100px;
}

@media (max-width: 768px) {
  .clinical-dashboard {
    margin-bottom: 170px;
  }
}

/* Header Section */
.dashboard-header {
  margin-bottom: 40px;
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.welcome-text-container {
  flex: 1;
  min-width: 300px;
}

.welcome-text {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 32px;
  font-weight: 700;
  color: #090914;
}

.greeting {
  font-weight: 500;
  color: #4f547b;
}

.username {
  color: #37b7c3;
}

.wave-emoji {
  font-size: 28px;
  margin-left: 5px;
}

.welcome-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: #4f547b;
}

.upgrade-link {
  color: #37b7c3;
  font-weight: 600;
  text-decoration: none;
  transition: color 0.3s ease;
}

.upgrade-link:hover {
  color: #2a9a9f;
  text-decoration: underline;
}

/* Stats Container */
.stats-container {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-card {
  background-color: white;
  border-radius: 12px;
  padding: 20px;
  min-width: 120px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #37b7c3;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #4f547b;
}

/* Dashboard Tabs */
.dashboard-tabs {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
  border-bottom: 1px solid #e9ecef;
  padding-bottom: 15px;
}

.tab-button {
  background: none;
  border: none;
  padding: 10px 20px;
  font-size: 16px;
  font-weight: 500;
  color: #4f547b;
  cursor: pointer;
  border-radius: 30px;
  transition: all 0.3s ease;
}

.tab-button:hover {
  color: #37b7c3;
  background-color: #f8f9fa;
}

.tab-button.active {
  color: white;
}

/* Section Styles */
.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #090914;
  margin-bottom: 20px;
  position: relative;
  padding-left: 15px;
}

.section-title::before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 5px;
  height: 24px;
  background-color: #37b7c3;
  border-radius: 3px;
}

.quick-actions-section,
.templates-section {
  margin-bottom: 50px;
}

.cards-container,
.templates-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
  width: 100%;
}

/* Responsive Styles */
@media only screen and (max-width: 992px) {
  .welcome-section {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .stats-container {
    width: 100%;
    justify-content: space-between;
  }
  
  .stat-card {
    flex: 1;
    min-width: 100px;
  }
  
  .cards-container,
  .templates-container {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media only screen and (max-width: 768px) {
  .clinical-dashboard {
    padding: 30px 20px;
  }
  
  .welcome-text {
    font-size: 28px;
  }
  
  .dashboard-tabs {
    overflow-x: auto;
    padding-bottom: 10px;
  }
  
  .tab-button {
    padding: 8px 15px;
    font-size: 14px;
    white-space: nowrap;
  }
  
  .section-title {
    font-size: 20px;
  }
  
  .cards-container,
  .templates-container {
    grid-template-columns: 1fr;
  }
}

@media only screen and (max-width: 576px) {
  .welcome-text {
    font-size: 24px;
  }
  
  .stats-container {
    flex-direction: column;
    gap: 15px;
  }
  
  .stat-card {
    width: 100%;
  }
}
