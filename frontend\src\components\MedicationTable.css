/* MedicationTable Container */
.med-table-container {
  width: calc(100% - 10px);
  background-color: #ffffff;
  border-radius: 8px;
  border: none;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  margin: 16px 5px;
}

/* Table Wrapper */
.med-table-wrapper {
  overflow-x: auto;
  overflow-y: auto;
  max-height: 600px;
}

.med-table-wrapper::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.med-table-wrapper::-webkit-scrollbar-track {
  background: #f9fafb;
}

.med-table-wrapper::-webkit-scrollbar-thumb {
  background-color: #d1d5db;
  border-radius: 3px;
}

.med-table-wrapper::-webkit-scrollbar-thumb:hover {
  background-color: #9ca3af;
}

/* Table */
.med-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  margin: 0;
}

/* Table Header */
.med-table thead {
  background-color: #EBF4F6;
  position: sticky;
  top: 0;
  z-index: 10;
}

.med-table-header {
  background-color: #EBF4F6;
  color: #374151;
  font-weight: 600;
  text-align: left;
  padding: 16px;
  border-bottom: none;
  font-size: 14px;
  white-space: nowrap;
  vertical-align: middle;
  border-right: none;
}

.med-table-header:last-child {
  border-right: none;
}

.med-table-actions-header {
  text-align: center;
  width: 120px;
  min-width: 120px;
}

/* Table Body */
.med-table tbody tr {
  border-bottom: none;
  transition: background-color 0.15s ease;
}

.med-table tbody tr:hover {
  background-color: #f9fafb;
}

.med-table tbody tr:last-child {
  border-bottom: none;
}

.med-table-row {
  background-color: #ffffff;
}

/* Table Cells */
.med-table-cell {
  padding: 16px;
  color: #374151;
  vertical-align: middle;
  font-size: 14px;
  line-height: 1.5;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  border-right: none;
  background-color: #ffffff;
}

.med-table-cell:last-child {
  border-right: none;
}

/* Actions Cell */
.med-table-actions-cell {
  text-align: center;
  width: 120px;
  min-width: 120px;
  padding: 12px 16px;
}

.med-table-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

/* Action Buttons */
.med-table-action-btn {
  background: none;
  border: none;
  padding: 6px;
  border-radius: 4px;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;
  width: 32px;
  height: 32px;
}

.med-table-action-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.med-table-action-btn:active {
  background-color: #e5e7eb;
}

/* Specific Action Button Styles */
.med-table-action-edit {
  color: #000000;
}

.med-table-action-edit:hover {
  background-color: #f3f4f6;
  color: #000000;
}

.med-table-action-delete {
  color: #ef4444;
}

.med-table-action-delete:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

.med-table-action-continue {
  color: #10b981;
}

.med-table-action-continue:hover {
  background-color: #d1fae5;
  color: #059669;
}

/* Loading and Empty States */
.med-table-loading,
.med-table-empty {
  text-align: center;
  padding: 40px 16px;
  color: #6b7280;
  font-style: italic;
  background-color: #f9fafb;
}

/* Pagination */
.med-table-pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  border-top: none;
  background-color: #ffffff;
  gap: 4px;
}

.med-table-pagination-btn {
  background: none;
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  font-weight: 500;
}

.med-table-pagination-btn:hover:not(:disabled) {
  background-color: #f3f4f6;
  color: #374151;
}

.med-table-pagination-btn:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.med-table-pagination-btn.active {
  background-color: #37B7C3;
  color: white;
  font-weight: 600;
}

.med-table-pagination-ellipsis {
  padding: 8px 4px;
  color: #6b7280;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
}

/* Status Styles */
.med-table-cell .status-continue {
  color: #3b82f6;
  font-weight: 500;
}

.med-table-cell .status-completed {
  color: #10b981;
  font-weight: 500;
}

.med-table-cell .status-paused {
  color: #f59e0b;
  font-weight: 500;
}

.med-table-cell .status-stopped {
  color: #ef4444;
  font-weight: 500;
}

/* Status column styling */
.med-table-cell[data-column="status"] {
  color: #007AFF;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .med-table-container {
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
  
  .med-table-cell,
  .med-table-header {
    padding: 12px 8px;
    font-size: 13px;
  }
  
  .med-table-actions-cell {
    padding: 8px;
  }
  
  .med-table-actions {
    gap: 4px;
  }
  
  .med-table-action-btn {
    width: 28px;
    height: 28px;
  }
  
  .med-table-pagination {
    padding: 16px 12px;
  }
  
  .med-table-pagination-btn {
    min-width: 28px;
    height: 28px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .med-table-cell,
  .med-table-header {
    padding: 8px 6px;
    font-size: 12px;
  }
  
  .med-table-actions-cell {
    padding: 6px;
  }
  
  .med-table-action-btn {
    width: 24px;
    height: 24px;
  }
  
  .med-table-pagination {
    padding: 12px 8px;
  }
  
  .med-table-pagination-btn {
    min-width: 24px;
    height: 24px;
    font-size: 12px;
    padding: 4px 6px;
  }
}

/* Print Styles */
@media print {
  .med-table-pagination {
    display: none;
  }
  
  .med-table-container {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .med-table-action-btn {
    display: none;
  }
  
  .med-table-actions-header,
  .med-table-actions-cell {
    display: none;
  }
}
