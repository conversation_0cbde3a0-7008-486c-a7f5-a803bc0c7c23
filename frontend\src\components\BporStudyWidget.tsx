import React from 'react';

interface BporStudyWidgetProps {
  distance?: string;
  query?: string;
  location?: string;
}

const BporStudyWidget: React.FC<BporStudyWidgetProps> = ({ 
  distance = "20", 
  query = "", 
  location = "London" 
}) => {
  return (
    <div className="bpor-widget-container">
      <h3>Find Clinical Trials</h3>
      <p>Browse clinical trials that match your profile and interests</p>
      <bpor-widget distance={distance} query={query} location={location}></bpor-widget>
    </div>
  );
};

export default BporStudyWidget;
