export type PermissionKey =
  | "viewDashboard"
  | "viewPolicy"
  | "addPolicy"
  | "viewHospitals"
  | "addHospital"
  | "viewDepartment"
  | "addDepartment"
  | "adminHospital"
  | "adminDepartment"
  | "addUser"
  | "viewUsers"
  | "viewProfile"
  | "viewSnippets"
  | "deletePatient"
  | "scheduleEvent"
  | "viewStudies"
  | "pendingRequest"
  | "MyHospital"
  | "MyDepartment"
  | "myLogs"
  | "studiesInvitations"
  | "viewSponsors"
  | "addSponsor"
  | "viewLiveChat";

// The logic in the switch case is mostly correct, but there are some typos and possible logic improvements.
// - Typo: isHospitalAmdin should be isHospitalAdmin
// - The logic for "MyHospital" and "myLogs" is that they are visible to everyone except superuser.
// - The logic for "viewStudies", "MyDepartment", "scheduleEvent" is visible to everyone except superuser and hospital admin.
// - The logic for "adminHospital", "viewDepartment", etc. is only for department admin (is_admin).
// - The logic for "viewPolicy", "viewSnippets", "addPolicy" is only for non-admins (not superuser, not hospital admin, not department admin).
// - "viewProfile" and "pendingRequest" are always visible.

export function hasPermission(permissionKey: PermissionKey, currentUser: any): boolean {
  const isSuperuser = !!currentUser?.is_superuser;
  const isHospitalAdmin = !!currentUser?.is_hospital_admin;
  const isDepartmentAdmin = !!currentUser?.is_admin;

  switch (permissionKey) {
    // Always visible
    case "viewProfile":
    case "pendingRequest":
      return true;

    // Only superuser
    case "viewDashboard":
    case "viewHospitals":
    case "addHospital":
    case "viewSponsors":
    case "addSponsor":
      return isSuperuser;

    // Everyone except superuser
    case "MyHospital":
    case "myLogs":
      return !isSuperuser;

    // Everyone except superuser and hospital admin
    case "viewStudies":
    case "MyDepartment":
    case "scheduleEvent":
      return !isSuperuser && !isHospitalAdmin;

    // Only department admin
    case "adminDepartment":
    case "viewUsers":
    case "addUser":
    case "studiesInvitations":
    case "viewLiveChat":
      return isDepartmentAdmin;

    // Only non-admins (not superuser, not hospital admin, not department admin)
    case "viewPolicy":
    case "viewSnippets":
    case "addPolicy":
      return !isSuperuser && !isHospitalAdmin && !isDepartmentAdmin;

    default:
      return false;
  }
}

export const getBasePath = (currentUser: any): string => {
  if (currentUser?.is_superuser) return "/superuser/dashboard";
  if (currentUser?.is_admin) return "/admin/dashboard";
  if (currentUser?.speciality) {
    console.log(currentUser?.speciality);
  }
  //if (currentUser?.is_staff) return "/staff/dashboard";
  return "/home";
};

export type UserRole =
  | "NurtifyAdmin"
  | "HospitalAdmin"
  | "DepartmentAdmin"
  | "DepartmentStaff"
  | "Patient"
  | "Sponsor"
  | "Unknown";

export const getUserRole = (currentUser: any): UserRole => {
  if (currentUser?.is_superuser) return "NurtifyAdmin";
  if (currentUser?.is_hospital_admin) return "HospitalAdmin";
  if (currentUser?.is_admin) return "DepartmentAdmin";
  if (currentUser?.is_staff) return "DepartmentStaff";
  if (currentUser?.user_type === "patient") return "Patient";
  if (currentUser?.user_type === "sponsor") return "Sponsor";
  return "Unknown";
};

type Restriction = string[];
type ActionRule = { role: string; restriction: Restriction };
type PermissionRules = Record<
  string,
  Record<string, ActionRule[]>
>;

function getPermissionsByRole(
  permissionRules: PermissionRules,
  targetRole: string
): Record<string, string[]> {
  const result: Record<string, string[]> = {};

  for (const [resource, actions] of Object.entries(permissionRules)) {
    for (const [action, rules] of Object.entries(actions)) {
      if (rules.some(rule => rule.role === targetRole)) {
        if (!result[resource]) {
          result[resource] = [];
        }
        result[resource].push(action);
      }
    }
  }

  return result;
}

export function can(
  role: string,
  resource: string,
  action: string
): boolean {
  const perms = getPermissionsByRole(PERMISSION_RULES, role);
  return perms[resource]?.includes(action) ?? false;
}

export const PERMISSION_RULES: PermissionRules = {
  Hospital: {
    create: [
      { role: "NurtifyAdmin", restriction: [] },
    ],
    update: [
      { role: "NurtifyAdmin", restriction: [] },
      { role: "HospitalAdmin", restriction: [] },
    ],
    delete: [
      { role: "NurtifyAdmin", restriction: [] },
    ],
  },
  Department: {
    create: [
      { role: "HospitalAdmin", restriction: [] },
    ],
    update: [
      { role: "HospitalAdmin", restriction: [] },
    ],
    delete: [
      { role: "HospitalAdmin", restriction: [] },
    ],
  },
  HospitalAdmin: {
    create: [
      { role: "NurtifyAdmin", restriction: [] },
    ],
    update: [
      { role: "NurtifyAdmin", restriction: [] },
    ],
    delete: [
      { role: "NurtifyAdmin", restriction: [] },
    ],
  },
  DepartmentAdmin: {
    create: [
      { role: "HospitalAdmin", restriction: [] },
    ],
    update: [
      { role: "HospitalAdmin", restriction: [] },
    ],
    delete: [
      { role: "HospitalAdmin", restriction: [] },
    ],
  },
};