import Preloader from "@/components/common/Preloader";
import LightFooter from "@/shared/LightFooter";
import RebuildForm, { SavedFormData } from "./SurveyFormRenderer";
import { useParams } from "react-router-dom";
import { useGetFormByUuid, useCreateSubmission } from "@/hooks/form.query";
import { useEffect, useState } from "react";
import DocumentationSidebar from "@/components/common/DocumentationSidebar";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import { AlertCircle, CheckCircle } from "lucide-react";

// Static form data to use as fallback
const staticFormData: SavedFormData = {
  formDetails: {
    name: "Fallback Form",
    description: "This is a fallback form used when actual data cannot be loaded",
    categories: "",
    privacy: "Public",
    password: "",
    study: null
  },
  "sections": [{"id": 1, "name": "Fallback Section", "description": "This is a fallback section", "questions": [{"id": 1, "type": "short-text", "answers": [""], "required": false, "nonClinical": false, "questionName": "This is a fallback question"}]}]
};

export default function FormBuilder() {
  const { formUuid } = useParams<{ formUuid?: string }>();
  // Access selected patient from the store
  const { selectedPatient } = useSelectedPatientStore();
  const [processedFormData, setProcessedFormData] = useState<SavedFormData | null>(null);
  const [processingError, setProcessingError] = useState<string | null>(null);
  const [submissionStatus, setSubmissionStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [submissionMessage, setSubmissionMessage] = useState<string | null>(null);

  // Get the createSubmission mutation for form preview testing
  const createSubmissionMutation = useCreateSubmission();

  const [activeTab, setActiveTab] = useState('showAll');
  const [patientsMode, setPatientsMode] = useState(false);

  const { data: formData, isLoading, error } = useGetFormByUuid(formUuid || "", {
    enabled: !!formUuid
  });

  // Log the selected patient UUID for debugging
  useEffect(() => {
    if (selectedPatient) {
      console.log("Form builder has access to patient UUID:", selectedPatient.uuid);
    }
  }, [selectedPatient]);

  useEffect(() => {
    if (!formUuid) {
      console.log("No form UUID provided, using static form data");
      setProcessedFormData(staticFormData);
      return;
    }

    if (isLoading) {
      console.log("Form data is loading...");
      return;
    }

    if (error) {
      console.error("Error loading form:", error);
      setProcessingError(`Failed to load form: ${(error as Error).message}`);
      setProcessedFormData(staticFormData);
      return;
    }

    if (!formData) {
      console.error("No form data found");
      setProcessingError("Form data is not available");
      setProcessedFormData(staticFormData);
      return;
    }

    try {
      console.log("Processing form data:", formData);

      // Extract form structure from the response
      let formStructure;

      if (formData.active_version?.form_structure) {
        if (typeof formData.active_version.form_structure === 'string') {
          formStructure = JSON.parse(formData.active_version.form_structure);
        } else {
          formStructure = formData.active_version.form_structure;
        }
      } else if (formData.active_version?.structure) {
        if (typeof formData.active_version.structure === 'string') {
          formStructure = JSON.parse(formData.active_version.structure);
        } else {
          formStructure = formData.active_version.structure;
        }
      } else {
        throw new Error("Form structure not found in the response");
      }

      // Process the form structure into the format expected by the form renderer
      const processedForm = {
        formDetails: {
          name: formData.name || "Untitled Form",
          description: formData.active_version?.description || "",
          categories: Array.isArray(formData.active_version?.categories)
            ? formData.active_version?.categories.join(', ')  // Convert array to comma-separated string
            : (formData.active_version?.categories || ""),    // Use as is if already a string or default to empty string
          study: formData.active_version?.study || null,
          password: formData.active_version?.password || "",
          privacy: formData.active_version?.privacy || "Public"  // Add privacy field with default value
        },
        sections: formStructure.sections || []
      };

      setProcessedFormData(processedForm);
      setProcessingError(null);
    } catch (e) {
      console.error("Error processing form data:", e);
      setProcessingError(`Error processing form data: ${(e as Error).message}`);
      setProcessedFormData(staticFormData); // Use fallback data on error
    }
  }, [formData, isLoading, error, formUuid]);

  const handleFilterChange = (filter: string) => {
    setActiveTab(filter);
  };

  const handlePatientsModeToggle = (enabled: boolean) => {
    setPatientsMode(enabled);
  };

  // Function to extract files from form answers
  type FileAnswer = FileList | { files?: FileList; [key: string]: unknown } | Record<string, unknown> | null | undefined;
  const extractFilesFromAnswer = (answer: FileAnswer): File[] => {
    const files: File[] = [];

    if (!answer) return files;

    // If answer is a FileList, convert to array of Files
    if (answer instanceof FileList) {
      return Array.from(answer);
    }

    // Handle case where answer might be an object with files
    if (typeof answer === 'object' && answer !== null) {
      // If the object has a "files" property that's a FileList
      if ('files' in answer && answer.files instanceof FileList) {
        return Array.from(answer.files);
      }

      // Try to find any File objects stored as properties
      Object.values(answer).forEach(value => {
        if (value instanceof File) {
          files.push(value);
        } else if (value instanceof FileList) {
          Array.from(value).forEach(file => files.push(file));
        }
      });
    }

    return files;
  };

  // Handle form submission for the preview mode
  const handleFormSubmit = (formData: Record<string, unknown>, isCompleted: boolean = true) => {
    if (!formUuid) {
      console.error("No form UUID available");
      setSubmissionMessage("Error: No form UUID available for test submission");
      setSubmissionStatus('error');
      return;
    }

    setSubmissionStatus('loading');
    setSubmissionMessage(null);

    console.log("Form preview submission:", { formData, isCompleted });

    // Extract file attachments from the form data
    const fileAttachments = {
      image_attachments: [] as File[],
      video_attachments: [] as File[],
      document_attachments: [] as File[]
    };

    interface FormQuestion {
      type: string;
      'user-answer'?: unknown;
      comment?: string;
      [key: string]: unknown;
    }

    interface FormSection {
      questions?: Array<FormQuestion>;
      [key: string]: unknown;
    }

    // Process sections to find file attachments
    if (formData.sections && Array.isArray(formData.sections)) {
      (formData.sections as FormSection[]).forEach(section => {
        if (section.questions && Array.isArray(section.questions)) {
          section.questions.forEach((question: FormQuestion) => {
            // Look for file attachments in user answers
            if (question.type === 'attach-file' && question['user-answer']) {
              const files = extractFilesFromAnswer(question['user-answer'] as Record<string, unknown>);

              // Categorize files by type
              files.forEach(file => {
                if (file.type.startsWith('image/')) {
                  fileAttachments.image_attachments.push(file);
                } else if (file.type.startsWith('video/')) {
                  fileAttachments.video_attachments.push(file);
                } else if (file.type === 'application/pdf' ||
                          file.type === 'application/msword' ||
                          file.type.includes('document')) {
                  fileAttachments.document_attachments.push(file);
                }
              });
            }
            // Log comments for preview
            if (question.comment) {
              console.log(`Comment for question:`, question.comment);
            }
          });
        }
      });
    }

    // Log the found attachments for debugging
    if (fileAttachments.image_attachments.length > 0 ||
        fileAttachments.video_attachments.length > 0 ||
        fileAttachments.document_attachments.length > 0) {
      console.log("Found attachments for test submission:", {
        images: fileAttachments.image_attachments.length,
        videos: fileAttachments.video_attachments.length,
        documents: fileAttachments.document_attachments.length
      });
    }

    // Create payload for test submission
    const payload = {
      form: formUuid, // Required by SubmissionPayload interface
      form_uuid: formUuid,
      // Use a mock user object for test submissions that matches the required type
      user: {
        identifier: "test-user",
        first_name: "Test",
        last_name: "User",
        email: "<EMAIL>"
      },
      submission: formData,
      is_completed: isCompleted,
      final_submission: false, // This is crucial to prevent 500 error
      attachments: fileAttachments
    };

    // For preview mode we'll make a test submission
    createSubmissionMutation.mutate(payload, {
      onSuccess: (response) => {
        console.log("Test form submission successful!", response);
        setSubmissionStatus('success');
        setSubmissionMessage("Form preview submitted successfully! This was a test submission and won't be stored permanently.");
      },
      onError: (error: {
        response?: {
          data?: {
            detail?: string;
            message?: string;
            [key: string]: unknown;
          };
          status?: number;
        };
        message?: string;
      }) => {
        console.error("Error with test submission:", error);
        let errorMessage = "Unknown error occurred";

        if (error.response) {
          console.error("Error response data:", error.response.data);
          console.error("Error response status:", error.response.status);
          // Extract a more detailed error message if available
          const errorDetail = error.response.data?.detail ||
                            error.response.data?.message ||
                            (typeof error.response.data === 'string' ? error.response.data : null) ||
                            JSON.stringify(error.response.data);
          errorMessage = `Error: ${errorDetail}`;
        } else if (error.message) {
          errorMessage = error.message;
        }

        setSubmissionMessage(errorMessage);
        setSubmissionStatus('error');
      }
    });
  };

  return (
    <div className="main-content bg-light-4">
      <Preloader />

      {submissionStatus === 'success' && (
        <div className="alert alert-success mx-4 my-2 d-flex align-items-center">
          <CheckCircle size={20} style={{ marginRight: '8px' }} />
          {submissionMessage}
        </div>
      )}

      {submissionStatus === 'error' && (
        <div className="alert alert-danger mx-4 my-2 d-flex align-items-center">
          <AlertCircle size={20} style={{ marginRight: '8px' }} />
          {submissionMessage}
        </div>
      )}

      <div style={{ display: 'flex' }}>
        <DocumentationSidebar
          onTabChange={handleFilterChange}
          onPatientsModeToggle={handlePatientsModeToggle}
          currentTab={activeTab}
          currentPatientsMode={patientsMode}
        />

        <div style={{ flexGrow: 1 }}>
          {isLoading ? (
            <div className="text-center py-5">
              <div className="spinner-container">
                <div className="spinner-border text-primary" role="status" style={{ width: "3rem", height: "3rem" }}></div>
                <p className="mt-3">Loading form data...</p>
              </div>
            </div>
          ) : processingError ? (
            <div className="text-center py-5">
              <div className="alert alert-danger">
                {processingError}
              </div>
              <div className="mt-3">
                <p>Using fallback form data instead...</p>
                <RebuildForm
                  formData={staticFormData}
                  formUuid={formUuid || ""}
                  activeFilter={activeTab}
                  patientsMode={patientsMode}
                  onSubmit={handleFormSubmit}
                  onSaveDraft={(data) => handleFormSubmit(data, false)}
                />
              </div>
            </div>
          ) : processedFormData ? (
            <RebuildForm
              formData={processedFormData}
              formUuid={formUuid || ""}
              activeFilter={activeTab}
              patientsMode={patientsMode}
              onSubmit={handleFormSubmit}
              onSaveDraft={(data) => handleFormSubmit(data, false)}
            />
          ) : (
            <div className="text-center py-5">
              <div className="alert alert-warning">
                No form data available. Using default template...
              </div>
              <RebuildForm
                formData={staticFormData}
                formUuid={formUuid || ""}
                activeFilter={activeTab}
                patientsMode={patientsMode}
                onSubmit={handleFormSubmit}
                onSaveDraft={(data) => handleFormSubmit(data, false)}
              />
            </div>
          )}
        </div>
      </div>

      <LightFooter />
    </div>
  );
}