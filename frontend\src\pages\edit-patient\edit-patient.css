.edit-patient-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.edit-patient-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.edit-patient-actions {
  display: flex;
  gap: 12px;
}

.edit-patient-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 30px;
  transition: all 0.3s ease;
}

.edit-patient-form {
  width: 100%;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
}

.form-control {
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
}

.form-control:focus {
  border-color: #37B7C3;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
  outline: none;
}

.form-select {
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.2s ease;
  background-color: white;
  cursor: pointer;
}

.form-select:focus {
  border-color: #37B7C3;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
  outline: none;
}

/* Enhanced Patient Warnings Tab Styles */
.edit-patient-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 24px;
  background: var(--color-light-4);
  padding: 6px;
  border-radius: 12px;
  border: 1px solid var(--color-light-2);
}

.tab-button {
  flex: 1;
  padding: 12px 24px;
  border: none;
  background: transparent;
  color: var(--color-light-1);
  font-size: var(--text-15);
  font-weight: 600;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
}

.tab-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(55, 183, 195, 0.1), transparent);
  transition: left 0.5s ease;
}

.tab-button:hover::before {
  left: 100%;
}

.tab-button:hover {
  color: var(--color-purple-1);
  background: rgba(55, 183, 195, 0.05);
}

.tab-button.active {
  background: var(--color-white);
  color: var(--color-purple-1);
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.15);
  border: 1px solid var(--color-purple-1);
}

.patient-warnings-tab {
  padding: 24px 0;
  max-height: calc(100vh - 300px);
  overflow-y: auto;
  overflow-x: hidden;
}

/* Custom scrollbar styling for warnings tab */
.patient-warnings-tab::-webkit-scrollbar {
  width: 8px;
}

.patient-warnings-tab::-webkit-scrollbar-track {
  background: var(--color-light-4);
  border-radius: 4px;
}

.patient-warnings-tab::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.patient-warnings-tab::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.3);
}

/* Firefox scrollbar styling */
.patient-warnings-tab {
  scrollbar-width: thin;
  scrollbar-color: var(--color-purple-1) var(--color-light-4);
}

.add-warning-section {
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  border: 1px solid var(--color-light-2);
  position: relative;
  overflow: hidden;
}

.add-warning-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
}

.add-warning-section h3 {
  font-size: var(--text-20);
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 24px 0;
  position: relative;
  padding-bottom: 12px;
}

.add-warning-section h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 2px;
}

.add-warning-form {
  width: 100%;
}

.add-warning-form .form-group {
  margin-bottom: 20px;
}

.add-warning-form .form-label {
  display: block;
  margin-bottom: 8px;
  font-size: var(--text-14);
  font-weight: 600;
  color: var(--color-dark-1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.add-warning-form .form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--color-light-2);
  border-radius: 8px;
  font-size: var(--text-15);
  color: var(--color-dark-1);
  background-color: var(--color-white);
  transition: all 0.3s ease;
}

.add-warning-form .form-control:focus {
  border-color: var(--color-purple-1);
  outline: none;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
  background-color: var(--color-light-6);
}

.add-warning-form textarea.form-control {
  resize: vertical;
  min-height: 80px;
}

.add-warning-form .form-check {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: var(--color-white);
  border-radius: 12px;
  border: 1px solid var(--color-light-2);
  transition: all 0.3s ease;
}

.add-warning-form .form-check:hover {
  border-color: var(--color-purple-1);
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.1);
}

.add-warning-form .form-check-input {
  width: 18px;
  height: 18px;
  border: 2px solid var(--color-light-2);
  border-radius: 4px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.add-warning-form .form-check-input:checked {
  background-color: var(--color-purple-1);
  border-color: var(--color-purple-1);
}

.add-warning-form .form-check-label {
  font-size: var(--text-15);
  font-weight: 600;
  color: var(--color-dark-1);
  cursor: pointer;
  user-select: none;
}

.warnings-list {
  margin-top: 32px;
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
}

/* Custom scrollbar for warnings list */
.warnings-list::-webkit-scrollbar {
  width: 6px;
}

.warnings-list::-webkit-scrollbar-track {
  background: var(--color-light-4);
  border-radius: 3px;
}

.warnings-list::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.warnings-list::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  box-shadow: 0 2px 6px rgba(55, 183, 195, 0.3);
}

/* Firefox scrollbar styling for warnings list */
.warnings-list {
  scrollbar-width: thin;
  scrollbar-color: var(--color-purple-1) var(--color-light-4);
}

.warnings-list h3 {
  font-size: var(--text-20);
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 24px 0;
  position: relative;
  padding-bottom: 12px;
}

.warnings-list h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 2px;
}

.warnings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
  padding-bottom: 20px;
}

/* Loading state for warnings */
.warnings-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  color: var(--color-light-1);
  font-size: var(--text-16);
}

/* Empty state for warnings */
.warnings-empty {
  text-align: center;
  padding: 40px 20px;
  color: var(--color-light-1);
  background: var(--color-light-6);
  border-radius: 12px;
  border: 2px dashed var(--color-light-2);
}

.warnings-empty h4 {
  font-size: var(--text-18);
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0 0 12px 0;
}

.warnings-empty p {
  font-size: var(--text-15);
  margin: 0;
  line-height: 1.5;
}

/* Smooth scroll behavior */
.patient-warnings-tab,
.warnings-list {
  scroll-behavior: smooth;
}

/* Focus states for accessibility */
.warning-card:focus-within {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

.tab-button:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

/* Animation for new warnings */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.warning-card {
  animation: slideInUp 0.3s ease-out;
}

/* Improved button styles */
.button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.button:hover::before {
  left: 100%;
}

.button:active {
  transform: scale(0.98);
}

/* Enhanced form validation styles */
.form-control.error {
  border-color: var(--color-error-1);
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-control.success {
  border-color: var(--color-green-4);
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

/* Tooltip styles for severity badges */
.severity-badge {
  position: relative;
}

.severity-badge:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--color-dark-1);
  color: var(--color-white);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: var(--text-12);
  white-space: nowrap;
  z-index: 1000;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

.warning-card {
  background: var(--color-white);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(55, 183, 195, 0.06);
  border: 1px solid var(--color-light-2);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
}

.warning-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-orange-4) 0%, var(--color-orange-1) 100%);
  transition: all 0.3s ease;
}

.warning-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(55, 183, 195, 0.12);
}

.warning-header {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 16px;
}

.warning-header h4 {
  font-size: var(--text-18);
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0;
  line-height: 1.3;
  flex: 1;
}

.severity-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: var(--text-11);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.severity-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.severity-badge:hover::before {
  left: 100%;
}

.severity-low {
  background: linear-gradient(135deg, var(--color-blue-2) 0%, var(--color-blue-6) 100%);
  color: var(--color-blue-3);
  border: 1px solid var(--color-blue-4);
}

.severity-medium {
  background: linear-gradient(135deg, var(--color-yellow-2) 0%, var(--color-yellow-5) 100%);
  color: var(--color-yellow-1);
  border: 1px solid var(--color-yellow-3);
}

.severity-high {
  background: linear-gradient(135deg, var(--color-orange-2) 0%, var(--color-orange-5) 100%);
  color: var(--color-orange-1);
  border: 1px solid var(--color-orange-4);
}

.severity-critical {
  background: linear-gradient(135deg, var(--color-red-2) 0%, var(--color-error-1) 100%);
  color: var(--color-red-1);
  border: 1px solid var(--color-red-3);
}

.warning-description {
  font-size: var(--text-15);
  color: var(--color-dark-1);
  line-height: 1.6;
  margin: 0 0 20px 0;
  font-weight: 400;
}

.warning-meta {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px 0;
  border-top: 1px solid var(--color-light-2);
  border-bottom: 1px solid var(--color-light-2);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: var(--text-13);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  width: fit-content;
}

.status-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.status-badge:hover::before {
  left: 100%;
}

.status-badge.active {
  background: linear-gradient(135deg, var(--color-green-2) 0%, var(--color-green-3) 100%);
  color: var(--color-green-5);
  border: 1px solid var(--color-green-4);
}

.status-badge.inactive {
  background: linear-gradient(135deg, var(--color-light-5) 0%, var(--color-light-8) 100%);
  color: var(--color-light-1);
  border: 1px solid var(--color-light-2);
}

.warning-meta small {
  font-size: var(--text-13);
  color: var(--color-light-1);
  font-style: italic;
}

.warning-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-top: 16px;
}

.warning-card form {
  background: var(--color-light-6);
  padding: 20px;
  border-radius: 12px;
  border: 2px solid var(--color-purple-1);
  box-shadow: 0 4px 20px rgba(55, 183, 195, 0.1);
  margin: -24px;
  margin-top: 0;
}

.warning-card form .form-group {
  margin-bottom: 16px;
}

.warning-card form .form-group:last-of-type {
  margin-bottom: 20px;
}

.warning-card form label {
  display: block;
  margin-bottom: 6px;
  font-size: var(--text-13);
  font-weight: 600;
  color: var(--color-dark-1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.warning-card form input,
.warning-card form textarea,
.warning-card form select {
  width: 100%;
  padding: 10px 14px;
  border: 1px solid var(--color-light-2);
  border-radius: 8px;
  font-size: var(--text-14);
  color: var(--color-dark-1);
  background-color: var(--color-white);
  transition: all 0.3s ease;
}

.warning-card form input:focus,
.warning-card form textarea:focus,
.warning-card form select:focus {
  border-color: var(--color-purple-1);
  outline: none;
  box-shadow: 0 0 0 2px rgba(55, 183, 195, 0.1);
  background-color: var(--color-light-6);
}

.warning-card form textarea {
  resize: vertical;
  min-height: 80px;
}

.warning-card form .form-check {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.warning-card form .form-check-input {
  width: 18px;
  height: 18px;
  border: 2px solid var(--color-light-2);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.warning-card form .form-check-input:checked {
  background-color: var(--color-purple-1);
  border-color: var(--color-purple-1);
}

.warning-card form .form-check-label {
  font-size: var(--text-14);
  font-weight: 500;
  color: var(--color-dark-1);
  cursor: pointer;
}

@media (max-width: 768px) {
  .edit-patient-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .edit-patient-card {
    padding: 20px;
  }

  .edit-patient-tabs {
    flex-direction: column;
    gap: 8px;
  }

  .tab-button {
    text-align: center;
  }

  .warnings-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .warning-card {
    padding: 20px;
  }

  .warning-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .warning-actions {
    flex-direction: column;
    gap: 8px;
  }

  .warning-actions .button {
    width: 100%;
    text-align: center;
  }

  .add-warning-section {
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .warnings-grid {
    gap: 12px;
  }

  .warning-card {
    padding: 16px;
  }

  .add-warning-section {
    padding: 20px;
  }
}
