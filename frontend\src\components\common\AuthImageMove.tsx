import React, { useEffect, useState } from 'react';
import { motion } from 'framer-motion';

type AuthImageMoveProps = object

const AuthImageMove: React.FC<AuthImageMoveProps> = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX,
        y: e.clientY
      });
    };

    window.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  const calculateMovement = (strength: number) => {
    const maxMovement = 20;
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;
    
    const moveX = ((mousePosition.x - centerX) / centerX) * maxMovement * (strength / 100);
    const moveY = ((mousePosition.y - centerY) / centerY) * maxMovement * (strength / 100);
    
    return { x: moveX, y: moveY };
  };

  return (
    <div
      className="form-page__img md:d-none"
      style={{
        background: "linear-gradient(135deg, #37B7C3, #2d9aa5)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        width: "100%",
        height: "100vh",
        padding: "120px 52px 52px 52px",
        color: "#FFF",
        display: "flex",
        flexDirection: "column",
        justifyContent: "flex-start",
        position: "relative",
        overflow: "hidden",
      }}
    >
      {/* Decorative elements */}
      <div 
        style={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          opacity: 0.1,
          backgroundImage: "url('/assets/img/authentication/bg-motif.png')",
          backgroundSize: "cover",
          backgroundPosition: "center",
          zIndex: 1,
        }}
      />
      
      {/* Animated circles */}
      <motion.div
        style={{
          position: "absolute",
          width: "300px",
          height: "300px",
          borderRadius: "50%",
          background: "rgba(255, 255, 255, 0.1)",
          top: "10%",
          right: "-50px",
          zIndex: 0,
        }}
        animate={{
          x: calculateMovement(20).x,
          y: calculateMovement(20).y,
        }}
        transition={{ type: "spring", stiffness: 50 }}
      />
      
      <motion.div
        style={{
          position: "absolute",
          width: "200px",
          height: "200px",
          borderRadius: "50%",
          background: "rgba(255, 255, 255, 0.1)",
          bottom: "15%",
          left: "-50px",
          zIndex: 0,
        }}
        animate={{
          x: calculateMovement(10).x,
          y: calculateMovement(10).y,
        }}
        transition={{ type: "spring", stiffness: 50 }}
      />

      {/* Content */}
      <motion.div
        style={{ position: "relative", zIndex: 2 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.2 }}
      >
        <motion.h1
          style={{
            fontSize: "58px",
            fontWeight: 700,
            lineHeight: "1.2",
            letterSpacing: "-1px",
            color: "white",
            marginBottom: "30px",
            textShadow: "0 2px 10px rgba(0, 0, 0, 0.1)",
          }}
          animate={{
            x: calculateMovement(5).x,
            y: calculateMovement(5).y,
          }}
          transition={{ type: "spring", stiffness: 50 }}
        >
          Welcome to our portal
        </motion.h1>
        
        <motion.p
          style={{
            fontSize: "18px",
            lineHeight: "1.6",
            maxWidth: "500px",
            marginBottom: "40px",
            opacity: 0.9,
          }}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          Join our community of healthcare professionals who are already using our platform to improve patient care and streamline their workflow.
        </motion.p>
        
        <motion.img
          src="/assets/img/authentication/white-logo.png"
          style={{ maxWidth: "300px" }}
          alt="Nurtify Logo"
          animate={{
            x: calculateMovement(8).x,
            y: calculateMovement(8).y,
          }}
          transition={{ type: "spring", stiffness: 50 }}
        />
      </motion.div>
      
      {/* Feature highlights */}
      <motion.div
        style={{
          position: "absolute",
          bottom: "80px",
          left: "52px",
          right: "52px",
          display: "flex",
          gap: "20px",
          zIndex: 2,
        }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.6 }}
      >
        {[
          { title: "Secure", description: "DTAC compliant platform" },
          { title: "Efficient", description: "Save time on documentation" },
          { title: "Collaborative", description: "Work seamlessly with your team" }
        ].map((feature, index) => (
          <motion.div
            key={index}
            style={{
              background: "rgba(255, 255, 255, 0.1)",
              backdropFilter: "blur(5px)",
              padding: "15px 20px",
              borderRadius: "12px",
              flex: 1,
            }}
            whileHover={{ y: -5, boxShadow: "0 10px 20px rgba(0, 0, 0, 0.1)" }}
          >
            <h3 style={{ fontSize: "18px", fontWeight: 600, marginBottom: "5px" }}>
              {feature.title}
            </h3>
            <p style={{ fontSize: "14px", opacity: 0.8 }}>
              {feature.description}
            </p>
          </motion.div>
        ))}
      </motion.div>
    </div>
  );
};

export default AuthImageMove;
