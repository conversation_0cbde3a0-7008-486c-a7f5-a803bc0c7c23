.vital-signs-chart {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  padding: 24px;
  margin: 20px 0;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.chart-header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.chart-type-selector,
.vital-sign-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-type-selector label,
.vital-sign-selector label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.chart-type-selector select,
.vital-sign-selector select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-type-selector select:hover,
.vital-sign-selector select:hover {
  border-color: #9ca3af;
}

.chart-type-selector select:focus,
.vital-sign-selector select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.chart-container {
  margin: 20px 0;
  min-height: 400px;
  position: relative;
}

.chart-loading,
.chart-no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  color: #6b7280;
}

.chart-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #e5e7eb;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  color: #374151;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
  display: inline-block;
}

.legend-color.green {
  background-color: #10b981;
}

.legend-color.blue {
  background-color: #3b82f6;
}

.legend-color.red {
  background-color: #ef4444;
}

.legend-color.orange {
  background-color: #f59e0b;
}

/* Responsive design */
@media (max-width: 768px) {
  .vital-signs-chart {
    padding: 16px;
    margin: 16px 0;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .chart-controls {
    width: 100%;
    justify-content: space-between;
  }

  .chart-type-selector,
  .vital-sign-selector {
    flex: 1;
    min-width: 0;
  }

  .chart-type-selector select,
  .vital-sign-selector select {
    width: 100%;
  }

  .chart-legend {
    gap: 16px;
  }

  .legend-item {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .chart-controls {
    flex-direction: column;
    gap: 12px;
  }

  .chart-type-selector,
  .vital-sign-selector {
    width: 100%;
  }

  .chart-legend {
    gap: 12px;
  }
}

/* Chart tooltip customization */
.recharts-tooltip-wrapper {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.recharts-default-tooltip {
  background-color: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  padding: 12px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.recharts-tooltip-label {
  color: #374151 !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
}

.recharts-tooltip-item {
  color: #6b7280 !important;
  font-size: 0.875rem !important;
  margin: 4px 0 !important;
}

/* Chart grid and axis customization */
.recharts-cartesian-grid-horizontal line,
.recharts-cartesian-grid-vertical line {
  stroke: #f3f4f6 !important;
  stroke-width: 1 !important;
}

.recharts-xAxis .recharts-cartesian-axis-tick-value,
.recharts-yAxis .recharts-cartesian-axis-tick-value {
  font-size: 0.75rem !important;
  fill: #6b7280 !important;
}

.recharts-cartesian-axis-line {
  stroke: #d1d5db !important;
  stroke-width: 1 !important;
}

/* Chart legend customization */
.recharts-legend-wrapper {
  padding: 16px 0 !important;
}

.recharts-legend-item {
  margin-right: 16px !important;
}

.recharts-legend-item-text {
  font-size: 0.875rem !important;
  color: #374151 !important;
} 