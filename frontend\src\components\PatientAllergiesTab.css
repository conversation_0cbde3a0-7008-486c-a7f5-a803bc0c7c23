.allergies-tab {
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.allergies-header {
  margin-bottom: 20px;
}

.allergies-header h3 {
  color: #2B3674;
  margin-bottom: 15px;
}

.add-allergy-form {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.allergy-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #E0E5F2;
  border-radius: 4px;
  font-size: 14px;
}

.allergy-input:focus {
  outline: none;
  border-color: #37b7c3;
}

.allergy-input:disabled {
  background-color: #f8f9fa;
  cursor: not-allowed;
}

.btn-add-allergy {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background-color: #37b7c3;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.btn-add-allergy:hover:not(:disabled) {
  background-color: #2d9ba6;
}

.btn-add-allergy:disabled {
  background-color: #a8d8dc;
  cursor: not-allowed;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background-color: #fee2e2;
  border: 1px solid #fecaca;
  border-radius: 4px;
  color: #dc2626;
  font-size: 14px;
  margin-top: 12px;
}

.loading-message {
  text-align: center;
  padding: 20px;
  color: #6b7280;
  font-size: 14px;
}

.allergies-list {
  margin-top: 20px;
}

.allergies-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.allergy-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 8px;
}

.allergy-item:last-child {
  margin-bottom: 0;
}

.btn-delete-allergy {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px;
  background: none;
  border: none;
  color: #dc2626;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.btn-delete-allergy:hover {
  background-color: #fee2e2;
}

.no-allergies {
  color: #6c757d;
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 4px;
} 