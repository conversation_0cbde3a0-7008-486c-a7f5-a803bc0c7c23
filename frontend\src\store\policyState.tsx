import { create } from "zustand";

// Define the Policy interface
export interface Policy {
  id: number;
  attach_content: File | string | null;
  uuid: string;
  title: string;
  category: string;
  author_name: string;
  job_title: string;
  description: string;
  created_at: string;
  updated_at: string;
  department: {
      id: number;
      name: string;
  };
  hospital: {
      id: number;
      name: string;
  };
  study?: {
      id: number;
      uuid: string;
      name: string;
  } | null;
  attachment_url?: string;
}

interface PolicyState {
  policy: Policy | null;
  searchTerm: string; // 🔥 Ajout de searchTerm
  setPolicy: (policy: Policy) => void;
  setSearchTerm: (term: string) => void; // 🔥 Ajout de setSearchTerm
}

export const usePolicyStore = create<PolicyState>((set) => ({
  policy: null,
  searchTerm: "", // ✅ Initialisation du searchTerm
  setPolicy: (policy) => set({ policy }),
  setSearchTerm: (term) => set({ searchTerm: term }), // ✅ Fonction pour modifier searchTerm
}));
