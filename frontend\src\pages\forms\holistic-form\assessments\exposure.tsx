import { faAdd } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { useState } from "react";
import skinImage from "./static/images/added/skin.png";
import useHolisticFormStore, { SoreData } from "@/store/holisticFormState";
import NurtifyRadio from "@/components/NurtifyRadio";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifySelect from "@/components/NurtifySelect";

const Exposure = () => {
  const { assessment, setAssessment, addSore } = useHolisticFormStore();
  const [, setDatixNumberFormShowing] = useState(false);
  const [, setOptionsModalShowing] = useState(false);
  const [addSoresFormShowing, setAddSoresFormShowing] = useState(false);

  const handleAddSores = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const newSore: SoreData = {
      name: formData.get("sore_name") as string,
      size: formData.get("sore_size") as string,
      grade: formData.get("sore_grade") as string,
      description: formData.get("sore_desc") as string,
    };

    addSore(newSore);
    setOptionsModalShowing(false);
    setAddSoresFormShowing(false);
  };

  return (
    <div className="block align-items-*-start flex-column flex-md-row p-4 gap-5">
      <div className="" id="division-28">
        {/* Section Title  */}
        <div className="inlineBlock mb-4 headinqQuestion">
          <img src={skinImage} className="imageEtiquette" alt="skin picture" />
          <span className="mb-2 py-2 fs-2 text-start etiquetteHeadingForms">
            Exposure
          </span>
        </div>
        {/* Temperature */}
        <div
          id="division-29"
          className="list-group  col-xl-6 col-lg-8 col-md-12"
        >
          <div className="input-group mb-1 gap-2 themed-grid-col col-md-4">
            <div className="d-flex flex-wrap  gap-2">
              <span className="inlineInput" style={{ width: "220px" }}>
                Temperature:
              </span>
              <input
                max="45"
                step="0.1"
                min="25"
                type="range"
                style={{ width: "25rem", boxShadow: "none" }}
                className="form-control-sm"
                onChange={(e) => {
                  setAssessment({
                    ...assessment,
                    exposure: {
                      ...assessment.exposure,
                      temperature: parseFloat(e.target.value),
                    },
                  });
                }}
                name=""
                value={assessment?.exposure?.temperature}
                id=""
                defaultValue={37}
              />

              <input
                type="number"
                placeholder="temp"
                title="temp"
                className="nurtify-input"
                value={assessment?.exposure?.temperature}
                onChange={(e) => {
                  setAssessment({
                    ...assessment,
                    exposure: {
                      ...assessment.exposure,
                      temperature: parseFloat(e.target.value),
                    },
                  });
                }}
                max="45"
                step="0.1"
                min="25"
              />

              <span className="mt-2" style={{ fontSize: "16px" }}>
                °C.
              </span>
            </div>
          </div>
        </div>

        <div className="d-flex flex-wrap align-items-*-start flex-row flex-md-row py-md-5">
          {/* Pressure sore */}
          <div className="list-group me-4 mt-3">
            <span className="headinqQuestion">
              Does the patient has pressure sore?
            </span>

            <NurtifyRadio
              name="hasPressureSore"
              label="No"
              value="false"
              checked={assessment?.exposure?.hasPresureSore === false}
              onChange={() => setAssessment({
                ...assessment,
                exposure: {
                  ...assessment.exposure,
                  hasPresureSore: false,
                },
              })}
            />
            <NurtifyRadio
              name="hasPressureSore"
              label="Yes"
              value="true"
              checked={assessment?.exposure?.hasPresureSore === true}
              onChange={() => setAssessment({
                ...assessment,
                exposure: {
                  ...assessment.exposure,
                  hasPresureSore: true,
                },
              })}
            />
          </div>

          {/* Reported or no  */}

          {assessment?.exposure?.hasPresureSore == true ? (
            <div id="division-29" className="list-group ">
              {/* The question Is the pressure sore reported? */}
              <div className=" list-group me-4 mt-3">
                <span className="headinqQuestion">
                  Is the pressure sore reported?
                </span>

                <NurtifyRadio
                  name="pressureSoreReported"
                  label="Yes"
                  value="true"
                  checked={assessment?.exposure?.pressureSoreWasReported === true}
                  onChange={(e) => {
                    console.log(e);
                    setAssessment({
                      ...assessment,
                      exposure: {
                        ...assessment.exposure,
                        pressureSoreWasReported: true,
                      },
                    });
                    setDatixNumberFormShowing(true);
                    setOptionsModalShowing(true);
                  }}
                />

                <NurtifyRadio
                  name="pressureSoreReported"
                  label="No"
                  value="false"
                  checked={assessment?.exposure?.pressureSoreWasReported === false}
                  onChange={(e) => {
                    console.log(e);
                    setAssessment({
                      ...assessment,
                      exposure: {
                        ...assessment.exposure,
                        pressureSoreWasReported: false,
                      },
                    });
                  }}
                />
              </div>
            </div>
          ) : null}
        </div>
      </div>

      {/* Add Pressure Sore */}
      {assessment?.exposure?.pressureSoreWasReported == false ? (
        <>
          <div className="d-flex lines flex-column gap-2">
            {assessment?.exposure?.sores.map((line) => {
              return (
                <>
                  <div className="pain" style={{backgroundColor: "#112D4E", color: "white", padding: "10px", borderRadius: "10px", width: "15%"}}>
                    <span>{line.description}</span>
                  </div>
                </>
              );
            })}
          </div>
          <div className="d-flex flex-row align-items-start justify-content-start mt-4">
            <button
              onClick={() => {
                setOptionsModalShowing(true);
                setAddSoresFormShowing(true);
              }}
              className="btn-nurtify w-25"
            >
              Add Presure Sore Report <FontAwesomeIcon icon={faAdd} />
            </button>
          </div>
        </>
      ) : null}

      {addSoresFormShowing === true ? (
        <div className="options-modal">
          <div className="center">
            <>
              <form
                onSubmit={handleAddSores}
                className="d-flex flex-column gap-1"
              >
                <div className="input">
                  <label className="subheading">
                    Name of affected body area
                  </label>
                  <NurtifyInput
                    type="text"
                    placeholder="Name"
                    name="sore_name"
                    className="nurtify-input"
                    required
                  />
                </div>
                <div className="input">
                  <label className="subheading">
                    Size (in numbers with Unit)?
                  </label>
                  <NurtifyInput
                    type="text"
                    placeholder="Size"
                    name="sore_size"
                    className="form-control mb-3"
                    required
                  />
                </div>
                {/* This div is not needed here */}
                {/* <div className="input">
                  <input
                    type="text"
                    name="sore_site"
                    className="form-control"
                  />
                  <label className="subheading">Insertion Site</label>
                </div>  */}
                <div className="input">
                  <label className="subheading">Grade of pressure sore</label>
                  <NurtifySelect options={[
                    {
                      label: "Healed Pressure Sore",
                      value: "Healed Pressure Sore",
                    },
                    {
                      label: "Stage 1: Nonblanchable erythma",
                      value: "Stage 1: Nonblanchable erythma",
                    },
                    {
                      label: "Stage 2: Partial Thickness Skin Loss",
                      value: "Stage 2: Partial Thickness Skin Loss",
                    },
                    {
                      label: "Stage 3: Full Thickness Skin Loss",
                      value: "Stage 3: Full Thickness Skin Loss",
                    },
                    {
                      label: "Stage 4: Full Thickness tissue Loss",
                      value: "Stage 4: Full Thickness tissue Loss",
                    },
                    {
                      label: "Unstageable: Depth Unknown",
                      value: "Unstageable: Depth Unknown",
                    }
                  ]}
                  />
                </div>
                <div className="input">
                  <label className="subheading">
                    Further Description of Sore
                  </label>
                  <NurtifyInput
                    type="text"
                    name="sore_desc"
                    className="nurtify-input"
                    required
                  />
                </div>
                <div className="d-flex btns gap-3 mt-3 flex-row">
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      setOptionsModalShowing(false);
                      setAddSoresFormShowing(false);
                    }}
                    className="btn btn-secondary"
                  >
                    CANCEL
                  </button>
                  <button type="submit" className="my-primary-btn">
                    save
                  </button>
                </div>
              </form>
            </>
          </div>
         
        </div>
      ) : null}
    </div>
  );
};

export default Exposure;
