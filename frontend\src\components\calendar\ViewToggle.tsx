import React, { useEffect, useState } from 'react';
import './ViewToggle.css';

export type ViewMode = 'list' | 'day' | 'week' | 'month';

interface ViewToggleProps {
  onChange: (mode: ViewMode) => void;
  initialMode?: ViewMode;
}

const ViewToggle: React.FC<ViewToggleProps> = ({ onChange, initialMode = 'list' }) => {
  const [mode, setMode] = useState<ViewMode>(initialMode);

  // Load the saved mode from localStorage on component mount
  useEffect(() => {
    const savedMode = localStorage.getItem('viewMode') as ViewMode;
    if (savedMode && ['list', 'day', 'week', 'month'].includes(savedMode)) {
      setMode(savedMode);
      onChange(savedMode);
    }
  }, [onChange]);

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only process if no input elements are focused
      if (document.activeElement?.tagName === 'INPUT' ||
          document.activeElement?.tagName === 'TEXTAREA') {
        return;
      }

      switch (e.key.toLowerCase()) {
        case 'l':
          handleModeChange('list');
          break;
        case 'd':
          handleModeChange('day');
          break;
        case 'w':
          handleModeChange('week');
          break;
        case 'm':
          handleModeChange('month');
          break;
        default:
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, []);

  // Handle mode change
  const handleModeChange = (newMode: ViewMode) => {
    setMode(newMode);
    localStorage.setItem('viewMode', newMode);
    onChange(newMode);
  };

  return (
    <div className="view-toggle-container">
      <div className="view-toggle">
        <button
          className={`view-toggle-button ${mode === 'list' ? 'active' : ''}`}
          onClick={() => handleModeChange('list')}
          aria-pressed={mode === 'list'}
          title="List View (L)"
        >
          List
        </button>
        <button
          className={`view-toggle-button ${mode === 'day' ? 'active' : ''}`}
          onClick={() => handleModeChange('day')}
          aria-pressed={mode === 'day'}
          title="Day View (D)"
        >
          Day
        </button>
        <button
          className={`view-toggle-button ${mode === 'week' ? 'active' : ''}`}
          onClick={() => handleModeChange('week')}
          aria-pressed={mode === 'week'}
          title="Week View (W)"
        >
          Week
        </button>
        <button
          className={`view-toggle-button ${mode === 'month' ? 'active' : ''}`}
          onClick={() => handleModeChange('month')}
          aria-pressed={mode === 'month'}
          title="Month View (M)"
        >
          Month
        </button>
      </div>
    </div>
  );
};

export default ViewToggle;
