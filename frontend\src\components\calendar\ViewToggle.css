.view-toggle-container {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 15px;
}

.view-toggle {
  display: inline-flex;
  background-color: #f0f0f0;
  border-radius: 20px;
  padding: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
}

.view-toggle-button {
  border: none;
  background: none;
  padding: 8px 12px;
  border-radius: 18px;
  cursor: pointer;
  font-size: 13px;
  font-weight: 500;
  color: #555;
  transition: all 0.2s ease;
  position: relative;
  outline: none;
  min-width: 60px;
  text-align: center;
}

.view-toggle-button:focus-visible {
  outline: 2px solid #23b7cd;
  outline-offset: 2px;
}

.view-toggle-button.active {
  background-color: #23b7cd;
  color: white;
  box-shadow: 0 2px 4px rgba(35, 183, 205, 0.3);
}

/* High contrast focus state for accessibility */
.view-toggle-button:focus {
  box-shadow: 0 0 0 2px rgba(35, 183, 205, 0.5);
}

/* Responsive styles */
@media (max-width: 768px) {
  .view-toggle-container {
    justify-content: center;
  }

  .view-toggle {
    width: 100%;
    justify-content: center;
  }

  .view-toggle-button {
    padding: 6px 8px;
    font-size: 12px;
    min-width: 50px;
  }
}

/* Keyboard shortcut indicator */
.view-toggle-button::after {
  content: attr(title);
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 10px;
  color: #777;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
}

.view-toggle-button:hover::after {
  opacity: 1;
}
