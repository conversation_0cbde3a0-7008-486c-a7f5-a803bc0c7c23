/* Hero Section Styles */

.news-container {
  padding: 60px 5%;
}

.news-subtitle {
  color: var(--color-purple-1);
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 16px;
  text-align: center;
}

.news-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 40px;
  text-align: center;
  color: #333;
}

.news-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto 40px;
}

.featured-article {
  display: flex;
  flex-direction: column;
  background-color: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.featured-article:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.featured-article-image {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  aspect-ratio: 16 / 9;
  overflow: hidden;
}

.featured-article-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  margin: 0 auto;
  max-width: 100%;
  transition: transform 0.5s ease;
}

.featured-article:hover .featured-article-image img {
  transform: scale(1.05);
}

.secondary-articles {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.secondary-article {
  display: flex;
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.secondary-article:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.secondary-article-image {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.secondary-article-image img {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
  margin: 0 auto;
  max-width: 100%;
  transition: transform 0.5s ease;
}

.secondary-article:hover .secondary-article-image img {
  transform: scale(1.05);
}

.secondary-article-content {
  padding: 15px 15px 0px 15px;
  flex-grow: 1;
}

@media screen and (max-width: 1400px) and (min-width: 1200px) {
  .secondary-article-content {
    padding: 2px 15px 0px 15px;
  }
}

@media (max-width: 992px) {
  .secondary-article-content {
    padding: 2px 15px 0px 15px;
  }
}
@media (max-width: 768px) {
  .secondary-article-content {
    padding: 2px 15px 0px 15px;
  }
}
@media (max-width: 576px) {
  .secondary-article-content {
    padding: 2px 15px 0px 15px;
  }
}

.article-meta {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding: 15px 15px 0;
}

.secondary-article .article-meta {
  padding: 0;
  margin-bottom: 5px;
}

.article-author {
  color: var(--color-purple-1);
  font-weight: 600;
  font-size: 14px;
}

.article-date {
  color: #6c757d;
  font-size: 14px;
  margin-left: 10px;
  position: relative;
}

.article-date::before {
  content: "•";
  position: absolute;
  left: -7px;
}

.article-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
  padding: 0 15px;
  color: #333;
}

.secondary-article .article-title {
  font-size: 16px;
  padding: 0;
  margin-bottom: 5px;
}

.article-excerpt {
  font-size: 16px;
  color: #6c757d;
  line-height: 1.6;
  padding: 0 15px 15px;
}

.secondary-article .article-excerpt {
  font-size: 14px;
  padding: 0;
}

.article-button {
  margin: 0 15px 15px;
  padding: 10px 20px;
  background-color: transparent;
  border: 2px solid #333;
  border-radius: 30px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.article-button:hover {
  background-color: var(--color-purple-1);
  border-color: var(--color-purple-1);
  color: white;
}

.view-all-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.view-all-button {
  padding: 12px 24px;
  background-color: var(--color-purple-1);
  color: white;
  border: none;
  border-radius: 30px;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.view-all-button:hover {
  background-color: #2a9a9f;
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(55, 183, 195, 0.3);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .news-title {
    font-size: 30px;
  }
  
  .news-subtitle {
    font-size: 16px;
  }
  
  .article-title {
    font-size: 18px;
  }
  
  .article-excerpt {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .news-container {
    padding: 40px 20px;
  }
  
  .news-grid {
    grid-template-columns: 1fr;
  }
  
  .featured-article {
    align-items: center;
  }
  .featured-article-image {
    width: 100%;
    aspect-ratio: 16 / 9;
    height: auto;
    min-height: 180px;
    max-width: 100vw;
  }
  .featured-article-image img {
    width: 100%;
    height: auto;
    min-height: 150px;
    max-width: 100vw;
    object-fit: cover;
    display: block;
    margin: 0 auto;
  }
  .secondary-article {
    flex-direction: column;
    align-items: center;
  }
  .secondary-article-image {
    width: 100%;
    aspect-ratio: 16 / 9;
    height: auto;
    min-height: 120px;
    max-width: 100vw;
  }
  .secondary-article-image img {
    width: 100%;
    height: auto;
    min-height: 100px;
    max-width: 100vw;
    object-fit: cover;
    display: block;
    margin: 0 auto;
  }
  
  .secondary-article-content {
    padding: 15px;
  }
  
  .article-meta {
    padding: 15px 15px 0;
  }
  
  .secondary-article .article-meta {
    padding: 0;
  }
  
  .article-title,
  .secondary-article .article-title {
    font-size: 18px;
    padding: 0 15px;
    margin-bottom: 10px;
  }
  
  .secondary-article .article-title {
    padding: 0;
  }
  
  .article-excerpt,
  .secondary-article .article-excerpt {
    font-size: 14px;
    padding: 0 15px 15px;
  }
  
  .secondary-article .article-excerpt {
    padding: 0;
  }
}
