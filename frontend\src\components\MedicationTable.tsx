import React from 'react';
import './MedicationTable.css';

export interface TableColumn {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
}

export interface TableAction {
  type: 'edit' | 'delete' | 'continue' | 'custom';
  icon?: React.ReactNode;
  label?: string;
  onClick: (row: any, index: number) => void;
  className?: string;
}

export interface MedicationTableProps {
  columns: TableColumn[];
  data: any[];
  actions?: TableAction[];
  currentPage?: number;
  totalPages?: number;
  onPageChange?: (page: number) => void;
  loading?: boolean;
  emptyMessage?: string;
}

const MedicationTable: React.FC<MedicationTableProps> = ({
  columns,
  data,
  actions = [],
  currentPage = 1,
  totalPages = 1,
  onPageChange,
  loading = false,
  emptyMessage = 'No data available'
}) => {
  const renderPagination = () => {
    // Always show pagination, even for single page

    const pages = [];
    const maxVisiblePages = 5;
    
    // Calculate start and end page numbers
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
    
    // Adjust start page if we're near the end
    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    pages.push(
      <button
        key="prev"
        className="med-table-pagination-btn"
        onClick={() => onPageChange?.(currentPage - 1)}
        disabled={currentPage === 1}
      >
        &lt;
      </button>
    );

    // First page and ellipsis
    if (startPage > 1) {
      pages.push(
        <button
          key={1}
          className={`med-table-pagination-btn ${currentPage === 1 ? 'active' : ''}`}
          onClick={() => onPageChange?.(1)}
        >
          1
        </button>
      );
      if (startPage > 2) {
        pages.push(
          <span key="ellipsis1" className="med-table-pagination-ellipsis">
            ...
          </span>
        );
      }
    }

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      pages.push(
        <button
          key={i}
          className={`med-table-pagination-btn ${currentPage === i ? 'active' : ''}`}
          onClick={() => onPageChange?.(i)}
        >
          {i}
        </button>
      );
    }

    // Last page and ellipsis
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        pages.push(
          <span key="ellipsis2" className="med-table-pagination-ellipsis">
            ...
          </span>
        );
      }
      pages.push(
        <button
          key={totalPages}
          className={`med-table-pagination-btn ${currentPage === totalPages ? 'active' : ''}`}
          onClick={() => onPageChange?.(totalPages)}
        >
          {totalPages}
        </button>
      );
    }

    // Next button
    pages.push(
      <button
        key="next"
        className="med-table-pagination-btn"
        onClick={() => onPageChange?.(currentPage + 1)}
        disabled={currentPage === totalPages}
      >
        &gt;
      </button>
    );

    return (
      <div className="med-table-pagination">
        {pages}
      </div>
    );
  };

  const renderActionButton = (action: TableAction, row: any, index: number) => {
    const getDefaultIcon = (type: string) => {
      switch (type) {
        case 'edit':
          return (
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
            </svg>
          );
        case 'delete':
          return (
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="3,6 5,6 21,6" />
              <path d="M19,6v14a2,2,0,0,1-2,2H7a2,2,0,0,1-2-2V6m3,0V4a2,2,0,0,1,2-2h4a2,2,0,0,1,2,2V6" />
            </svg>
          );
        case 'continue':
          return (
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="9,18 15,12 9,6" />
            </svg>
          );
        default:
          return null;
      }
    };

    return (
      <button
        key={action.type}
        className={`med-table-action-btn med-table-action-${action.type} ${action.className || ''}`}
        onClick={() => action.onClick(row, index)}
        title={action.label || action.type}
      >
        {action.icon || getDefaultIcon(action.type)}
      </button>
    );
  };

  return (
    <div className="med-table-container">
      <div className="med-table-wrapper">
        <table className="med-table">
          <thead>
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className="med-table-header"
                  style={{ width: column.width }}
                >
                  {column.label}
                </th>
              ))}
              {actions.length > 0 && (
                <th className="med-table-header med-table-actions-header">
                  Action
                </th>
              )}
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td
                  colSpan={columns.length + (actions.length > 0 ? 1 : 0)}
                  className="med-table-loading"
                >
                  Loading...
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td
                  colSpan={columns.length + (actions.length > 0 ? 1 : 0)}
                  className="med-table-empty"
                >
                  {emptyMessage}
                </td>
              </tr>
            ) : (
              data.map((row, index) => (
                <tr key={index} className="med-table-row">
                  {columns.map((column) => (
                    <td 
                      key={column.key} 
                      className="med-table-cell"
                      data-column={column.key}
                    >
                      {row[column.key]}
                    </td>
                  ))}
                  {actions.length > 0 && (
                    <td className="med-table-cell med-table-actions-cell">
                      <div className="med-table-actions">
                        {actions.map((action) => renderActionButton(action, row, index))}
                      </div>
                    </td>
                  )}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
      {renderPagination()}
    </div>
  );
};

export default MedicationTable;
