import React, { useState } from "react";
import { motion } from "framer-motion";
import { Plus } from "lucide-react";
import DataTable, { Column } from "@/components/common/DataTable";
import { type Symptom } from "@/services/api/symptom.service";
import { Navigate } from "react-router-dom";
import AddSymptomModal from "@/components/patient-clinical/AddSymptomModal";
import "@/components/patient-clinical/AddSymptomModal.css";
import { useSymptomsQuery, useUpdateSymptomMutation } from "@/hooks/symptom.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { usePatientConcomitantMedicationsQuery, useCreateConcomitantMedicationMutation, useUpdateConcomitantMedicationMutation } from "@/hooks/patient.query";
import { useQueryClient } from "@tanstack/react-query";
import type { ConcomitantMedication, CreateConcomitantMedicationData } from "@/services/api/types";
import { formatDate } from "@/utils/date";

interface AddMedicationModalProps {
  isOpen: boolean;
  onClose: () => void;
  patientUuid: string;
  onSuccess: () => void;
}

const AddMedicationModal: React.FC<AddMedicationModalProps> = ({
  isOpen,
  onClose,
  patientUuid,
  onSuccess,
}) => {
  const createMedicationMutation = useCreateConcomitantMedicationMutation();
  const [newMedication, setNewMedication] = useState<CreateConcomitantMedicationData>({
    patient_uuid: patientUuid,
    medication: "",
    indication: "",
    dose: 0,
    dose_units: "1",
    schedule: "1",
    dose_form: "1",
    route: "1",
    start_date: "",
    end_date: "",
    is_baseline: false,
    is_continuing: true
  });

  const resetForm = () => {
    setNewMedication({
      patient_uuid: patientUuid,
      medication: "",
      indication: "",
      dose: 0,
      dose_units: "1",
      schedule: "1",
      dose_form: "1",
      route: "1",
      start_date: "",
      end_date: "",
      is_baseline: false,
      is_continuing: true
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const medicationData = {
        ...newMedication,
        // Convert empty string to undefined for end_date
        end_date: newMedication.end_date || undefined,
        is_continuing: !newMedication.end_date
      };
      await createMedicationMutation.mutateAsync(medicationData);
      resetForm(); // Reset form after successful submission
      onSuccess();
    } catch (error) {
      console.error("Error creating medication:", error);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h3>Add New Medication</h3>
          <button className="close-button" onClick={onClose}>×</button>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="row g-3">
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="medication">Medication</label>
                <input
                  type="text"
                  className="form-control"
                  id="medication"
                  value={newMedication.medication}
                  onChange={(e) => setNewMedication(prev => ({ ...prev, medication: e.target.value }))}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="indication">Indication</label>
                <input
                  type="text"
                  className="form-control"
                  id="indication"
                  value={newMedication.indication}
                  onChange={(e) => setNewMedication(prev => ({ ...prev, indication: e.target.value }))}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="dose">Dose</label>
                <input
                  type="number"
                  step="0.01"
                  className="form-control"
                  id="dose"
                  value={newMedication.dose}
                  onChange={(e) => setNewMedication(prev => ({ ...prev, dose: parseFloat(e.target.value) }))}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="dose_units">Dose Units</label>
                <select
                  className="form-control"
                  id="dose_units"
                  value={newMedication.dose_units}
                  onChange={(e) => setNewMedication(prev => ({ ...prev, dose_units: e.target.value }))}
                  required
                >
                  <option value="1">g (gram)</option>
                  <option value="2">mg (milligram)</option>
                  <option value="3">µg (microgram)</option>
                  <option value="4">L (liter)</option>
                  <option value="5">mL (milliliter)</option>
                  <option value="6">IU (International Unit)</option>
                  <option value="7">Other</option>
                </select>
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="schedule">Schedule</label>
                <select
                  className="form-control"
                  id="schedule"
                  value={newMedication.schedule}
                  onChange={(e) => setNewMedication(prev => ({ ...prev, schedule: e.target.value }))}
                  required
                >
                  <option value="1">QD (once a day)</option>
                  <option value="2">BID (twice a day)</option>
                  <option value="3">TID (three times a day)</option>
                  <option value="4">QID (four times a day)</option>
                  <option value="5">QOD (every other day)</option>
                  <option value="6">QM (every month)</option>
                  <option value="7">QOM (every other month)</option>
                  <option value="8">QH (every hour)</option>
                  <option value="9">AC (before meals)</option>
                  <option value="10">PC (after meals)</option>
                  <option value="11">PRN (as needed)</option>
                  <option value="12">Other</option>
                </select>
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="dose_form">Dose Form</label>
                <select
                  className="form-control"
                  id="dose_form"
                  value={newMedication.dose_form}
                  onChange={(e) => setNewMedication(prev => ({ ...prev, dose_form: e.target.value }))}
                  required
                >
                  <option value="1">Tablet</option>
                  <option value="2">Capsule</option>
                  <option value="3">Ointment</option>
                  <option value="4">Suppository</option>
                  <option value="5">Aerosol</option>
                  <option value="6">Spray</option>
                  <option value="7">Suspension</option>
                  <option value="8">Patch</option>
                  <option value="9">Gas</option>
                  <option value="10">Gel</option>
                  <option value="11">Cream</option>
                  <option value="12">Powder</option>
                  <option value="13">Implant</option>
                  <option value="14">Chewable</option>
                  <option value="15">Liquid</option>
                  <option value="99">Other</option>
                </select>
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="route">Route</label>
                <select
                  className="form-control"
                  id="route"
                  value={newMedication.route}
                  onChange={(e) => setNewMedication(prev => ({ ...prev, route: e.target.value }))}
                  required
                >
                  <option value="1">Oral</option>
                  <option value="2">Topical</option>
                  <option value="3">Subcutaneous</option>
                  <option value="4">Intradermal</option>
                  <option value="5">Transdermal</option>
                  <option value="6">Intraocular</option>
                  <option value="7">Intramuscular</option>
                  <option value="8">Inhalation</option>
                  <option value="9">Intravenous</option>
                  <option value="10">Intraperitoneal</option>
                  <option value="11">Nasal</option>
                  <option value="12">Vaginal</option>
                  <option value="13">Rectal</option>
                  <option value="14">Other</option>
                </select>
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="start_date">Start Date</label>
                <input
                  type="date"
                  className="form-control"
                  id="start_date"
                  value={newMedication.start_date}
                  onChange={(e) => setNewMedication(prev => ({ ...prev, start_date: e.target.value }))}
                  required
                />
              </div>
            </div>
            <div className="col-md-6">
              <div className="form-group">
                <label htmlFor="end_date">End Date (Optional)</label>
                <input
                  type="date"
                  className="form-control"
                  id="end_date"
                  value={newMedication.end_date}
                  onChange={(e) => setNewMedication(prev => ({ ...prev, end_date: e.target.value }))}
                />
                <small className="form-text text-muted">
                  If you provide an end date, this medication will be marked as discontinued.
                </small>
              </div>
            </div>
          </div>
          <div className="modal-footer">
            <button type="submit" className="button -md btn-nurtify text-white me-2" disabled={createMedicationMutation.isPending}>
              {createMedicationMutation.isPending ? "Saving..." : "Save"}
            </button>
            <button type="button" className="button -md btn-nurtify-lighter" onClick={onClose}>
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

const DiarySection: React.FC = () => {
  const queryClient = useQueryClient();
  const { data: currentUser } = useCurrentUserQuery();
  const patientUuid = (currentUser && typeof currentUser === 'object' && 'patient_uuid' in currentUser)
    ? (currentUser as { patient_uuid: string }).patient_uuid
    : "";
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isMedicationModalOpen, setIsMedicationModalOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'diary' | 'medications'>('diary');
  const [editingSymptom, setEditingSymptom] = useState<{
    uuid: string;
    status: string;
  } | null>(null);
  const [editingMedication, setEditingMedication] = useState<{
    uuid: string;
    end_date?: string;
  } | null>(null);
  
  const updateSymptomMutation = useUpdateSymptomMutation();
  const updateMedicationMutation = useUpdateConcomitantMedicationMutation();
  
  console.log('Patient UUID from current user:', patientUuid); // Debug log
  
  const { data: symptoms = [], isLoading: isLoadingSymptoms } = useSymptomsQuery(patientUuid);
  const { data: medications, isLoading: isLoadingMedications, refetch: refetchMedications } = usePatientConcomitantMedicationsQuery(patientUuid);

  // Show all medications but only allow editing for patient-added ones
  const allMedications = medications?.results || [];

  if (!patientUuid) {
    return <Navigate to="/patient" replace />;
  }

  const handleEditSymptom = (symptom: Symptom) => {
    setEditingSymptom({
      uuid: symptom.uuid,
      status: symptom.status
    });
  };

  const handleCancelEditSymptom = () => {
    setEditingSymptom(null);
  };

  const handleSaveEditSymptom = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingSymptom) return;

    try {
      await updateSymptomMutation.mutateAsync({
        uuid: editingSymptom.uuid,
        data: {
          status: editingSymptom.status
        }
      });
      setEditingSymptom(null);
    } catch (error) {
      console.error("Error updating symptom:", error);
    }
  };

  const handleEditMedication = (medication: ConcomitantMedication) => {
    setEditingMedication({
      uuid: medication.uuid,
      end_date: medication.end_date || ""
    });
  };

  const handleCancelEditMedication = () => {
    setEditingMedication(null);
  };

  const handleSaveEditMedication = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingMedication || !patientUuid) return;

    try {
      await updateMedicationMutation.mutateAsync({
        uuid: editingMedication.uuid,
        data: {
          end_date: editingMedication.end_date || undefined,
          is_continuing: !editingMedication.end_date
        }
      });
      // Refresh the medications list after updating
      refetchMedications();
      setEditingMedication(null);
    } catch (error) {
      console.error("Error updating medication:", error);
    }
  };

  const symptomColumns: Column<Symptom>[] = [
    { key: "description", header: "Description", sortable: true },
    { 
      key: "start_date", 
      header: "Start Date", 
      sortable: true,
      render: (value: string | number | boolean | null | undefined) => {
        if (typeof value === 'string') {
          return formatDate(value);
        }
        return String(value ?? '');
      }
    },
    { key: "start_time", header: "Start Time", sortable: true },
    { key: "severity", header: "Severity", sortable: true },
    { 
      key: "hospitalization_required", 
      header: "Hospitalized", 
      sortable: true,
      render: (value: string | number | boolean | null | undefined) => {
        if (typeof value === 'boolean') {
          return value ? "Yes" : "No";
        }
        return String(value ?? '');
      }
    },
    { key: "status_display", header: "Status", sortable: true },
    { key: "patient_comment", header: "Comment", sortable: true },
    {
      key: "actions" as keyof Symptom,
      header: "Actions",
      sortable: false,
      render: (_: string | number | boolean | null | undefined, row?: Symptom) => row && (
        <div className="d-flex gap-2">
          <button
            className="button -sm btn-nurtify-lighter"
            onClick={() => handleEditSymptom(row)}
            disabled={row.status === '1'}
            title={row.status === '1' ? "Cannot edit resolved symptom" : "Edit symptom status"}
          >
            Edit
          </button>
        </div>
      )
    }
  ];

  const medicationColumns: Column<ConcomitantMedication>[] = [
    { key: "medication", header: "Medication", sortable: true },
    { key: "indication", header: "Indication", sortable: true },
    { 
      key: "dose", 
      header: "Dose", 
      sortable: true,
      render: (value: string | number | boolean | null | undefined, row?: ConcomitantMedication) => {
        if (typeof value === 'number' && row) {
          return `${value} ${row.dose_units_display}`;
        }
        return String(value ?? '');
      }
    },
    { key: "schedule_display", header: "Schedule", sortable: true },
    { key: "dose_form_display", header: "Form", sortable: true },
    { key: "route_display", header: "Route", sortable: true },
    { 
      key: "start_date", 
      header: "Start Date", 
      sortable: true,
      render: (value: string | number | boolean | null | undefined) => {
        if (typeof value === 'string') {
          return formatDate(value);
        }
        return String(value ?? '');
      }
    },
    { 
      key: "is_continuing", 
      header: "Status", 
      sortable: true,
      render: (value: string | number | boolean | null | undefined) => {
        if (typeof value === 'boolean') {
          return value ? "Continuing" : "Discontinued";
        }
        return String(value ?? '');
      }
    },
    {
      key: "actions" as keyof ConcomitantMedication,
      header: "Actions",
      sortable: false,
      render: (_: string | number | boolean | null | undefined, row?: ConcomitantMedication) => row && (
        <div className="d-flex gap-2">
          <button
            className="button -sm btn-nurtify-lighter"
            onClick={() => handleEditMedication(row)}
            disabled={!row.is_continuing || !row.added_by_patient}
            title={!row.is_continuing ? "Cannot edit discontinued medication" : 
                   !row.added_by_patient ? "Cannot edit organization-added medication" : 
                   "Edit medication"}
          >
            Edit
          </button>
        </div>
      )
    }
  ];

  const handleAddSymptom = () => {
    if (!patientUuid) {
      console.error('No patient UUID available');
      return;
    }
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    // Reset the form when modal is closed
    if (isModalOpen) {
      // This will trigger the form reset in AddSymptomModal component
      queryClient.invalidateQueries({ queryKey: ['symptoms'] });
    }
  };

  const handleAddMedication = () => {
    setIsMedicationModalOpen(true);
  };

  const handleMedicationModalClose = () => {
    setIsMedicationModalOpen(false);
  };
  
  const handleMedicationSuccess = () => {
    // Directly refetch the medications data using the query's refetch function
    refetchMedications();
    
    // Also invalidate any related queries to ensure data consistency
    queryClient.invalidateQueries({ queryKey: ['patient-concomitant-medications'] });
    
    setIsMedicationModalOpen(false);
  };

  return (
    <motion.div
      className="patclin-tab-content"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="d-flex justify-content-between align-items-center mb-4">
        <h2 className="patclin-section-title">My Health Records</h2>
        {activeTab === 'diary' ? (
          <button
            className="btn btn-primary d-flex align-items-center gap-2"
            onClick={handleAddSymptom}
          >
            <Plus size={20} />
            Add Symptom
          </button>
        ) : (
          <button
            className="btn btn-primary d-flex align-items-center gap-2"
            onClick={handleAddMedication}
          >
            <Plus size={20} />
            Add Medication
          </button>
        )}
      </div>

      {/* Tabs */}
      <div className="nav nav-tabs mb-4">
        <button
          className={`nav-link ${activeTab === 'diary' ? 'active' : ''}`}
          onClick={() => setActiveTab('diary')}
        >
          Diary
        </button>
        <button
          className={`nav-link ${activeTab === 'medications' ? 'active' : ''}`}
          onClick={() => setActiveTab('medications')}
        >
          Medications
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'diary' ? (
        isLoadingSymptoms ? (
          <div className="text-center">Loading symptoms...</div>
        ) : symptoms.length > 0 ? (
          <DataTable
            data={symptoms}
            columns={symptomColumns}
            noDataMessage="No symptoms recorded yet"
          />
        ) : (
          <div className="patclin-empty-state">
            <p>Your diary is empty. Start tracking your health journey today.</p>
          </div>
        )
      ) : (
        isLoadingMedications ? (
          <div className="text-center">Loading medications...</div>
        ) : allMedications.length > 0 ? (
          <DataTable
            data={allMedications}
            columns={medicationColumns}
            noDataMessage="No medications recorded yet"
          />
        ) : (
          <div className="patclin-empty-state">
            <p>No medications recorded yet.</p>
          </div>
        )
      )}

      <AddSymptomModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        patientUuid={patientUuid}
        onSuccess={handleModalClose}
      />

      <AddMedicationModal
        isOpen={isMedicationModalOpen}
        onClose={handleMedicationModalClose}
        patientUuid={patientUuid}
        onSuccess={handleMedicationSuccess}
      />

      {/* Edit Symptom Modal */}
      {editingSymptom && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Update Symptom Status</h3>
              <button className="close-button" onClick={handleCancelEditSymptom}>×</button>
            </div>
            <form onSubmit={handleSaveEditSymptom}>
              <div className="form-group">
                <label htmlFor="status">Status</label>
                <select
                  className="form-control"
                  id="status"
                  value={editingSymptom.status}
                  onChange={(e) => setEditingSymptom(prev => ({ ...prev!, status: e.target.value }))}
                  required
                >
                  <option value="1">Resolved</option>
                  <option value="2">Recovered with sequelae</option>
                  <option value="3">Ongoing / Continuing treatment</option>
                  <option value="4">Condition worsening</option>
                  <option value="5">Unknown</option>
                </select>
              </div>
              <div className="modal-footer">
                <button type="submit" className="button -md btn-nurtify text-white me-2" disabled={updateSymptomMutation.isPending}>
                  {updateSymptomMutation.isPending ? "Saving..." : "Save"}
                </button>
                <button type="button" className="button -md btn-nurtify-lighter" onClick={handleCancelEditSymptom}>
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Edit Medication Modal */}
      {editingMedication && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h3>Update Medication Status</h3>
              <button className="close-button" onClick={handleCancelEditMedication}>×</button>
            </div>
            <form onSubmit={handleSaveEditMedication}>
              <div className="form-group">
                <label htmlFor="end_date">End Date (Optional)</label>
                <input
                  type="date"
                  className="form-control"
                  id="end_date"
                  value={editingMedication.end_date}
                  onChange={(e) => setEditingMedication(prev => ({ ...prev!, end_date: e.target.value }))}
                />
                <small className="form-text text-muted">
                  If you provide an end date, this medication will be marked as discontinued.
                </small>
              </div>
              <div className="modal-footer">
                <button type="submit" className="button -md btn-nurtify text-white me-2" disabled={updateMedicationMutation.isPending}>
                  {updateMedicationMutation.isPending ? "Saving..." : "Save"}
                </button>
                <button type="button" className="button -md btn-nurtify-lighter" onClick={handleCancelEditMedication}>
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default DiarySection;
