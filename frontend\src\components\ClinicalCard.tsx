import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import './clinicalcard.css'


type ClinicalCardProps = {
  title: string;
  description?: string;
  link?: string;
  icon: string;
  isModalOpen: boolean;
  setIsModalOpen: (value: boolean) => void;
};

function ClinicalCard({
  title,
  description,
  link,
  icon,
  setIsModalOpen,
}: ClinicalCardProps): JSX.Element {
  const handleClick = (checkTitle: string) => {
    if (checkTitle === "Search/Add Patient") {
      setIsModalOpen(true);
    } else {
      setIsModalOpen(false);
    }
  };

  return (
    <motion.div
      className="main-box1"
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true, amount: 0.5 }}
      whileHover={{
        scale: 1.05,
        boxShadow: "0px 4px 15px rgba(0, 0, 0, 0.2)",
        cursor: "pointer",
      }}
      onClick={() => handleClick(title)}
    >
      <div className="tier1-box">
        <motion.img
          src={icon}
          alt={title}
          className="box-icon"
          initial={{ scale: 0.8 }}
          whileInView={{ scale: 1 }}
          transition={{ duration: 0.4, delay: 0.2 }}
        />
        <div className='abss'>
          {link && link !== "#" ? (
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.3 }}
            >
              <Link to={link}>
                <h5 className="box-title">
                  {title}
                </h5>
              </Link>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.3 }}
            >
              <h5 className="box-title">
                {title}
              </h5>
            </motion.div>
          )}

          <motion.p
            className='box-sub'
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.4, delay: 0.4 }}
          >
            {description}
          </motion.p>
        </div>
      </div>

    </motion.div>
  );
}

export default ClinicalCard;
