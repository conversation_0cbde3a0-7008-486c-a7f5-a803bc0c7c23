import React, { useState, useEffect, useCallback } from 'react';
import { ChevronLeft, ChevronRight, ListFilter, User, FileText, Mic } from 'lucide-react';
import './DocumentationSidebar.css';
import SnippetsModal from '@/components/modal/SnippetsModal';

// Add props to handle filter changes
type DocumentationSidebarProps = {
  onTabChange?: (tab: string) => void;
  onPatientsModeToggle?: (enabled: boolean) => void;
  currentTab?: string;
  currentPatientsMode?: boolean;
};

const DocumentationSidebar: React.FC<DocumentationSidebarProps> = ({
  onTabChange,
  onPatientsModeToggle,
  currentTab: externalActiveTab,
  currentPatientsMode: externalPatientsMode
}) => {
  // State for collapsed sidebar
  const [isCollapsed, setIsCollapsed] = useState(() => {
    const stored = localStorage.getItem('docSidebarCollapsed') === 'true';
    return stored || window.innerWidth <= 991;
  });
  
  const [activeTab, setActiveTab] = useState(externalActiveTab || 'showAll');
  
  const [patientsMode, setPatientsMode] = useState(externalPatientsMode || false);
  
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 991);

  const [isSnippetsModalOpen, setSnippetsModalOpen] = useState(false);

  const handleResize = useCallback(() => {
    setIsMobile(window.innerWidth <= 991);
  }, []);

  useEffect(() => {
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  useEffect(() => {
    localStorage.setItem('docSidebarCollapsed', isCollapsed.toString());
  }, [isCollapsed]);

  useEffect(() => {
    if (externalActiveTab !== undefined) {
      setActiveTab(externalActiveTab);
    }
  }, [externalActiveTab]);
  
  useEffect(() => {
    if (externalPatientsMode !== undefined) {
      setPatientsMode(externalPatientsMode);
    }
  }, [externalPatientsMode]);

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const togglePatientsMode = () => {
    const newMode = !patientsMode;
    setPatientsMode(newMode);
    if (onPatientsModeToggle) {
      onPatientsModeToggle(newMode);
    }
  };
  
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    if (onTabChange) {
      onTabChange(tab);
    }
  };

  return (
    <>
      <div 
        className={`documentation-sidebar ${isCollapsed ? 'collapsed' : ''} ${isMobile ? 'mobile' : ''}`}
        role="complementary"
        aria-label="Documentation sidebar"
      >
        <div className="doc-toggle-button" onClick={toggleSidebar}>
          {isCollapsed ? <ChevronLeft size={20} /> : <ChevronRight size={20} />}
        </div>
        
        {isCollapsed ? (
          // Collapsed view - only icons
          <div className="doc-icons-container">
            {/* Add onClick handlers to icons to make them functional in collapsed mode */}
            <div className="doc-icon" onClick={() => handleTabChange('showAll')}>
              <ListFilter size={20} />
            </div>
            <div className="doc-icon" onClick={togglePatientsMode}>
              <User size={20} />
            </div>
            <div className="doc-icon" onClick={() => setSnippetsModalOpen(true)}>
              <FileText size={20} />
            </div>
            <div className="doc-icon">
              <Mic size={20} />
            </div>
          </div>
        ) : (
          // Expanded view - full content
          <div className="doc-content-container">
            {/* Tabs */}
            <div className="doc-tabs">
              <div 
                className={`doc-tab ${activeTab === 'showAll' ? 'active' : ''}`}
                onClick={() => handleTabChange('showAll')}
              >
                <ListFilter size={16} />
                <span>Show All</span>
              </div>
              <div 
                className={`doc-tab ${activeTab === 'unanswered' ? 'active' : ''}`}
                onClick={() => handleTabChange('unanswered')}
              >
                <span>Unanswered</span>
              </div>
            </div>
            
            {/* Patients Mode Toggle */}
            <div className="doc-toggle-row">
              <div className="doc-toggle-label">
                <User size={16} />
                <span>Patients Mode</span>
              </div>
              <div 
                className={`doc-toggle ${patientsMode ? 'active' : ''}`}
                onClick={togglePatientsMode}
              >
                <div className="doc-toggle-slider"></div>
                <span>{patientsMode ? 'On' : 'Off'}</span>
              </div>
            </div>
            
            {/* Snippets */}
            <div className="doc-menu-item" onClick={() => setSnippetsModalOpen(true)}>
              <FileText size={16} />
              <span>Snippets</span>
            </div>
            
            {/* Transcribe */}
            <div className="doc-menu-item">
              <Mic size={16} />
              <span>Transcribe</span>
            </div>
          </div>
        )}
      </div>

      {/* Snippets Modal */}
      <SnippetsModal isOpen={isSnippetsModalOpen} onClose={() => setSnippetsModalOpen(false)} />
    </>
  );
};

export default DocumentationSidebar;
