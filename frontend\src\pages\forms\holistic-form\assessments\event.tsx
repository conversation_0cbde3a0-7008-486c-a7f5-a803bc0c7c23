import { useState } from "react";
import othersxImage from "./static/images/added/othersx.png";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyCheckbox from "@/components/NurtifyCheckBox";

const Event = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  const [mentalPresentationSymptoms, setMentalPresentationSymptoms] = useState<string[]>([]);

  const handleMentalSymptomsCheckboxChange = (value: string, checked: boolean) => {
    setMentalPresentationSymptoms((prev) =>
      checked ? [...prev, value] : prev.filter((item) => item !== value)
    );
    setAssessment({
      ...assessment,
      event: [...mentalPresentationSymptoms, value]
    });
  };

  return (
      <div
        className="block align-items-*-start flex-column flex-md-row p-4 mt-2"
        id="division-38"
      >
        <div>
          <div className="inlineBlock mb-4 headinqQuestion">
            <img
              src={othersxImage}
              className="imageEtiquette"
              alt="Image describing mental health section"
            />
            <span className="mb-2 py-2 fs-2 text-start etiquetteHeadingForms">
              Associated Event/Symptoms
            </span>
          </div>

          <div className="list-group me-4 mt-3 col-xl-6 col-lg-8 col-md-12">
            <span className="headinqQuestion">
              Please select all that apply
            </span>

            <div>
              <NurtifyCheckbox
                label="Total Loss of consciousness (TLOC)"
                value="Total Loss of consciousness (TLOC) "
                id="Total Loss of consciousness (TLOC)"
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>

            <div>
              <NurtifyCheckbox
                label="Any recent travel"
                value="recent travel"
                id="Any recent travel"
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>

            <div>
              <NurtifyCheckbox
                label="Any Vomiting"
                value="Vomiting"
                id="Any Vomiting"
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>

            <div>
              <NurtifyCheckbox
                label="Anyone in the house have same symptoms"
                value="Someone in the accomodation have/has same symptoms"
                id="Anyone in the house have same symptoms"
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>

            <div>
              <NurtifyCheckbox
                label="Any Urine abnormal symptoms"
                value="Urine symptoms reported"
                id="Any Urine abnormal symptoms"
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>

            <div>
              <NurtifyCheckbox
                label="Any alcohol"
                value="ETOH on board"
                id="Any alcohol "
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>

            <div>
              <NurtifyCheckbox
                label="Any recreational drugs"
                value="recreational drugs "
                id="Any recreational drugs "
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>

            <div>
              <NurtifyCheckbox
                label="Any loss of weight recently"
                value="loss of weight recently  "
                id="Any loss of weight recently "
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>

            <div>
              <NurtifyCheckbox
                label="Any Bone deformities during assessment"
                value="Bone deformities during assessment "
                id="Any Bone deformities during assessment "
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>

            <div>
              <NurtifyCheckbox
                label="Any long lie"
                value="long lie"
                id="Any long lie"
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>

            <div>
              <NurtifyCheckbox
                label="Any recent long flight"
                value="recent long flight"
                id="Any recent long flight"
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>

            <div>
              <NurtifyCheckbox
                label="Any photophobia"
                value="photophobia"
                id="Any photophobia?"
                onChange={(event) => handleMentalSymptomsCheckboxChange(event.target.value, event.target.checked)}
              />
            </div>
          </div>
        </div>
      </div>
  );
};

export default Event;
