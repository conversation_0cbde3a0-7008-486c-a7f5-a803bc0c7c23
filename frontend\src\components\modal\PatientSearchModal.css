.patient-search-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
  box-sizing: border-box;
}

.patient-search-modal {
  background-color: white;
  border-radius: 12px;
  padding: 0;
  width: 100%;
  max-width: 900px;
  max-height: calc(100vh - 40px);
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  margin: auto;
  position: relative;
}

.patient-search-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #e0e0e0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.patient-search-modal-title {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: #333;
  text-align: center;
  flex: 1;
}

.patient-search-modal-close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
}

.patient-search-modal-close-button:hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  transform: scale(1.1);
}

.patient-search-modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  background-color: #fafbfc;
  display: flex;
  flex-direction: column;
  min-height: 0;
  max-height: calc(100vh - 120px);
}

/* Enhanced Patient Search Container Styling */
.patient-search-modal-content .patient-search-container {
  background-color: white;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
  margin-bottom: 0;
  border: none;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 0;
}

.patient-search-modal-content .patient-search-container .search-title {
  display: none; /* Hide the title since we have it in the modal header */
}

/* Enhanced Search Form Styling */
.patient-search-modal-content .search-form {
  padding: 32px;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-bottom: 1px solid #e9ecef;
}

.patient-search-modal-content .search-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.patient-search-modal-content .search-form-field {
  display: flex;
  flex-direction: column;
}

.patient-search-modal-content .search-form-field label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Enhanced Input Styling */
.patient-search-modal-content .search-form-field input,
.patient-search-modal-content .search-form-field select {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  background-color: white;
  border-left: 4px solid #37b7c3;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.patient-search-modal-content .search-form-field input:focus,
.patient-search-modal-content .search-form-field select:focus {
  border-color: #37b7c3;
  outline: none;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1), 0 4px 12px rgba(55, 183, 195, 0.15);
  transform: translateY(-1px);
}

.patient-search-modal-content .search-form-field input::placeholder {
  color: #9ca3af;
  font-style: italic;
}

/* Enhanced Button Styling */
.patient-search-modal-content .search-form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e9ecef;
}

.patient-search-modal-content .clear-button {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #4b5563;
  border: 2px solid #d1d5db;
  border-radius: 8px;
  padding: 12px 24px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 14px;
}

.patient-search-modal-content .clear-button:hover {
  background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.patient-search-modal-content .search-button {
  background: linear-gradient(135deg, #37b7c3 0%, #1c8e98 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  cursor: pointer;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.patient-search-modal-content .search-button:hover {
  background: linear-gradient(135deg, #1c8e98 0%, #0f6b75 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(55, 183, 195, 0.4);
}

.patient-search-modal-content .search-button:active {
  transform: translateY(0);
}

/* Enhanced Results Styling */
.patient-search-modal-content .search-results {
  padding: 32px;
  background-color: white;
}

.patient-search-modal-content .search-results h3 {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 24px;
  color: #1f2937;
  padding-bottom: 12px;
  border-bottom: 2px solid #37b7c3;
  display: inline-block;
}

.patient-search-modal-content .results-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.patient-search-modal-content .patient-result-item {
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.patient-search-modal-content .patient-result-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(135deg, #37b7c3 0%, #1c8e98 100%);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.patient-search-modal-content .patient-result-item:hover {
  border-color: #37b7c3;
  box-shadow: 0 8px 25px rgba(55, 183, 195, 0.15);
  transform: translateY(-2px);
}

.patient-search-modal-content .patient-result-item:hover::before {
  transform: scaleY(1);
}

.patient-search-modal-content .patient-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.patient-search-modal-content .patient-details h4,
.patient-search-modal-content .patient-name {
  margin: 0 0 8px 0;
  color: #1f2937;
  font-weight: 700;
  font-size: 18px;
}

.patient-search-modal-content .patient-details p,
.patient-search-modal-content .patient-details {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.patient-search-modal-content .patient-details span {
  background-color: #f3f4f6;
  padding: 4px 8px;
  border-radius: 6px;
  font-weight: 500;
}

.patient-search-modal-content .patient-nhs {
  background-color: #dbeafe !important;
  color: #1e40af !important;
}

.patient-search-modal-content .patient-dob {
  background-color: #f0fdf4 !important;
  color: #166534 !important;
}

.patient-search-modal-content .patient-mrn {
  background-color: #fef3c7 !important;
  color: #92400e !important;
}

.patient-search-modal-content .select-patient-btn {
  background: linear-gradient(135deg, #37b7c3 0%, #1c8e98 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.3);
}

.patient-search-modal-content .select-patient-btn:hover {
  background: linear-gradient(135deg, #1c8e98 0%, #0f6b75 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.4);
}

/* Enhanced No Results Section */
.patient-search-modal-no-results-section {
  margin-top: 32px;
  padding: 40px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  text-align: center;
  border: 2px dashed #d1d5db;
  position: relative;
}

.patient-search-modal-no-results-section::before {
  content: '🔍';
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
  opacity: 0.5;
}

.patient-search-modal-no-results-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.patient-search-modal-no-results-message p {
  margin: 0;
  color: #4b5563;
  font-size: 18px;
  font-weight: 500;
}

.patient-search-modal-add-patient-button {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
}

.patient-search-modal-add-patient-button:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(16, 185, 129, 0.4);
}

.patient-search-modal-add-patient-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.25);
}

.patient-search-modal-add-patient-button:active {
  transform: translateY(-1px);
}

/* Loading and Error States */
.patient-search-modal-content .search-loading {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;
  font-size: 16px;
  font-weight: 500;
}

.patient-search-modal-content .search-error {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
  border-left: 4px solid #ef4444;
  padding: 16px 20px;
  margin: 20px 0;
  color: #dc2626;
  border-radius: 8px;
  font-weight: 500;
}

.patient-search-modal-content .loading-spinner {
  display: inline-block;
  width: 24px;
  height: 24px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #37b7c3;
  border-radius: 50%;
  animation: patient-search-modal-spin 1s linear infinite;
  margin-right: 12px;
}

@keyframes patient-search-modal-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .patient-search-modal-overlay {
    padding: 15px;
  }

  .patient-search-modal {
    width: 100%;
    max-width: 100%;
    max-height: calc(100vh - 30px);
  }

  .patient-search-modal-header {
    padding: 20px 24px;
  }

  .patient-search-modal-title {
    font-size: 20px;
  }

  .patient-search-modal-content {
    padding: 0;
  }

  .patient-search-modal-content .search-form {
    padding: 24px 20px;
  }

  .patient-search-modal-content .search-form-row {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .patient-search-modal-content .search-form-actions {
    flex-direction: column;
    gap: 12px;
  }

  .patient-search-modal-content .clear-button,
  .patient-search-modal-content .search-button {
    width: 100%;
    justify-content: center;
  }

  .patient-search-modal-no-results-section {
    padding: 32px 20px;
  }

  .patient-search-modal-add-patient-button {
    padding: 14px 28px;
    font-size: 14px;
  }

  .patient-search-modal-content .patient-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .patient-search-modal-content .patient-details {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .patient-search-modal-overlay {
    padding: 10px;
  }

  .patient-search-modal {
    max-height: calc(100vh - 20px);
  }

  .patient-search-modal-header {
    padding: 16px 20px;
  }

  .patient-search-modal-title {
    font-size: 18px;
  }

  .patient-search-modal-content {
    padding: 0;
  }

  .patient-search-modal-content .search-form {
    padding: 20px 16px;
  }

  .patient-search-modal-close-button {
    width: 36px;
    height: 36px;
    font-size: 1.25rem;
  }

  .patient-search-modal-content .search-form-field input,
  .patient-search-modal-content .search-form-field select {
    font-size: 14px;
    padding: 10px 14px;
  }
}
