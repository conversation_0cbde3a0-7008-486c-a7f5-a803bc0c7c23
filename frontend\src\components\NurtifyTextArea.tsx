import { useState, ChangeEvent, useEffect } from "react";
import "./styles.css";

interface NurtifyTextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
    id?: string;
    label?: string;
    name?: string;
    value?: string;
    onChange?: (e: ChangeEvent<HTMLTextAreaElement>) => void;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
}

const NurtifyTextArea: React.FC<NurtifyTextAreaProps> = ({ 
    label, 
    name, 
    value, 
    onChange, 
    placeholder, 
    className, 
    disabled, 
    ...rest 
}) => {
    const [inputValue, setInputValue] = useState(value || "");
    
    // Update local state when value prop changes
    useEffect(() => {
        setInputValue(value || "");
    }, [value]);

    const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
        setInputValue(e.target.value);
        onChange?.(e);
    };

    return (
        <div className={`nurtify-input ${className || ""}`}>
            {label && <label htmlFor={name}>{label}</label>}
            <textarea
                id={name}
                name={name}
                value={inputValue}
                onChange={handleChange}
                placeholder={placeholder || "Write your text here"}
                className={className}
                disabled={disabled}
                style={{ width: "100%", border: "none", outline: "none", background: "none" }}
                {...rest} // Spread other props
            />
        </div>
    );
}

export default NurtifyTextArea;
