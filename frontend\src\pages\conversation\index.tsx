import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useCreateConversation } from "@/hooks/conversation.query";
import "./conversation.css";
import LightFooter from "@/shared/LightFooter";
import { ConversationCreateData } from "@/services/api/types";
import NurtifyToggle from "@/components/NurtifyToggle";

interface Question {
  id: number;
  type: string;
  questionName: string;
  required: boolean;
  options?: string[];
}

interface QuestionResponse {
  questionName: string;
  answer: unknown;
  type: string;
}

const formTemplates = {
  emergency: {
    questions: [
      {
        id: 1,
        type: "long-text",
        questionName: "Please describe the emergency situation",
        required: true,
      },
      {
        id: 2,
        type: "long-text",
        questionName: "Additional Details",
        required: false,
      },
    ],
  },
  medication: {
    questions: [
      {
        id: 1,
        type: "short-text",
        questionName: "Name of the medication/Non-drug therapy",
        required: true,
      },
      {
        id: 2,
        type: "short-text",
        questionName: "Reason for taking the medication",
        required: true,
      },
      {
        id: 3,
        type: "short-text",
        questionName: "Dose per single administration (Unit)",
        required: true,
      },
      {
        id: 4,
        type: "short-text",
        questionName: "Schedule/Frequency",
        required: true,
      },
      {
        id: 5,
        type: "dropdown",
        questionName: "Dose Form",
        options: [
          "Tablet",
          "Capsule",
          "Ointment",
          "Suppository",
          "Aerosol",
          "Spray",
          "Suspension",
          "Patch",
          "Gas",
          "Gel",
          "Cream",
          "Powder",
          "Implant",
          "Chewable",
          "Liquid",
          "Other",
        ],
        required: true,
      },
      {
        id: 6,
        type: "dropdown",
        questionName: "Route of Administration",
        options: [
          "Oral",
          "Topical",
          "Subcutaneous",
          "Intradermal",
          "Transdermal",
          "Intraocular",
          "Intramuscular",
          "Inhalation",
          "Intravenous",
          "Intraperitoneal",
          "Nasal",
          "Vaginal",
          "Rectal",
          "Other",
        ],
        required: true,
      },
      {
        id: 7,
        type: "date",
        questionName: "Start date of medication",
        required: true,
      },
      {
        id: 8,
        type: "boolean",
        questionName: "Are you still taking this medication?",
        required: true,
      },
      {
        id: 9,
        type: "date",
        questionName: "If No, when was the end date?",
        required: false,
      },
      {
        id: 10,
        type: "boolean",
        questionName: "Do you usually take this medication?",
        required: true,
      },
      {
        id: 10,
        type: "long-text",
        questionName: "Additional Details",
        required: false,
      },
    ],
  },
  symptoms: {
    questions: [
      {
        id: 1,
        type: "long-text",
        questionName: "Describe your symptoms",
        required: true,
      },
      {
        id: 2,
        type: "date",
        questionName: "Date of symptom start",
        required: true,
      },
      {
        id: 3,
        type: "time",
        questionName: "Time of symptom start",
        required: true,
      },
      {
        id: 4,
        type: "short-text",
        questionName: "When did you last take the trial medication?",
        required: false,
      },
      {
        id: 5,
        type: "dropdown",
        questionName: "Symptom Frequency",
        options: ["Isolated", "Intermittent", "Continuous", "Unknown"],
        required: true,
      },
      {
        id: 6,
        type: "dropdown",
        questionName: "Severity",
        options: ["Mild", "Moderate", "Severe"],
        required: true,
      },
      {
        id: 7,
        type: "boolean",
        questionName: "Did you require a consultation?",
        required: true,
      },
      {
        id: 8,
        type: "boolean",
        questionName: "Have you been admitted to the hospital?",
        required: true,
      },
      {
        id: 9,
        type: "boolean",
        questionName: "Have you taken any medication for these symptoms?",
        required: true,
      },
      {
        id: 10,
        type: "date",
        questionName: "If resolved, enter the date of resolution",
        required: false,
      },
      {
        id: 11,
        type: "long-text",
        questionName: "Additional Details",
        required: false,
      },
    ],
  },
  consultation: {
    questions: [
      {
        id: 1,
        type: "date",
        questionName: "Date of consultation",
        required: true,
      },
      {
        id: 2,
        type: "short-text",
        questionName: "Doctor's name",
        required: true,
      },
      {
        id: 3,
        type: "long-text",
        questionName: "Consultation details",
        required: true,
      },
      {
        id: 4,
        type: "attach-file",
        questionName: "Attach any relevant documents",
        required: false,
      },
      {
        id: 5,
        type: "long-text",
        questionName: "Additional Details",
        required: false,
      },
    ],
  },
  event: {
    questions: [
      { id: 1, type: "short-text", questionName: "Event type", required: true },
      { id: 2, type: "date", questionName: "Event date", required: true },
      {
        id: 3,
        type: "long-text",
        questionName: "Event description",
        required: true,
      },
    ],
  },
  other: {
    questions: [
      { id: 1, type: "short-text", questionName: "Subject", required: true },
      { id: 2, type: "long-text", questionName: "Description", required: true },
      {
        id: 3,
        type: "short-text",
        questionName: "Preferred contact time",
        required: false,
      },
    ],
  },
  "trial-changes": {
    questions: [
      {
        id: 1,
        type: "boolean",
        questionName: "Is there any changes on taking the trial medicine?",
        required: true,
      },
      {
        id: 2,
        type: "long-text",
        questionName: "If yes, please specify the reason",
        required: false,
      },
      {
        id: 3,
        type: "long-text",
        questionName: "How the trial medicine schedule has been changed?",
        required: true,
      },
      {
        id: 4,
        type: "date",
        questionName: "Date of changes",
        required: true,
      },
      {
        id: 5,
        type: "time",
        questionName: "Time of changes",
        required: true,
      },
      {
        id: 6,
        type: "long-text",
        questionName: "Additional Details",
        required: false,
      },
    ],
  },
  refund: {
    questions: [
      {
        id: 1,
        type: "long-text",
        questionName: "Please describe the expenses that you request refund",
        required: true,
      },
      {
        id: 2,
        type: "date",
        questionName: "Date of expenses",
        required: true,
      },
      {
        id: 3,
        type: "long-text",
        questionName: "Additional comments (include amount in GBP £)",
        required: false,
      },
      {
        id: 4,
        type: "attach-file",
        questionName: "Attach file",
        required: false,
      },
      {
        id: 5,
        type: "long-text",
        questionName: "Additional Details",
        required: false,
      },
    ],
  },
  queries: {
    questions: [
      {
        id: 1,
        type: "short-text",
        questionName: "Subject",
        required: true,
      },
      {
        id: 2,
        type: "long-text",
        questionName: "Description",
        required: true,
      },
      {
        id: 3,
        type: "short-text",
        questionName: "When you want to be contacted",
        required: false,
      },
      {
        id: 4,
        type: "long-text",
        questionName: "Additional Details",
        required: false,
      },
    ],
  },
};

interface SuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const SuccessModal: React.FC<SuccessModalProps> = ({ isOpen, onClose }) => {
  if (!isOpen) return null;

  return (
    <div className="conv-modal-overlay">
      <div className="conv-modal-content">
        <h2 className="conv-modal-title">Submission Successful</h2>
        <p className="conv-modal-message">
          Thank you for submitting your form. We wish you good health. Your
          request has been successfully received and will be carefully reviewed.
          We will contact you if necessary. Take care!
        </p>
        <button className="conv-modal-button" onClick={onClose}>
          Close
        </button>
      </div>
    </div>
  );
};

const ConversationPage: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const createConversation = useCreateConversation();
  const [formData, setFormData] = useState<Record<number, QuestionResponse>>({});
  const [patientData] = useState({
    nhs_number: "",
    first_name: "",
    last_name: "",
    date_of_birth: "",
    phone_number: "",
    email: "",
    hospital_name: "",
    deparmtent_name: "",
    status: "pending",
    treating_clinicain: "",
    patient: "",
    attach_content: null,
    last_update_person: "",
    comments: "",
  });
  const [showModal, setShowModal] = useState(false);
  const [showMessage, setShowMessage] = useState(true); // State to control message visibility
  const formType = new URLSearchParams(location.search).get("type") || "other";
  const currentForm = formTemplates[formType as keyof typeof formTemplates];

  const handleInputChange = (question: Question, value: unknown) => {
    setFormData((prev) => ({
      ...prev,
      [question.id]: {
        questionName: question.questionName,
        answer: value, // value will be true/false from the toggle
        type: question.type,
      },
    }));
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setShowMessage(false);
    }, 30000); // 20 seconds

    // Cleanup timeout on component unmount
    return () => clearTimeout(timer);
  }, []);

  // const handlePatientDataChange = (field: keyof typeof patientData, value: string) => {
  //   setPatientData((prev) => ({
  //     ...prev,
  //     [field]: value,
  //   }));
  // };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const questions = Object.values(formData).reduce(
        (acc, curr) => ({
          ...acc,
          [curr.questionName]: {
            answer: curr.type === "attach-file" && curr.answer instanceof File ? curr.answer.name : curr.answer,
            type: curr.type,
          },
        }),
        {}
      );

      const conversationData = new FormData();
      conversationData.append("status", patientData.status);
      conversationData.append("treating_clinicain", patientData.treating_clinicain);
      conversationData.append("patient", patientData.patient);
      conversationData.append("last_update_person", patientData.last_update_person || "");
      conversationData.append("comments", patientData.comments || "");
      conversationData.append("questions", JSON.stringify(questions));

      // Append file if it exists
      const fileQuestion = Object.values(formData).find(q => q.type === "attach-file" && q.answer instanceof File);
      if (fileQuestion && fileQuestion.answer) {
        conversationData.append("attach_content", fileQuestion.answer as File);
      }

      await createConversation.mutateAsync(conversationData as unknown as ConversationCreateData); // Type assertion due to FormData
      setShowMessage(true);
    } catch (error) {
      console.error("Error creating conversation:", error);
    }
  };

  const handleModalClose = () => {
    setShowModal(false);
    navigate("/patient");
  };

  const renderQuestion = (question: Question) => {
    switch (question.type) {
      case "short-text":
        return (
          <input
            type="text"
            className="conv-form-control"
            required={question.required}
            onChange={(e) => handleInputChange(question, e.target.value)}
          />
        );
      case "long-text":
        return (
          <textarea
            className="conv-form-control conv-form-textarea"
            required={question.required}
            rows={5}
            onChange={(e) => handleInputChange(question, e.target.value)}
          />
        );
      case "date":
        return (
          <input
            type="date"
            className="conv-form-control"
            required={question.required}
            onChange={(e) => handleInputChange(question, e.target.value)}
          />
        );
      case "time":
        return (
          <input
            type="time"
            className="conv-form-control"
            required={question.required}
            onChange={(e) => handleInputChange(question, e.target.value)}
          />
        );
      case "boolean":
        return (
          <div className="conv-form-check">
            <NurtifyToggle
              name={question.id.toString()}
              value={formData[question.id]?.answer?.toString() || "false"}
              onChange={(newValue) => handleInputChange(question, newValue === "true")}
              labels={["Yes", "No"]}
            />
          </div>
        );
      case "dropdown":
        return (
          <select
            className="conv-form-select"
            required={question.required}
            onChange={(e) => handleInputChange(question, e.target.value)}
          >
            <option value="">Select...</option>
            {question.options?.map((option: string) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        );
      case "attach-file":
        return (
          <input
            type="file"
            className="conv-form-control"
            required={question.required}
            onChange={(e) => handleInputChange(question, e.target.files?.[0] || null)}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="conv-main-content bg-light-4">
     
      <div className="conv-container">
        {showMessage && (
          <div
            className="convdetails-comments-section"
            style={{
              backgroundColor: "#BF313160",
              borderRadius: "10px",
              padding: "15px",
              margin: "11px auto",
              display: "flex",
              maxWidth: "100%",
              justifyContent: "center",
              alignItems: "center",
              color: "black",
              fontWeight: "bold",
            }}
          >
            <p className="convdetails-comment">
              In an emergency 🚨, call 999 immediately for urgent issues like severe chest pain, breathing difficulty, unconsciousness, or serious injuries.<br />
              For non-emergencies, use NHS 111 (24/7) for advice and referrals to GPs, urgent care, or other services. Contact your GP or pharmacy for minor concerns. Reserve hospitals and A&E for life-threatening conditions. Unsure? Call NHS 111 for guidance.
            </p>
          </div>
        )}
        <form onSubmit={handleSubmit} className="conv-form" style={{ width: "870px" }}>
          <div className="conv-questions-section">
            <h2>
              {formType.charAt(0).toUpperCase() + formType.slice(1)} Questions
            </h2>
            {currentForm.questions.map((question, index) => (
              <div key={question.id} className="conv-form-group">
                <span className="conv-question-number">
                  Question n° {index + 1}
                </span>
                <label className="conv-form-label">
                  {question.questionName}
                  {question.required && (
                    <span className="conv-text-danger">*</span>
                  )}
                </label>
                {renderQuestion(question)}
              </div>
            ))}
          </div>
          <button type="submit" className="conv-btn-primary" onClick={() => setShowModal(true)}>
            Submit
          </button>
        </form>
        <br /> <br /> <br />
      </div>
      <SuccessModal isOpen={showModal} onClose={handleModalClose} />
      <LightFooter />
    </div>
  );
};

export default ConversationPage;
