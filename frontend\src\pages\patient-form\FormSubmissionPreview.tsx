import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { ArrowLeft, User, Calendar, Paperclip, FileText, CheckSquare, 
         List, ToggleLeft, Type, ChevronDown, Sliders, Grid, Signature, Table as TableIcon, 
         HelpCircle, MessageSquare, AlertCircle, CheckCircle } from "lucide-react";
import { createFormSubmissionQuery } from "../../services/api/form.service";
import { formatDate } from "@/utils/date";
import "./patientfrom.css";
import { GeneratePdfButton } from "./GeneratePdfButton";

interface Query {
  id: string;
  text: string;
  priority: 'low' | 'medium' | 'high';
  status: 'open' | 'answered' | 'resolved';
  createdAt: string;
  createdBy: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  reply?: string | null;
  repliedAt?: string | null;
  repliedBy?: {
    identifier: string;
    first_name: string;
    last_name: string;
    email: string;
  } | null;
}

interface QuestionQuery {
  questionId: string;
  queries: Query[];
}

interface FormSubmissionPreviewProps {
  submission: any;
  onClose: () => void;
  hideRaiseQuery?: boolean;
}

const FormSubmissionPreview: React.FC<FormSubmissionPreviewProps> = ({ submission, onClose, hideRaiseQuery = false }) => {
  const [questionQueries, setQuestionQueries] = useState<QuestionQuery[]>([]);
  const [activeQuestionId, setActiveQuestionId] = useState<string | null>(null);
  const [showHistory, setShowHistory] = useState<Record<string, boolean>>({});

  if (!submission) {
    return null;
  }

  // Extract form name from submission data
  let formName = "Untitled Form";
  if (submission.submission?.formDetails?.name) {
    formName = submission.submission.formDetails.name;
  } else if (typeof submission.form !== 'string' && submission.form?.name) {
    formName = submission.form.name;
  }

  // Format user name
  const formatUserName = (user: { first_name?: string; last_name?: string; identifier?: string } | null) => {
    if (!user) return "Unknown User";
    const firstName = user.first_name || "";
    const lastName = user.last_name || "";
    return firstName || lastName ? `${firstName} ${lastName}`.trim() : user.identifier || "Unknown User";
  };

  // Get question type display name with enhanced styling
  const getQuestionTypeDisplay = (type?: string) => {
    if (!type) return 'Unknown';
    
    const typeMap: {[key: string]: string} = {
      'short-text': 'Short Text',
      'long-text': 'Long Text',
      'single-choice': 'Single Choice',
      'multiple-choice': 'Multiple Choice',
      'boolean': 'Yes/No',
      'multiple-text-boxes': 'Multiple Inputs',
      'dropdown': 'Dropdown',
      'multi-select-dropdown': 'Multi-select Dropdown',
      'attach-file': 'File Attachment',
      'signature': 'Signature',
      'range': 'Range Slider',
      'table': 'Table',
      'Expression': 'Expression'
    };
    
    return typeMap[type] || type.charAt(0).toUpperCase() + type.slice(1).replace(/-/g, ' ');
  };

  // Get badge color based on question type
  const getQuestionBadgeColor = (type?: string) => {
    const colorMap: {[key: string]: string} = {
      'short-text': '#4299e1',
      'long-text': '#3182ce',
      'single-choice': '#38b2ac',
      'multiple-choice': '#319795',
      'boolean': '#805ad5',
      'multiple-text-boxes': '#667eea',
      'dropdown': '#ed8936',
      'multi-select-dropdown': '#dd6b20',
      'attach-file': '#9f7aea',
      'signature': '#d53f8c',
      'range': '#38a169',
      'table': '#2b6cb0',
      'Expression': '#718096'
    };
    
    return colorMap[type || ''] || '#718096';
  };

  // Get question icon based on type with enhanced styling
  const getQuestionIcon = (type?: string) => {
    const iconStyle = {
      padding: '8px',
      borderRadius: '50%',
      backgroundColor: `${getQuestionBadgeColor(type)}20`, // 20 is hex for 12% opacity
      color: getQuestionBadgeColor(type),
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxShadow: '0 2px 5px rgba(0, 0, 0, 0.05)'
    };
    
    const icon = (() => {
      switch(type) {
        case 'single-choice':
          return <CheckSquare size={18} />;
        case 'multiple-choice':
          return <List size={18} />;
        case 'boolean':
          return <ToggleLeft size={18} />;
        case 'short-text':
          return <Type size={18} />;
        case 'long-text':
          return <FileText size={18} />;
        case 'multiple-text-boxes':
          return <Grid size={18} />;
        case 'dropdown':
        case 'multi-select-dropdown':
          return <ChevronDown size={18} />;
        case 'attach-file':
          return <Paperclip size={18} />;
        case 'signature':
          return <Signature size={18} />;
        case 'range':
          return <Sliders size={18} />;
        case 'table':
          return <TableIcon size={18} />;
        case 'Expression':
          return <FileText size={18} />;
        default:
          return <List size={18} />;
      }
    })();
    
    return <div style={iconStyle}>{icon}</div>;
  };

  // Helper function to normalize filenames for better matching
  const normalizeFileName = (name: string): string => {
    if (!name) return '';
    return name.toLowerCase()
      .replace(/[^a-z0-9.]/g, '') // Remove special characters
      .replace(/\s+/g, '')        // Remove spaces
      .replace(/\(\d+\)$/g, '');  // Remove "(1)" suffix if any
  };

  // Extract base name without extension
  const getBaseName = (filename: string): string => {
    if (!filename) return '';
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex > 0 ? filename.substring(0, lastDotIndex) : filename;
  };

  // Get file extension
  const getFileExtension = (filename: string): string => {
    if (!filename) return '';
    const lastDotIndex = filename.lastIndexOf('.');
    return lastDotIndex > 0 ? filename.substring(lastDotIndex + 1).toLowerCase() : '';
  };

  // Render file attachments from API response format
  const renderAttachments = (question: any) => {
    if (question.type !== 'attach-file') {
      return null;
    }

    console.log("Processing attach-file question:", question.questionName || question.id, "Question ID:", question.id);
    
    // Get metadata about file attachments from question
    const fileMetadata = question['user-answer-metadata'];
    if (!fileMetadata || !Array.isArray(fileMetadata) || fileMetadata.length === 0) {
      return <div className="no-attachments">No files attached</div>;
    }
    
    console.log("File metadata found:", fileMetadata);
    console.log("All attachments:", submission.attachments);
    
    return (
      <div className="attachment-files-list">
        {fileMetadata.map((meta: any, index: number) => {
          // Determine file type from metadata
          const isImage = meta.type?.startsWith('image/');
          const isDocument = meta.type === 'application/pdf' || 
                            meta.type?.includes('document') || 
                            meta.name?.toLowerCase().endsWith('.pdf');
          
          // Find matching attachment by comparing file names in URLs
          let fileUrl = '';
          let matchingAttachment = null;
          
          if (submission.attachments && Array.isArray(submission.attachments)) {
            // Get base name and extension of the current file
            const metaBaseName = normalizeFileName(getBaseName(meta.name));
            const metaExtension = getFileExtension(meta.name);
            
            
            matchingAttachment = submission.attachments.find((att: { file: string; }) => {
              if (!att.file) return false;
              
              
              const urlFileName = decodeURIComponent(att.file.split('/').pop() || '');
              const urlBaseName = normalizeFileName(getBaseName(urlFileName));
              const urlExtension = getFileExtension(urlFileName);
              
              
              console.log(`Comparing file: meta=${metaBaseName}.${metaExtension} with url=${urlBaseName}.${urlExtension}`);
              
              
              return (
                (urlBaseName === metaBaseName && urlExtension === metaExtension) || 
                (urlBaseName === metaBaseName && isImage && urlExtension.match(/^(jpg|jpeg|png|gif|bmp|webp|svg)$/)) ||
                (urlBaseName.includes(metaBaseName) || metaBaseName.includes(urlBaseName))
              );
            });
            
            
            if (!matchingAttachment && isImage) {
              
              matchingAttachment = submission.attachments.find((att: { file: string; }) => {
                if (!att.file) return false;
                const urlExtension = getFileExtension(att.file);
                return urlExtension.match(/^(jpg|jpeg|png|gif|bmp|webp|svg)$/);
              });
            }
          }
          
          // Get the URL from the matching attachment
          if (matchingAttachment) {
            fileUrl = matchingAttachment.file;
            console.log(`Match found for ${meta.name}:`, fileUrl);
          } else {
            console.log(`No match found for ${meta.name}`);
          }
          
          return (
            <div key={index} className="attachment-file-item">
              {/* Icon based on file type */}
              {isImage && <span className="file-type-icon">🖼️</span>}
              {isDocument && <span className="file-type-icon">📄</span>}
              {!isImage && !isDocument && <span className="file-type-icon">📎</span>}
              
              {/* File name as a link if URL exists */}
              {fileUrl ? (
                <a 
                  href={fileUrl} 
                  target="_blank" 
                  rel="noopener noreferrer" 
                  className="file-name file-link"
                >
                  {meta.name}
                </a>
              ) : (
                <span className="file-name">{meta.name}</span>
              )}
              
              <span className="file-size">({(meta.size / 1024).toFixed(1)} KB)</span>
              
              {/* Show image preview for images */}
              {fileUrl && isImage && (
                <div className="file-preview">
                  <img 
                    src={fileUrl} 
                    alt={meta.name} 
                    className="image-preview" 
                    onClick={() => window.open(fileUrl, '_blank')}
                    onError={(e) => {
                      console.error(`Error loading image: ${fileUrl}`);
                      (e.target as HTMLImageElement).style.display = 'none';
                    }}
                  />
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // Render specific question types with enhanced styling
  const renderQuestionAnswer = (question: any) => {
    if (!question) return null;
    
    const userAnswer = question['user-answer'];
    
    switch(question.type) {
      case 'short-text':
      case 'long-text':
        return (
          <div className="question-answer-text">
            {userAnswer ? (
              <div className="answer-content">
                {String(userAnswer)}
              </div>
            ) : (
              <div className="no-answer">
                <em>No answer provided</em>
              </div>
            )}
          </div>
        );
        
      case 'single-choice':
        return (
          <div className="question-answer-single-choice">
            {userAnswer ? (
              <div className="selected-option">
                <CheckSquare size={16} className="option-icon" />
                <span className="option-text">{userAnswer}</span>
              </div>
            ) : (
              <div className="no-answer">
                <em>No option selected</em>
              </div>
            )}
          </div>
        );
        
      case 'multiple-choice':
        if (!userAnswer || !Array.isArray(userAnswer) || userAnswer.length === 0) {
          return <div className="no-answer"><em>No options selected</em></div>;
        }
        
        return (
          <div className="question-answer-multiple-choice">
            {userAnswer.map((option, idx) => (
              <div key={idx} className="selected-option">
                <CheckSquare size={16} className="option-icon" />
                <span className="option-text">{option}</span>
              </div>
            ))}
          </div>
        );
        
      case 'boolean':
        return (
          <div className="question-answer-boolean">
            {userAnswer ? (
              <div className="selected-option">
                {userAnswer === 'Yes' ? (
                  <span className="boolean-yes">✓ Yes</span>
                ) : (
                  <span className="boolean-no">✗ No</span>
                )}
              </div>
            ) : (
              <div className="no-answer">
                <em>No answer provided</em>
              </div>
            )}
          </div>
        );
        
      case 'dropdown':
        return (
          <div className="question-answer-dropdown">
            {userAnswer ? (
              <div className="selected-option">
                <ChevronDown size={16} className="option-icon" />
                <span className="option-text">{userAnswer}</span>
              </div>
            ) : (
              <div className="no-answer">
                <em>No option selected</em>
              </div>
            )}
          </div>
        );
        
      case 'multi-select-dropdown':
        if (!userAnswer || !Array.isArray(userAnswer) || userAnswer.length === 0) {
          return <div className="no-answer"><em>No options selected</em></div>;
        }
        
        return (
          <div className="question-answer-multi-select">
            {userAnswer.map((option, idx) => (
              <div key={idx} className="selected-option">
                <CheckSquare size={16} className="option-icon" />
                <span className="option-text">{option}</span>
              </div>
            ))}
          </div>
        );
        
      case 'multiple-text-boxes':
        if (!userAnswer || !Array.isArray(userAnswer) || userAnswer.length === 0) {
          return <div className="no-answer"><em>No values provided</em></div>;
        }
        
        return (
          <div className="question-answer-multiple-textboxes">
            {question.options?.map((option: string, idx: number) => (
              <div key={idx} className="textbox-item">
                <span className="textbox-label">{option}:</span>
                <span className="textbox-value">{userAnswer[idx] || '-'}</span>
              </div>
            ))}
          </div>
        );
        
      case 'range':
        return (
          <div className="question-answer-range">
            {userAnswer !== null && userAnswer !== undefined ? (
              <div className="range-value">
                <div className="range-bar">
                  <div 
                    className="range-fill" 
                    style={{
                      width: `${((Number(userAnswer) - (Number(question.range?.min) || 0)) / 
                              ((Number(question.range?.max) || 100) - (Number(question.range?.min) || 0))) * 100}%`
                    }}
                  ></div>
                </div>
                <div className="range-labels">
                  <span className="range-min">Min: {question.range?.min || 0}</span>
                  <span className="range-selected">Selected: {userAnswer}</span>
                  <span className="range-max">Max: {question.range?.max || 100}</span>
                </div>
              </div>
            ) : (
              <div className="no-answer">
                <em>No value selected</em>
              </div>
            )}
          </div>
        );
        
      case 'table':
        if (!userAnswer || typeof userAnswer !== 'object' || !userAnswer.data || !Array.isArray(userAnswer.data)) {
          return <div className="no-answer"><em>No table data provided</em></div>;
        }
        
        return (
          <div className="question-answer-table">
            <div className="table-responsive">
              <table className="answer-table">
                <thead>
                  <tr>
                    {question.table?.columns?.map((col: string, idx: number) => (
                      <th key={idx}>{col}</th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {userAnswer.data.map((row: any[], rowIdx: number) => (
                    <tr key={rowIdx}>
                      {row.map((cell, cellIdx) => (
                        <td key={cellIdx}>{cell || '-'}</td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        );
        
      case 'signature':
        if (!userAnswer || typeof userAnswer !== 'object') {
          return <div className="no-answer"><em>No signature provided</em></div>;
        }
        
        return (
          <div className="question-answer-signature">
            {userAnswer.fullName && (
              <div className="signature-name">
                <span className="signature-label">Name:</span>
                <span className="signature-value">{userAnswer.fullName}</span>
              </div>
            )}
            {userAnswer.date && (
              <div className="signature-date">
                <span className="signature-label">Date:</span>
                <span className="signature-value">{userAnswer.date}</span>
              </div>
            )}
          </div>
        );
        
      case 'attach-file':
        return renderAttachments(question);
        
      default:
        if (userAnswer === null || userAnswer === undefined) {
          return <div className="no-answer"><em>No answer provided</em></div>;
        }
        
        return (
          <div className="question-answer-default">
            <div className="answer-content">
              {typeof userAnswer === 'object' ? JSON.stringify(userAnswer, null, 2) : String(userAnswer)}
            </div>
          </div>
        );
    }
  };

  // Render form answers
  const renderFormAnswers = () => {
    if (!submission.submission?.sections) {
      return <p>No form data available.</p>;
    }

    return submission.submission.sections.map((section: any, sectionIndex: number) => (
      <div className="formpreview-section" key={sectionIndex}>
        {section.name && <h3 className="formpreview-section-name">{section.name}</h3>}
        {section.description && <p className="formpreview-section-description">{section.description}</p>}
        
        {section.questions && section.questions.map((question: any, questionIndex: number) => {
          // Skip expression questions in the display
          if (question.type === 'Expression') return null;
          
          const questionId = `${section.id}-${question.id}`;
          const currentQuestionQueries = questionQueries.find((q: QuestionQuery) => q.questionId === questionId)?.queries || [];
          const openQueries = currentQuestionQueries.filter((q: Query) => q.status === 'open');
          
          return (
            <div className="formpreview-question-item" key={questionIndex}>
              <div className="formpreview-question-header">
                <div className="question-icon">{getQuestionIcon(question.type)}</div>
                <div className="question-content">
                  <div className="question-header-row">
                    <h4 className="question-name">
                      {question.questionName || `Question ${questionIndex + 1}`}
                      <HelpCircle size={16} style={{ marginLeft: '8px', color: '#64748b', verticalAlign: 'middle' }} />
                      {question.required && <span className="question-required">*</span>}
                    </h4>
                    {!hideRaiseQuery && (
                      <button 
                        className="raise-query-button"
                        onClick={() => handleOpenQueryDialog(questionId)}
                        title="Raise Query"
                      >
                        <MessageSquare size={16} />
                        Raise Query
                      </button>
                    )}
                  </div>
                  <div className="question-type">
                    <span className="badge">{getQuestionTypeDisplay(question.type)}</span>
                    {question.nonClinical && <span className="badge badge-non-clinical">Non-Clinical</span>}
                    {openQueries.length > 0 && (
                      <span className="badge badge-query">
                        {openQueries.length} Open {openQueries.length === 1 ? 'Query' : 'Queries'}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <div className="formpreview-answer">
                {renderQuestionAnswer(question)}
                {/* Current answer author - move this just below the answer */}
                {question['answered-by'] && (
                  <div className="formpreview-answer-author" style={{color: '#888', fontSize: '0.97em', marginBottom: 4}}>
                    Answered by: {formatUserName(question['answered-by'])}
                  </div>
                )}
                {/* Toggleable Answer History Section */}
                {Array.isArray(question.answer_history) && question.answer_history.length > 0 && (
                  <>
                    <button
                      className="show-history-link"
                      style={{
                        background: 'none',
                        border: 'none',
                        color: '#3182ce',
                        cursor: 'pointer',
                        padding: 0,
                        margin: '6px 0 8px 0',
                        fontSize: '0.98em',
                        textDecoration: 'underline',
                        display: 'block',
                        textAlign: 'left'
                      }}
                      onClick={() => setShowHistory(prev => ({ ...prev, [questionId]: !prev[questionId] }))}
                    >
                      {showHistory[questionId] ? 'Hide' : 'Show'} Answer History ({question.answer_history.length})
                    </button>
                    {showHistory[questionId] && (
                      <div className="formpreview-answer-history">
                        <strong>Answer History:</strong>
                        <ul style={{marginTop: 8, marginBottom: 0, paddingLeft: 18}}>
                          {question.answer_history.map((entry: any, idx: number) => (
                            <li key={idx} style={{marginBottom: 4}}>
                              <span style={{fontWeight: 500}}>{typeof entry.answer === 'object' ? JSON.stringify(entry.answer) : String(entry.answer)}</span>
                              {entry['answered-at'] && (
                                <span style={{color: '#888', marginLeft: 8, fontSize: '0.95em'}}>
                                  (at {formatDate(entry['answered-at'])})
                                </span>
                              )}
                              {entry['answered-by'] && (
                                <span style={{color: '#888', marginLeft: 8, fontSize: '0.95em'}}>
                                  by {formatUserName(entry['answered-by'])}
                                </span>
                              )}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </>
                )}
                {question.comment && (
                  <div className="formpreview-question-comment">
                    <span className="formpreview-comment-label">Comment:</span> {question.comment}
                  </div>
                )}
                
                {/* Query Section */}
                {currentQuestionQueries.length > 0 && (
                  <div className="question-queries">
                    <h5>Queries</h5>
                    {currentQuestionQueries.map((query: Query) => (
                      <div key={query.id} className={`query-item ${query.status}`}>
                        <div className="query-header">
                          <div className="query-meta">
                            <span className={`query-status ${query.status}`}>
                              {query.status === 'open' && <AlertCircle size={14} />}
                              {query.status === 'answered' && <MessageSquare size={14} />}
                              {query.status === 'resolved' && <CheckCircle size={14} />}
                              {query.status.charAt(0).toUpperCase() + query.status.slice(1)}
                            </span>
                            <span className="query-priority">{query.priority} Priority</span>
                            <span className="query-date">{formatDate(query.createdAt)}</span>
                          </div>
                          {query.status === 'open' && (
                            <button 
                              className="resolve-query-button"
                              onClick={() => handleResolveQuery(questionId, query.id)}
                            >
                              Mark as Resolved
                            </button>
                          )}
                        </div>
                        <div className="query-content">
                          <p className="query-text">{query.text}</p>
                          {query.reply && (
                            <div className="query-reply">
                              <span className="reply-label">Reply:</span>
                              <p className="reply-text">{query.reply}</p>
                              <span className="reply-meta">
                                By {query.repliedBy ? formatUserName(query.repliedBy) : 'Unknown User'} on {formatDate(query.repliedAt || '')}
                              </span>
                            </div>
                          )}
                          {query.status === 'open' && (
                            <div className="query-reply-form">
                              <textarea
                                placeholder="Enter your reply..."
                                rows={2}
                                onChange={(e) => handleReplyToQuery(questionId, query.id, e.target.value)}
                              />
                              <button 
                                className="submit-reply-button"
                                onClick={() => handleReplyToQuery(questionId, query.id, '')}
                              >
                                Submit Reply
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    ));
  };

  const handleOpenQueryDialog = (questionId: string) => {
    setActiveQuestionId(questionId);
  };

  const handleCloseQueryDialog = () => {
    setActiveQuestionId(null);
  };

  const handleSubmitQuery = async () => {
    if (!activeQuestionId) return;

    try {
      const response = await createFormSubmissionQuery({
        form_submission_uuid: submission.uuid,
        question_number: activeQuestionId,
        description: '',
        priority: 'medium'
      });

      // Add the new query to the state
      setQuestionQueries((prev: QuestionQuery[]) => {
        const existingQuestion = prev.find(q => q.questionId === activeQuestionId);
        if (existingQuestion) {
          return prev.map(q => 
            q.questionId === activeQuestionId 
              ? {
                  ...q,
                  queries: [...q.queries, {
                    id: response.uuid,
                    text: response.description,
                    priority: response.priority,
                    status: 'open',
                    createdAt: response.created_at,
                    createdBy: response.created_by,
                    reply: null,
                    repliedAt: null,
                    repliedBy: null
                  }]
                }
              : q
          );
        } else {
          return [...prev, {
            questionId: activeQuestionId,
            queries: [{
              id: response.uuid,
              text: response.description,
              priority: response.priority,
              status: 'open',
              createdAt: response.created_at,
              createdBy: response.created_by,
              reply: null,
              repliedAt: null,
              repliedBy: null
            }]
          }];
        }
      });

      setActiveQuestionId(null);
    } catch (error) {
      console.error('Error creating query:', error);
    }
  };

  const handleReplyToQuery = (questionId: string, queryId: string, reply: string) => {
    setQuestionQueries(prev => 
      prev.map(q => {
        if (q.questionId === questionId) {
          return {
            ...q,
            queries: q.queries.map(query => 
              query.id === queryId
                ? {
                    ...query,
                    status: 'answered',
                    reply,
                    repliedAt: new Date().toISOString(),
                    repliedBy: {
                      identifier: 'current-user-id', // TODO: Get from auth context
                      first_name: 'Current',
                      last_name: 'User',
                      email: '<EMAIL>'
                    }
                  }
                : query
            )
          };
        }
        return q;
      })
    );
  };

  const handleResolveQuery = (questionId: string, queryId: string) => {
    setQuestionQueries(prev => 
      prev.map(q => {
        if (q.questionId === questionId) {
          return {
            ...q,
            queries: q.queries.map(query => 
              query.id === queryId
                ? { ...query, status: 'resolved' }
                : query
            )
          };
        }
        return q;
      })
    );
  };

  const QueryDialog: React.FC<{
    isOpen: boolean;
    questionId: string | null;
    formSubmissionUuid: string;
    onClose: () => void;
    onSuccess: (query: Query) => void;
  }> = ({ isOpen, questionId, formSubmissionUuid, onClose, onSuccess }) => {
    const [text, setText] = useState('');
    const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [successMessage, setSuccessMessage] = useState<string | null>(null);

    useEffect(() => {
      if (isOpen) {
        setText('');
        setPriority('medium');
        setError(null);
        setSuccessMessage(null);
      }
    }, [isOpen, questionId]);

    if (!isOpen || !questionId) return null;

    const getPriorityColor = (priority: string) => {
      switch (priority) {
        case 'high': return '#ef4444';
        case 'medium': return '#f59e0b';
        case 'low': return '#10b981';
        default: return '#6b7280';
      }
    };

    const handleSubmit = async () => {
      if (!text.trim()) {
        setError('Query description is required');
        return;
      }
      setIsSubmitting(true);
      setError(null);
      setSuccessMessage(null);
      try {
        const response = await createFormSubmissionQuery({
          form_submission_uuid: formSubmissionUuid,
          question_number: questionId,
          description: text,
          priority: priority
        });
        setSuccessMessage('Query submitted successfully!');
        onSuccess({
          id: response.uuid,
          text: response.description,
          priority: response.priority,
          status: 'open',
          createdAt: response.created_at,
          createdBy: response.created_by,
          reply: null,
          repliedAt: null,
          repliedBy: null
        });
        // Add a small delay before closing to show the success message
        setTimeout(() => {
          onClose();
        }, 1000);
      } catch {
        setError('Failed to submit query. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
    };

    return (
      <div className="query-dialog-overlay">
        <div className="query-dialog">
          <div className="query-dialog-header">
            <h3>Raise Query</h3>
            <button className="close-button" onClick={onClose}>×</button>
          </div>
          <div className="query-form">
            <div className="form-group">
              <label htmlFor="query-text">Query Description</label>
              <textarea
                id="query-text"
                value={text}
                onChange={e => setText(e.target.value)}
                placeholder="Enter your query details..."
                rows={4}
                className={error && !text.trim() ? 'error' : ''}
              />
              {error && !text.trim() && (
                <span className="error-message">Query description is required</span>
              )}
            </div>
            <div className="form-group">
              <label htmlFor="query-priority">Priority Level</label>
              <div className="priority-selector">
                {(['low', 'medium', 'high'] as const).map((p) => (
                  <button
                    key={p}
                    className={`priority-option ${priority === p ? 'selected' : ''}`}
                    onClick={() => setPriority(p)}
                    style={{
                      backgroundColor: priority === p ? getPriorityColor(p) : 'transparent',
                      borderColor: getPriorityColor(p),
                      color: priority === p ? 'white' : getPriorityColor(p)
                    }}
                    type="button"
                  >
                    {p.charAt(0).toUpperCase() + p.slice(1)}
                  </button>
                ))}
              </div>
            </div>
            {error && (
              <div className="error-banner">
                <AlertCircle size={16} />
                <span>{error}</span>
              </div>
            )}
            {successMessage && (
              <div className="success-banner">
                <CheckCircle size={16} />
                <span>{successMessage}</span>
              </div>
            )}
            <div className="query-dialog-actions">
              <button onClick={onClose} className="btn-secondary" disabled={isSubmitting}>Cancel</button>
              <button onClick={handleSubmit} className="btn-primary" disabled={!text.trim() || isSubmitting}>
                {isSubmitting ? (<><span className="loading-spinner"></span>Submitting...</>) : 'Submit Query'}
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };
  

  return (
    <motion.div
      className="formpreview-main"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <div className="formpreview-content">
        <div className="formpreview-container">
          
          <div className="formpreview-header">
          <div className="form-actions">
            <GeneratePdfButton uuid={submission.uuid} isSubmission />                  
          </div>
            <button className="formpreview-back-button" onClick={onClose}>
              <ArrowLeft size={20} />
              Back
            </button>
            <h1>{formName}</h1>
          </div>

          <div className="formpreview-card">
            <div className="formpreview-info">
              <h2>Form Information</h2>
              <div className="formpreview-info-grid">
                <div className="formpreview-info-item">
                  <label><User size={16} style={{ marginRight: '4px' }} /> Submitted By:</label>
                  <span>{formatUserName(submission.user)}</span>
                </div>
                <div className="formpreview-info-item">
                  <label><Calendar size={16} style={{ marginRight: '4px' }} /> Submitted:</label>
                  <span>{formatDate(submission.created_at)}</span>
                </div>
                {submission.last_updated_at && (
                  <div className="formpreview-info-item">
                    <label><Calendar size={16} style={{ marginRight: '4px' }} /> Last Updated:</label>
                    <span>{formatDate(submission.last_updated_at)}</span>
                  </div>
                )}
                <div className="formpreview-info-item">
                  <label>Status:</label>
                  <span className="submission-status">
                    {submission.is_finalized ? "Submitted" : submission.is_completed ? "Completed" : "Draft"}
                  </span>
                </div>
              </div>
            </div>
          
            {submission.comments && (
              <div className="formpreview-comments-section">
                <h2>Comments</h2>
                <p className="formpreview-comment">{submission.comments}</p>
              </div>
            )}

            <div className="formpreview-form-data">
              <h2>Form Responses</h2>
              {renderFormAnswers()}
            </div>
          </div>
        </div>
      </div>
      
      {/* Query Dialog */}
      <QueryDialog
        isOpen={!!activeQuestionId}
        questionId={activeQuestionId}
        formSubmissionUuid={submission.uuid}
        onClose={handleCloseQueryDialog}
        onSuccess={handleSubmitQuery}
      />
    </motion.div>
  );
};

export default FormSubmissionPreview;
