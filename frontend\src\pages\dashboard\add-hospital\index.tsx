import "./addhospital.css";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import NurtifyText from "@components/NurtifyText.tsx";
import NurtifyInput from "@components/NurtifyInput.tsx";
import NurtifyTextArea from "@components/NurtifyTextArea.tsx";
import { motion } from "framer-motion";
import { Building, MapPin, CheckCircle } from "lucide-react";
import { useCreateHospitalMutation } from "@/hooks/hospital.query";
import AddDepHospModal from "@/components/modal/AddDepHospModal";

export default function AddHospital() {
  const createHospitalMutation = useCreateHospitalMutation();
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [newHospitalUuid, setNewDepartmentUuid] = useState<string | null>(null);
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState({
    name: "",
    phone_number: "",
    extension: "",
    point_of_contact_Person: "",
    primary_address: "",
    secondary_address: "",
    postcode: "",
    country: "",
    description: "",
  });

  const validateField = (name: string, value: string) => {
    const maxLengths: Record<string, number> = {
      name: 50,
      phone_number: 20,
      extension: 20,
      point_of_contact_Person: 50,
      primary_address: 50,
      secondary_address: 50,
      postcode: 20,
      country: 20,
      description: 100,
    };

    if (name === 'description') {
      if (value && value.length > maxLengths[name]) {
        return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} must not exceed ${maxLengths[name]} characters`;
      }
      return '';
    }
    
    if (!value.trim()) {
      return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} is required`;
    }
    if (value.length > maxLengths[name]) {
      return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} must not exceed ${maxLengths[name]} characters`;
    }
    return '';
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    
    // Validate on change
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all fields
    const newErrors: Record<string, string> = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof typeof formData]);
      if (error) newErrors[key] = error;
    });

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      try {
        // Transform hospital name: replace spaces with underscores
        const formattedData = {
          ...formData,
          name: formData.name.trim().replace(/\s+/g, '_')
        };

        const response = await createHospitalMutation.mutateAsync(formattedData);
        setNewDepartmentUuid(response.uuid);
        setShowPopup(true);
      } catch (error) {
        console.error("Error creating hospital:", error);
      }
    } else {
      // Move to the step with errors
      const step1Fields = ['name', 'phone_number', 'extension', 'point_of_contact_Person'];
      const hasStep1Errors = step1Fields.some(field => newErrors[field]);
      setCurrentStep(hasStep1Errors ? 1 : 2);
    }
  };
  
  const nextStep = () => {
    if (currentStep < 2) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePopupYes = () => {
    if (newHospitalUuid) {
      navigate(`/org/dashboard/hospital-details/${newHospitalUuid}/hospitalAdmin`); // Navigate to admin assignment
    }
    setShowPopup(false);
  };

  const handlePopupNo = () => {
    navigate("/org/dashboard/hospitals",{ replace: true });
    setShowPopup(false);
  };

  return (
    <div className="add-hospital-container">
      <div className="add-hospital-header">
        <div className="add-hospital-title">
          <h1>
            <Building size={24} style={{ marginRight: "10px" }} />
            Add New Hospital
          </h1>
        </div>
        <div className="add-hospital-subtitle">
          <h6>Create and manage hospital information in your network</h6>
        </div>
      </div>
      
      {/* Progress Indicator */}
      <div className="hospital-progress">
        <div
          className={`hospital-step ${currentStep >= 1 ? "active" : ""} ${
            currentStep > 1 ? "completed" : ""
          }`}
        >
          <div className="step-indicator">
            {currentStep > 1 ? <CheckCircle size={16} /> : 1}
          </div>
          <div className="step-label">Hospital Info</div>
        </div>
        <div
          className={`hospital-step ${currentStep >= 2 ? "active" : ""}`}
        >
          <div className="step-indicator">2</div>
          <div className="step-label">Address Details</div>
        </div>
      </div>
      
      <form className="add-hospital-form" onSubmit={handleSubmit}>
        {/* Step 1: Hospital Information */}
        {currentStep === 1 && (
          <motion.div
            className="form-section"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="form-section-title">
              <Building size={18} style={{ marginRight: "8px", verticalAlign: "middle" }} />
              Hospital Information
            </h3>
            <div className="row y-gap-30">
              <div className="col-md-6">
                <NurtifyText label="Hospital Name*" />
                <NurtifyInput
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter Hospital Name here"
                  maxLength={50}
                  
                />
                {errors.name && <div className="field-error">{errors.name}</div>}
              </div>
            </div>

            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Phone*" />
                <NurtifyInput
                  type="text"
                  name="phone_number"
                  value={formData.phone_number}
                  onChange={handleInputChange}
                  placeholder="Enter Phone Number here"
                  maxLength={20}
                  
                />
                {errors.phone_number && <div className="field-error">{errors.phone_number}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Extension*" />
                <NurtifyInput
                  type="text"
                  name="extension"
                  value={formData.extension}
                  onChange={handleInputChange}
                  placeholder="Enter Extension here"
                  maxLength={20}
                  
                />
                {errors.extension && <div className="field-error">{errors.extension}</div>}
              </div>
            </div>

            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Point of Contact Person*" />
                <NurtifyInput
                  type="text"
                  name="point_of_contact_Person"
                  value={formData.point_of_contact_Person}
                  onChange={handleInputChange}
                  placeholder="Enter Point of Contact Person here"
                  maxLength={50}
                  
                />
                {errors.point_of_contact_Person && <div className="field-error">{errors.point_of_contact_Person}</div>}
              </div>
            </div>
            
            <div className="hospital-form-actions">
              <button
                type="button"
                className="hospital-btn-submit"
                onClick={nextStep}
              >
                Next
              </button>
            </div>
          </motion.div>
        )}
        
        {/* Step 2: Address Information */}
        {currentStep === 2 && (
          <motion.div
            className="form-section"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="form-section-title">
              <MapPin size={18} style={{ marginRight: "8px", verticalAlign: "middle" }} />
              Address Information
            </h3>
            
            <div className="row y-gap-30">
              <div className="col-md-6">
                <NurtifyText label="Primary Address*" />
                <NurtifyInput
                  type="text"
                  name="primary_address"
                  value={formData.primary_address}
                  onChange={handleInputChange}
                  placeholder="Enter Primary Address here"
                  maxLength={50}
                  
                />
                {errors.primary_address && <div className="field-error">{errors.primary_address}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Secondary Address*" />
                <NurtifyInput
                  type="text"
                  name="secondary_address"
                  value={formData.secondary_address}
                  onChange={handleInputChange}
                  placeholder="Enter Secondary Address here"
                  maxLength={50}
                  
                />
                {errors.secondary_address && <div className="field-error">{errors.secondary_address}</div>}
              </div>
            </div>
            
            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Postcode*" />
                <NurtifyInput
                  type="text"
                  name="postcode"
                  value={formData.postcode}
                  onChange={handleInputChange}
                  placeholder="Enter Postcode here"
                  maxLength={20}
                  
                />
                {errors.postcode && <div className="field-error">{errors.postcode}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Country*" />
                <NurtifyInput
                  type="text"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  placeholder="Enter Country here"
                  maxLength={20}
                  
                />
                {errors.country && <div className="field-error">{errors.country}</div>}
              </div>  
            </div>
            
            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-12">
                <NurtifyText label="Description" />
                <NurtifyTextArea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter Description here"
                  maxLength={100}
                />
                {errors.description && <div className="field-error">{errors.description}</div>}
              </div>
            </div>
            
            <div className="hospital-form-actions">
              <button
                type="button"
                className="hospital-btn-cancel"
                onClick={prevStep}
              >
                Back
              </button>
              <button
                type="submit"
                className="hospital-btn-submit"
                disabled={createHospitalMutation.isPending}
              >
                {createHospitalMutation.isPending
                  ? "Submitting..."
                  : "Add Hospital"}
              </button>
            </div>
          </motion.div>
        )}
      </form>
      {showPopup && (
        <AddDepHospModal
          type="Hospital"
          isOpen={showPopup}
          onNo={handlePopupNo}
          onYes={handlePopupYes}
        />
      )}
    </div>
  );
}
