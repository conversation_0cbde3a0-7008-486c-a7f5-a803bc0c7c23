import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getAllSymptoms, createSymptom, updateSymptom, getSymptomLogs, type Symptom, type CreateSymptomData, type SymptomLog } from "@/services/api/symptom.service";

export const SYMPTOM_KEYS = {
  all: ['symptoms'] as const,
  byPatient: (patientUuid: string) => [...SYMPTOM_KEYS.all, 'patient', patientUuid] as const,
  logs: (symptomUuid: string) => [...SYMPTOM_KEYS.all, 'logs', symptomUuid] as const,
};

export const useSymptomsQuery = (patientUuid: string) => {
  return useQuery<Symptom[], Error>({
    queryKey: SYMPTOM_KEYS.byPatient(patientUuid),
    queryFn: () => getAllSymptoms(patientUuid),
    enabled: !!patientUuid,
  });
};

export const useCreateSymptomMutation = (patientUuid: string) => {
  const queryClient = useQueryClient();
  return useMutation<Symptom, Error, CreateSymptomData>({
    mutationFn: (data) => {
      // Ensure patient UUID is included in the mutation data
      const mutationData = {
        ...data,
        patient: patientUuid,
      };
      console.log('Mutation data:', mutationData); // Debug log
      return createSymptom(mutationData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ 
        queryKey: SYMPTOM_KEYS.byPatient(patientUuid) 
      });
    },
  });
};

export const useUpdateSymptomMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Symptom, Error, { uuid: string; data: Partial<CreateSymptomData> }>({
    mutationFn: ({ uuid, data }) => updateSymptom(uuid, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ 
        queryKey: SYMPTOM_KEYS.byPatient(data.patient) 
      });
    },
  });
};

export const useSymptomLogsQuery = (symptomUuid: string) => {
  return useQuery<SymptomLog[], Error>({
    queryKey: SYMPTOM_KEYS.logs(symptomUuid),
    queryFn: () => getSymptomLogs(symptomUuid).then(response => response.results),
    enabled: !!symptomUuid,
  });
}; 