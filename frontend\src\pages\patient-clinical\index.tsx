import React, { useState } from "react";
import LightFooter from "@/shared/LightFooter";
import "./patient-clinical.css";
import PatientSidebar from "@/components/common/PatientSidebar";
import AIHelper from "@/components/AIHelper";
import TabContent from "@/components/patient/TabContent";

const PatientClinical: React.FC = () => {
  const [activeTab, setActiveTab] = useState('report');

  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
  };

  return (
    <div className="patclin-dashboard">
      <PatientSidebar onTabChange={handleTabChange} currentTab={activeTab} notificationCount={0} />
      
      <main className="patclin-content">
        <div className="patclin-container">
          <TabContent activeTab={activeTab} />
        </div>
      </main>
      
      <AIHelper />
      <LightFooter />
    </div>
  );
};

export default PatientClinical;
