import { Link } from "react-router-dom";

interface FooterLink {
  href: string;
  label: string;
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

interface FooterLinksProps {
  allClasses?: string;
}

export default function FooterLinks({ allClasses }: FooterLinksProps) {
  const footerLinks: FooterSection[] = [
    {
      title: "ABOUT",
      links: [
        { href: "/about-1", label: "About Us" },
        { href: "/blog-list-1", label: "Learner Stories" },
        { href: "/instructor-become", label: "Careers" },
        { href: "/blog-list-1", label: "Press" },
        { href: "#", label: "Leadership" },
        { href: "/contact-1", label: "Contact Us" },
      ],
    },
    {
      title: "CATEGORIES",
      links: [
        { href: "/courses/1", label: "Development" },
        { href: "/courses-single-2/3", label: "Business" },
        { href: "/courses-single-3/3", label: "Finance & Accounting" },
        { href: "/courses-single-4/3", label: "IT & Software" },
        { href: "/courses-single-5/3", label: "Office Productivity" },
        { href: "/courses-single-6/3", label: "Design" },
        { href: "/courses/1", label: "Marketing" },
      ],
    },
    {
      title: "Others",
      links: [
        { href: "/courses/1", label: "Lifestyle" },
        { href: "/courses-single-2/3", label: "Photography & Video" },
        { href: "/courses-single-3/3", label: "Health & Fitness" },
        { href: "/courses-single-4/3", label: "Music" },
        { href: "/courses-single-5/3", label: "UX Design" },
        { href: "/courses-single-6/3", label: "SEO" },
      ],
    },
    {
      title: "SUPPORT",
      links: [
        { href: "/terms", label: "Documentation" },
        { href: "/help-center", label: "FAQs" },
        { href: "/dashboard", label: "Dashboard" },
        { href: "/contact-1", label: "Contact" },
      ],
    },
  ];
  
  return (
    <>
      {footerLinks.map((elm, i) => (
        <div key={i} className="col-xl-2 col-lg-4 col-md-6">
          <div className={`${allClasses ? allClasses : ""}`}>{elm.title}</div>
          <div className="d-flex y-gap-10 flex-column">
            {elm.links.map((itm, index) => (
              <Link key={index} to={itm.href}>
                {itm.label}
              </Link>
            ))}
          </div>
        </div>
      ))}
    </>
  );
}
