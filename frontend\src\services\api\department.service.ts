import api from "@/services/api.ts";
import { Department } from "./types.ts";

export const getAllDepartments = async (params?: string) => {
    try {
        const queryParams = params?.search ? `?search=${params.search}` : '';
        const response = await api.get(`hospital/departments/${queryParams}`);
        return response.data.results;
    } catch (error) {
        console.error("Error fetching departments:", error);
        throw error;
    }
};

export const getDepartmentsByHospital = async (hospitalUuid: string) => {
    try {
        const response = await api.get(`hospital/departments/hospital/${hospitalUuid}/`);
        // Follow the same pattern as getAllDepartments and getAllHospitals
        return response.data.results;
    } catch (error) {
        console.error("Error fetching departments by hospital:", error);
        throw error;
    }
};

export const getDepartmentById = async (id: string) => {
    const { data } = await api.get(`hospital/departments/${id}/`);
    return data;
};

export const createDepartment = async (DepartmentData: Department) => {
    const { data } = await api.post("hospital/departments/", DepartmentData);
    return data;
};

export const updateDepartment = async (uuid: string, departmentData: Partial<Department>) => {
    const { data } = await api.patch(`hospital/departments/${uuid}/`, departmentData);
    return data;
};

export const deleteDepartment = async (uuid: string) => {
    try {
        await api.delete(`hospital/departments/${uuid}/`)

    } catch (error) {
        console.error("Error fetching department:", error);
        throw error;
    }

}
