import api from "@/services/api"; // Assurez-vous que ce chemin correspond à votre config axios
import type { Allergy, Patient, PatientCreateData, PatientVisitToday, RegistrationStatusLog, UpdateVisitData, UpdateVisitResponse } from "./types";
import type { PatientAccessLog, AddPatientAccessLogData, MedicalHistory, PaginatedResponse, CreateMedicalHistoryData, MedicalHistoryConditionCreate } from "./types";
import { PatientEnrollment } from "@/store/scheduleEventState";
import type { ConcomitantMedication, CreateConcomitantMedicationData, PatientSearchParams } from "./types";

export const getAllPatients = async (): Promise<Patient[]> => {
  const response = await api.get("/patient/patients/");
  return response.data.results || response.data;
};

export const getPatientByNhsNumber = async (nhs_number: string): Promise<Patient> => {
  const { data } = await api.get(`/patient/patients/${nhs_number}/`);
  return data;
};

export const getPatientAllergies = async (nhs_number: string): Promise<Allergy[]> => {
  const { data } = await api.get(`/patient/patients/allergies-by-nhs/${nhs_number}/`);
  return data;
};

export const createPatient = async (patientData: PatientCreateData): Promise<Patient> => {
  const { data } = await api.post("/patient/patients/", patientData);
  return data;
};

export const updatePatient = async (
  nhs_number: string,
  patientData: Partial<Patient>
): Promise<Patient> => {
  const response = await api.put(`/patient/patients/${nhs_number}/`, patientData);
  return response.data;
};

export const partialUpdatePatient = async (
  uuid: string,
  patientData: Partial<Patient>
): Promise<Patient> => {
  const response = await api.patch(`/patient/patients/${uuid}/`, patientData);
  return response.data;
};

export const deletePatientByNhsNumber = async (nhs_number: string): Promise<void> => {
  await api.delete(`/patient/patients/${nhs_number}/`);
};

export const createPatientEnrollment = async (enrollment: PatientEnrollment): Promise<PatientEnrollment> => {
  const response = await api.post("/study/enrollments/enroll/", enrollment);
  return response.data;
};

export const getPatientsWithVisitsToday = async (): Promise<PatientVisitToday[]> => {
  const response = await api.get("/patient/patients/visits-today/");
  return response.data;
};

export const updatePatientVisit = async (
  patientUuid: string,
  visitUuid: string,
  data: UpdateVisitData
): Promise<UpdateVisitResponse> => {
  const response = await api.patch<UpdateVisitResponse>(`/patient/patients/${patientUuid}/update-visit/${visitUuid}/`, data);
  return response.data;
};

// These functions are now implemented below as wrappers around the new getVisits function

/**
 * Get visits based on the specified time period.
 * @param period Time period (D=day, W=week, M=month)
 * @param date Optional reference date in YYYY-MM-DD format. Defaults to current date if not provided.
 * @param useLocalDate Whether to use client's local date instead of server date
 * @returns Array of patient visits
 */
export const getVisits = async (
  period: "D" | "W" | "M",
  date?: string,              // ISO "YYYY-MM-DD"
  useLocalDate = false
): Promise<PatientVisitToday[]> => {
  // Use existing endpoints that are known to work
  // This is a temporary solution until the CORS issue is resolved
  let endpoint = "";
  const params: Record<string, string> = {};

  if (date) {
    params.date = date;
  }

  if (useLocalDate) {
    params.use_local_date = "true";
    if (!date) {
      params.local_date = new Date().toISOString().split('T')[0];
    } else {
      params.local_date = date;
    }
  }

  // Use the unified endpoint with the period parameter
  endpoint = "/patient/patients/visits/";
  params.period = period;

  const qs = new URLSearchParams(params).toString();
  const url = `${endpoint}?${qs}`;

  const { data } = await api.get<PatientVisitToday[]>(url);
  return data;
};

// Legacy function - marked as deprecated, will be removed in future versions
export const getVisitsByDate = async (date: string): Promise<PatientVisitToday[]> => {
  console.warn("getVisitsByDate is deprecated. Please use getVisits('D', date) instead.");
  return getVisits("D", date);
};

// Legacy function - marked as deprecated, will be removed in future versions
export const getTodayVisits = async (): Promise<PatientVisitToday[]> => {
  console.warn("getTodayVisits is deprecated. Please use getVisits('D') instead.");
  return getVisits("D");
};

export const updateVisit = async (
  patientUuid: string,
  visitUuid: string,
  data: {
    registration_status: "Not Arrived" | "In Hospital" | "Discharged";
    location: string;
    nurse_identifier: string;
  }
): Promise<UpdateVisitResponse> => {
  const response = await api.patch<UpdateVisitResponse>(
    `/patient/patients/${patientUuid}/update-visit/${visitUuid}/`,
    data
  );
  return response.data;
};

export const getRegistrationStatusLogsByVisit = async (visitUuid: string): Promise<RegistrationStatusLog[]> => {
  const response = await api.get<RegistrationStatusLog[]>(`/patient/registration-status-logs/by-visit/${visitUuid}/`);
  return response.data;
}
export const getPatientAccessLogs = async (): Promise<PatientAccessLog[]> => {
  const response = await api.get("/patient/access-logs/");
  return response.data.results || response.data;
};

export const getPatientAccessLogsByUuid = async (patientUuid: string): Promise<PatientAccessLog[]> => {
  const response = await api.get(`/patient/access-logs/by-patient/${patientUuid}/`);
  return response.data;
};

export const getMyAccessLogs = async (): Promise<PatientAccessLog[]> => {
  const response = await api.get("/patient/access-logs/my-logs/");
  return response.data;
};

export const addPatientAccessLog = async (data: AddPatientAccessLogData): Promise<PatientAccessLog> => {
  const response = await api.post("/patient/access-logs/add-log/", data);
  return response.data;
};

export const getPatientMedicalHistory = async (patientUuid: string): Promise<PaginatedResponse<MedicalHistory>> => {
  const response = await api.get(`/patient/patients/${patientUuid}/medical-history/`);
  return response.data;
};

export const createMedicalHistory = async (
  patientUuid: string,
  data: CreateMedicalHistoryData
): Promise<MedicalHistory> => {
  const response = await api.post(`/patient/patients/${patientUuid}/medical-history/`, data);
  return response.data;
};

export const updateMedicalHistory = async (
  patientUuid: string,
  medicalHistoryUuid: string,
  data: CreateMedicalHistoryData
): Promise<MedicalHistory> => {
  const response = await api.patch(
    `/patient/patients/${patientUuid}/medical-history/${medicalHistoryUuid}/`,
    data
  );
  return response.data;
};

export const editMedicalHistoryCondition = async (
  patientUuid: string,
  medicalHistoryUuid: string,
  conditionUuid: string,
  data: MedicalHistoryConditionCreate
): Promise<MedicalHistory> => {
  const response = await api.patch(
    `/patient/patients/${patientUuid}/medical-history/${medicalHistoryUuid}/edit-condition/${conditionUuid}/`,
    data
  );
  return response.data;
};

export const deleteMedicalHistoryCondition = async (
  patientUuid: string,
  medicalHistoryUuid: string,
  conditionUuid: string
): Promise<void> => {
  await api.delete(
    `/patient/patients/${patientUuid}/medical-history/${medicalHistoryUuid}/remove-condition/${conditionUuid}/`
  );
};

export const getPatientConcomitantMedications = async (patientUuid: string): Promise<PaginatedResponse<ConcomitantMedication>> => {
  const response = await api.get(`/patient/concomitant-medications/?patient_uuid=${patientUuid}`);
  return response.data;
};

export const createConcomitantMedication = async (
  data: CreateConcomitantMedicationData
): Promise<ConcomitantMedication> => {
  const response = await api.post("/patient/concomitant-medications/", data);
  return response.data;
};

export const updateConcomitantMedication = async (
  uuid: string,
  data: Partial<CreateConcomitantMedicationData>
): Promise<ConcomitantMedication> => {
  const response = await api.patch(`/patient/concomitant-medications/${uuid}/`, data);
  return response.data;
};

export const deleteConcomitantMedication = async (uuid: string): Promise<void> => {
  await api.delete(`/patient/concomitant-medications/${uuid}/`);
};

/**
 * Search for patients using various criteria.
 * All parameters are optional and can be combined for more specific searches.
 * @param params Search parameters (nhs_number, first_name, last_name, full_name, date_of_birth, medical_record_number, gender, study)
 * @returns Array of matching patients
 */
export const searchPatients = async (params: PatientSearchParams): Promise<PaginatedResponse<Patient>> => {
  const response = await api.get("/patient/patients/search/", { params });
  return response.data;
};

export const updatePatientProfilePicture = async (
  patientUuid: string,
  profilePicture: File
): Promise<{ uuid: string; profile_picture: string; profile_picture_url: string }> => {
  const formData = new FormData();
  formData.append('profile_picture', profilePicture);
  
  const response = await api.patch(`/patient/patients/${patientUuid}/update-profile-picture/`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};
