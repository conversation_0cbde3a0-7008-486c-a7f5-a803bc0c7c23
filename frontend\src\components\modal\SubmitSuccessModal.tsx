import { useState } from "react";
import NurtifyInput from "../NurtifyInput";
import { usePatientStore } from "@/store/patientState";
import { useAllergyStore } from '@/store/AllergyState';
import {
  useCreatePatientMutation,
} from "@/hooks/patient.query";
import { useNavigate } from "react-router-dom";

type SubmitSuccessModalProps = {
  isOpen: boolean;
  setNextModel: (value: boolean) => void;
};

function SubmitSuccessModal({
  isOpen,
  setNextModel,
}: SubmitSuccessModalProps): JSX.Element | null {
  const patientState = usePatientStore();
  const {clearAllergies} = useAllergyStore();
  const createPatientMutation = useCreatePatientMutation();

  const [confirmNhs, setConfirmNhs] = useState("");
  const [confirmMrn, setConfirmMrn] = useState("");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const navigate = useNavigate();

  if (!isOpen) return null;

  const handleCancel = () => {   
    setNextModel(false);
    setError(null);
    setSuccess(false);
    setConfirmNhs("");
    setConfirmMrn("");
    clearAllergies();
  };

  const handleConfirm = () => {
    // Validate confirmation
    if (confirmNhs !== patientState.nhs_number) {
      setError("NHS number does not match");
      navigate("/patients");

      return;
    }

    if (confirmMrn !== patientState.medical_record_number) {
      setError("Medical record number does not match");
      return;
    }

    // Clear any previous errors
    setError(null);

    // Get patient data from store
    const patientData = patientState.getPatientData();

    // Call API to create patient
    createPatientMutation.mutate(patientData, {
      onSuccess: () => {
        setSuccess(true);
        // Navigate to patient details page
        //navigate(`/patient-board/patient-details/${confirmNhs}`);
        navigate("/patients");
        // Reset form after successful submission
        setTimeout(() => {
          setNextModel(false);
          patientState.resetForm();
          setSuccess(false);
          clearAllergies();
        }, 2000);
      },
      onError: (error) => {
        setError(`Failed to create patient: ${error.message}`);
      },
    });
  };

  return (
    <div className="add-patient-form-modal" style={{ backgroundColor: 'rgba(0, 0, 0, 0.5)' }}>
      <div className="add-patient-form" style={{ backgroundColor: 'white', boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)' }}>
        {success ? (
          <div style={{ textAlign: "center", marginTop: "50px" }}>
            <h2 style={{ color: "#37B7C3", marginBottom: "20px" }}>
              Patient Created Successfully!
            </h2>
            <p>The patient has been added to the database.</p>
          </div>
        ) : (
          <>
            <h2
              style={{
                marginTop: "27px",
                fontSize: "24px",
                fontWeight: "bold",
                textAlign: "center",
                lineHeight: "1.5",
                color: "#000000",
              }}
            >
              Confirm Medical Record Number
            </h2>
            {error && (
              <div
                style={{
                  color: "red",
                  textAlign: "center",
                  marginBottom: "10px",
                }}
              >
                {error}
              </div>
            )}
            <form
              style={{
                display: "flex",
                flexDirection: "column",
                marginBottom: "20px",
                marginLeft: "20px",
                gap: "10px",
              }}
            >
              <div
                style={{
                  width: "100%",
                  maxWidth: "100%",
                  padding: "0.8rem",
                  fontSize: "1rem",
                }}
              >
                <h6>Confirm your medical record number</h6>
                <NurtifyInput
                  type="text"
                  placeholder="Confirm your medical record number"
                  value={confirmMrn}
                  onChange={(e) => setConfirmMrn(e.target.value)}
                />
              </div>
              <div
                style={{
                  width: "100%",
                  maxWidth: "100%",
                  padding: "0.8rem",
                  fontSize: "1rem",
                }}
              >
                <h6>Confirm your NHS number</h6>

                <NurtifyInput
                  type="text"
                  placeholder="Confirm your NHS number"
                  value={confirmNhs}
                  onChange={(e) => setConfirmNhs(e.target.value)}
                />
              </div>
            </form>
            <div
              className="button-container"
              style={{
                paddingLeft: "0.8rem",
                paddingRight: "0.8rem",
              }}
            >
              <button
                type="button"
                className="cancel-button"
                onClick={handleCancel}
              >
                Cancel
              </button>
              <button
                type="button"
                className="submit-button"
                onClick={handleConfirm}
                disabled={createPatientMutation.isPending}
              >
                {createPatientMutation.isPending ? "Submitting..." : "Confirm"}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export default SubmitSuccessModal;
