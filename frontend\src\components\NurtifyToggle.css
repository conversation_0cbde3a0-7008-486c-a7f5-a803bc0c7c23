.nurtify-toggle-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nurtify-toggle-label {
  font-size: 14px;
  color: #6a7a99;
}

.nurtify-toggle {
  position: relative;
  display: inline-block;
  width: 242px;
  height: 65px;
  cursor: pointer;
}

.nurtify-toggle-slider {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #dff3f5;
  border-radius: 95px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.nurtify-toggle-slider.checked {
  background-color: #dff3f5;
}

.nurtify-toggle-circle {
  position: absolute;
  height: 65px;
  width: 130px;
  background-color: #37b7c3;
  border-radius: 95px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nurtify-toggle-slider.checked .nurtify-toggle-circle {
  transform: translateX(112px);
}

.nurtify-toggle-text {
  color: #0719526B;
  font-size: 17px;
  font-weight: 700;
  position: absolute;
  width: 100%;
  text-align: center;
  transition: color 0.3s ease;
}