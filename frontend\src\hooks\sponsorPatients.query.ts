import { useQuery } from "@tanstack/react-query";
import { getSponsorPatients } from "@/services/api/sponsorPatients.service";
import { SponsorPatientsFilters } from "@/services/api/types";

export const SPONSOR_PATIENTS_KEYS = {
  GET_ALL: "sponsor-patients",
  GET_FILTERED: (filters: SponsorPatientsFilters) => ["sponsor-patients", filters],
};

export const useSponsorPatientsQuery = (filters?: SponsorPatientsFilters) => {
  return useQuery({
    queryKey: filters ? SPONSOR_PATIENTS_KEYS.GET_FILTERED(filters) : [SPONSOR_PATIENTS_KEYS.GET_ALL],
    queryFn: () => getSponsorPatients(filters),
    refetchOnWindowFocus: false,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}; 