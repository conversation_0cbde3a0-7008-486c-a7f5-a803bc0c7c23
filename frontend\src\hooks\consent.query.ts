import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import {
  createConsentForm,
  getConsentForms,
  getConsentFormByUuid,
  updateConsentForm,
  duplicateConsentForm,
  deleteConsentForm,
  getConsentQuestions,
  createPatientConsent,
  getPatientConsents,
  getPatientConsentByUuid,
  getPatientConsentsByPatient,
  getPatientConsentData,
  getStudyConsent,
  attachConsentToStudy,
  startPatientConsent,
  submitPatientConsent,
  getSponsorStudiesPatientsConsent,
  getSponsorPatientConsent,
  getConsentFormsByPatientUuid,
  submitConsentByUuid,
} from "@/services/api/consent.service";
import {
  ConsentFormCreate,
  ConsentFormUpdate,
  PatientConsentCreate,
  PatientConsentAnswerCreate,
} from "@/types/types";

// Query Keys
export const CONSENT_KEYS = {
  GET_ALL: "consent-forms",
  GET_BY_UUID: "consent-form",
  GET_QUESTIONS: "consent-questions",
  GET_PATIENT_CONSENTS: "patient-consents",
  GET_PATIENT_CONSENT: "patient-consent",
  GET_PATIENT_CONSENTS_BY_PATIENT: "patient-consents-by-patient",
  GET_PATIENT_CONSENT_DATA: "patient-consent-data",
  GET_STUDY_CONSENT: "study-consent",
};

// Consent Forms Queries
export const useConsentFormsQuery = (params?: {
  sponsor_id?: string;
  study_id?: string;
  is_active?: boolean;
  page?: number;
  page_size?: number;
}) => {
  return useQuery({
    queryKey: [CONSENT_KEYS.GET_ALL, params],
    queryFn: () => getConsentForms(params),
    enabled: !!params?.sponsor_id || !!params?.study_id,
  });
};

export const useConsentFormByUuidQuery = (uuid: string) => {
  return useQuery({
    queryKey: [CONSENT_KEYS.GET_BY_UUID, uuid],
    queryFn: () => getConsentFormByUuid(uuid),
    enabled: !!uuid,
  });
};

export const useConsentQuestionsQuery = (consent_form_id: string) => {
  return useQuery({
    queryKey: [CONSENT_KEYS.GET_QUESTIONS, consent_form_id],
    queryFn: () => getConsentQuestions(consent_form_id),
    enabled: !!consent_form_id,
  });
};

// Patient Consents Queries
export const usePatientConsentsQuery = (params?: {
  patient_id?: string;
  consent_form_id?: string;
  consent_status?: 'Consented' | 'Not Consented';
  page?: number;
  page_size?: number;
}) => {
  return useQuery({
    queryKey: [CONSENT_KEYS.GET_PATIENT_CONSENTS, params],
    queryFn: () => getPatientConsents(params),
    enabled: !!params?.patient_id || !!params?.consent_form_id,
  });
};

export const usePatientConsentByUuidQuery = (uuid: string) => {
  return useQuery({
    queryKey: [CONSENT_KEYS.GET_PATIENT_CONSENT, uuid],
    queryFn: () => getPatientConsentByUuid(uuid),
    enabled: !!uuid,
  });
};

// Query for getting patient consents by patient identifier (old endpoint)
export const usePatientConsentsByPatientQuery = (patientIdentifier: string) => {
  return useQuery({
    queryKey: [CONSENT_KEYS.GET_PATIENT_CONSENTS_BY_PATIENT, patientIdentifier],
    queryFn: () => getPatientConsentsByPatient(patientIdentifier),
    enabled: !!patientIdentifier,
  });
};

// Query for getting patient consent data (new endpoint)
export const usePatientConsentDataQuery = (patientIdentifier: string) => {
  return useQuery({
    queryKey: [CONSENT_KEYS.GET_PATIENT_CONSENT_DATA, patientIdentifier],
    queryFn: () => getPatientConsentData(patientIdentifier),
    enabled: !!patientIdentifier,
  });
};

// Study Consent Query
export const useStudyConsentQuery = (study_id: string) => {
  return useQuery({
    queryKey: [CONSENT_KEYS.GET_STUDY_CONSENT, study_id],
    queryFn: () => getStudyConsent(study_id),
    enabled: !!study_id,
  });
};

// Consent Forms Mutations
export const useCreateConsentFormMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: ConsentFormCreate) => createConsentForm(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CONSENT_KEYS.GET_ALL] });
    },
  });
};

export const useUpdateConsentFormMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: ConsentFormUpdate }) =>
      updateConsentForm(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({ queryKey: [CONSENT_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [CONSENT_KEYS.GET_BY_UUID, uuid] });
    },
  });
};

export const useDuplicateConsentFormMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ uuid, data }: { uuid: string; data: Partial<ConsentFormCreate> }) =>
      duplicateConsentForm(uuid, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CONSENT_KEYS.GET_ALL] });
    },
  });
};

export const useDeleteConsentFormMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (uuid: string) => deleteConsentForm(uuid),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CONSENT_KEYS.GET_ALL] });
    },
  });
};

// Patient Consent Mutations
export const useCreatePatientConsentMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: PatientConsentCreate) => createPatientConsent(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CONSENT_KEYS.GET_PATIENT_CONSENTS] });
    },
  });
};

// Study Consent Mutations
export const useAttachConsentToStudyMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ study_id, consent_form_id }: { study_id: string; consent_form_id: string }) =>
      attachConsentToStudy(study_id, consent_form_id),
    onSuccess: (_, { study_id }) => {
      queryClient.invalidateQueries({ queryKey: [CONSENT_KEYS.GET_STUDY_CONSENT, study_id] });
    },
  });
};

// Patient Consent Flow Mutations
export const useStartPatientConsentMutation = () => {
  return useMutation({
    mutationFn: ({ patient_id, consent_form_id }: { patient_id: string; consent_form_id: string }) =>
      startPatientConsent(patient_id, consent_form_id),
  });
};

export const useSubmitPatientConsentMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      patient_id,
      consent_form_id,
      answers,
    }: {
      patient_id: string;
      consent_form_id: string;
      answers: PatientConsentAnswerCreate[];
    }) => submitPatientConsent(patient_id, consent_form_id, answers),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CONSENT_KEYS.GET_PATIENT_CONSENTS] });
    },
  });
};

// Mutation for submitting consent by UUID (for editing existing consent)
export const useSubmitConsentByUuidMutation = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      patientUuid,
      consent_form_id,
      answers,
    }: {
      patientUuid: string;
      consent_form_id: string;
      answers: Array<{
        question_id: string;
        answer: boolean;
      }>;
    }) => submitConsentByUuid(patientUuid, { consent_form_id, answers }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [CONSENT_KEYS.GET_PATIENT_CONSENTS] });
    },
  });
};

// Sponsor-specific consent queries
export const useSponsorStudiesPatientsConsentQuery = (params?: {
  study?: string;
  patient_search?: string;
  consent_status?: string;
  include_questions?: boolean;
}) => {
  return useQuery({
    queryKey: [CONSENT_KEYS.GET_ALL, 'sponsor-studies-patients', params],
    queryFn: () => getSponsorStudiesPatientsConsent(params),
  });
};

export const useSponsorPatientConsentQuery = (patientUuid: string) => {
  return useQuery({
    queryKey: [CONSENT_KEYS.GET_PATIENT_CONSENT, 'sponsor-patient', patientUuid],
    queryFn: () => getSponsorPatientConsent(patientUuid),
    enabled: !!patientUuid,
  });
};

// Query for getting consent forms by patient UUID (General endpoint)
export const useConsentFormsByPatientUuidQuery = (patientUuid: string, params?: {
  study?: string;
  consent_status?: string;
  include_questions?: boolean;
}) => {
  return useQuery({
    queryKey: [CONSENT_KEYS.GET_PATIENT_CONSENTS, 'by-patient-uuid', patientUuid, params],
    queryFn: () => getConsentFormsByPatientUuid(patientUuid, params),
    enabled: !!patientUuid,
  });
};