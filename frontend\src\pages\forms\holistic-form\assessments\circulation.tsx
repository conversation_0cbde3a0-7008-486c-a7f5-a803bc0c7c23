 
import { useState, useEffect } from "react";
import heartImage from "./static/images/added/heart.png";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyInput from "@/components/NurtifyInput";
import NurtifyRange from "@/components/NurtifyRange";
import NurtifyCheckBox from "@/components/NurtifyCheckBox";
import NurtifyText from "@/components/NurtifyText";

const Circulation = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  const [abdomenCirculation, setAbdomenCirculation] = useState<string[]>(
    assessment?.circulation?.abdomen || []
  );
  const [sourceOfBleedingCirculation, setSourceOfBleedingCirculation] = useState<string[]>(
    assessment?.circulation?.bleeding || []
  );
  const [urineOutputCirculation, setUrineOutputCirculation] = useState<string[]>(
    assessment?.circulation?.urineOutput || []
  );

  const setCirculation = (circulationData: any) => {
    setAssessment({
      ...assessment,
      circulation: {
        ...assessment.circulation,
        ...circulationData
      }
    });
  };

  // Update the state when the local state changes
  useEffect(() => {
    setCirculation({
      abdomen: abdomenCirculation,
      bleeding: sourceOfBleedingCirculation,
      urineOutput: urineOutputCirculation
    });
  }, [abdomenCirculation, sourceOfBleedingCirculation, urineOutputCirculation]);

  const handleCheckboxChange = (stateUpdater: React.Dispatch<React.SetStateAction<string[]>>) => (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    stateUpdater((prev) =>
      event.target.checked ? [...prev, value] : prev.filter((item) => item !== value)
    );
  };

  return (
    <div className="block align-items-*-start flex-column flex-md-row">
      <div className="mt-5 mb-3" id="division-25">
        {/* Circulation Section Title  */}
        <div className="inlineBlock mb-4 headinqQuestion">
          <img
            src={heartImage}
            className="imageEtiquette"
            alt="patient face image round"
          />
          <span className="mb-2 py-2 fs-2 text-start etiquetteHeadingForms">
            Circulation
          </span>
        </div>
        {/* Measurements */}
        <div className="list-group me-4 mt-3 col-xl-6 col-lg-8 col-md-12">
          {/* HR */}
          <div className="d-flex align-items-center my-2">
            <NurtifyText label="Heart Rate (bpm):" className="me-2" />
            <div style={{ width: "100px" }}>
              <NurtifyInput
                type="number"
                placeholder="HR"
                value={assessment?.circulation?.heartRate}
                onChange={(e) => {
                  setCirculation({ heartRate: parseInt(e.target.value) || 0 });
                }}
                min={20}
                max={260}
                step={1}
              />
            </div>
            <NurtifyText label="bpm." className="ms-2" />
          </div>

          {/* Blood Pressure Systolic / Diastolic  */}
          <div className="d-flex align-items-center my-2">
            <NurtifyText label="Blood Pressure (Sys/Dia):" className="me-2" />
            <div style={{ width: "80px" }}>
              <NurtifyInput
                type="number"
                placeholder="BP Sys"
                value={assessment?.circulation?.bloodPressureSystolic || ""}
                onChange={(e) => {
                  const systolic = parseInt(e.target.value) || 0;
                  const diastolic = assessment?.circulation?.bloodPressureDiastolic || 0;
                  setCirculation({ 
                    bloodPressureSystolic: systolic,
                    bloodPressure: `${systolic}/${diastolic}`
                  });
                }}
                min={0}
                max={2000}
                step={1}
              />
            </div>
            <NurtifyText label="/" className="mx-2" />
            <div style={{ width: "80px" }}>
              <NurtifyInput
                type="number"
                placeholder="BP Dia"
                value={assessment?.circulation?.bloodPressureDiastolic || ""}
                onChange={(e) => {
                  const diastolic = parseInt(e.target.value) || 0;
                  const systolic = assessment?.circulation?.bloodPressureSystolic || 0;
                  setCirculation({ 
                    bloodPressureDiastolic: diastolic,
                    bloodPressure: `${systolic}/${diastolic}`
                  });
                }}
                min={0}
                max={350}
                step={1}
              />
            </div>
            <NurtifyText label="mmHg." className="ms-2" />
          </div>

          {/*CRT */}
          <div className="d-flex align-items-center my-2">
            <NurtifyText label="CRT (secs):" className="me-2" />
            <div style={{ width: "300px" }}>
              <NurtifyRange
                min={0}
                max={10}
                step={1}
                value={assessment?.circulation?.CRT || 0}
                onChange={(e) => {
                  const crtValue = parseInt(e.target.value);
                  setCirculation({ 
                    CRT: crtValue,
                    capillaryRefill: `${crtValue} seconds`
                  });
                }}
              />
            </div>
          </div>
        </div>
        {/* Circulation MCQ */}
        <div className="d-flex flex-wrap align-items-*-start flex-column flex-md-row mt-4">
          {/* Abdomen */}
          <div className="list-group me-4 mt-3">
            <NurtifyText label="Abdomen" className="mb-2 fw-bold" />
            <NurtifyCheckBox
              label="Soft, Non Tender"
              name="abdomen"
              value="Soft, Non Tender"
              checked={abdomenCirculation.includes("Soft, Non Tender")}
              onChange={handleCheckboxChange(setAbdomenCirculation)}
            />
            <NurtifyCheckBox
              label="Tender"
              name="abdomen"
              value="Tender"
              checked={abdomenCirculation.includes("Tender")}
              onChange={handleCheckboxChange(setAbdomenCirculation)}
            />
            <NurtifyCheckBox
              label="Distended"
              name="abdomen"
              value="Distended"
              checked={abdomenCirculation.includes("Distended")}
              onChange={handleCheckboxChange(setAbdomenCirculation)}
            />
            <NurtifyCheckBox
              label="Other Abdomen Condition"
              name="abdomen"
              value="Other Abdomen Condition"
              checked={abdomenCirculation.includes("Other Abdomen Condition")}
              onChange={handleCheckboxChange(setAbdomenCirculation)}
            />
          </div>
          <div className="list-group me-4 mt-3">
            <NurtifyText label="Source of Bleeding" className="mb-2 fw-bold" />
            <NurtifyCheckBox
              label="Nil"
              name="bleeding"
              value="No Bleeding"
              checked={sourceOfBleedingCirculation.includes("No Bleeding")}
              onChange={handleCheckboxChange(setSourceOfBleedingCirculation)}
            />
            <NurtifyCheckBox
              label="Yes"
              name="bleeding"
              value="Bleeding (Yes)"
              checked={sourceOfBleedingCirculation.includes("Bleeding (Yes)")}
              onChange={handleCheckboxChange(setSourceOfBleedingCirculation)}
            />
          </div>
          <div className="list-group me-4 mt-3">
            <NurtifyText label="Urine Output" className="mb-2 fw-bold" />
            <NurtifyCheckBox
              label="Normal Urine Output"
              name="urineOutput"
              value="Normal Urine Output"
              checked={urineOutputCirculation.includes("Normal Urine Output")}
              onChange={handleCheckboxChange(setUrineOutputCirculation)}
            />
            <NurtifyCheckBox
              label="Frequent Urination"
              name="urineOutput"
              value="Frequent Urination"
              checked={urineOutputCirculation.includes("Frequent Urination")}
              onChange={handleCheckboxChange(setUrineOutputCirculation)}
            />
            <NurtifyCheckBox
              label="Low Urine Output"
              name="urineOutput"
              value="Low Urine Output"
              checked={urineOutputCirculation.includes("Low Urine Output")}
              onChange={handleCheckboxChange(setUrineOutputCirculation)}
            />
            <NurtifyCheckBox
              label="Hematuria"
              name="urineOutput"
              value="Hematuria"
              checked={urineOutputCirculation.includes("Hematuria")}
              onChange={handleCheckboxChange(setUrineOutputCirculation)}
            />
            <NurtifyCheckBox
              label="Anuric"
              name="urineOutput"
              value="Anuric"
              checked={urineOutputCirculation.includes("Anuric")}
              onChange={handleCheckboxChange(setUrineOutputCirculation)}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Circulation;
