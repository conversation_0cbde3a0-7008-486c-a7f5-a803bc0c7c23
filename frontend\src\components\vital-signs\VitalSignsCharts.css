.vital-signs-charts {
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  padding: 24px;
  margin-top: 24px;
}

.charts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.charts-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.charts-controls {
  display: flex;
  gap: 16px;
  align-items: center;
  flex-wrap: wrap;
}

.chart-type-selector,
.vital-sign-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-type-selector label,
.vital-sign-selector label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.chart-type-selector select,
.vital-sign-selector select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #374151;
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-type-selector select:focus,
.vital-sign-selector select:focus {
  outline: none;
  border-color: #37B7C3;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

.chart-container {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.charts-summary {
  border-top: 1px solid #e5e7eb;
  padding-top: 20px;
}

.charts-summary h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.summary-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.summary-value {
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

/* Custom tooltip styles */
.custom-tooltip {
  background: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tooltip-label {
  font-weight: 600;
  margin: 0 0 8px 0;
  font-size: 14px;
}

.custom-tooltip p {
  margin: 4px 0;
  font-size: 13px;
}

/* Loading and error states */
.charts-loading,
.no-chart-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.charts-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #37B7C3;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.charts-loading p,
.no-chart-data p {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* Recharts customization */
.recharts-cartesian-grid-horizontal line,
.recharts-cartesian-grid-vertical line {
  stroke: #e5e7eb;
  stroke-width: 1;
}

.recharts-cartesian-axis-tick-value {
  font-size: 12px;
  fill: #666;
}

.recharts-legend-item-text {
  font-size: 13px;
  color: #374151;
}

.recharts-tooltip-wrapper {
  outline: none;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .vital-signs-charts {
    padding: 16px;
  }
  
  .charts-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .charts-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .chart-type-selector,
  .vital-sign-selector {
    flex: 1;
  }
  
  .chart-type-selector select,
  .vital-sign-selector select {
    width: 100%;
  }
  
  .chart-container {
    padding: 16px;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .charts-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .chart-type-selector,
  .vital-sign-selector {
    flex: none;
  }
} 