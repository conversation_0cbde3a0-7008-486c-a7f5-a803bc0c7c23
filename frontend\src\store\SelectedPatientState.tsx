import { create } from 'zustand';

export interface Patient {
  uuid: string;
  nhs_number: string;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: string;
  email?: string;
  phone_number?: string;
  medical_record_number?: string;
  profile_picture?: string;
  profile_picture_url?: string;
  created_at: string;
  updated_at?: string;
  ethnic_background?:string;
}

interface SelectedPatientState {
  selectedPatient: Patient | null;
  isLoading: boolean;
  error: Error | null;
  setSelectedPatient: (patient: Patient | null) => void;
  clearSelectedPatient: () => void;
  setLoading: (isLoading: boolean) => void;
  setError: (error: Error | null) => void;
  updateProfilePicture: (profilePicture: string, profilePictureUrl: string) => void;
}

const useSelectedPatientStore = create<SelectedPatientState>((set) => ({
  selectedPatient: null,
  isLoading: false,
  error: null,
  setSelectedPatient: (patient) => set({ selectedPatient: patient, error: null }),
  clearSelectedPatient: () => set({ selectedPatient: null, error: null }),
  setLoading: (isLoading) => set({ isLoading }),
  setError: (error) => set({ error, isLoading: false }),
  updateProfilePicture: (profilePicture, profilePictureUrl) => set((state) => ({
    selectedPatient: state.selectedPatient ? {
      ...state.selectedPatient,
      profile_picture: profilePicture,
      profile_picture_url: profilePictureUrl
    } : null
  })),
}));

export default useSelectedPatientStore;
