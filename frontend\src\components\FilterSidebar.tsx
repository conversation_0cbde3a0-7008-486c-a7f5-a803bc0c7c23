import { FileSearch } from "lucide-react";

interface Category {
  id: string;
  label: string;
  count: number;
}

interface Study {
  id: string;
  label: string;
  count: number;
}

interface FilterSidebarProps {
  title: string;
  categories?: Category[];
  selectedCategories?: string[];
  onCategoryChange?: (categoryId: string) => void;
  studies?: Study[];
  selectedStudies?: string[];
  onStudyChange?: (studyId: string) => void;
}

export default function FilterSidebar({
  title,
  categories = [],
  selectedCategories = [],
  onCategoryChange = () => {},
  studies = [],
  selectedStudies = [],
  onStudyChange = () => {},
}: FilterSidebarProps) {
  return (
    <div className="sidebar -dashboard">
      <div className="sidebar__item">
        <div className="sidebar__header">
          <h5 className="sidebar__title">{title}</h5>

          {/* Search input */}
          <div className="sidebar__search">
            <div className="search-field">
              <input
                type="text"
                placeholder="Search..."
                className="search-input"
              />
              <FileSearch className="search-icon" />
            </div>
          </div>
        </div>

        {/* Categories */}
        {categories.length > 0 && (
          <div className="sidebar__categories">
            <h6 className="sidebar__subtitle">Categories</h6>
            <div className="sidebar-checkbox">
              {categories.map((category) => (
                <div className="form-checkbox" key={category.id}>
                  <input
                    type="checkbox"
                    id={category.id}
                    className="form-checkbox__input"
                    checked={selectedCategories.includes(category.id)}
                    onChange={() => onCategoryChange(category.id)}
                  />
                  <label
                    htmlFor={category.id}
                    className="form-checkbox__label"
                  >
                    {category.label}
                    <span className="form-checkbox__count">
                      {category.count}
                    </span>
                  </label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Studies */}
        {studies.length > 0 && (
          <div className="sidebar__categories">
            <h6 className="sidebar__subtitle">Studies</h6>
            <div className="sidebar-checkbox">
              {studies.map((study) => (
                <div className="form-checkbox" key={study.id}>
                  <input
                    type="checkbox"
                    id={study.id}
                    className="form-checkbox__input"
                    checked={selectedStudies.includes(study.id)}
                    onChange={() => onStudyChange(study.id)}
                  />
                  <label
                    htmlFor={study.id}
                    className="form-checkbox__label"
                  >
                    {study.label}
                    <span className="form-checkbox__count">
                      {study.count}
                    </span>
                  </label>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Popular Tags */}
        <div className="sidebar__tags">
          <h6 className="sidebar__subtitle">Popular Tags</h6>
          <div className="tags-wrapper">
            {["New", "Featured", "Popular", "Trending"].map((tag) => (
              <span key={tag} className="tag-item">
                {tag}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
