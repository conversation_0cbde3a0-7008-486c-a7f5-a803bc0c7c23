/* Sponsor Consents Page Styles */
.consents-container {
  padding: 24px;
  background-color: #f9fafb;
  min-height: 100vh;
}

.consents-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.consents-title h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 12px;
}

.consents-title p {
  margin: 0;
  color: #6b7280;
  font-size: 1.1rem;
  line-height: 1.6;
}

.create-consent-btn {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: transform 0.2s, box-shadow 0.2s;
  font-size: 14px;
}

.create-consent-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.create-consent-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Filters Section */
.consents-filters {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  align-items: center;
  flex-wrap: wrap;
}

.search-filter {
  flex: 1;
  min-width: 300px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #9ca3af;
  z-index: 1;
}

.search-input {
  padding-left: 40px !important;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.search-input:focus {
  border-color: #37b7c3;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

.study-filter {
  min-width: 200px;
}

.study-select {
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.study-select:focus {
  border-color: #37b7c3;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

/* Table Container */
.consents-table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.consents-table {
  width: 100%;
}

/* Table Cell Styles */
.consent-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  color: #1f2937;
}

.consent-icon {
  color: #37b7c3;
}

.version-badge {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.questions-count {
  color: #6b7280;
  font-size: 13px;
  font-weight: 500;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.status-badge.active {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-badge.inactive {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.date-cell {
  color: #6b7280;
  font-size: 13px;
}

/* Alert Messages */
.alert {
  padding: 16px 20px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
}

.alert-danger {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
}

.alert-success {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  color: #16a34a;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .consents-header {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }

  .consents-title h1 {
    font-size: 1.75rem;
  }

  .create-consent-btn {
    align-self: flex-start;
  }
}

@media (max-width: 768px) {
  .consents-container {
    padding: 16px;
  }

  .consents-header {
    padding: 20px;
    margin-bottom: 24px;
  }

  .consents-title h1 {
    font-size: 1.5rem;
  }

  .consents-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-filter {
    min-width: auto;
  }

  .study-filter {
    min-width: auto;
  }

  .create-consent-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .consents-container {
    padding: 12px;
  }

  .consents-header {
    padding: 16px;
  }

  .consents-title h1 {
    font-size: 1.25rem;
  }

  .consents-filters {
    padding: 16px;
  }
}