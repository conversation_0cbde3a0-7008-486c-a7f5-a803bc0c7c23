import { useState } from 'react';

interface NurtifyAttachFileBoxProps {
    onChange: (file: File | null) => void;
}

export default function NurtifyAttachFileBox({ onChange }: NurtifyAttachFileBoxProps) {
    const [hasFile, setHasFile] = useState(false);

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0] || null;
        console.log("File in NurtifyAttachFileBox:", file);
        setHasFile(!!file);
        onChange(file);
    };

    return (
        <div style={{
            margin: '15px 0',
            position: 'relative',
            width: '100%'
        }}>
            <label
                htmlFor="file-upload"
                style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '10px',
                    padding: '12px 20px',
                    backgroundColor: hasFile ? '#DFF3F5' : '#DFF3F5',
                    border: `1px solid ${hasFile ? '#37B7C3' : '#ddd'}`,
                    borderRadius: '8px',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    fontSize: '14px',
                    color: hasFile ? '#000000' : '#000000',
                    width: '100%'
                }}
            >
                <svg
                    width="30"
                    height="30"
                    viewBox="0 0 30 30"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <g clipPath="url(#clip0_1_2)">
                        <path
                            d="M3.05899 2.44566C3.14938 2.44548 3.23976 2.44526 3.33015 2.44498C3.57757 2.44438 3.82499 2.44451 4.07241 2.44476C4.33962 2.4449 4.60683 2.44437 4.87405 2.44394C5.39717 2.44319 5.92029 2.44308 6.44342 2.44322C6.86869 2.44333 7.29397 2.44322 7.71924 2.44297C7.81008 2.44292 7.81008 2.44292 7.90276 2.44287C8.0258 2.4428 8.14883 2.44273 8.27187 2.44265C9.42532 2.44202 10.5788 2.44214 11.7322 2.4425C12.787 2.4428 13.8417 2.44214 14.8965 2.44103C15.9801 2.43989 17.0637 2.43941 18.1472 2.43963C18.7554 2.43974 19.3635 2.4396 19.9716 2.43877C20.4893 2.43807 21.007 2.43799 21.5247 2.4387C21.7887 2.43904 22.0527 2.43911 22.3167 2.43841C22.5586 2.43777 22.8006 2.43795 23.0425 2.43877C23.1298 2.43893 23.217 2.43879 23.3043 2.43832C24.1615 2.43402 24.9128 2.58278 25.5647 3.17253C26.0331 3.6851 26.2791 4.27634 26.2711 4.96588C26.2713 5.01466 26.2714 5.06344 26.2716 5.11371C26.2719 5.27565 26.2709 5.43755 26.27 5.59949C26.2699 5.71618 26.2699 5.83287 26.27 5.94955C26.2701 6.26524 26.2691 6.58092 26.2678 6.89661C26.2667 7.22693 26.2666 7.55725 26.2664 7.88757C26.2659 8.51259 26.2644 9.1376 26.2627 9.76262C26.2607 10.4744 26.2597 11.1862 26.2588 11.8979C26.257 13.3616 26.2539 14.8253 26.25 16.2891C25.8246 16.2891 25.3992 16.2891 24.9609 16.2891C24.9604 16.0735 24.9604 16.0735 24.9599 15.8536C24.9566 14.5008 24.9523 13.148 24.9471 11.7953C24.9445 11.0997 24.9422 10.4042 24.9406 9.70872C24.9393 9.10252 24.9373 8.49632 24.9346 7.89012C24.9332 7.56914 24.9321 7.24816 24.9318 6.92717C24.9314 6.625 24.9303 6.32284 24.9285 6.02067C24.9279 5.90983 24.9277 5.79898 24.9278 5.68813C24.9279 5.53661 24.927 5.38514 24.9257 5.23363C24.926 5.18991 24.9263 5.14619 24.9266 5.10115C24.9215 4.7267 24.8334 4.46295 24.5911 4.1748C24.2274 3.87035 23.8844 3.80142 23.4216 3.80089C23.3098 3.80055 23.3098 3.80055 23.1958 3.80021C23.1132 3.80026 23.0306 3.80031 22.9481 3.80036C22.8603 3.80021 22.7725 3.80004 22.6847 3.79984C22.4433 3.79936 22.2019 3.79926 21.9605 3.79922C21.7002 3.79911 21.4399 3.79866 21.1796 3.79827C20.6104 3.79747 20.0411 3.79712 19.4719 3.79686C19.1164 3.7967 18.761 3.79645 18.4055 3.79619C17.4213 3.79547 16.4371 3.79487 15.4528 3.79467C15.3898 3.79466 15.3269 3.79465 15.262 3.79463C15.1672 3.79461 15.1672 3.79461 15.0706 3.79459C14.9427 3.79457 14.8147 3.79454 14.6868 3.79452C14.6233 3.7945 14.5599 3.79449 14.4945 3.79448C13.4662 3.79425 12.4379 3.79322 11.4096 3.79186C10.3538 3.79047 9.29795 3.78974 8.24212 3.78967C7.64935 3.78962 7.05659 3.78928 6.46382 3.78822C5.95905 3.78731 5.45428 3.78701 4.94951 3.7875C4.69204 3.78773 4.43458 3.78767 4.17712 3.78685C3.9412 3.7861 3.7053 3.78617 3.46938 3.78688C3.38425 3.787 3.29912 3.78682 3.21399 3.78631C2.67305 3.78326 2.18197 3.8042 1.70654 4.08325C1.39599 4.4212 1.31292 4.76446 1.31947 5.21323C1.31927 5.26411 1.31907 5.315 1.31887 5.36743C1.31848 5.53685 1.31977 5.70621 1.32105 5.87562C1.32112 5.9975 1.32108 6.11937 1.32095 6.24124C1.32088 6.57135 1.32226 6.90143 1.32389 7.23153C1.32536 7.57679 1.3255 7.92204 1.32577 8.2673C1.3265 8.92075 1.32843 9.5742 1.33078 10.2276C1.3334 10.9717 1.33469 11.7158 1.33586 12.4599C1.33831 13.9902 1.34243 15.5205 1.34766 17.0508C1.39585 17.0025 1.44403 16.9541 1.49368 16.9044C1.94705 16.4498 2.40057 15.9954 2.85424 15.5412C3.08749 15.3076 3.32069 15.074 3.55376 14.8403C3.77855 14.6148 4.00347 14.3896 4.22849 14.1644C4.31447 14.0783 4.40039 13.9922 4.48627 13.906C4.60623 13.7856 4.72636 13.6654 4.84655 13.5452C4.88229 13.5093 4.91803 13.4733 4.95485 13.4363C5.17603 13.2157 5.29178 13.115 5.60303 13.0957C5.65092 13.0915 5.6988 13.0872 5.74814 13.0829C6.18687 13.1917 6.55714 13.6976 6.86829 14.011C6.93362 14.0765 6.93362 14.0765 7.00027 14.1432C7.13769 14.281 7.2749 14.4189 7.41211 14.5569C7.50594 14.651 7.59978 14.7451 7.69364 14.8392C7.92222 15.0684 8.15063 15.2978 8.37891 15.5273C8.59169 15.4375 8.72691 15.3107 8.88903 15.1477C8.92928 15.1075 8.92928 15.1075 8.97034 15.0665C9.06004 14.9767 9.14931 14.8865 9.23859 14.7963C9.30287 14.7318 9.36718 14.6673 9.43151 14.6028C9.56993 14.4641 9.70816 14.3251 9.84625 14.186C10.0646 13.9661 10.2833 13.7466 10.5022 13.5272C10.9667 13.0615 11.4306 12.5952 11.8945 12.1289C12.3954 11.6255 12.8963 11.1222 13.3977 10.6194C13.6153 10.4013 13.8326 10.1829 14.0496 9.9642C14.1847 9.82829 14.32 9.69263 14.4553 9.55703C14.518 9.49406 14.5807 9.431 14.6432 9.36782C14.7286 9.28164 14.8142 9.19583 14.9 9.1101C14.9479 9.06196 14.9958 9.01383 15.0451 8.96424C15.2656 8.76756 15.4533 8.6916 15.7553 8.7017C16.239 8.81424 16.7029 9.48765 17.0517 9.83708C17.1216 9.90691 17.1915 9.97672 17.2615 10.0465C17.4501 10.2349 17.6385 10.4234 17.8268 10.6119C18.0242 10.8094 18.2217 11.0068 18.4192 11.2042C18.7503 11.5352 19.0812 11.8663 19.4121 12.1975C19.7951 12.5808 20.1783 12.9639 20.5616 13.3469C20.8909 13.6759 21.2201 14.005 21.5492 14.3342C21.7457 14.5308 21.9422 14.7274 22.1388 14.9238C22.3232 15.1081 22.5075 15.2925 22.6917 15.477C22.7595 15.5448 22.8273 15.6127 22.8952 15.6804C22.9875 15.7725 23.0795 15.8647 23.1715 15.957C23.2232 16.0087 23.2748 16.0604 23.3281 16.1137C23.4375 16.2305 23.4375 16.2305 23.4375 16.2891C23.3807 16.2894 23.324 16.2897 23.2655 16.2901C22.7268 16.2935 22.1882 16.2977 21.6495 16.3029C21.3727 16.3055 21.0959 16.3078 20.819 16.3094C20.5512 16.3109 20.2833 16.3133 20.0155 16.3162C19.914 16.3172 19.8125 16.3178 19.711 16.3182C18.0346 16.3249 16.6116 16.8173 15.3886 18.0059C14.5977 18.842 14.154 19.8194 13.9314 20.939C13.8867 21.1523 13.8867 21.1523 13.8281 21.2695C12.3047 21.275 10.7813 21.2791 9.25783 21.2817C8.55044 21.2829 7.84304 21.2845 7.13565 21.2872C6.51891 21.2896 5.90218 21.2911 5.28543 21.2916C4.95903 21.2919 4.63264 21.2926 4.30624 21.2943C3.99863 21.2959 3.69105 21.2964 3.38344 21.296C3.27092 21.2961 3.15839 21.2966 3.04588 21.2975C2.1423 21.3044 1.38412 21.2065 0.693971 20.5664C0.292287 20.1467 -0.00647211 19.6106 -0.007863 19.0188C-0.0081242 18.9671 -0.00838536 18.9154 -0.00865448 18.8621C-0.00864367 18.8055 -0.00863297 18.749 -0.00862184 18.6907C-0.00883727 18.6304 -0.00905278 18.5701 -0.00927474 18.508C-0.00992166 18.3054 -0.0101466 18.1028 -0.0103697 17.9001C-0.0107368 17.7551 -0.0111263 17.6101 -0.0115365 17.465C-0.0126632 17.0293 -0.0132842 16.5936 -0.0138057 16.1579C-0.0140627 15.9525 -0.014377 15.7472 -0.0146839 15.5418C-0.0156806 14.8589 -0.0165299 14.1759 -0.0169543 13.493C-0.0170662 13.3158 -0.0171788 13.1386 -0.0172934 12.9615C-0.0173218 12.9174 -0.0173501 12.8734 -0.0173793 12.828C-0.0178653 12.1152 -0.0193495 11.4024 -0.0212533 10.6896C-0.0231932 9.95719 -0.0242464 9.22482 -0.0244335 8.49244C-0.0245601 8.08145 -0.0250698 7.67047 -0.0265698 7.25949C-0.0279785 6.87268 -0.0281819 6.48587 -0.0275801 6.09906C-0.0275511 5.95731 -0.027921 5.81557 -0.0287259 5.67383C-0.0297657 5.47982 -0.0293469 5.28585 -0.0285775 5.09184C-0.0292202 5.0362 -0.029863 4.98055 -0.0305252 4.92322C-0.0243344 4.27338 0.220943 3.71128 0.644533 3.22265C1.36275 2.54836 2.10307 2.443 3.05899 2.44566Z"
                            fill="black"
                            fillOpacity="0.45"
                        />
                        <path
                            d="M19.8822 17.4382C19.9673 17.438 20.0524 17.4376 20.1375 17.4373C20.3653 17.4365 20.5931 17.4368 20.8209 17.4373C21.0605 17.4378 21.3001 17.4374 21.5398 17.4371C21.9412 17.4368 22.3426 17.4372 22.744 17.438C23.2077 17.439 23.6713 17.4387 24.135 17.4377C24.5346 17.4369 24.9342 17.4368 25.3338 17.4372C25.5718 17.4375 25.8099 17.4376 26.0479 17.437C26.2712 17.4365 26.4943 17.4368 26.7176 17.4378C26.7994 17.4381 26.8812 17.438 26.963 17.4376C27.0747 17.4371 27.1864 17.4378 27.2981 17.4386C27.3918 17.4387 27.3918 17.4387 27.4874 17.4388C27.7223 17.4696 27.8699 17.5753 28.0538 17.7177C28.1786 17.8838 28.1518 18.0547 28.1463 18.2549C28.1167 18.4544 28.0379 18.5571 27.8906 18.6914C27.7353 18.769 27.6181 18.7578 27.4442 18.7585C27.3741 18.7589 27.304 18.7593 27.2319 18.7597C27.1546 18.7599 27.0773 18.76 26.9977 18.7602C26.9158 18.7605 26.8339 18.7609 26.752 18.7614C26.5752 18.7622 26.3985 18.7629 26.2217 18.7635C25.9417 18.7644 25.6616 18.7658 25.3815 18.7673C24.585 18.7714 23.7884 18.7749 22.9919 18.7779C22.5522 18.7796 22.1125 18.7817 21.6728 18.7842C21.3951 18.7857 21.1174 18.7868 20.8396 18.7875C20.6657 18.788 20.4917 18.789 20.3177 18.7902C20.2001 18.7908 20.0825 18.7909 19.9649 18.7909C18.942 18.799 18.0004 18.9751 17.2215 19.6811C16.5629 20.3624 16.3182 21.179 16.333 22.1066C16.3822 22.838 16.7111 23.5592 17.2266 24.082C17.8276 24.575 18.4823 24.9077 19.2729 24.911C19.3773 24.9116 19.3773 24.9116 19.4837 24.9122C19.5596 24.9124 19.6354 24.9126 19.7135 24.9127C19.7946 24.9131 19.8757 24.9135 19.9567 24.9139C20.2224 24.9151 20.4881 24.9157 20.7539 24.9163C20.8456 24.9165 20.9373 24.9168 21.029 24.917C21.46 24.9181 21.891 24.919 22.322 24.9195C22.8184 24.92 23.3148 24.9216 23.8112 24.9239C24.1956 24.9257 24.58 24.9266 24.9644 24.9268C25.1937 24.9269 25.423 24.9274 25.6522 24.9289C25.8681 24.9303 26.084 24.9305 26.2999 24.9299C26.4161 24.9299 26.5323 24.931 26.6484 24.9322C27.2601 24.9286 27.7435 24.804 28.1978 24.3791C28.5976 23.9082 28.692 23.3922 28.6523 22.793C28.5807 22.3767 28.3746 22.0255 28.0664 21.7383C27.6594 21.4842 27.2853 21.3211 26.805 21.3203C26.7391 21.32 26.6733 21.3198 26.6054 21.3195C26.5334 21.3196 26.4613 21.3196 26.3871 21.3196C26.2731 21.3193 26.2731 21.3193 26.1567 21.319C25.9482 21.3185 25.7397 21.3184 25.5313 21.3183C25.401 21.3183 25.2706 21.3182 25.1403 21.318C24.6856 21.3175 24.2308 21.3172 23.776 21.3173C23.3524 21.3173 22.9287 21.3167 22.5051 21.3158C22.1411 21.315 21.7772 21.3147 21.4132 21.3147C21.196 21.3147 20.9787 21.3146 20.7614 21.3139C20.519 21.3133 20.2767 21.3135 20.0343 21.3139C19.9625 21.3136 19.8907 21.3132 19.8168 21.3129C19.7505 21.3131 19.6843 21.3134 19.6161 21.3136C19.5588 21.3136 19.5016 21.3136 19.4426 21.3135C19.253 21.3303 19.1004 21.3696 18.9258 21.4453C18.8531 21.6633 18.8275 21.8638 18.8672 22.0898C19.0251 22.296 19.1416 22.3961 19.3945 22.4414C19.4855 22.4466 19.5766 22.449 19.6677 22.4491C19.7223 22.4493 19.7769 22.4496 19.8332 22.4498C19.9225 22.4498 19.9225 22.4498 20.0135 22.4497C20.108 22.45 20.108 22.45 20.2044 22.4503C20.3412 22.4508 20.478 22.4511 20.6149 22.4513C20.8313 22.4517 21.0477 22.4526 21.2642 22.4537C21.8796 22.4568 22.495 22.4596 23.1104 22.4605C23.4868 22.4611 23.8632 22.4628 24.2396 22.4653C24.3831 22.466 24.5266 22.4663 24.6701 22.4662C24.8707 22.466 25.0713 22.4673 25.2719 22.4689C25.361 22.4684 25.361 22.4684 25.4518 22.4678C25.7095 22.4712 25.8572 22.4747 26.0804 22.6118C26.2771 22.829 26.2729 23.0074 26.2708 23.2919C26.25 23.4375 26.25 23.4375 26.1145 23.6169C25.9068 23.7667 25.7598 23.7964 25.5105 23.7973C25.4549 23.7976 25.3993 23.798 25.342 23.7983C25.2506 23.7984 25.2506 23.7984 25.1574 23.7985C25.0932 23.7988 25.0289 23.7991 24.9628 23.7994C24.7497 23.8002 24.5366 23.8006 24.3235 23.801C24.1754 23.8013 24.0273 23.8016 23.8792 23.802C23.608 23.8025 23.3368 23.8029 23.0656 23.8031C22.6675 23.8033 22.2695 23.8043 21.8714 23.806C21.5259 23.8075 21.1804 23.8079 20.8348 23.808C20.6883 23.8082 20.5417 23.8087 20.3952 23.8094C20.1896 23.8104 19.9841 23.8103 19.7785 23.8099C19.7184 23.8105 19.6583 23.811 19.5964 23.8116C18.9237 23.8077 18.4269 23.6365 17.9459 23.1622C17.5769 22.7586 17.4396 22.3079 17.4486 21.7651C17.4945 21.2298 17.7188 20.7408 18.127 20.3879C18.6741 19.9854 19.1252 19.9631 19.7899 19.9644C19.8714 19.9641 19.953 19.9638 20.0346 19.9635C20.2553 19.9627 20.476 19.9626 20.6967 19.9628C20.8814 19.9629 21.0662 19.9626 21.251 19.9623C21.6872 19.9616 22.1235 19.9616 22.5598 19.962C23.0085 19.9623 23.4573 19.9616 23.906 19.9604C24.2924 19.9593 24.6788 19.959 25.0651 19.9592C25.2954 19.9593 25.5257 19.9591 25.7559 19.9583C25.9727 19.9576 26.1895 19.9577 26.4064 19.9584C26.5229 19.9586 26.6394 19.958 26.7559 19.9573C27.6777 19.9626 28.4909 20.2448 29.1648 20.8896C29.7419 21.5257 30.0099 22.249 30.0146 23.0933C30.0165 23.1723 30.0165 23.1723 30.0183 23.253C30.0233 24.1261 29.6536 24.8328 29.0524 25.4471C28.5262 25.9501 27.8551 26.2558 27.1223 26.2586C27.0488 26.259 26.9753 26.2595 26.8996 26.2599C26.8183 26.2601 26.7369 26.2602 26.6556 26.2604C26.5698 26.2607 26.4841 26.2611 26.3983 26.2615C26.1165 26.2628 25.8348 26.2634 25.5531 26.264C25.4558 26.2642 25.3585 26.2644 25.2612 26.2647C24.8038 26.2658 24.3463 26.2666 23.8888 26.2671C23.3624 26.2677 22.8361 26.2692 22.3097 26.2716C21.9019 26.2734 21.4941 26.2742 21.0863 26.2744C20.8432 26.2746 20.6002 26.2751 20.3571 26.2766C20.1279 26.2779 19.8986 26.2782 19.6693 26.2776C19.5859 26.2776 19.5025 26.2779 19.419 26.2787C18.1738 26.2902 17.093 25.851 16.1971 24.9875C15.4372 24.2152 14.9849 23.1618 14.9725 22.0792C14.9731 22.0186 14.9737 21.9581 14.9744 21.8958C14.9749 21.8318 14.9753 21.7678 14.9758 21.7019C14.9984 20.6181 15.3875 19.6746 16.1133 18.8672C16.1436 18.8314 16.174 18.7956 16.2053 18.7587C17.1458 17.7237 18.5444 17.432 19.8822 17.4382Z"
                            fill="black"
                            fillOpacity="0.45"
                        />
                        <path
                            d="M9.00879 5.42358C9.53709 5.83601 9.89419 6.36929 10.0195 7.03125C10.0973 7.83904 9.96094 8.51623 9.45259 9.1674C8.95788 9.70867 8.3553 10.0132 7.62105 10.047C6.85993 10.0585 6.24768 9.83197 5.68359 9.31641C5.16803 8.75232 4.94149 8.14006 4.953 7.37895C4.98675 6.6447 5.29133 6.04212 5.83259 5.54741C6.78213 4.80614 8.00552 4.75336 9.00879 5.42358Z"
                            fill="black"
                            fillOpacity="0.45"
                        />
                    </g>
                    <defs>
                        <clipPath id="clip0_1_2">
                            <rect width="30" height="30" fill="white" />
                        </clipPath>
                    </defs>
                </svg>
                {hasFile ? 'File selected' : 'Click to attach file (.pdf, .doc, .docx, .png, .jpg, .jpeg)'}
            </label>
            <input
                type="file"
                id="file-upload"
                onChange={handleFileChange}
                accept=".pdf,.doc,.docx"
                style={{
                    position: 'absolute',
                    width: '1px',
                    height: '1px',
                    padding: '0',
                    margin: '-1px',
                    overflow: 'hidden',
                    clip: 'rect(0,0,0,0)',
                    border: '0'
                }}
            />
        </div>
    );
}