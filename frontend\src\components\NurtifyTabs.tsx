import React from 'react';
import './tabs.css'; // We'll create this CSS file next

interface Tab {
  id: string;
  label: string;
}

interface NurtifyTabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
  className?: string; // Optional class for the wrapper
}

const NurtifyTabs: React.FC<NurtifyTabsProps> = ({
  tabs,
  activeTab,
  onTabChange,
  className = '',
}) => {
  return (
    <div className={`nurtify-tabs-wrapper ${className}`}>
      {tabs.map((tab) => (
        <button
          key={tab.id}
          className={`nurtify-tab-button ${
            activeTab === tab.id ? 'active' : ''
          }`}
          onClick={() => onTabChange(tab.id)}
          role="tab"
          aria-selected={activeTab === tab.id}
          aria-controls={`tabpanel-${tab.id}`} // Optional for accessibility
          id={`tab-${tab.id}`} // Optional for accessibility
        >
          {tab.label}
        </button>
      ))}
    </div>
  );
};

export default NurtifyTabs;
