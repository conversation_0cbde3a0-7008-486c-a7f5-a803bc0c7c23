import { Facebook, Twitter, Instagram } from 'lucide-react';

interface SocialsProps {
  componentsClass?: string;
  textSize?: string;
}

export default function Socials({ componentsClass }: SocialsProps) {
  // Temporary social media links until data file is created
  const socialMediaLinks = [
    {
      href: "https://facebook.com",
      icon: Facebook
    },
    {
      href: "https://twitter.com", 
      icon: Twitter
    },
    {
      href: "https://instagram.com",
      icon: Instagram
    }
  ];

  return (
    <>
      {socialMediaLinks.map((link, index) => (
        <a
          key={index}
          className={componentsClass || ""}
          href={link.href}
        >
          <link.icon size={20} className="text-white" />
        </a>
      ))}
    </>
  );
}
