import "./analytics.css";
import { useEffect, useState, useMemo, useRef } from "react";
import { useNavigate } from "react-router-dom";
import DashboardKPICard from "@/components/DashboardKPICard";
import NoResult from "@/components/common/NoResult";
import { SquareArrowOutUpRight, BarChart as BarChartIcon, ChevronLeft, ChevronRight } from "lucide-react";
import {
  BarChart, Bar, PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip,
  Legend, ResponsiveContainer
} from 'recharts';
import type { PieLabelRenderProps } from 'recharts';
import {
  Department as DepartmentData,
  User as UserData,
} from "@/services/api/types.ts";
import { useAnalyticsQuery } from "@/hooks/user.query";

export default function Analytics() {
  const { data: analyticsData } = useAnalyticsQuery();
  const [departmentData, setDepartmentData] = useState<
    Partial<DepartmentData>[]
  >([]); // Initialize as empty array
  const [userData, setUserData] = useState<Partial<UserData>[]>([]); // Initialize as empty array
  const navigate = useNavigate();
  const scrollRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (analyticsData?.data) {
      setDepartmentData(analyticsData.data.departments || []);
      setUserData(analyticsData.data.users || []);
      console.log("users", analyticsData.data.users);
    }
  }, [analyticsData]);

  const dashboardKPICards = [
    { title: "Hospitals", number: analyticsData?.counts?.["hospitals"] ?? "0" },
    {
      title: "Departments",
      number: analyticsData?.counts?.["departments"] ?? "0",
    },
    { title: "Hospital Admins", number: analyticsData?.counts?.["hospital_admins"] ?? "0" },
    { title: "Department Admins", number: analyticsData?.counts?.["department_admins"] ?? "0" },
    { title: "Staffs", number: analyticsData?.counts?.["department_staffs"] ?? "0" },
    { title: "Patients", number: analyticsData?.counts?.["patients"] ?? "0" },
    { title: "Sponsors", number: analyticsData?.counts?.["sponsors"] ?? "0" },
    { title: "Studies", number: analyticsData?.counts?.["studies"] ?? "0" },
    { title: "Forms", number: analyticsData?.counts?.["forms"] ?? "0" },
    { title: "Policies", number: analyticsData?.counts?.["policies"] ?? "0" },
  ];

  // Process department data for charts
  const departmentChartData = useMemo(() => {
    // Group departments by hospital
    const hospitalGroups = departmentData.reduce((acc, dept) => {
      const hospitalName = String(dept.hospital || 'Unknown');
      if (!acc[hospitalName]) {
        acc[hospitalName] = 0;
      }
      acc[hospitalName]++;
      return acc;
    }, {} as Record<string, number>);

    // Convert to array format for Recharts
    return Object.entries(hospitalGroups).map(([name, value]) => ({
      name,
      value,
    }));
  }, [departmentData]);

  // Process user data for charts
  const userChartData = useMemo(() => {
    // Group users by hospital
     
    const hospitalGroups = userData.reduce((acc, user: any) => {
      const hospitalName = String(user.hospital || 'Unknown');
      if (!acc[hospitalName]) {
        acc[hospitalName] = 0;
      }
      acc[hospitalName]++;
      return acc;
    }, {} as Record<string, number>);

    // Convert to array format for Recharts
    return Object.entries(hospitalGroups).map(([name, value]) => ({
      name,
      value,
    }));
  }, [userData]);

  // Colors for charts - using a more cohesive color palette that matches the app's theme
  const COLORS = ['#37B7C3', '#4ECDC4', '#7DCFB6', '#00B2CA', '#1D4E89', '#2B3674', '#FBB13C', '#FC6E51'];

  const handleDepartmentLists = () => navigate("/org/dashboard/department");
  const handleUserLists = () => navigate("/org/dashboard/users");

  const scrollLeft = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: -200, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollRef.current) {
      scrollRef.current.scrollBy({ left: 200, behavior: 'smooth' });
    }
  };

  // Render success state
  return (
    <div className="analytics-container">
      <div className="analytics-header">
        <div className="analytics-title">
          <h1>
            <BarChartIcon size={24} style={{ marginRight: "10px" }} />
            Dashboard
          </h1>
        </div>
        <div className="analytics-subtitle">
          <h6>View and analyze your organization's metrics</h6>
        </div>
      </div>
      <div>
        <div className="kpi-cards-container">
          <button className="scroll-button left" onClick={scrollLeft}>
            <ChevronLeft size={24} />
          </button>
          <div ref={scrollRef} className="d-flex carrousel-KPIcard">
            {dashboardKPICards.map((card, index) => (
              <div key={index} style={{ scrollSnapAlign: "start", minWidth: "200px" }}>
                <DashboardKPICard title={card.title} number={card.number} />
              </div>
            ))}
          </div>
          <button className="scroll-button right" onClick={scrollRight}>
            <ChevronRight size={24} />
          </button>
        </div>
        <div className="chart-grid">
          <div className="analytics-chart-container chart-card">
            {departmentData.length > 0 ? (
              <>
                <div className="chart-header">
                  <h4 className="chart-title">Departments by Hospital</h4>
                  <button
                    className="iconButton editIcon"
                    aria-label="View Department List"
                    onClick={handleDepartmentLists}
                  >
                    <SquareArrowOutUpRight size={20} />
                  </button>
                </div>
                <div className="chart-summary">
                  <div className="summary-item">
                    <span className="summary-label">Total Departments</span>
                    <span className="summary-value">{departmentData.length}</span>
                  </div>
                  <div className="summary-item">
                    <span className="summary-label">Hospitals</span>
                    <span className="summary-value">{departmentChartData.length}</span>
                  </div>
                  <div className="summary-item">
                    <span className="summary-label">Most Departments</span>
                    <span className="summary-value">
                      {departmentChartData.length > 0
                        ? departmentChartData.reduce((prev, current) =>
                            (prev.value > current.value) ? prev : current
                          ).name
                        : 'N/A'}
                    </span>
                  </div>
                </div>
                <div className="chart-wrapper">
                  <ResponsiveContainer width="100%" height={350}>
                    <PieChart>
                      <Pie
                        data={departmentChartData}
                        cx="50%"
                        cy="50%"
                        labelLine={true}
                        label={(props: PieLabelRenderProps) => {
                          const { name, percent } = props;
                          return `${name}: ${percent ? (percent * 100).toFixed(0) : 0}%`;
                        }}
                        outerRadius={120}
                        innerRadius={60}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {departmentChartData.map((_entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => [`${value} departments`, 'Count']} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </>
            ) : (
              <NoResult addPath="/dashboard/add-department" title="Department" />
            )}
          </div>
          <div className="analytics-chart-container chart-card">
            {userData.length > 0 ? (
              <>
                <div className="chart-header">
                  <h4 className="chart-title">Users by Hospital</h4>
                  <button
                    className="iconButton editIcon"
                    aria-label="View User List"
                    onClick={handleUserLists}
                  >
                    <SquareArrowOutUpRight size={20} />
                  </button>
                </div>
                <div className="chart-summary">
                  <div className="summary-item">
                    <span className="summary-label">Total Users</span>
                    <span className="summary-value">{userData.length}</span>
                  </div>
                  <div className="summary-item">
                    <span className="summary-label">Hospitals</span>
                    <span className="summary-value">{userChartData.length}</span>
                  </div>
                  <div className="summary-item">
                    <span className="summary-label">Most Users</span>
                    <span className="summary-value">
                      {userChartData.length > 0
                        ? userChartData.reduce((prev, current) =>
                            (prev.value > current.value) ? prev : current
                          ).name
                        : 'N/A'}
                    </span>
                  </div>
                </div>
                <div className="chart-wrapper">
                  <ResponsiveContainer width="100%" height={350}>
                    <BarChart
                      data={userChartData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value} users`, 'Count']} />
                      <Legend />
                      <Bar
                        dataKey="value"
                        name="Users"
                        fill="#37B7C3"
                        radius={[10, 10, 0, 0]}
                        barSize={40}
                        animationDuration={1500}
                      >
                        {userChartData.map((_entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </>
            ) : (
              <NoResult addPath="/dashboard/add-user" title="User" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
