import React, { lazy, Suspense } from "react";
import { Routes, Route } from "react-router-dom"; // Removed Navigate
import ProtectedRoute from "@/components/common/ProtectedRoute";
import ProfileGuard from "@/components/ProfileGuard";
import Home from "@/pages/home";
import LandingPage from "@/pages/landing/LandingPage"; // Import the new landing page
import SDK from "@/pages/sdk";
import ClinicalDoc from "@/pages/clinical-documentation/Clinical-doc";
import PageLoader from "@/components/common/PageLoader";
import NotFound from "@/pages/not-found";
import ContactUs from "@/pages/contact-us";
import HolisticForm from "@/pages/forms/holistic-form/HolisticForm";
import Forms from "@/pages/forms";
import Dashboard from "@/pages/dashboard";
import Profile from "@/pages/profile";
import MyTemplates from "@/pages/my-templates";
import FormPreview from "@/pages/my-templates/FormPreview";
import ListOfPatients from "@/pages/List-of-patients";
import Cpr from "@/pages/Cpr";
import PolicyList from "@/pages/policy-list";
import PolicyDetails from "@/pages/policy-details";
import SurveyForms from "@/pages/survey-forms";
import Example from "@/pages/example/Example";
import HospitalDetailsAndAdmins from "@/pages/hospital-details";
import SponsorOrgDetailsAndAdmins from "@/pages/sponsorOrg-details";
import DepartmentDetails from "@/pages/department-details";
import FormBuilder from "@/pages/form-builder";
import PatientBoard from "./pages/patient-board";
import MedicalDashboard from "./pages/medical-dashboard";
import PatientDocumentation from "./pages/patient-documentation";
// import UserSelectionPage from "./pages/user-selection";
import PatientClinicalLayout from "./pages/patient-clinical/layout";
import SponsorLayout from "./pages/sponsor/layout";
import Sponsor from "./pages/sponsor/index";
import SponsorStudiesSection from "./pages/sponsor/sponsor_studies";
import GlobalStudyInvitationSection from "./pages/sponsor/sponsor_studies_invitation";
import ActivitiesSection from "./pages/patient-clinical/activities";
import ReportSection from "./pages/patient-clinical/report";
import ConversationsSection from "./pages/patient-clinical/conversations";
import StudiesSection from "./pages/patient-clinical/studies";
import DiarySection from "./pages/patient-clinical/diary";
import AppointmentsSection from "./pages/patient-clinical/appointments";
import ResourcesSection from "./pages/patient-clinical/resources";
import ConsentSection from "./pages/patient-clinical/consent";
import ShowInterestSection from "./pages/patient-clinical/show-interest";
import ConversationPage from "./pages/conversation";
import AskNurtifiers from "./pages/ask-nurtifiers";
import ConversationDetails from "./pages/conversation-details";
import PatientConversations from "./pages/patient-conversations";
import PatientConversationDetails from "./pages/patient-conversation-details";
import TermsAndConditionsPage from "@/pages/terms-and-conditions";
import TermsOfUse from "@/pages/terms-and-conditions/termsOfUse";
import PrescriptionList from "@/pages/prescription-list-home";
import Privacy from "@/pages/terms-and-conditions/privacy";
import Cookies from "@/pages/terms-and-conditions/cookies";
import BlogList from "@/pages/blog";
import BlogDetail from "@/pages/blog/BlogDetail";
import FormsClinical from "./pages/patient-clinical/forms-clinical";
import FormsQueries from "./pages/patient-queries";
import PatientLogs from "@/pages/patient-board/PatientLogs";
import UserLogs from "./pages/dashboard/UserLogs";
import PatientDiary from "./pages/patient-diary";
import PatientMedications from "./pages/patient-medications";
import PatientVitalSigns from "./pages/patient-vital-signs";
import LiveChatPatient from "./pages/live-chat-patient/LiveChatPatient";
import LiveChatConversation from "./pages/live-chat-conversation";
import OrgLiveChat from "@/pages/org/live-chat";
import OrgChatDetail from "@/pages/org/live-chat/OrgChatDetail";
import SelectChatPlaceholder from "@/pages/org/live-chat/SelectChatPlaceholder"; // Import the new component
import StudyPatients from "@/pages/sponsor/study-patients/index";
import PatientDetailsPage from "@/pages/sponsor/study-patients/patient-details";
import AllStuff from "@/pages/sponsor/all-stuff";
import SponsorConsents from "@/pages/sponsor/consents";

// Lazy load dashboard components
const DashboardIndex = lazy(
  () => import("@/pages/dashboard/index/dashboard-index")
);
const PatientForm = lazy(() => import("@/pages/patient-form"));
const Policy = lazy(() => import("@/pages/dashboard/policy"));
const AddPolicy = lazy(() => import("@/pages/dashboard/add-policy"));
const Analytics = lazy(() => import("@/pages/dashboard/analytics"));
const Hospitals = lazy(() => import("@/pages/dashboard/hospitals"));
const AddHospitals = lazy(() => import("@/pages/dashboard/add-hospital"));
const SponsorsList = lazy(() => import("@/pages/dashboard/sponsors-list"));
const AddSponsor = lazy(() => import("@/pages/dashboard/add-sponsor"));
const Department = lazy(() => import("@/pages/dashboard/department"));
const AddDepartment = lazy(() => import("@/pages/dashboard/add-department"));
const AddUser = lazy(() => import("@/pages/dashboard/add-user"));

const UsersList = lazy(() => import("@/pages/dashboard/users-list"));
const UserInfo = lazy(() => import("@/pages/dashboard/user-info"));
const UpdatePolicy = lazy(() => import("@/pages/dashboard/update-policy"));
const AdminHospitalDashboard = lazy(() => import("@/pages/dashboard/hospital-dashboard"))
const AdminDepartmentDashboard = lazy(
  () => import("@/pages/dashboard/admin-department/dashboard")
);
const TestNewDashboard = lazy(
  () => import("@/pages/dashboard/admin-department/dashboard/test-new-dashboard")
);
const ScheduleEvent = lazy(() => import("@/pages/dashboard/schedule-event"));
const Studies = lazy(() => import("@/pages/dashboard/studies"));
const GlobalStudyInvitation = lazy(() => import("@/pages/dashboard/studies-invitations"));
const StudyDetail = lazy(() => import("@/pages/dashboard/studies/StudyDetail"));
const PatientDetails = lazy(() => import("@/pages/patient-details"));
const EditPatient = lazy(() => import("@/pages/edit-patient"));
const Prescriptions = lazy(() => import("@/pages/prescription"));
const PatientStudies = lazy(() => import("@/pages/patient-studies"));
const PendingRequests = lazy(() => import("@/pages/pending-request"));

const ReportsReviews = lazy(() => import("@/pages/patient-reports-reviews"));
const ReportDetails = lazy(() => import("@/pages/patient-reports-reviews/[reportUuid]"));

const AppRouter: React.FC = () => {
  return (
    <div>
      <Routes>
        <Route element={<PageLoader />}>
          <Route
            path="/"
            element={<LandingPage />} // Render LandingPage at the root
          />
          {/* <Route path="/selection" element={<UserSelectionPage />} /> */}
          {/* Patient Clinical routes */}
          <Route
            path="test-new-dashboard"
            element={
              <Suspense fallback={<PageLoader />}>
                <TestNewDashboard />
              </Suspense>
            }
          />
          <Route path="/medical-dashboard" element={<MedicalDashboard />} />
          <Route
            path="/patient"
            element={
              <ProtectedRoute>
                <PatientClinicalLayout />
              </ProtectedRoute>
            }
          >
            <Route
              index
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ReportSection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="activities"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ActivitiesSection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="report"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ReportSection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="conversations"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ConversationsSection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="live-chat"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <LiveChatPatient />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="live-chat/:chatId"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <LiveChatConversation />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="studies"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <StudiesSection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="diary"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <DiarySection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="appointments"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <AppointmentsSection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="resources"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ResourcesSection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="consent"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ConsentSection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="show-interest"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ShowInterestSection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
             <Route
              path="my-forms"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <FormsClinical />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="conversation"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ConversationPage />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="forms-queries"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <FormsQueries />
                  </ProtectedRoute>
                </Suspense>
              }
            />
          </Route>
          <Route
            path="/sponsor"
            element={
              <ProtectedRoute>
                <SponsorLayout />
              </ProtectedRoute>
            }
          >
           <Route
              path=""
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <Sponsor />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="studies"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <SponsorStudiesSection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="invitations"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <GlobalStudyInvitationSection />
                  </ProtectedRoute>
                </Suspense>
              }
            />
             <Route
              path="study-patients"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <StudyPatients />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="study-patients/:patientUuid"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientDetailsPage />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="consents"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <SponsorConsents />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="all-stuff"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <AllStuff />
                  </ProtectedRoute>
                </Suspense>
              }
            />
          </Route>
          <Route path="/sdk" element={<SDK />} />
          <Route
            path="/home"
            element={
              <ProtectedRoute>
                <Home />
              </ProtectedRoute>
            }
          />
          <Route
            path="/clinical"
            element={
              <ProtectedRoute>
                <ClinicalDoc />
              </ProtectedRoute>
            }
          />
          <Route path="/contact-us" element={<ContactUs />} />
          <Route path="/forms" element={<Forms />} />
          <Route
            path="/terms-and-conditions/*"
            element={<TermsAndConditionsPage />}
          >
            <Route index element={<TermsOfUse />} />
          </Route>
          <Route path="/privacy/*" element={<TermsAndConditionsPage />}>
            <Route index element={<Privacy />} />
          </Route>
          <Route path="/cookies/*" element={<TermsAndConditionsPage />}>
            <Route index element={<Cookies />} />
          </Route>
          <Route path="/patients" element={<ProtectedRoute><ListOfPatients /></ProtectedRoute>} />
          <Route
            path="/patient-board"
            element={

                <PatientBoard />

            }
          ></Route>
          <Route path="/templates" element={<MyTemplates />} />
          <Route path="/form/preview/:formUuid" element={<FormPreview />} />
          <Route path="/holistic-form" element={<HolisticForm />} />
          <Route path="/survey-forms" element={<SurveyForms />} />
          <Route path="/survey-forms/edit/:formUuid" element={<SurveyForms />} />
          <Route path="/survey-forms/new" element={<SurveyForms />} />
          <Route path="/ask-nurtifiers" element={<AskNurtifiers />} />
          <Route path="/blog" element={<BlogList />} />
          <Route path="/blog/:idAndTitle" element={<BlogDetail />} />
          <Route
            path="/survey-forms/edit/:formUuid"
            element={<SurveyForms />}
          />
          <Route path="/cpr" element={<Cpr />} />

          {/* Unified Organization Dashboard */}
          <Route
            path="/org/dashboard"
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          >
            {/* Index route will be handled dynamically in ProtectedRoute */}
            <Route
              index
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <DashboardIndex /> {/* Fallback component */}
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="policy"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <Policy />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="add-policy"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <AddPolicy />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="analytics"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <Analytics />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="hospitals"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <Hospitals />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="add-hospital"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <AddHospitals />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="sponsors"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <SponsorsList />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="add-sponsor"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <AddSponsor />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="department"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <Department />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="add-department"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <AddDepartment />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="add-user"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <AddUser />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="update-policy/:uuid"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <UpdatePolicy />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="users"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <UsersList />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="hospital-details/:uuid/:tab?"
              element={
                <ProtectedRoute>
                  <ProfileGuard>
                    <HospitalDetailsAndAdmins />
                  </ProfileGuard>
                </ProtectedRoute>
              }
            />
            <Route
              path="my-hospital/:uuid/:tab?"
              element={
                <ProtectedRoute>
                  <ProfileGuard>
                    <HospitalDetailsAndAdmins />
                  </ProfileGuard>
                </ProtectedRoute>
              }
            />
            <Route
              path="department-details/:uuid/:tab?"
              element={
                <ProtectedRoute>
                  <ProfileGuard>
                    <DepartmentDetails />
                  </ProfileGuard>
                </ProtectedRoute>
              }
            />
            <Route
              path="my-department/:uuid/:tab?"
              element={
                <ProtectedRoute>
                  <ProfileGuard>
                    <DepartmentDetails />
                  </ProfileGuard>
                </ProtectedRoute>
              }
            />
            <Route
              path="sponsorOrg-details/:uuid/:tab?"
              element={
                <ProtectedRoute>
                  <ProfileGuard>
                    <SponsorOrgDetailsAndAdmins/>
                  </ProfileGuard>
                </ProtectedRoute>
              }
            />
            <Route
              path="user-info/:uuid/:tab?"
              element={
                <ProtectedRoute>
                  <ProfileGuard>
                    <UserInfo />
                  </ProfileGuard>
                </ProtectedRoute>
              }
            />
            <Route
              path="profile"
              element={
                <ProtectedRoute>
                  <Profile />
                </ProtectedRoute>
              }
            />
            <Route
              path="hospital-dashboard"
              element={
                <ProtectedRoute>
                  <ProfileGuard>
                    <AdminHospitalDashboard />
                  </ProfileGuard>
                </ProtectedRoute>
              }
            />
            <Route
              path="admin-department/:departmentUuid/"
              element={
                <ProtectedRoute>
                  <ProfileGuard>
                    <AdminDepartmentDashboard />
                  </ProfileGuard>
                </ProtectedRoute>
              }
            />
            <Route
              path="study-settings"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <ScheduleEvent />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="studies"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <Studies />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="studies/:studyId"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <StudyDetail />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="invitations"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <GlobalStudyInvitation />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="logsPatient"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <PatientLogs />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="user-logs"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                    <UserLogs />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="pending-request"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ProfileGuard>
                      <PendingRequests />
                    </ProfileGuard>
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route path="live-chat" element={<OrgLiveChat />}>
              <Route index element={<SelectChatPlaceholder />} />
              <Route path=":uuid" element={<OrgChatDetail />} />
            </Route>
          </Route>

          {/* Patient Board Routes */}
          <Route
            path="/org/dashboard/patient-board"
            element={
              <ProtectedRoute>
                <MedicalDashboard />
              </ProtectedRoute>
            }
          >
            <Route
              index
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientDetails />
                  </ProtectedRoute>
                </Suspense>
              }
            />
             <Route
              path="patient-medications"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientMedications />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="vital-signs"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientVitalSigns />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="forms-queries"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <FormsQueries />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="patient-conversation-details/:uuid"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientConversationDetails />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="patient-details"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientDetails />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="edit-patient"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <EditPatient />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="patient-forms"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientForm />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="patient-documentation"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientDocumentation />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="conversations"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientConversations />
                  </ProtectedRoute>
                </Suspense>
              }
            />
             <Route
              path="prescription"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <Prescriptions />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="patient-diary"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientDiary />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="patient-studies"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientStudies />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="patient-logs"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <PatientLogs />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="patient-report"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ReportsReviews />
                  </ProtectedRoute>
                </Suspense>
              }
            />
            <Route
              path="/org/dashboard/patient-board/patient-report/:reportUuid"
              element={
                <Suspense fallback={<PageLoader />}>
                  <ProtectedRoute>
                    <ReportDetails />
                  </ProtectedRoute>
                </Suspense>
              }
            />
          </Route>
          <Route
            path="/policies"
            element={<PolicyList />}
          />
          <Route path="/policy-details/:uuid" element={<PolicyDetails />} />
          <Route path="/policy-details" element={<PolicyDetails />} />
          <Route path="/example" element={<Example />} />
          <Route path="/form-builder" element={<FormBuilder />} />
          <Route path="/form-builder/:formUuid" element={<FormBuilder />} />
          <Route path="/ask-nurtifierss" element={<AskNurtifiers />} />
          <Route
            path="/conversation-details/:uuid"
            element={<ConversationDetails />}
          />
          <Route path="/prescription-list" element={<PrescriptionList />} />
          <Route path="*" element={<NotFound />} />


        </Route>
      </Routes>
    </div>
  );
};

export default AppRouter;
