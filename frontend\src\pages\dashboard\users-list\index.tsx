import "./usersList.css";
import {
  ArrowUpRight,
  Plus,
  Users,
  Search,
} from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useCurrentUserQuery, useUsersByDepartmentQuery } from "@/hooks/user.query.ts";
import { User } from "@/types/types";
import DataTable, { DataTableSearch } from "@/components/common/DataTable";
import { useMemo, ReactNode, useState } from "react";

// Extended User type with fullName property for sorting
interface ExtendedUser extends User {
  fullName: string;
}

export default function UsersList() {
  // Current user from query
  const currentUser = useCurrentUserQuery();
  const departmentUuid = currentUser.data?.department?.uuid || '';

  const navigate = useNavigate();

  // Use React Query to fetch users from the department
  const { data, isLoading, isError } =
    useUsersByDepartmentQuery(departmentUuid);

  // State for filtered data
  const [filteredData, setFilteredData] = useState<ExtendedUser[]>([]);

  const handleRedirect = (userUuid: string) => {
    navigate(`/org/dashboard/user-info/${userUuid}`);
  };

  // Define columns for the DataTable
  const columns = useMemo(() => [
    {
      key: "fullName" as keyof ExtendedUser,
      header: "User Name",
      sortable: true,
      render: (_value: unknown, row?: ExtendedUser) => {
        return `${row?.first_name || ''} ${row?.last_name || ''}`;
      },
    },
    {
      key: "speciality" as keyof ExtendedUser,
      header: "Job Title",
      sortable: true,
      render: (value: unknown): ReactNode => {
        return value ? String(value) : "N/A";
      },
    },
    {
      key: "phone_number" as keyof ExtendedUser,
      header: "Phone Number",
      sortable: true,
    },
    {
      key: "email" as keyof ExtendedUser,
      header: "Email",
      sortable: true,
    },
  ], []);

  // Define actions for the DataTable
  const actions = useMemo(() => [
    {
      icon: <ArrowUpRight size={18} />,
      tooltipText: "View User Details",
      onClick: (user: ExtendedUser) => handleRedirect(user.uuid || user.identifier || ''),
    },
  ], []);

  // Prepare data for the DataTable
  const tableData = useMemo<ExtendedUser[]>(() => {
    if (!data) return [];

    // Add a fullName property to each user for sorting
    return data.map((user: User): ExtendedUser => ({
      ...user,
      fullName: `${user.first_name || ''} ${user.last_name || ''}`,
    }));
  }, [data]);

  return (
    <div className="users-container">
      <div className="users-header">
        <div className="users-title">
          <h1>
            <Users size={24} style={{ marginRight: "10px" }} />
            Users
          </h1>
        </div>
        <div className="users-subtitle">
          <h6>Manage and view all users in your organization</h6>
        </div>
      </div>

      <div className="users-controls">
        <div className="users-search-container">
          <div className="users-search-box">
            <Search size={18} className="search-icon" />
            <DataTableSearch
              data={tableData}
              onFilter={setFilteredData}
              placeholder="Search users..."
            />
          </div>
          <Link
            to="/org/dashboard/add-user"
            className="users-add-button"
          >
            Add User <Plus size={20} />
          </Link>
        </div>
      </div>

      <div className="users-table-container">
        {isLoading ? (
          <div className="loading-message">Loading users...</div>
        ) : isError ? (
          <div className="error-message">Error loading users</div>
        ) : (
          <DataTable
            data={tableData}
            filteredData={filteredData}
            columns={columns}
            actions={actions}
            noDataMessage="No users found"
            defaultItemsPerPage={10}
          />
        )}
      </div>
    </div>
  );
}
