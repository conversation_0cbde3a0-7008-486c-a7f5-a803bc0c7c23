import { useState } from "react";
import { X } from "lucide-react";
import { useDepartmentsQuery } from "@/hooks/department.query";
import { useStudyInvitationsByInviterQuery } from "@/hooks/study.query";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { Department } from "@/services/api/types";
import "./modal.css";

interface Invitation {
  department: string;
  status: string;
  study: string;
}

interface InviteDepartmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (selectedDepartments: string[]) => void;
  studyName: string;
  studyUuid: string;
}

export default function InviteDepartmentModal({
  isOpen,
  onClose,
  onSave,
  studyName,
  studyUuid,
}: InviteDepartmentModalProps) {
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
  const { data: currentUser } = useCurrentUserQuery();
  const { data: departments, isLoading: isLoadingDepartments } = useDepartmentsQuery();
  const { data: invitations, isLoading: isLoadingInvitations } = useStudyInvitationsByInviterQuery(currentUser?.identifier || "");

  // Filter out departments that have pending or accepted invitations for THIS specific study
  const filteredDepartments = departments?.filter((department: Department) => {
    if (!department.uuid) return false;
    const departmentInvitations = invitations?.filter((inv: Invitation) => 
      inv.department === department.uuid && inv.study === studyUuid
    ) || [];
    return !departmentInvitations.some((inv: Invitation) => 
      inv.status === "pending" || inv.status === "accepted"
    );
  });

  const handleDepartmentChange = (departmentId: string) => {
    setSelectedDepartments(prev => {
      if (prev.includes(departmentId)) {
        return prev.filter(id => id !== departmentId);
      }
      return [...prev, departmentId];
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(selectedDepartments);
  };

  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Invite Departments to {studyName}</h2>
          <button className="close-button" onClick={onClose}>
            <X size={20} />
          </button>
        </div>
        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            {isLoadingDepartments || isLoadingInvitations ? (
              <div className="loading">Loading departments...</div>
            ) : (
              <div className="department-selection">
                <label className="form-label">Select Departments</label>
                <div className="department-list">
                  {filteredDepartments?.map((department: Department) => (
                    <div key={department.uuid} className="department-item">
                      <label className="checkbox-label">
                        <input
                          type="checkbox"
                          checked={selectedDepartments.includes(department.uuid || '')}
                          onChange={() => handleDepartmentChange(department.uuid || '')}
                        />
                        <span>{department.name}</span>
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          <div className="modal-footer">
            <button type="button" className="cancel-button" onClick={onClose}>
              Cancel
            </button>
            <button
              type="submit"
              className="submit-button"
              disabled={selectedDepartments.length === 0}
            >
              Invite Selected Departments
            </button>
          </div>
        </form>
      </div>
    </div>
  );
} 