

.policy-container {
    background-color: #ffffff;
}

.policy-title {
    margin-bottom: 1rem;
}

.policy-title h1 {
    font-size: 2rem;
    font-weight: 600;
    color: #333;
}

.policy-title h6 {
    color: #666;
    font-weight: 400;
}

.policy-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.policy-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-container {
    position: relative;
    /* width: 250px; */
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-input {
    width: 100%;
    padding: 8px 10px 8px 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.clear-search {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-search:hover {
    color: #333;
}

.policy-table-container {
    overflow-x: auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    margin-bottom: 100px;
}

.policy-table {
    width: 100%;
    border-collapse: collapse;
}

.policy-table th, .policy-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.policy-table th {
    background-color: #37B7C3;
    color: #fff;
    font-weight: 600;
    position: sticky;
    top: 0;
}

.policy-table tr:hover {
    background-color: #f5f9fa;
}

.policy-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.arrowUp {
    background-color: #37B7C3;
    border: none;
    border-radius: 8px;
    color: white;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}
.iconButton {
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    width: auto;
    height: auto;
    padding: 0.5rem;
    border-radius: 8px;
    border: none;
    transition: all 0.2s ease;
    background-color: #37B7C3;
    border-radius: 10px;
    border-color: black;
    color: white;
  }

.arrowUp:hover {
    background-color: #2da8b4;
    transform: translateY(-2px);
}

.downloadd {
    background-color: #4F547B;
    border: none;
    border-radius: 8px;
    color: white;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.downloadd:hover {
    background-color: #3f4361;
    transform: translateY(-2px);
}

.search-loading {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
    color: #37B7C3;
}

.search-loading svg {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.no-results {
    text-align: center;
    padding: 40px 0;
    color: #666;
}

.no-results svg {
    margin-bottom: 16px;
    color: #999;
}

.error-message {
    text-align: center;
    padding: 20px;
    color: #d32f2f;
    background-color: #ffebee;
    border-radius: 8px;
    margin: 20px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .policy-actions {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }
    
    .policy-table th, 
    .policy-table td {
        padding: 12px 8px;
    }
    
    .policy-title h1 {
        font-size: 1.5rem;
    }
}
