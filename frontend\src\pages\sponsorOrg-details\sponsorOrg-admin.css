/* Reuse most of the hospital-admin.css styles with minor adjustments */
.delete-btn {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-size: 16px;
}

.delete-btn:hover {
  color: #dc2626;
}

.user-management {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
    font-family: 'Arial', sans-serif;
  }
  
  .team-members-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .users-list {
    display: flex;
    flex-direction: column;
  }
  
  .user-row {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
  }
  
  .user-info {
    flex: 2;
    display: flex;
    gap: 12px;
  }
  
  .user-initials {
    width: 32px;
    height: 32px;
    background-color: #e0e7ff;
    color: #6366f1;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
  }
  
  .user-name {
    font-size: 14px;
    font-weight: 500;
    margin: 0;
  }
  
  .user-email {
    font-size: 12px;
    color: #666;
    margin: 2px 0 0;
  }

  .user-role {
    font-size: 12px;
    color: #666;
    margin: 2px 0 0;
  }
  
  .user-status {
    flex: 1;
  }
  
  .status {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
  }
  
  .status.active {
    background-color: #d1fae5;
    color: #10b981;
  }
  
  .status.inactive {
    background-color: #e5e7eb;
    color: #6b7280;
  }
  
  .status.invited {
    background-color: #fef3c7;
    color: #d97706;
  }
  
  .user-actions {
    flex: 0.5;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
  
  
  .add-member-btn {
    padding: 10px 20px;
    background: #37B7C3;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: background 0.2s;
  }
  
  .add-member-btn:hover {
    background: #2d919a;
  }
  
  .add-member-btn.active {
    background: #dc3545;
  }
  
  .add-member-btn.active:hover {
    background: #b02a37;
  }
  
  .add-member-section {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    transition: all 0.3s ease;
    margin-bottom: 20px;
  }
  
  .new-admin-form {
    display: flex;
    gap: 12px;
    align-items: center;
  }
  
  .enhanced-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
  }
  
  .enhanced-input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
  }
  
  .action-btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
  }
  
  .action-btn.primary {
    background: #37B7C3;
    color: white;
  }
  
  .action-btn.primary:hover {
    background: #2d919a;
  }
  
  .action-btn:disabled {
    background: #ced4da;
    cursor: not-allowed;
  }
  
  .resend-btn {
    background-color: transparent;
    border: 1px solid #666;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
  }
  
  .resend-btn:hover {
    background-color: #f5f5f5;
    border-color: #444;
    color: #444;
  }
  
  .resend-btn:active {
    background-color: #e0e0e0;
  }
  
  .resend-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }