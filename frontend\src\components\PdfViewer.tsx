import { Viewer, Worker } from '@react-pdf-viewer/core';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import "./PdfViewer.css";

interface PdfViewerProps {
    fileUrl: string;
}

const PdfViewer: React.FC<PdfViewerProps> = ({ fileUrl }) => {
    return (
        <div className="pdf-viewer" style={{ border: '3px solid black'}}>
            <Worker workerUrl={`https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js`}>
                <Viewer
                    fileUrl={fileUrl}
                    theme={{
                        theme: 'ligth',
                    }}
                />
            </Worker>
        </div>
    );
};

export default PdfViewer;
