import { useState } from "react";
import Preloader from "@/components/common/Preloader";
import "./dashboard.css";
import DashboardSidebar from "@/components/common/DashboardSidebar";
import MobileSidebarIcons from "@/components/common/MobileSidebarIcons";
import {
  Scissors,
  FileText,
  FilePlus,
  BarChart,
  Hospital,
  Building,
  Layers,
  FolderPlus,
  UserPlus,
  Users,
  User,
  Calendar,
  MessageCircleQuestion,
  Send,
  Handshake,
  MessageSquare, // Added for Live Chat icon
} from "lucide-react";
import SnippetsModal from "@/components/modal/SnippetsModal";
import AddPatientFormModal from "@/components/modal/AddPatientFormModal";
import DeletePatientModal from "@/components/modal/DeletePatientModal";
import { Outlet } from "react-router-dom";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { hasPermission, PermissionKey } from "@/services/permission-system";

type MenuItem = {
  icon: JSX.Element;
  label: string;
  path: string;
  onClick?: () => void;
  permissionKey: string;
};

export default function Dashboard() {
  const [switchModal, setSwitchModal] = useState(false);
  const [isSnippetsModalOpen, setSnippetsModalOpen] = useState(false);
  const [isAddPatientFormModalOpen, setAddPatientFormModalOpen] =
    useState(false);
  const [isDeletePatientModalOpen, setDeletePatientModalOpen] = useState(false);
  const currentUser = useCurrentUserQuery();
  const hospitalUuid = !currentUser.data?.is_superuser ? (currentUser.data?.hospital?.uuid || null) : null;
  const departmentUuid = !currentUser.data?.is_superuser &&!currentUser.data?.is_hospital_admin ?(currentUser.data?.department?.uuid || null) : null;
  const menuItems: MenuItem[] = [
    // super user
    {
      icon: <BarChart size={20} />,
      label: "Nurtify Dashboard",
      path: "/analytics", // Changed from "/dashboard/analytics"
      permissionKey: "viewDashboard",
    },
    {
      icon: <Hospital size={20} />,
      label: "Hospitals",
      path: "/hospitals", // Changed from "/dashboard/hospitals"
      permissionKey: "viewHospitals",
    },
    {
      icon: <Building size={20} />,
      label: "Add Hospital",
      path: "/add-hospital", // Changed from "/dashboard/add-hospital"
      permissionKey: "addHospital",
    },
    {
      icon: <Handshake size={20} />,
      label: "Sponsors",
      path: "/sponsors", // Changed from "/dashboard/add-hospital"
      permissionKey: "viewSponsors",
    },
    {
      icon: <UserPlus size={20} />,
      label: "Add Sponsor",
      path: "/add-sponsor", // Changed from "/dashboard/add-hospital"
      permissionKey: "addSponsor",
    },
    // hospital Admin
    {
      icon: <BarChart size={20} />,
      label: "Hospital Dashboard",
      path: "/hospital-dashboard", // Still needs the dynamic UUID
      permissionKey: "adminHospital",
    },
    // department Admin
    {
      icon: <BarChart size={20} />,
      label: "Department Dashboard",
      path: `/admin-department/${departmentUuid}/`, // Still needs the dynamic UUID
      permissionKey: "adminDepartment",
    },
    {
      icon: <BarChart size={20} />,
      label: "My Hospital",
      path: `/my-hospital/${hospitalUuid}/`, // Still needs the dynamic UUID
      permissionKey: "MyHospital",
    },
    {
      icon: <BarChart size={20} />,
      label: "My Department",
      path: `/my-department/${departmentUuid}/`, // Still needs the dynamic UUID
      permissionKey: "MyDepartment",
    },  
    {
      icon: <Layers size={20} />,
      label: "Departments",
      path: "/department", // Changed from "/dashboard/department"
      permissionKey: "viewDepartment",
    },
    {
      icon: <FolderPlus size={20} />,
      label: "Add Department",
      path: "/add-department", // Changed from "/dashboard/add-department"
      permissionKey: "addDepartment",
    },
    {
      icon: <Users size={20} />,
      label: "Users List",
      path: "/users", // Changed from "/dashboard/users"
      permissionKey: "viewUsers",
    },
    {
      icon: <UserPlus size={20} />,
      label: "Add User",
      path: "/add-user", // Changed from "/dashboard/add-user"
      permissionKey: "addUser",
    },
    // department Admin + department user
    {
      icon: <FileText size={20} />,
      label: "Policy",
      path: "/policy", // Changed from "/dashboard/policy"
      permissionKey: "viewPolicy",
    },
    {
      icon: <FilePlus size={20} />,
      label: "Add Policy",
      path: "/add-policy", // Changed from "/dashboard/add-policy"
      permissionKey: "addPolicy",
    },
    {
      icon: <Scissors size={20} />,
      label: "Snippets",
      path: "/snippets", // Changed from "/dashboard/snippets"
      onClick: () => setSnippetsModalOpen(true),
      permissionKey: "viewSnippets",
    },
    {
      icon: <Calendar size={20} />,
      label: "Study Settings",
      path: "/study-settings", // Changed from "/dashboard/schedule-event"
      permissionKey: "scheduleEvent",
    },
    {
      icon: <FileText size={20} />,
      label: "Studies",
      path: "/studies", // Changed from "/dashboard/studies"
      permissionKey: "viewStudies",
    },
    {
      icon: <Send size={20} />,
      label: "Studies Invitations",
      path:"/invitations",
      permissionKey:"studiesInvitations",
    },
    // super user + department Admin + department user
    {
      icon: <MessageCircleQuestion size={20} />,
      label: "Pending Request",
      path:"/pending-request",
      permissionKey:"pendingRequest",
    },
    {
      icon: <MessageSquare size={20} />,
      label: "Live Chat",
      path: "/live-chat",
      permissionKey: "viewLiveChat", // Assuming this permission will be added/handled
    },
    {
      icon: <Calendar size={20} />,
      label: "My Logs",
      path: "/user-logs",
      permissionKey: "myLogs",
    },
    {
      icon: <User size={20} />,
      label: "Profile",
      path: "/profile", // Changed from "/dashboard/profile"
      permissionKey: "viewProfile",
    },
  ];

  const filteredMenuItems = menuItems.filter((item) =>
    hasPermission(item.permissionKey as PermissionKey, currentUser.data)
  );

  // Base path for dashboard routes
  const basePath = "/org/dashboard";

  return (
    <div className="main-content bg-light-4">
      <Preloader />
      <div className="content-wrapper js-content-wrapper">
        <div className="dashboard__content bg-light-4">
          <div className="container-fluid px-0">
            <div className="row"> {/* Removed y-gap-30 from this main layout row */}
              {/* Sidebar Column */}
              <div className="col-xl-2 col-lg-3">
                <DashboardSidebar menuItems={filteredMenuItems} basePath={basePath} />
              </div>

              {/* Main Content */}
              <div className="col-xl-10 col-lg-9">
                <Outlet />
              </div>
            </div>
          </div>
        </div>
        <MobileSidebarIcons menuItems={filteredMenuItems} basePath={basePath} />
      </div>
      <SnippetsModal
        isOpen={isSnippetsModalOpen}
        onClose={() => setSnippetsModalOpen(false)}
      />
      <AddPatientFormModal
        isOpen={isAddPatientFormModalOpen}
        setIsModalOpen={setAddPatientFormModalOpen}
        setSwitchModal={setSwitchModal}
        switchModal={switchModal}
        isEditing={false}
      />
      <DeletePatientModal
        isOpen={isDeletePatientModalOpen}
        onClose={() => setDeletePatientModalOpen(false)}
      />
    </div>
  );
}
