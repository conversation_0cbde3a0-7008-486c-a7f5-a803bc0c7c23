/* Home Page Styles */

/* Hero Section */
.home-section {
  display: flex;
  flex-direction: column;
}

.hero-container {
  padding: 0 5%;
  margin-bottom: 40px;
}

.hero-background-home {
  border-radius: 0; /* Removed border-radius */
  background-image: url('/assets/img/home/<USER>');
  background-size: cover;
  background-position: center;
  padding: 60px 40px;
  margin-top: 20px;
  /* box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08); */
}

.hero-content-hero {
  padding: 0 120px;
}

.hero-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.hero-logo-container {
  margin-bottom: 30px;
}

.hero-logo {
  max-width: 300px;
  height: auto;
}

.hero-tagline {
  margin-bottom: 30px;
  max-width: 600px;
}

.hero-tagline p {
  font-size: 20px;
  line-height: 1.6;
  color: #333;
  font-weight: 500;
}

.hero-cta {
  margin-top: 10px;
}

@media (max-width: 768px) {
  .hero-cta {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
  }
}

.hero-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 32px;
  font-weight: 600;
  font-size: 18px;
  border-radius: 30px;
  background: var(--color-purple-1, #2a9a9f);
  color: #fff;
  border: none;
  box-shadow: 0 2px 8px rgba(42, 154, 159, 0.08);
  transition: background 0.2s, box-shadow 0.2s;
}

.hero-button-icon {
  margin-left: 10px;
  width: 20px;
  height: 20px;
}

.hero-button:hover {
  background: #218b8f;
  box-shadow: 0 4px 16px rgba(42, 154, 159, 0.15);
}

/* Features Section */
.features-section {
  padding: 60px 1%;
  background-color: #f8f9fa;
  text-align: center;
}

@media (max-width: 768px) {
  .features-section {
    margin-top: 50px ;
  }
}

.features-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #333;
}

.features-subtitle {
  font-size: 18px;
  color: #6c757d;
  max-width: 700px;
  margin: 0 auto 40px;
}

.text-highlight {
  color: var(--color-purple-1);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card-container {
  display: flex;
  justify-content: center;
}

.feature-card-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: white;
  border-radius: 16px;
  padding: 30px 20px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  border: 1px solid #e6e6e6;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  width: 100%;
  max-width: 360px;
  position: relative;
  overflow: hidden;
}

.feature-card-wrapper:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.feature-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 4px 10px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 12px;
  color: white;
  z-index: 1;
}

.new-badge {
  background-color: #28a745;
}

.popular-badge {
  background-color: #dc3545;
}

.feature-content {
  width: 100%;
  text-align: center;
  margin-top: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.feature-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 10px 0 5px;
}

.feature-description {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.4;
  margin-bottom: 15px;
}

.feature-navigate-hint {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-purple-1);
  margin-top: 5px;
  cursor: pointer;
}

.feature-arrow {
  font-style: normal;
  transition: transform 0.2s ease;
}

.feature-navigate-hint:hover .feature-arrow {
  transform: translateX(3px);
}

/* Benefits Section */
.benefits-section {
  padding: 80px 5%;
  background-color: white;
}

.benefits-title {
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 50px;
  text-align: center;
  color: #333;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.benefit-card {
  background-color: white;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  border: 1px solid #e6e6e6;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: center;
}

.benefit-card:hover {
  transform: translateY(-10px);
  box-shadow: none; /* Removed box-shadow on hover */
}

.benefit-icon {
  font-size: 40px;
  margin-bottom: 20px;
}

.benefit-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.benefit-description {
  font-size: 16px;
  color: #6c757d;
  line-height: 1.6;
}

/* News Section */
.news-section {
  padding: 40px 0;
  background-color: #f8f9fa;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .hero-background-home {
    padding: 40px 30px;
  }
  
  .hero-tagline p {
    font-size: 18px;
  }
  
  .features-title, 
  .benefits-title {
    font-size: 30px;
  }
  
  .features-subtitle {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .hero-container {
    padding: 0 20px;
  }
  
  .hero-background-home {
    padding: 30px 20px;
  }
  
  .hero-logo {
    max-width: 250px;
  }
  
  .hero-tagline p {
    font-size: 16px;
  }
  
  .features-title, 
  .benefits-title {
    font-size: 26px;
  }
  
  .features-section,
  .benefits-section {
    padding: 40px 20px;
  }
  
  .features-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .feature-card-wrapper {
    flex-direction: row;
    padding: 15px;
    text-align: left;
    max-width: 100%;
    border-radius: 12px;
  }
  
  .feature-content {
    text-align: left;
    align-items: flex-start;
    margin-left: 15px;
    margin-top: 0;
  }
  
  .benefits-grid {
    grid-template-columns: 1fr;
  }
}

/* Accessibility - For users who prefer reduced motion */
@media (prefers-reduced-motion: reduce) {
  .feature-card-wrapper, 
  .benefit-card {
    transition: none;
  }
  
  .feature-card-wrapper:hover,
  .benefit-card:hover {
    transform: none;
  }
}

/* Force all home page padding to 2% */
.home-container {
  padding: 2% !important;
}
