import React, { useState } from 'react';
import { usePatientAllergiesQuery } from '@/hooks/patient.query';
import api from '@/services/api';
import { Plus, Trash2, AlertCircle } from 'lucide-react';
import './PatientAllergiesTab.css';

interface PatientAllergiesTabProps {
  patientUuid: string;
  nhsNumber: string;
}

const PatientAllergiesTab: React.FC<PatientAllergiesTabProps> = ({ patientUuid, nhsNumber }) => {
  const [newAllergy, setNewAllergy] = useState('');
  const [isAdding, setIsAdding] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { data: allergies, refetch: refetchAllergies, isLoading } = usePatientAllergiesQuery(nhsNumber);

  const handleAddAllergy = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newAllergy.trim()) return;

    setIsAdding(true);
    setError(null);

    try {
      await api.post(`/patient/allergies/add-to-patient/${patientUuid}/`, {
        name: newAllergy.trim()
      });
      setNewAllergy('');
      await refetchAllergies();
    } catch (error) {
      console.error('Error adding allergy:', error);
      setError('Failed to add allergy. Please try again.');
    } finally {
      setIsAdding(false);
    }
  };

  const handleDeleteAllergy = async (allergyUuid: string) => {
    if (!window.confirm('Are you sure you want to delete this allergy?')) return;

    try {
      await api.delete(`/patient/allergies/${allergyUuid}/`);
      await refetchAllergies();
    } catch (error) {
      console.error('Error deleting allergy:', error);
      setError('Failed to delete allergy. Please try again.');
    }
  };

  return (
    <div className="allergies-tab">
      <div className="allergies-header">
        <h3>Patient Allergies</h3>
        <form onSubmit={handleAddAllergy} className="add-allergy-form">
          <input
            type="text"
            value={newAllergy}
            onChange={(e) => setNewAllergy(e.target.value)}
            placeholder="Enter allergy name"
            className="allergy-input"
            disabled={isAdding}
          />
          <button 
            type="submit" 
            className="btn-add-allergy"
            disabled={isAdding}
          >
            {isAdding ? 'Adding...' : (
              <>
                <Plus size={16} />
                Add Allergy
              </>
            )}
          </button>
        </form>
        {error && (
          <div className="error-message">
            <AlertCircle size={16} />
            {error}
          </div>
        )}
      </div>

      <div className="allergies-list">
        {isLoading ? (
          <div className="loading-message">Loading allergies...</div>
        ) : allergies && allergies.length > 0 ? (
          <ul>
            {allergies.map((allergy) => (
              <li key={allergy.uuid} className="allergy-item">
                <span>{allergy.name}</span>
                <button
                  className="btn-delete-allergy"
                  onClick={() => handleDeleteAllergy(allergy.uuid || '')}
                  title="Delete allergy"
                >
                  <Trash2 size={16} />
                </button>
              </li>
            ))}
          </ul>
        ) : (
          <p className="no-allergies">No allergies recorded</p>
        )}
      </div>
    </div>
  );
};

export default PatientAllergiesTab; 