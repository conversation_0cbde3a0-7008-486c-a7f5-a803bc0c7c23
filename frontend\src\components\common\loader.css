.loader-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    background: linear-gradient(135deg, #e0f7fa, #80deea); /* Added gradient background */
}

.flipping-cards {
    display: flex;
    justify-content: space-between;
    gap: 5px;
    width: 350px; /* Adjusted based on the increased size of cards */
}

.card-loader {
    width: 70px; /* Increased width */
    height: 50px; /* Increased height */
    padding: 10px;
    background-color: #37b7c3;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px; /* Increased font size */
    font-weight: bold;
    border-radius: 50%; /* Increased border radius for smoother corners */
    backface-visibility: hidden;
    transform-style: preserve-3d;
    animation: flip 2s infinite;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2); /* Added shadow for depth */
}

.card-loader:nth-child(1) {
    animation-delay: 0s;
}

.card-loader:nth-child(2) {
    animation-delay: 0.3s;
}

.card-loader:nth-child(3) {
    animation-delay: 0.6s;
}

.card-loader:nth-child(4) {
    animation-delay: 0.9s;
}

.card-loader:nth-child(5) {
    animation-delay: 1.2s;
}

.card-loader:nth-child(6) {
    animation-delay: 1.5s;
}

.card-loader:nth-child(7) {
    animation-delay: 1.8s;
}

@keyframes flip {
    0%,
    100% {
        transform: rotateY(0deg);
    }
    50% {
        transform: rotateY(180deg);
    }
}
