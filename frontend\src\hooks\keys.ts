// Auth keys
export const AUTH_KEYS = {
  LOGIN: 'auth/login',
  LOGOUT: 'auth/logout',
};

// Example keys
export const EXAMPLE_KEYS = {
  GET_EXAMPLE: 'example/getExample',
};


export const POLICY_KEYS = {
  GET_ALL: 'policy/getAll',
  GET_BY_UUID: 'policy/getById',
  CREATE: 'policy/create',
  UPDATE: 'policy/update',
  DELETE: 'policy/delete',
} as const;

export const USER_KEYS = {
  GET_USER: 'user/get',
  GET_ALL: 'user/getAll',
  GET_BY_ID: 'user/getById',
  CREATE: 'user/create',
  CREATE_ADMIN: 'admin/create',
  GET_BY_DEPARTMENT: 'user/getByDepartment',
  UPDATE: 'user/update',
  DELETE: 'user/delete',
  VERIFY_OTP: "user/verifyOtp",
  RESEND_OTP: "user/resendOtp",
  RESEND_ACTIVATION: "user/resendActivationMail",
  DELETE_ADMIN: "admin/delete"
};

export const Hospital_KEYS = {
  GET_ALL: 'hospital/getAll',
  GET_LIST: 'hospital/getLIST',
  GET_BY_ID: 'hospital/getById',
  CREATE: 'hospital/create',
  Update: 'hospital/update'
} as const;

export const Department_KEYS = {
  GET_ALL: 'department/getAll',
  GET_BY_ID: 'department/getById',
  CREATE: 'department/create',
  Update: 'department/update',
  GET_BY_HOSPITAL: 'department/getByHospital'
} as const;


export const ANALYTICS_KEYS = {
  GET_ANALYTICS: 'analysis/get',
  GET_ANALYTICS_DEPARTMENT_ADMIN: 'analysis/getDepartmentAdmin',
  GET_ANALYTICS_HOSPITAL_ADMIN: 'analysis/getDepartmentAdmin',
}

export const FORM_KEYS = {
  LIST: '/form/forms/',
  CREATE: '/form/forms/',
  READ: '/form/forms/{uuid}/',
  UPDATE: '/form/forms/{uuid}/',
  DELETE: '/form/forms/{uuid}/',
  BY_USER: '/form/forms/user/{identifier}/',
  BY_PATIENT: '/form/forms/patient/{patientUuid}/',
  PENDING: '/form/forms/pending/',
  VERSIONS: '/form/forms/{uuid}/versions/',
  ACCEPT: '/form/forms/{uuid}/accept/',
  REJECT: '/form/forms/{uuid}/reject/',
};

export const STUDY_KEYS = {
  GET_ALL: "studies",
  GET_BY_UUID: "study",
  GET_BY_SPONSOR: "studies-by-sponsor",
  GET_BY_DEPARTMENT: "studies-by-department",
  CREATE: "create-study",
  UPDATE: "update-study",
  DELETE: "delete-study",
  GET_PATIENTS_PER_STUDY: "patients-per-study",
  GET_PATIENTS_PER_STATUS: "patients-per-status",
  GET_PATIENT_ENROLLMENTS: "patient-enrollments",
  GET_TEAM_MEMBERS: "team-members",
  GET_INVITATIONS_BY_DEPARTMENT: "study-invitations-by-department",
};

export const GLOBAL_STUDY_KEYS = {
  GET_ALL: 'study/getAll',
  GET_BY_UUID: 'Globalstudy/getStudiesByGlobalstudyUuid',
  CREATE: 'study/create',
  UPDATE: 'study/update',
  DELETE: 'study/delete',
  GET_PATIENTS_PER_STUDY: 'study/studies/kpi/patients-per-study/',
  GET_PATIENTS_PER_STATUS: 'study/studies/{studyUuid}/kpi/patients-per-status/',
  GET_PATIENT_ENROLLMENTS: 'study/enrollments/by-patient',
  GET_TEAM_MEMBERS: '/study/studies/{studyUuid}/team-members/',
};

export const VISIT_KEYS = {
  GET_ALL: 'visit/getAll',
  GET_BY_UUID: 'visit/getByUuid',
  GET_BY_STUDY: 'visit/getByStudy',
  GET_BY_ENROLLMENT: 'visit/getByEnrollment',
  CREATE: 'visit/create',
  UPDATE: 'visit/update',
  DELETE: 'visit/delete',
  GET_TEMPLATES: 'visit/getTemplates',
  GET_TEMPLATES_BY_STUDY: 'visit/getTemplatesByStudy',
  GET_TEMPLATE_BY_UUID: 'visit/getTemplateByUuid',
};

export const SUBMISSION_KEYS = {
  LIST: '/form/submissions/',
  CREATE: '/form/submissions/',
  READ: '/form/submissions/{uuid}/',
  UPDATE: '/form/submissions/{uuid}/',
  DELETE: '/form/submissions/{uuid}/',
  BY_FORM: '/form/submissions/form/{uuid}/',
  BY_PATIENT: '/form/submissions/patient/{patient_uuid}/',
  PATIENT_ASSIGNED_FORMS: '/form/submissions/patient/{patient_uuid}/assigned-forms/',
  UPDATE_BY_FORM_PATIENT: '/form/submissions/update/form/{form_uuid}/patient/{patient_uuid}/',
  DELETE_BY_FORM_PATIENT: '/form/submissions/delete/form/{form_uuid}/patient/{patient_uuid}/',
  PARTIAL_UPDATE: '/form/submissions/{uuid}/',
  FINALIZE: '/form/submissions/finalize/',
};


export const SNIPPET_KEYS = {
  GET_ALL: 'snippet/getAll',
  GET_BY_UUID: 'snippet/getByUuid',
  CREATE: 'snippet/create',
  UPDATE: 'snippet/update',
  PARTIAL_UPDATE: 'snippet/partial_update',
  DELETE: 'snippet/delete',
};

export const TAG_KEYS = {
  LIST: '/form/tags/',
  CREATE: '/form/tags/',
  READ: '/form/tags/{uuid}/',
  UPDATE: '/form/tags/{uuid}/',
  DELETE: '/form/tags/{uuid}/',
} as const;


export const HOLISTIC_FORM_KEYS = {
  LIST: 'holistic-forms-list',
  DETAIL: (uuid: string) => ['holistic-form', uuid],
} as const;

export const PATIENT_KEYS = {
  GET_ALL: "patient/getAll",
  GET_BY_NHS_NUMBER: "patient/getByNhsNumber",
  GET_Allergies_BY_NHS_NUMBER: "patient/getAllergiesByNhsNumber",
  GET_TODAY_VISITS: "patient/getTodayVisits",
  GET_VISITS_BY_DATE: "patient/getVisitsByDate",
  GET_VISITS: "patient/getVisits", // New key for the unified visits endpoint
  GET_REGISTRATION_STATUS_LOGS: "patient/getRegistrationStatusLogs",
  CREATE: "patient/create",
  UPDATE: "patient/update",
  PARTIAL_UPDATE: "patient/partialUpdate",
  DELETE: "patient/delete",
  GET_ACCESS_LOGS: "patient/accessLogs",
  GET_ACCESS_LOGS_BY_UUID: "patient/accessLogsByUuid",
  GET_MY_ACCESS_LOGS: "patient/myAccessLogs",
  GET_MEDICAL_HISTORY: "patient/getMedicalHistory",
  GET_CONCOMITANT_MEDICATIONS: "patient/getConcomitantMedications",
  SEARCH: "patient/search", // New key for the search endpoint
} as const;

export const Prescription_KEYS = {
  GET_ALL:'prescription/getAll',
  GET_ALL_By_Patient: 'prescription/getAllByPatient',
  CREATE: 'prescription/create',
  UPDATE: 'prescription/update',
} as const;

export const SponsorOrg_KEYS = {
  GET_ALL: 'sponsorOrg/getAll',
  GET_LIST: 'sponsorOrg/getLIST',
  GET_BY_ID: 'sponsorOrg/getById',
  CREATE: 'sponsorOrg/create',
  Update: 'sponsorOrg/update'
} as const;

export const LIVE_CHAT_KEYS = {
  GET_ALL: "livechat/getAll",
  GET_BY_UUID: "livechat/getByUuid",
  CREATE: "livechat/create",
  UPDATE: "livechat/update",
  PARTIAL_UPDATE: "livechat/partialUpdate",
  DELETE: "livechat/delete",
  CLOSE: "livechat/close",
  REOPEN: "livechat/reopen",
  GET_MESSAGES: "livechat/getMessages",
  SEND_MESSAGE: "livechat/sendMessage",
  GET_ANALYTICS: "livechat/getAnalytics",
  GET_NOTIFICATIONS: "livechat/getNotifications",
  GET_NOTIFICATION_BY_ID: "livechat/getNotificationById",
  MARK_NOTIFICATION_READ: "livechat/markNotificationRead",
  MARK_ALL_NOTIFICATIONS_READ: "livechat/markAllNotificationsRead",
  FILTER_BY_STATUS: "livechat/filterByStatus",
  SEARCH: "livechat/search",
  GET_ACTIVE: "livechat/getActive",
  GET_INACTIVE: "livechat/getInactive",
} as const;

export const REPORT_KEYS = {
  GET_ALL: 'report/getAll',
  GET_BY_UUID: 'report/getByUuid',
  GET_BY_PATIENT: 'report/getByPatient',
  CREATE: 'report/create',
  UPDATE: 'report/update',
  DELETE: 'report/delete',
  GET_CONTENT: 'report/getContent',
  GET_TYPES: 'report/getTypes',
  GET_STATS: 'report/getStats',
  // Review Reports
  GET_ALL_REVIEWS: 'reviewreport/getAll',
  GET_BY_UUID_REVIEW: 'reviewreport/getByUuid',
  CREATE_REVIEW: 'reviewreport/create',
  UPDATE_REVIEW: 'reviewreport/update',
  DELETE_REVIEW: 'reviewreport/delete',
  GET_BY_REPORT: 'reviewreport/getByReport',
} as const;
