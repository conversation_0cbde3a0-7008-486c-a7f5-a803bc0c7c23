/* Patient Details Page Styles */
.patient-details-page {
  padding: 30px;
  min-height: calc(100vh - 80px);
  background-color: #f5f7fa;
  font-family: var(--font-primary);
}

/* Page Header */
.patient-details-header {
  margin-bottom: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(55, 183, 195, 0.08);
  color: var(--color-purple-1);
  border: 1px solid rgba(55, 183, 195, 0.2);
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.back-button:hover {
  background: rgba(55, 183, 195, 0.15);
  border-color: rgba(55, 183, 195, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.15);
  color: var(--color-purple-1);
  text-decoration: none;
}

.patient-details-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0;
  flex: 1;
}

/* Patient Info Card */
.patient-info-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  border: 1px solid rgba(55, 183, 195, 0.1);
  transition: all 0.3s ease;
}

.patient-info-card:hover {
  box-shadow: 0 12px 32px rgba(55, 183, 195, 0.12),
              0 4px 8px rgba(55, 183, 195, 0.06);
  transform: translateY(-2px);
}

.patient-info-card h2 {
  font-size: 20px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.info-item .label {
  font-size: 13px;
  font-weight: 600;
  color: var(--color-light-1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item .value {
  font-size: 15px;
  font-weight: 600;
  color: var(--color-dark-1);
  padding: 8px 12px;
  background: rgba(55, 183, 195, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(55, 183, 195, 0.1);
}

/* Patient Tabs */
.patient-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 30px;
  background: #ffffff;
  border-radius: 12px;
  padding: 6px;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.05);
  border: 1px solid rgba(55, 183, 195, 0.1);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.patient-tabs::-webkit-scrollbar {
  display: none;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: transparent;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-light-1);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: fit-content;
}

.tab-button:hover {
  background: rgba(55, 183, 195, 0.08);
  color: var(--color-purple-1);
}

.tab-button.active {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, #2d919a 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.tab-button svg {
  width: 16px;
  height: 16px;
}

/* Tab Content Container */
.tab-content-container {
  background: #ffffff;
  border-radius: 16px;
  padding: 30px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  border: 1px solid rgba(55, 183, 195, 0.1);
  transition: all 0.3s ease;
  min-height: 400px;
}

.tab-content-container:hover {
  box-shadow: 0 12px 32px rgba(55, 183, 195, 0.12),
              0 4px 8px rgba(55, 183, 195, 0.06);
}

/* Tab Content */
.tab-content {
  animation: fadeInUp 0.3s ease-out;
}

.tab-content h3 {
  font-size: 20px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 20px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Loading States */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--color-light-1);
  font-size: 16px;
  font-weight: 500;
}

.loading::before {
  content: "";
  width: 20px;
  height: 20px;
  border: 2px solid rgba(55, 183, 195, 0.2);
  border-top: 2px solid var(--color-purple-1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

.loading-message {
  text-align: center;
  padding: 40px 20px;
  color: var(--color-light-1);
}

.loading-message .note {
  font-size: 14px;
  font-style: italic;
  margin-top: 10px;
  color: var(--color-light-1);
}

/* No Data States */
.no-data {
  text-align: center;
  padding: 40px 20px;
  color: var(--color-light-1);
  font-size: 16px;
  font-style: italic;
  background: rgba(55, 183, 195, 0.03);
  border-radius: 12px;
  border: 1px dashed rgba(55, 183, 195, 0.2);
}

/* Lists Styling */
.allergies-list,
.warnings-list,
.medical-history-list,
.medications-list,
.forms-list,
.symptoms-list,
.prescriptions-list,
.visits-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Item Cards */
.allergy-item,
.warning-item,
.history-item,
.medication-item,
.form-item,
.symptom-item,
.prescription-item,
.visit-item {
  background: rgba(55, 183, 195, 0.03);
  border: 1px solid rgba(55, 183, 195, 0.1);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.allergy-item:hover,
.warning-item:hover,
.history-item:hover,
.medication-item:hover,
.form-item:hover,
.symptom-item:hover,
.prescription-item:hover,
.visit-item:hover {
  background: rgba(55, 183, 195, 0.05);
  border-color: rgba(55, 183, 195, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.08);
}

/* Item Headers */
.allergy-name,
.warning-name,
.condition-name,
.medication-name,
.form-name,
.symptom-name,
.prescription-name,
.visit-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin-bottom: 8px;
}

/* Item Details */
.warning-header,
.condition-header,
.medication-header,
.form-header,
.symptom-header,
.prescription-header,
.visit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.warning-description,
.condition-dates,
.medication-details,
.form-details,
.symptom-details,
.prescription-details,
.visit-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  font-size: 14px;
  color: var(--color-light-1);
}

.condition-dates span,
.medication-details span,
.form-details span,
.symptom-details span,
.prescription-details span,
.visit-details span {
  background: rgba(55, 183, 195, 0.08);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
}

/* Status Badges */
.severity-badge,
.status-badge,
.priority-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Severity Badges */
.severity-badge.severity-low {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.severity-badge.severity-medium {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.severity-badge.severity-high {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Status Badges */
.status-badge.current,
.status-badge.continuing,
.status-badge.completed {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-badge.resolved,
.status-badge.discontinued {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.status-badge.draft,
.status-badge.pending {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

/* Action Buttons */
.view-submission-btn,
.view-query-btn,
.preview-form-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, #2d919a 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.25);
  text-decoration: none;
}

.view-submission-btn:hover,
.view-query-btn:hover,
.preview-form-btn:hover {
  background: linear-gradient(135deg, #2d919a 0%, #237a82 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(55, 183, 195, 0.4);
  color: white;
  text-decoration: none;
}

.form-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.final-submission-badge {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Query System Styles */
.query-tabs {
  display: flex;
  gap: 4px;
  margin-bottom: 20px;
  background: rgba(55, 183, 195, 0.05);
  border-radius: 8px;
  padding: 4px;
}

.query-tab {
  flex: 1;
  padding: 10px 16px;
  background: transparent;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-light-1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.query-tab:hover {
  background: rgba(55, 183, 195, 0.1);
  color: var(--color-purple-1);
}

.query-tab.active {
  background: var(--color-purple-1);
  color: white;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.3);
}

.queries-table-container {
  overflow-x: auto;
  border-radius: 12px;
  border: 1px solid rgba(55, 183, 195, 0.1);
}

.queries-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.queries-table thead {
  background: linear-gradient(135deg, rgba(55, 183, 195, 0.08) 0%, rgba(55, 183, 195, 0.12) 100%);
}

.queries-table th {
  padding: 16px 20px;
  text-align: left;
  font-weight: 600;
  color: var(--color-dark-1);
  border-bottom: 2px solid rgba(55, 183, 195, 0.15);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
}

.queries-table td {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(55, 183, 195, 0.1);
  color: var(--color-dark-1);
  vertical-align: middle;
}

.queries-table tbody tr:hover {
  background: rgba(55, 183, 195, 0.03);
}

.query-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Priority Badges */
.priority-badge.priority-low {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.priority-badge.priority-medium {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.priority-badge.priority-high {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Query Detail View */
.query-detail-view {
  margin-top: 30px;
  padding: 24px;
  background: rgba(55, 183, 195, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(55, 183, 195, 0.1);
}

.query-detail-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.query-detail-header h4 {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0;
  flex: 1;
}

.query-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.08);
  border: 1px solid rgba(55, 183, 195, 0.1);
}

.query-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  gap: 20px;
}

.query-info {
  flex: 1;
}

.query-title {
  font-size: 18px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 8px 0;
}

.query-description {
  font-size: 15px;
  color: var(--color-light-1);
  line-height: 1.5;
  margin: 0;
}

.query-meta {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

.query-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item .label {
  font-size: 12px;
  font-weight: 600;
  color: var(--color-light-1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item .value {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-dark-1);
}

/* Responses Section */
.responses-section {
  margin-bottom: 24px;
}

.responses-section h5 {
  font-size: 16px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 16px 0;
}

.response-item {
  background: rgba(55, 183, 195, 0.03);
  border: 1px solid rgba(55, 183, 195, 0.1);
  border-radius: 10px;
  padding: 16px;
  margin-bottom: 12px;
}

.response-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.response-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.responder-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-dark-1);
}

.clarification-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
  padding: 2px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.response-date {
  font-size: 12px;
  color: var(--color-light-1);
}

.response-message {
  font-size: 14px;
  color: var(--color-dark-1);
  line-height: 1.5;
  margin: 0;
}

/* Response Form */
.response-form {
  margin-bottom: 20px;
}

.response-input-container {
  margin-bottom: 16px;
}

.response-input-container textarea {
  width: 100%;
  padding: 16px;
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 10px;
  font-size: 14px;
  color: var(--color-dark-1);
  background: rgba(55, 183, 195, 0.02);
  transition: all 0.3s ease;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
}

.response-input-container textarea:focus {
  outline: none;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
  background: #ffffff;
}

.response-options {
  margin-top: 12px;
}

.clarification-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.clarification-toggle input[type="checkbox"] {
  width: 16px;
  height: 16px;
  accent-color: var(--color-purple-1);
}

.toggle-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-dark-1);
}

.submit-response-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, #2d919a 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.25);
}

.submit-response-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2d919a 0%, #237a82 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(55, 183, 195, 0.4);
}

.submit-response-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(55, 183, 195, 0.1);
}

/* Resolve Section */
.resolve-section {
  border-top: 1px solid rgba(55, 183, 195, 0.1);
  padding-top: 20px;
}

.resolve-query-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.25);
}

.resolve-query-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #15803d 0%, #166534 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
}

.resolve-query-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.1);
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: #ffffff;
  border-radius: 16px;
  padding: 30px;
  max-width: 500px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(55, 183, 195, 0.1);
}

.modal-content h3 {
  font-size: 20px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0 0 16px 0;
}

.modal-content p {
  font-size: 15px;
  color: var(--color-light-1);
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.modal-content textarea {
  width: 100%;
  padding: 16px;
  border: 1px solid rgba(55, 183, 195, 0.15);
  border-radius: 10px;
  font-size: 14px;
  color: var(--color-dark-1);
  background: rgba(55, 183, 195, 0.02);
  transition: all 0.3s ease;
  resize: vertical;
  min-height: 120px;
  font-family: inherit;
  margin-bottom: 20px;
}

.modal-content textarea:focus {
  outline: none;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
  background: #ffffff;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.cancel-btn {
  padding: 12px 20px;
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.2);
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.cancel-btn:hover {
  background: rgba(107, 114, 128, 0.15);
  border-color: rgba(107, 114, 128, 0.3);
}

.resolve-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #16a34a 0%, #15803d 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(34, 197, 94, 0.25);
}

.resolve-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #15803d 0%, #166534 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(34, 197, 94, 0.4);
}

.resolve-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.1);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .info-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }
  
  .query-details {
    grid-template-columns: 1fr;
  }
  
  .query-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .query-meta {
    align-items: flex-start;
    flex-direction: row;
    gap: 12px;
  }
}

@media (max-width: 991px) {
  .patient-details-page {
    padding: 20px;
  }
  
  .patient-details-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .patient-details-header h1 {
    font-size: 24px;
  }
  
  .patient-info-card {
    padding: 20px;
    margin-bottom: 20px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .patient-tabs {
    margin-bottom: 20px;
    padding: 4px;
  }
  
  .tab-button {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .tab-content-container {
    padding: 20px;
  }
  
  .queries-table-container {
    overflow-x: scroll;
  }
  
  .queries-table {
    min-width: 800px;
  }
  
  .query-actions {
    flex-direction: column;
    gap: 6px;
  }
  
  .modal-content {
    padding: 24px;
    margin: 20px;
  }
}

@media (max-width: 767px) {
  .patient-details-page {
    padding: 15px;
  }
  
  .patient-details-header h1 {
    font-size: 20px;
  }
  
  .back-button {
    padding: 10px 16px;
    font-size: 13px;
  }
  
  .patient-info-card {
    padding: 16px;
  }
  
  .patient-info-card h2 {
    font-size: 18px;
  }
  
  .patient-tabs {
    flex-wrap: wrap;
    gap: 2px;
  }
  
  .tab-button {
    flex: 1 0 auto;
    min-width: 120px;
    padding: 8px 10px;
    font-size: 12px;
  }
  
  .tab-content-container {
    padding: 16px;
    min-height: 300px;
  }
  
  .tab-content h3 {
    font-size: 18px;
  }
  
  .allergy-item,
  .warning-item,
  .history-item,
  .medication-item,
  .form-item,
  .symptom-item,
  .prescription-item,
  .visit-item {
    padding: 16px;
  }
  
  .warning-header,
  .condition-header,
  .medication-header,
  .form-header,
  .symptom-header,
  .prescription-header,
  .visit-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .form-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .view-submission-btn,
  .view-query-btn,
  .preview-form-btn {
    width: 100%;
    justify-content: center;
    padding: 10px 16px;
  }
  
  .query-detail-view {
    padding: 16px;
  }
  
  .query-card {
    padding: 16px;
  }
  
  .query-details {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .response-item {
    padding: 12px;
  }
  
  .response-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .submit-response-btn,
  .resolve-query-btn {
    width: 100%;
    justify-content: center;
  }
  
  .modal-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .cancel-btn,
  .resolve-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .patient-details-page {
    padding: 12px;
  }
  
  .tab-button {
    min-width: 100px;
    padding: 6px 8px;
    font-size: 11px;
  }
  
  .tab-button svg {
    width: 14px;
    height: 14px;
  }
  
  .queries-table th,
  .queries-table td {
    padding: 12px 8px;
    font-size: 12px;
  }
  
  .severity-badge,
  .status-badge,
  .priority-badge {
    font-size: 10px;
    padding: 3px 8px;
  }
  
  .condition-dates span,
  .medication-details span,
  .form-details span,
  .symptom-details span,
  .prescription-details span,
  .visit-details span {
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .final-submission-badge,
  .clarification-badge {
    font-size: 10px;
    padding: 2px 6px;
  }
}

/* Focus states for accessibility */
.back-button:focus,
.tab-button:focus,
.view-submission-btn:focus,
.view-query-btn:focus,
.preview-form-btn:focus,
.query-tab:focus,
.submit-response-btn:focus,
.resolve-query-btn:focus,
.cancel-btn:focus,
.resolve-btn:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

.response-input-container textarea:focus,
.modal-content textarea:focus {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .patient-info-card,
  .tab-content-container,
  .query-detail-view,
  .query-card {
    border: 2px solid var(--color-dark-1);
  }
  
  .back-button,
  .tab-button,
  .view-submission-btn,
  .view-query-btn,
  .preview-form-btn,
  .submit-response-btn,
  .resolve-query-btn {
    border: 2px solid var(--color-dark-1);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .patient-details-page {
    background: white;
    padding: 0;
  }
  
  .back-button,
  .view-submission-btn,
  .view-query-btn,
  .preview-form-btn,
  .submit-response-btn,
  .resolve-query-btn,
  .response-form,
  .resolve-section {
    display: none;
  }
  
  .patient-tabs {
    display: none;
  }
  
  .tab-content-container {
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .query-detail-view {
    page-break-inside: avoid;
  }
}
