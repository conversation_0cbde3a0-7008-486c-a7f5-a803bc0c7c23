import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type { Snippet, SnippetCreateData } from "@/services/api/types";
import {
  getAllSnippets,
  getSnippetByUuid,
  createSnippet,
  updateSnippet,
  partialUpdateSnippet,
  deleteSnippetByUuid,
} from "@/services/api/snippet.service";
import { SNIPPET_KEYS } from "./keys";

export const useSnippetsQuery = () => {
  return useQuery<Snippet[], Error>({
    queryKey: [SNIPPET_KEYS.GET_ALL],
    queryFn: getAllSnippets,
  });
};

export const useSnippetQuery = (uuid: string) => {
  return useQuery<Snippet, Error>({
    queryKey: [SNIPPET_KEYS.GET_BY_UUID, uuid],
    queryFn: () => getSnippetByUuid(uuid),
    enabled: !!uuid,
  });
};

export const useCreateSnippetMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Snippet, Error, SnippetCreateData>({
    mutationFn: createSnippet,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [SNIPPET_KEYS.GET_ALL] });
    },
  });
};

export const useUpdateSnippetMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Snippet, Error, { uuid: string; data: Partial<Snippet> }>({
    mutationFn: ({ uuid, data }) => updateSnippet(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({ queryKey: [SNIPPET_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [SNIPPET_KEYS.GET_BY_UUID, uuid] });
    },
  });
};

export const usePartialUpdateSnippetMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<Snippet, Error, { uuid: string; data: Partial<Snippet> }>({
    mutationFn: ({ uuid, data }) => partialUpdateSnippet(uuid, data),
    onSuccess: (_, { uuid }) => {
      queryClient.invalidateQueries({ queryKey: [SNIPPET_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [SNIPPET_KEYS.GET_BY_UUID, uuid] });
    },
  });
};

export const useDeleteSnippetMutation = () => {
  const queryClient = useQueryClient();
  return useMutation<void, Error, string>({
    mutationFn: (uuid) => deleteSnippetByUuid(uuid),
    onSuccess: (_, uuid) => {
      queryClient.invalidateQueries({ queryKey: [SNIPPET_KEYS.GET_ALL] });
      queryClient.invalidateQueries({ queryKey: [SNIPPET_KEYS.GET_BY_UUID, uuid] });
    },
  });
}