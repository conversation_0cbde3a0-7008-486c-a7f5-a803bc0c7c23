import { useState, useEffect } from "react";

type PreloaderProps = object

const Preloader: React.FC<PreloaderProps> = () => {
  const [preloaderDisable, setPreloaderDisable] = useState<boolean>(false);

  useEffect((): void => {
    setPreloaderDisable(true);
  }, []);

  return (
    <div className="preloader js-preloader">
      <div
        className="preloader__bg"
        style={preloaderDisable ? { transform: "scale(1,0)" } : {}}
      ></div>
    </div>
  );
};

export default Preloader;
