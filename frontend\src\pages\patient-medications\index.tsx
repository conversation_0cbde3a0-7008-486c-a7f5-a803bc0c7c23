import { useState } from "react";
import { Search, Plus, Edit, Trash2 } from "lucide-react";
import Preloader from "@/components/common/Preloader";
import useSelectedPatientStore from "@/store/SelectedPatientState";
import "./patient-medications.css";
import { 
  usePatientConcomitantMedicationsQuery,
  useCreateConcomitantMedicationMutation,
  useUpdateConcomitantMedicationMutation,
  useDeleteConcomitantMedicationMutation
} from "@/hooks/patient.query";
import type { ConcomitantMedication, CreateConcomitantMedicationData } from "@/services/api/types";
import EditMedicationModal from "@/components/modal/EditMedicationModal";
import AddMedicationModal from "@/components/modal/AddMedicationModal";
import MedicationTable, { TableColumn, TableAction } from "@/components/MedicationTable";

export default function PatientMedications() {
  const { selectedPatient, isLoading, error } = useSelectedPatientStore();
  const { data: concomitantMedications, isLoading: isConcomitantMedicationsLoading, refetch: refetchMedications } = usePatientConcomitantMedicationsQuery(selectedPatient?.uuid || "");
  
  const createConcomitantMedicationMutation = useCreateConcomitantMedicationMutation();
  const updateConcomitantMedicationMutation = useUpdateConcomitantMedicationMutation();
  const deleteConcomitantMedicationMutation = useDeleteConcomitantMedicationMutation();

  const [isAddMedicationModalOpen, setIsAddMedicationModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedMedication, setSelectedMedication] = useState<ConcomitantMedication | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 5;

  const handleAddMedication = () => {
    setIsAddMedicationModalOpen(true);
  };

  const handleSubmitMedication = async (newMedication: CreateConcomitantMedicationData) => {
    try {
      const medicationData = { ...newMedication };
      
      if (medicationData.end_date === "") {
        delete medicationData.end_date;
      }
      
      await createConcomitantMedicationMutation.mutateAsync(medicationData);
      setIsAddMedicationModalOpen(false);
      refetchMedications();
    } catch (error) {
      console.error("Error creating medication:", error);
    }
  };

  const handleEditMedication = (medication: ConcomitantMedication) => {
    setSelectedMedication(medication);
    setIsEditModalOpen(true);
  };

  const handleSaveEditMedication = async (updatedMedication: {
    medication: string;
    non_drug_therapy?: string;
    indication: string;
    dose: number;
    dose_units: string;
    schedule: string;
    dose_form: string;
    route: string;
    start_date: string;
    end_date?: string;
    is_baseline: boolean;
    is_continuing: boolean;
  }) => {
    if (!selectedMedication || !selectedPatient?.uuid) return;

    try {
      const medicationData = { ...updatedMedication };
      
      if (medicationData.end_date === "") {
        delete medicationData.end_date;
      }
      
      await updateConcomitantMedicationMutation.mutateAsync({
        uuid: selectedMedication.uuid,
        data: medicationData
      });
      setIsEditModalOpen(false);
      setSelectedMedication(null);
      refetchMedications();
    } catch (error) {
      console.error("Error updating medication:", error);
    }
  };

  const handleDeleteMedication = async (medicationUuid: string) => {
    if (!selectedPatient?.uuid) return;

    if (window.confirm("Are you sure you want to delete this medication?")) {
      try {
        await deleteConcomitantMedicationMutation.mutateAsync({
          uuid: medicationUuid,
          patientUuid: selectedPatient.uuid
        });
        refetchMedications();
      } catch (error) {
        console.error("Error deleting medication:", error);
      }
    }
  };

  if (isLoading) {
    return <Preloader />;
  }

  if (error || !selectedPatient) {
    return <div>Error loading patient details</div>;
  }

  const medicationsData = concomitantMedications?.results || [];
  
  // Filter medications based on search term
  const filteredMedications = medicationsData.filter(med => 
    med.medication.toLowerCase().includes(searchTerm.toLowerCase()) ||
    med.indication.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (med.non_drug_therapy && med.non_drug_therapy.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Pagination
  const totalPages = Math.ceil(filteredMedications.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentMedications = filteredMedications.slice(startIndex, endIndex);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'numeric',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Transform data for MedicationTable
  const tableData = currentMedications.map(medication => ({
    medication: medication.medication,
    indication: medication.indication,
    dose: `${medication.dose} ${medication.dose_units_display}`,
    schedule: medication.schedule_display,
    form: medication.dose_form_display,
    route: medication.route_display,
    startDate: formatDate(medication.start_date),
    endDate: medication.end_date ? formatDate(medication.end_date) : '-',
    status: medication.is_continuing ? 'Continue' : 'Discontinued',
    uuid: medication.uuid
  }));

  // Define table columns
  const columns: TableColumn[] = [
    { key: 'medication', label: 'Medication' },
    { key: 'indication', label: 'Indication' },
    { key: 'dose', label: 'Dose' },
    { key: 'schedule', label: 'Schedule' },
    { key: 'form', label: 'Form' },
    { key: 'route', label: 'Route' },
    { key: 'startDate', label: 'Start Date' },
    { key: 'endDate', label: 'End Date' },
    { key: 'status', label: 'Status' }
  ];

  // Define table actions
  const actions: TableAction[] = [
    {
      type: 'edit',
      icon: <Edit size={16} />,
      label: 'Edit',
      onClick: (row) => {
        const medication = currentMedications.find(med => med.uuid === row.uuid);
        if (medication) {
          handleEditMedication(medication);
        }
      }
    },
    {
      type: 'delete',
      icon: <Trash2 size={16} />,
      label: 'Delete',
      onClick: (row) => handleDeleteMedication(row.uuid)
    }
  ];

  return (
    <div className="medications-page">
      {/* Page Content */}
      <div className="page-content">
        <div className="page-header">
          <h1 className="page-title">Concomitant Medications</h1>
        </div>

        <div className="controls-section">
          <div className="search-container">
            <div className="search-input-wrapper">
              <Search className="search-icon" size={20} />
              <input
                type="text"
                placeholder="Text Here"
                className="search-input"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1); // Reset to first page when searching
                }}
              />
            </div>
          </div>
          <button className="add-medication-btn" onClick={handleAddMedication}>
            <Plus size={20} />
            Add Medication
          </button>
        </div>

        {isConcomitantMedicationsLoading ? (
          <div className="loading-container">
            <Preloader />
          </div>
        ) : (
          <MedicationTable
            columns={columns}
            data={tableData}
            actions={actions}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
            loading={isConcomitantMedicationsLoading}
            emptyMessage={searchTerm ? 'No medications found matching your search.' : 'No concomitant medications recorded for this patient.'}
          />
        )}
      </div>

      {/* Modals */}
      <AddMedicationModal
        isOpen={isAddMedicationModalOpen}
        onClose={() => setIsAddMedicationModalOpen(false)}
        onSave={handleSubmitMedication}
        patientUuid={selectedPatient?.uuid || ""}
      />

      {selectedMedication && (
        <EditMedicationModal
          isOpen={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          onSave={handleSaveEditMedication}
          medicationData={selectedMedication}
        />
      )}
    </div>
  );
}
