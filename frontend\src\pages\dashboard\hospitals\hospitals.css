
/* Hospitals List Page Styles */
.hospitals-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    padding: 15px 30px 30px 30px; /* Changed top padding */
    margin-bottom: 30px;
    width: 100%;
}

.hospitals-header {
    display: flex;
    flex-direction: column;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e5e7eb;
}

.hospitals-title {
    margin-bottom: 10px;
}

.hospitals-title h1 {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.hospitals-subtitle {
    color: #4F547B;
    font-size: 16px;
    font-weight: 400;
    margin-top: 5px;
}

.hospitals-subtitle h6 {
    margin: 0;
    font-weight: 400;
}

.hospitals-search-container {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 30px;
}

.hospitals-search-box {
    position: relative;
    flex: 1;
    max-width: 550px;
    display: flex;
    align-items: center;
}

.hospitals-search-box .search-icon {
    position: absolute;
    left: 15px;
    color: #64748b;
    z-index: 1;
}

.hospitals-search-box .data-table-search {
    width: 100%;
}

.hospitals-search-box .data-table-search-input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: 1px solid #EDEDED !important;
    border-radius: 0; /* Changed */
    outline: none;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: none; /* Changed */
}

.hospitals-search-box .data-table-search-input:focus {
    border-color: #3EC1C9 !important;
    box-shadow: none; /* Changed */
}

.hospitals-add-button {
    min-width: 150px;
    height: 48px;
    background-color: #37B7C3;
    color: white !important;
    border: none;
    border-radius: 0; /* Changed */
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    padding: 0 20px;
    box-shadow: none; /* Changed */
}

.hospitals-add-button:hover {
    background-color: #2A9A9F;
    transform: none; /* Changed */
    box-shadow: none; /* Changed */
}

.hospitals-table-container {
    background-color: #ffffff;
    border-radius: 0; /* Changed */
    box-shadow: none; /* Changed */
    overflow: hidden;
    margin-bottom: 30px;
    border: 1px solid #e5e7eb;
    padding: 20px;
}

.hospitals-table-title {
    font-size: 18px;
    font-weight: 600;
    color: #2B3674;
    margin-bottom: 20px;
}

.title-Hospitals {
    font-size: 24px;
    font-weight: 600;
    color: #1a1a1a;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
    padding-bottom: 15px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .hospitals-container {
        padding: 25px;
    }
    
    .hospitals-search-container {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .hospitals-search-box {
        width: 100%;
        max-width: none;
    }
    
    .hospitals-add-button {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .hospitals-container {
        padding: 20px;
    }
    
    .hospitals-title h1 {
        font-size: 20px;
    }
    
    .hospitals-subtitle {
        font-size: 14px;
    }
}

@media (max-width: 576px) {
    .hospitals-container {
        padding: 15px;
    }
}
