import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  FileText, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  ArrowRight, 
  ArrowLeft,
  User,
  Calendar,
  Shield,
  ChevronRight
} from "lucide-react";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { 
  usePatientConsentDataQuery,
  useSubmitPatientConsentMutation
} from "@/hooks/consent.query";
import { ConsentQuestion } from "@/types/types";
import "./consent.css";

interface PatientConsentAnswer {
  question_id: string;
  answer: boolean;
}

const ConsentSection: React.FC = () => {
  const { data: currentUser } = useCurrentUserQuery();
  const patientIdentifier = (currentUser as any)?.patient_identifier || (currentUser as any)?.identifier || (currentUser as any)?.uuid || "";
  
  // Get patient's consent forms from the API
  const { data: patientConsentData, isLoading, error } = usePatientConsentDataQuery(patientIdentifier);
  
  const submitConsentMutation = useSubmitPatientConsentMutation();
  
  const [selectedConsentForm, setSelectedConsentForm] = useState<any>(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState<{ [questionId: string]: boolean }>({});
  const [isConsentStarted, setIsConsentStarted] = useState(false);
  const [isConsentCompleted, setIsConsentCompleted] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Check if any consent form is already completed
  useEffect(() => {
    if (patientConsentData?.consent_forms) {
      const completedConsent = patientConsentData.consent_forms.find(
        consent => consent.patient_consent?.consent_status === 'Consented'
      );
      if (completedConsent) {
        setSelectedConsentForm(completedConsent);
        setIsConsentCompleted(true);
      }
    }
  }, [patientConsentData]);

  // Handle case when patient identifier is not available
  if (!patientIdentifier) {
    return (
      <motion.div
        className="patclin-tab-content"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="consent-container">
          <div className="error-state">
            <AlertCircle size={48} />
            <h3>Patient Information Not Available</h3>
            <p>Unable to identify patient. Please contact support.</p>
          </div>
        </div>
      </motion.div>
    );
  }

  const handleStartConsent = (consentForm: any) => {
    setSelectedConsentForm(consentForm);
    setIsConsentStarted(true);
    setCurrentQuestionIndex(0);
    setAnswers({});
    setErrorMessage(null);
    setSuccessMessage(null);
  };

  const handleAnswerQuestion = (questionId: string, answer: boolean) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: answer
    }));
  };

  const handleNextQuestion = () => {
    if (currentQuestionIndex < (selectedConsentForm?.questions?.length || 0) - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
    }
  };

  const handlePreviousQuestion = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
    }
  };

  const handleSubmitConsent = async () => {
    if (!selectedConsentForm) return;

    const requiredQuestions = selectedConsentForm.questions?.filter((q: ConsentQuestion) => q.is_required) || [];
    const answeredRequiredQuestions = requiredQuestions.filter((q: ConsentQuestion) => 
      answers[q.uuid] !== undefined
    );

    if (answeredRequiredQuestions.length !== requiredQuestions.length) {
      setErrorMessage("Please answer all required questions before submitting.");
      return;
    }

    try {
      const answersArray: PatientConsentAnswer[] = Object.entries(answers).map(([questionId, answer]) => ({
        question_id: questionId,
        answer
      }));

      await submitConsentMutation.mutateAsync({
        patient_id: patientIdentifier,
        consent_form_id: selectedConsentForm.uuid,
        answers: answersArray
      });

      setIsConsentCompleted(true);
      setSuccessMessage("Consent submitted successfully!");
      setErrorMessage(null);
    } catch (error: any) {
      setErrorMessage(error.response?.data?.message || "Failed to submit consent. Please try again.");
      setSuccessMessage(null);
    }
  };

  const currentQuestion = selectedConsentForm?.questions?.[currentQuestionIndex];
  const totalQuestions = selectedConsentForm?.questions?.length || 0;
  const progressPercentage = totalQuestions > 0 ? ((currentQuestionIndex + 1) / totalQuestions) * 100 : 0;

  if (isLoading) {
    return (
      <motion.div
        className="patclin-tab-content"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="consent-container">
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <p>Loading consent forms...</p>
          </div>
        </div>
      </motion.div>
    );
  }

  if (error) {
    return (
      <motion.div
        className="patclin-tab-content"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="consent-container">
          <div className="error-state">
            <AlertCircle size={48} />
            <h3>Error Loading Consent Forms</h3>
            <p>Unable to load your consent forms. Please try again later.</p>
            {error && (
              <details style={{ marginTop: '10px', fontSize: '12px', color: '#666' }}>
                <summary>Error Details</summary>
                <pre>{JSON.stringify(error, null, 2)}</pre>
              </details>
            )}
          </div>
        </div>
      </motion.div>
    );
  }

  // Handle case when data is not available
  if (!patientConsentData) {
    return (
      <motion.div
        className="patclin-tab-content"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="consent-container">
          <div className="error-state">
            <AlertCircle size={48} />
            <h3>No Data Available</h3>
            <p>Unable to retrieve consent form data. Please try again later.</p>
          </div>
        </div>
      </motion.div>
    );
  }

  const consentForms = patientConsentData?.consent_forms || [];
  const patientName = patientConsentData?.patient_name || (currentUser?.first_name && currentUser?.last_name 
    ? `${currentUser.first_name} ${currentUser.last_name}`
    : 'Patient');

  return (
    <motion.div
      className="patclin-tab-content"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="consent-container">
        <div className="consent-header">
          <h2 className="patclin-section-title">
            <Shield size={24} />
            Consent Management
          </h2>
          <p className="consent-description">
            Review and provide consent for research participation and data sharing.
          </p>
        </div>

        {/* Messages */}
        {errorMessage && (
          <div className="consent-alert consent-alert-error">
            <AlertCircle size={16} />
            {errorMessage}
          </div>
        )}
        {successMessage && (
          <div className="consent-alert consent-alert-success">
            <CheckCircle size={16} />
            {successMessage}
          </div>
        )}

        {/* Consent Forms Selection */}
        {!selectedConsentForm && !isConsentStarted && consentForms.length > 0 && (
          <div className="consent-forms-section">
            <div className="patient-info">
              <h3>Welcome, {patientName}</h3>
              <p>You have {consentForms.length} consent form(s) to review.</p>
            </div>
            
            <div className="consent-forms-grid">
              {consentForms.map((consentForm) => (
                <div 
                  key={consentForm.uuid} 
                  className={`consent-form-card ${consentForm.patient_consent?.consent_status === 'Consented' ? 'completed' : ''}`}
                >
                  <div className="consent-form-header">
                    <FileText size={24} />
                    <div className="consent-form-info">
                      <h4>{consentForm.name}</h4>
                      <div className="consent-form-meta">
                        <span className="version-badge">v{consentForm.version}</span>
                        <span className="questions-count">{consentForm.questions?.length || 0} questions</span>
                        {consentForm.patient_consent?.consent_status === 'Consented' && (
                          <span className="status-badge completed">Completed</span>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {consentForm.description && (
                    <p className="consent-form-description">{consentForm.description}</p>
                  )}
                  
                  <div className="consent-form-actions">
                    {consentForm.patient_consent?.consent_status === 'Consented' ? (
                      <div className="completion-info">
                        <CheckCircle size={16} />
                        <span>Completed on {new Date(consentForm.patient_consent.signed_at || '').toLocaleDateString()}</span>
                      </div>
                    ) : (
                      <button 
                        className="start-consent-btn"
                        onClick={() => handleStartConsent(consentForm)}
                        disabled={!consentForm.can_submit}
                      >
                        {consentForm.has_consent_record ? 'Continue Consent' : 'Start Consent'}
                        <ChevronRight size={16} />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Consent Questions Flow */}
        {selectedConsentForm && isConsentStarted && !isConsentCompleted && currentQuestion && (
          <div className="consent-questions-flow">
            <div className="consent-progress">
              <div className="progress-bar">
                <div 
                  className="progress-fill" 
                  style={{ width: `${progressPercentage}%` }}
                ></div>
              </div>
              <div className="progress-text">
                Question {currentQuestionIndex + 1} of {totalQuestions}
              </div>
            </div>

            <div className="question-card">
              <div className="question-header">
                <div className="question-number">Q{currentQuestionIndex + 1}</div>
                {currentQuestion.is_required && (
                  <div className="required-badge">Required</div>
                )}
              </div>
              
              <div className="question-content">
                <p className="question-text">{currentQuestion.question_text}</p>
              </div>

              <div className="answer-options">
                <div className="answer-option">
                  <input
                    type="radio"
                    id={`yes-${currentQuestion.uuid}`}
                    name={`answer-${currentQuestion.uuid}`}
                    checked={answers[currentQuestion.uuid] === true}
                    onChange={() => handleAnswerQuestion(currentQuestion.uuid, true)}
                  />
                  <label htmlFor={`yes-${currentQuestion.uuid}`} className="answer-label">
                    <CheckCircle size={20} />
                    <span>Yes</span>
                  </label>
                </div>

                <div className="answer-option">
                  <input
                    type="radio"
                    id={`no-${currentQuestion.uuid}`}
                    name={`answer-${currentQuestion.uuid}`}
                    checked={answers[currentQuestion.uuid] === false}
                    onChange={() => handleAnswerQuestion(currentQuestion.uuid, false)}
                  />
                  <label htmlFor={`no-${currentQuestion.uuid}`} className="answer-label">
                    <XCircle size={20} />
                    <span>No</span>
                  </label>
                </div>
              </div>
            </div>

            <div className="consent-navigation">
              <button 
                className="nav-btn prev-btn"
                onClick={handlePreviousQuestion}
                disabled={currentQuestionIndex === 0}
              >
                <ArrowLeft size={16} />
                Previous
              </button>
              
              {currentQuestionIndex < totalQuestions - 1 ? (
                <button 
                  className="nav-btn next-btn"
                  onClick={handleNextQuestion}
                  disabled={answers[currentQuestion.uuid] === undefined}
                >
                  Next
                  <ArrowRight size={16} />
                </button>
              ) : (
                <button 
                  className="nav-btn submit-btn"
                  onClick={handleSubmitConsent}
                  disabled={submitConsentMutation.isPending || answers[currentQuestion.uuid] === undefined}
                >
                  {submitConsentMutation.isPending ? "Submitting..." : "Submit Consent"}
                </button>
              )}
            </div>
          </div>
        )}

        {/* Consent Completed */}
        {isConsentCompleted && selectedConsentForm && (
          <div className="consent-completed">
            <div className="completed-icon">
              <CheckCircle size={48} />
            </div>
            <h3>Consent Completed Successfully!</h3>
            <p>You have successfully provided consent for {selectedConsentForm.name || 'the consent form'}.</p>
            <div className="completion-details">
              <div className="detail-item">
                <Calendar size={16} />
                <span>Completed on {new Date().toLocaleDateString()}</span>
              </div>
              <div className="detail-item">
                <User size={16} />
                <span>Patient: {currentUser?.first_name} {currentUser?.last_name}</span>
              </div>
            </div>
            <button 
              className="reset-consent-btn"
              onClick={() => {
                setSelectedConsentForm(null);
                setIsConsentStarted(false);
                setIsConsentCompleted(false);
                setCurrentQuestionIndex(0);
                setAnswers({});
                setErrorMessage(null);
                setSuccessMessage(null);
              }}
            >
              Back to Consent Forms
            </button>
          </div>
        )}

        {/* No Consent Forms */}
        {consentForms.length === 0 && (
          <div className="no-consent-forms">
            <div className="empty-state">
              <FileText size={48} />
              <h3>No Consent Forms Available</h3>
              <p>You don't have any consent forms to review at this time.</p>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default ConsentSection;
