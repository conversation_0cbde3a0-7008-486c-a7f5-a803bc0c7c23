/* Study Team specific styles */
.no-study-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background-color: #f9fafb;
  border-radius: 0; /* Changed */
  margin: 20px 0;
}

.no-study-selected .placeholder-icon {
  color: #9ca3af;
  margin-bottom: 20px;
}

.no-study-selected h3 {
  color: #374151;
  margin-bottom: 10px;
  font-size: 20px;
  font-weight: 600;
}

.no-study-selected p {
  color: #6b7280;
  font-size: 16px;
  margin: 0;
}

.add-users-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.cancel-btn {
  min-width: 120px;
  height: 48px;
  background-color: #6b7280;
  color: white !important;
  border: none;
  border-radius: 0; /* Changed */
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  text-decoration: none;
  padding: 0 20px;
  box-shadow: none; /* Changed */
}

.cancel-btn:hover {
  background-color: #4b5563;
  transform: none; /* Changed */
  box-shadow: none; /* Changed */
}

.user-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  accent-color: #37B7C3;
}

.users-add-button:disabled {
  background-color: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.users-add-button:disabled:hover {
  background-color: #d1d5db;
  transform: none;
  box-shadow: none; /* Ensured */
}

/* Responsive adjustments for study team */
@media (max-width: 768px) {
  .add-users-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .add-users-actions button {
    width: 100%;
  }
}
