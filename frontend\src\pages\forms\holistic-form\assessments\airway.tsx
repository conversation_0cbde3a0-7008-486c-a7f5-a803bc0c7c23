 
import { useState } from "react";
import airwayImage from "./static/images/added/airway.jpg";
import partialAirwayObsImage from "./static/images/partial_airway_obstruction.png";
import completeAirwayObsImage from "./static/images/complete_airway_obstruction.png";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyRadio from "@/components/NurtifyRadio";
import NurtifyCheckBox from "@/components/NurtifyCheckBox";
import NurtifyText from "@/components/NurtifyText";

type AirwayProps = object;

const Airway: React.FC<AirwayProps> = () => {
  const { assessment, setAssessment } = useHolisticFormStore();
  const [partialAirwayObstructionModal, setPartialAirwayObstructionModal] =
    useState<boolean>(false);
  const [completeAirwayObstructionModal, setCompleteAirwayObstructionModal] =
    useState<boolean>(false);
  const [interventions, setInterventions] = useState<string[]>(
    assessment?.airway?.interventions || []
  );

  const setAirway = (airwayData: any) => {
    setAssessment({
      ...assessment,
      airway: {
        ...assessment.airway,
        ...airwayData
      }
    });
  };

  const handleInterventionsCheckboxChange = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = event.target.value;
    const updatedInterventions = event.target.checked
      ? [...interventions, value]
      : interventions.filter((item) => item !== value);
    
    setInterventions(updatedInterventions);
    setAirway({ interventions: updatedInterventions });
  };

  return (
    <div className="block align-items-*-start flex-column flex-md-row p-4 gap-5">
      <div className="list-group">
        <div id="division-20">

            <div className="inlineBlock mb-4 headinqQuestion">
              <img
                src={airwayImage}
                className="imageEtiquette"
                alt="patient face image round"
              />
              <span className="mb-2 py-2 fs-2 text-start etiquetteHeadingForms">
                Airway
              </span>
            </div>

            <div className="list-group col-xl-6 col-lg-8 col-md-12">
              <NurtifyText label="Airway Assessment" className="mb-2 fw-bold" />

              <div onClick={() => {
                setAirway({
                  assessment: "patent",
                });
              }}>
                <NurtifyRadio
                  name="airway-assessment"
                  value="patent"
                  label="Patent"
                  checked={assessment?.airway?.assessment === "patent"}
                  onChange={() => {}}
                />
              </div>

              <div onClick={() => {
                setAirway({
                  assessment: "partial airway",
                });
                setPartialAirwayObstructionModal(true);
              }}>
                <NurtifyRadio
                  name="airway-assessment"
                  value="partial airway"
                  label="Partial airway obstruction (air entry is diminished and usually noisy)"
                  checked={assessment?.airway?.assessment === "partial airway"}
                  onChange={() => {}}
                />
              </div>

              <div onClick={() => {
                setAirway({
                  assessment: "Complete Airway",
                });
                setCompleteAirwayObstructionModal(true);
              }}>
                <NurtifyRadio
                  name="airway-assessment"
                  value="Complete Airway"
                  label="Complete Airway obstruction"
                  checked={assessment?.airway?.assessment === "Complete Airway"}
                  onChange={() => {}}
                />
              </div>
            </div>
          </div>
        </div>

        {assessment?.airway?.assessment === "partial airway" &&
        partialAirwayObstructionModal ? (
          <>
            <div id="division-21" className="options-modal">
              <div className="center">
                <NurtifyText label="Partial Airway Obstruction Instruction" className="fw-bold text-danger" />
                <img
                  style={{ maxWidth: "100%", maxHeight: "300px", objectFit: "contain" }}
                  src={partialAirwayObsImage}
                  alt="Partial airway obstruction diagram"
                  className="mt-4 mb-3"
                />
                <div className="d-flex justify-content-center gap-2">
                  <button
                    onClick={() => {
                      setPartialAirwayObstructionModal(false);
                    }}
                    className="my-primary-btn "
                  >
                    Proceed with partial airway obstruction
                  </button>

                  <button
                    className="btn btn-danger"
                    onClick={() => {
                        setAirway({ assessment: "" });
                        setPartialAirwayObstructionModal(false);
                      }
                    }
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </>
        ) : null}
        {assessment?.airway?.assessment === "partial airway" ? (
          <>
            <div
              className="list-group col-xl-6 col-lg-8 col-md-12 mt-2"
              id="division-23"
            >
              <NurtifyText label="What is the intervention performed?" className="mt-2 mb-2 fw-bold" />

              <NurtifyCheckBox
                id="Suction"
                label="Suction"
                checked={interventions.includes("Suction")}
                onChange={handleInterventionsCheckboxChange}
                value="Suction"
              />

              <NurtifyCheckBox
                id="Head tilt and chin lift"
                label="Head tilt and chin lift"
                checked={interventions.includes("Head tilt and chin lift")}
                onChange={handleInterventionsCheckboxChange}
                value="Head tilt and chin lift"
              />

              <NurtifyCheckBox
                id="Jaw thrust"
                label="Jaw thrust"
                checked={interventions.includes("Jaw thrust")}
                onChange={handleInterventionsCheckboxChange}
                value="Jaw thrust"
              />

              <NurtifyCheckBox
                id="Oropharyngeal airway"
                label="Oropharyngeal airway"
                checked={interventions.includes("Oropharyngeal airway")}
                onChange={handleInterventionsCheckboxChange}
                value="Oropharyngeal airway"
              />

              <NurtifyCheckBox
                id="Nasopharyngeal airway"
                label="Nasopharyngeal airway"
                checked={interventions.includes("Nasopharyngeal airway")}
                onChange={handleInterventionsCheckboxChange}
                value="Nasopharyngeal airway"
              />

              <NurtifyCheckBox
                id="Oxygen administration"
                label="Oxygen administration"
                checked={interventions.includes("Oxygen administration")}
                onChange={handleInterventionsCheckboxChange}
                value="Oxygen administration"
              />

              <NurtifyCheckBox
                id="bag-mask ventilation"
                label="bag-mask ventilation"
                checked={interventions.includes("bag-mask ventilation")}
                onChange={handleInterventionsCheckboxChange}
                value="bag-mask ventilation"
              />

              <NurtifyCheckBox
                id="Laryngeal mask airway LMA"
                label="Laryngeal mask airway LMA"
                checked={interventions.includes("Laryngeal mask airway LMA")}
                onChange={handleInterventionsCheckboxChange}
                value="Laryngeal mask airway LMA"
              />

              <NurtifyCheckBox
                id="The ProSeal LMA"
                label="The ProSeal LMA"
                checked={interventions.includes("The ProSeal LMA")}
                onChange={handleInterventionsCheckboxChange}
                value="The ProSeal LMA"
              />

              <NurtifyCheckBox
                id="Laryngeal tube"
                label="Laryngeal tube"
                checked={interventions.includes("Laryngeal tube")}
                onChange={handleInterventionsCheckboxChange}
                value="Laryngeal tube"
              />

              <NurtifyCheckBox
                id="i-gel"
                label="i-gel"
                checked={interventions.includes("i-gel")}
                onChange={handleInterventionsCheckboxChange}
                value="i-gel"
              />

              <NurtifyCheckBox
                id="Tracheal intubation"
                label="Tracheal intubation"
                checked={interventions.includes("Tracheal intubation")}
                onChange={handleInterventionsCheckboxChange}
                value="Tracheal intubation"
              />

              <NurtifyCheckBox
                id="Other Intervention"
                label="Other Intervention"
                checked={interventions.includes("Other Intervention")}
                onChange={handleInterventionsCheckboxChange}
                value="Other Intervention"
              />
            </div>
          </>
        ) : null}
        {assessment?.airway?.assessment === "Complete Airway" && 
        completeAirwayObstructionModal ? (
          <>
            <div className="options-modal" id="division-22">
              <div className="center">
                <NurtifyText label="Complete airway obstruction:" className="fw-bold text-danger" />

                <img
                  style={{ maxWidth: "100%", maxHeight: "300px", objectFit: "contain" }}
                  src={completeAirwayObsImage}
                  alt="Complete airway obstruction diagram"
                  className="mt-4 mb-3"
                />

                <div className="d-flex justify-content-center gap-2">
                  <button
                    className="my-primary-btn "
                    onClick={() =>
                      window.open(
                        "https://www.resus.org.uk/sites/default/files/2024-01/Adult%20Advanced%20Life%20Support%20Algorithm%202021%20Aug%202023.pdf",
                        "_blank",
                        "noopener,noreferrer"
                      )
                    }
                  >
                    ACLS
                  </button>

                  <button
                    className="btn btn-danger"
                    onClick={() => {
                      setCompleteAirwayObstructionModal(false);
                    }}
                    
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </>
        ) : null}
      </div>
    )
};

export default Airway;
