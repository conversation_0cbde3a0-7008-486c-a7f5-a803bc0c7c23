/* View Consent Modal Styles */
.view-consent-modal {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.view-consent-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 24px 32px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
}

.header-content {
  flex: 1;
}

.header-content h2 {
  margin: 0 0 12px 0;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.consent-meta {
  display: flex;
  gap: 12px;
  align-items: center;
}

.version-badge {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background-color: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.status-badge.inactive {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

/* Modal Content */
.view-consent-modal .modal-content {
  flex: 1;
  overflow-y: auto;
  padding: 32px;
}

.info-section,
.questions-section,
.audit-section {
  margin-bottom: 32px;
}

.info-section h3,
.questions-section h3,
.audit-section h3 {
  margin: 0 0 20px 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.info-item {
  background-color: #f9fafb;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.info-item label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.info-value {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #374151;
  font-weight: 500;
}

/* Questions Section */
.questions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-card {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.question-card:hover {
  border-color: #37b7c3;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.1);
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.question-number {
  background: linear-gradient(135deg, #37b7c3 0%, #2ea2b0 100%);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
}

.question-badges {
  display: flex;
  gap: 8px;
}

.required-badge {
  background-color: #ef4444;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.sequence-badge {
  background-color: #6b7280;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.question-content {
  margin-top: 12px;
}

.question-text {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #374151;
  line-height: 1.6;
}

.question-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 11px;
  color: #6b7280;
  padding-top: 8px;
  border-top: 1px solid #f3f4f6;
}

.question-id {
  font-family: monospace;
  background-color: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.no-questions {
  text-align: center;
  padding: 40px 20px;
  background-color: #f9fafb;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  color: #6b7280;
}

.no-questions p {
  margin: 0;
  font-style: italic;
}

/* Audit Section */
.audit-info {
  background-color: #f9fafb;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.audit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
  font-size: 14px;
}

.audit-item:last-child {
  border-bottom: none;
}

.audit-item strong {
  color: #374151;
  font-weight: 600;
}

.immutable-badge {
  background-color: #fef3c7;
  color: #92400e;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.version-info {
  color: #6b7280;
  font-size: 12px;
}

/* Modal Footer */
.view-consent-modal .modal-footer {
  padding: 24px 32px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
  display: flex;
  justify-content: flex-end;
}

.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s;
  font-size: 14px;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .view-consent-modal {
    margin: 10px;
    max-height: 95vh;
  }

  .view-consent-modal .modal-header,
  .view-consent-modal .modal-content,
  .view-consent-modal .modal-footer {
    padding: 16px 20px;
  }

  .header-content h2 {
    font-size: 1.25rem;
  }

  .consent-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .question-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .question-badges {
    align-self: flex-end;
  }

  .question-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .audit-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}