import React, { useState, useEffect } from 'react';
import { 
  X, 
  Heart, 
  Thermometer, 
  Droplets, 
  Scale, 
  Ruler, 
  Eye, 
  Wind,
  Save,
  Edit3
} from 'lucide-react';
import { VitalSign, UpdateVitalSignData } from '../../services/api/vital-signs.service';
import './UpdateVitalSignModal.css';

interface UpdateVitalSignModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: UpdateVitalSignData) => Promise<void>;
  vitalSign: VitalSign | null;
  loading: boolean;
}

const UpdateVitalSignModal: React.FC<UpdateVitalSignModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  vitalSign,
  loading
}) => {
  const [formData, setFormData] = useState<UpdateVitalSignData>({});
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (vitalSign && isOpen) {
      // Initialize form with current vital sign data
      setFormData({
        temperature: vitalSign.temperature,
        temp_unit: vitalSign.temp_unit,
        temp_location: vitalSign.temp_location,
        heart_rate: vitalSign.heart_rate,
        systolic_bp: vitalSign.systolic_bp,
        diastolic_bp: vitalSign.diastolic_bp,
        respiratory_rate: vitalSign.respiratory_rate,
        oxygen_saturation: vitalSign.oxygen_saturation,
        consciousness_level: vitalSign.consciousness_level,
        supplemental_oxygen: vitalSign.supplemental_oxygen,
        blood_sugar: vitalSign.blood_sugar,
        blood_sugar_unit: vitalSign.blood_sugar_unit,
        height: vitalSign.height,
        height_unit: vitalSign.height_unit,
        weight: vitalSign.weight,
        weight_unit: vitalSign.weight_unit,
        notes: vitalSign.notes
      });
      setErrors({});
    }
  }, [vitalSign, isOpen]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Temperature validation
    if (formData.temperature !== undefined) {
      if (formData.temperature < 30 || formData.temperature > 45) {
        newErrors.temperature = 'Temperature must be between 30-45°C';
      }
    }

    // Heart rate validation
    if (formData.heart_rate !== undefined) {
      if (formData.heart_rate < 30 || formData.heart_rate > 250) {
        newErrors.heart_rate = 'Heart rate must be between 30-250 bpm';
      }
    }

    // Blood pressure validation
    if (formData.systolic_bp !== undefined) {
      if (formData.systolic_bp < 50 || formData.systolic_bp > 300) {
        newErrors.systolic_bp = 'Systolic BP must be between 50-300 mmHg';
      }
    }

    if (formData.diastolic_bp !== undefined) {
      if (formData.diastolic_bp < 30 || formData.diastolic_bp > 200) {
        newErrors.diastolic_bp = 'Diastolic BP must be between 30-200 mmHg';
      }
    }

    if (formData.systolic_bp !== undefined && formData.diastolic_bp !== undefined) {
      if (formData.diastolic_bp >= formData.systolic_bp) {
        newErrors.diastolic_bp = 'Diastolic BP must be lower than systolic BP';
      }
    }

    // Respiratory rate validation
    if (formData.respiratory_rate !== undefined) {
      if (formData.respiratory_rate < 5 || formData.respiratory_rate > 60) {
        newErrors.respiratory_rate = 'Respiratory rate must be between 5-60/min';
      }
    }

    // Oxygen saturation validation
    if (formData.oxygen_saturation !== undefined) {
      if (formData.oxygen_saturation < 70 || formData.oxygen_saturation > 100) {
        newErrors.oxygen_saturation = 'Oxygen saturation must be between 70-100%';
      }
    }

    // Weight validation
    if (formData.weight !== undefined) {
      if (formData.weight < 0.5 || formData.weight > 500) {
        newErrors.weight = 'Weight must be between 0.5-500 kg';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error updating vital sign:', error);
    }
  };

  if (!isOpen || !vitalSign) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content update-vital-sign-modal">
        <div className="vital-modal-header">
          <div className="vital-modal-title">
            <Edit3 className="vital-modal-icon" />
            <h2>Update Vital Signs</h2>
          </div>
          <button className="vital-modal-close" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="vital-sign-form">
          <div className="form-section">
            <h3><Thermometer size={18} /> Temperature</h3>
            <div className="form-row">
              <div className="form-group">
                <label><Thermometer size={16} /> Temperature (°C)</label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.temperature || ''}
                  onChange={(e) => handleInputChange('temperature', e.target.value ? parseFloat(e.target.value) : undefined)}
                  placeholder="e.g., 37.2"
                />
                {errors.temperature && <span className="error">{errors.temperature}</span>}
              </div>
              <div className="form-group">
                <label>Unit</label>
                <select
                  value={formData.temp_unit || 'C'}
                  onChange={(e) => handleInputChange('temp_unit', e.target.value)}
                >
                  <option value="C">°C</option>
                  <option value="F">°F</option>
                </select>
              </div>
              <div className="form-group">
                <label>Location</label>
                <input
                  type="text"
                  value={formData.temp_location || ''}
                  onChange={(e) => handleInputChange('temp_location', e.target.value)}
                  placeholder="e.g., Oral, Axillary"
                />
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3><Heart size={18} /> Cardiovascular</h3>
            <div className="form-row">
              <div className="form-group">
                <label><Heart size={16} /> Heart Rate (bpm)</label>
                <input
                  type="number"
                  value={formData.heart_rate || ''}
                  onChange={(e) => handleInputChange('heart_rate', e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="e.g., 80"
                />
                {errors.heart_rate && <span className="error">{errors.heart_rate}</span>}
              </div>
              <div className="form-group">
                <label>Systolic BP (mmHg)</label>
                <input
                  type="number"
                  value={formData.systolic_bp || ''}
                  onChange={(e) => handleInputChange('systolic_bp', e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="e.g., 120"
                />
                {errors.systolic_bp && <span className="error">{errors.systolic_bp}</span>}
              </div>
              <div className="form-group">
                <label>Diastolic BP (mmHg)</label>
                <input
                  type="number"
                  value={formData.diastolic_bp || ''}
                  onChange={(e) => handleInputChange('diastolic_bp', e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="e.g., 80"
                />
                {errors.diastolic_bp && <span className="error">{errors.diastolic_bp}</span>}
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3><Wind size={18} /> Respiratory</h3>
            <div className="form-row">
              <div className="form-group">
                <label><Wind size={16} /> Respiratory Rate (/min)</label>
                <input
                  type="number"
                  value={formData.respiratory_rate || ''}
                  onChange={(e) => handleInputChange('respiratory_rate', e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="e.g., 16"
                />
                {errors.respiratory_rate && <span className="error">{errors.respiratory_rate}</span>}
              </div>
              <div className="form-group">
                <label>Oxygen Saturation (%)</label>
                <input
                  type="number"
                  value={formData.oxygen_saturation || ''}
                  onChange={(e) => handleInputChange('oxygen_saturation', e.target.value ? parseInt(e.target.value) : undefined)}
                  placeholder="e.g., 98"
                />
                {errors.oxygen_saturation && <span className="error">{errors.oxygen_saturation}</span>}
              </div>
              <div className="form-group">
                <label>Consciousness Level</label>
                <select
                  value={formData.consciousness_level || ''}
                  onChange={(e) => handleInputChange('consciousness_level', e.target.value || undefined)}
                >
                  <option value="">Select level</option>
                  <option value="A">Alert</option>
                  <option value="V">Voice</option>
                  <option value="P">Pain</option>
                  <option value="U">Unresponsive</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group checkbox-group">
                <label>
                  <input
                    type="checkbox"
                    checked={formData.supplemental_oxygen || false}
                    onChange={(e) => handleInputChange('supplemental_oxygen', e.target.checked)}
                  />
                  Supplemental Oxygen
                </label>
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3><Scale size={18} /> Additional Measurements</h3>
            <div className="form-row">
              <div className="form-group">
                <label><Droplets size={16} /> Blood Sugar</label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.blood_sugar || ''}
                  onChange={(e) => handleInputChange('blood_sugar', e.target.value ? parseFloat(e.target.value) : undefined)}
                  placeholder="e.g., 120"
                />
              </div>
              <div className="form-group">
                <label>Unit</label>
                <select
                  value={formData.blood_sugar_unit || 'mg/dL'}
                  onChange={(e) => handleInputChange('blood_sugar_unit', e.target.value)}
                >
                  <option value="mg/dL">mg/dL</option>
                  <option value="mmol/L">mmol/L</option>
                </select>
              </div>
            </div>
            <div className="form-row">
              <div className="form-group">
                <label><Ruler size={16} /> Height</label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.height || ''}
                  onChange={(e) => handleInputChange('height', e.target.value ? parseFloat(e.target.value) : undefined)}
                  placeholder="e.g., 170"
                />
              </div>
              <div className="form-group">
                <label>Unit</label>
                <select
                  value={formData.height_unit || 'cm'}
                  onChange={(e) => handleInputChange('height_unit', e.target.value)}
                >
                  <option value="cm">cm</option>
                  <option value="in">inches</option>
                </select>
              </div>
              <div className="form-group">
                <label><Scale size={16} /> Weight</label>
                <input
                  type="number"
                  step="0.1"
                  value={formData.weight || ''}
                  onChange={(e) => handleInputChange('weight', e.target.value ? parseFloat(e.target.value) : undefined)}
                  placeholder="e.g., 70"
                />
                {errors.weight && <span className="error">{errors.weight}</span>}
              </div>
              <div className="form-group">
                <label>Unit</label>
                <select
                  value={formData.weight_unit || 'kg'}
                  onChange={(e) => handleInputChange('weight_unit', e.target.value)}
                >
                  <option value="kg">kg</option>
                  <option value="lbs">lbs</option>
                </select>
              </div>
            </div>
          </div>

          <div className="form-section">
            <h3><Eye size={18} /> Notes</h3>
            <div className="form-group">
              <label><Eye size={16} /> Notes</label>
              <textarea
                value={formData.notes || ''}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Additional notes about the vital signs..."
                rows={3}
              />
            </div>
          </div>

          <div className="modal-actions">
            <button type="button" className="btn btn-secondary" onClick={onClose}>
              <X size={16} />
              Cancel
            </button>
            <button type="submit" className="btn btn-primary" disabled={loading}>
              <Save size={16} />
              {loading ? 'Updating...' : 'Update Vital Signs'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default UpdateVitalSignModal;
