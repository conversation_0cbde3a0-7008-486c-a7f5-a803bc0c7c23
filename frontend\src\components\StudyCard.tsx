import React from "react";
import { Study } from "@/store/scheduleEventState";
import { Check, Calendar, Users, FileText } from "lucide-react";

interface StudyCardProps {
  study: Study;
  onSelect: (study: Study) => void;
}

const StudyCard: React.FC<StudyCardProps> = ({ study, onSelect }) => {
  return (
    <div className="study-card">
      <div className="study-header">
        <h3>{study.name}</h3>
        <div className="study-actions">
          <button
            type="button"
            className="select-btn"
            onClick={() => {
              onSelect(study);
            }}
            title="Select study"
          >
            <Check size={16} /> Select
          </button>
        </div>
      </div>
      
      <div className="study-details">
        {study.full_title && (
          <div className="study-full-title">{study.full_title}</div>
        )}
        
        {study.description && (
          <div className="study-description">{study.description}</div>
        )}
        
        <div className="study-metadata">
          <div className="study-metadata-item">
            <Calendar size={14} />
            <span>{study.visits?.length || 0} visits defined</span>
          </div>
          
          {study.enrollment_count !== undefined && (
            <div className="study-metadata-item">
              <Users size={14} />
              <span>{study.enrollment_count} patients enrolled</span>
            </div>
          )}
          
          {study.iras && (
            <div className="study-metadata-item">
              <FileText size={14} />
              <span>IRAS: {study.iras}</span>
            </div>
          )}
        </div>
        
        {study.team_email && (
          <div className="study-team">
            Team contact: {study.team_email}
          </div>
        )}
      </div>
    </div>
  );
};

export default StudyCard;
