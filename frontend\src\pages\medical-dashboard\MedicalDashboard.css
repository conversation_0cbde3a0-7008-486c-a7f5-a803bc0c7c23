.med-dashboard-container {
  display: flex;
  min-height: 100vh;
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: #333;
  font-size: 14px;
}

.med-dashboard-container * {
  box-sizing: border-box;
}

/* Sidebar Styles */
.med-sidebar {
  width: 280px;
  background-color: #ffffff;
  border-right: 1px solid #e9ecef;
  padding: 20px 0;
  position: fixed;
  height: calc(100vh - 60px); /* Adjust for header */
  margin-top: 60px; /* Account for header */
  overflow-y: auto;
  box-shadow: 2px 0 4px rgba(0,0,0,0.05);
  transition: width 0.3s ease;
  z-index: 100;
}

.med-sidebar-minimized {
  width: 70px;
}

.med-sidebar-header {
  padding: 0 20px 20px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.med-sidebar-title {
  font-size: 20px;
  font-weight: 700;
  color: #2c3e50;
}

/* Minimize Button - match old board style */
.med-minimize-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: none;
  border: none;
  color: #37b7c3;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.med-minimize-btn:hover {
  background-color: #f0f9ff;
  color: #2563eb;
}

.med-minimize-btn.med-minimize-btn-collapsed {
  justify-content: center;
  padding: 8px;
  margin: 0 auto;
}

.med-sidebar-minimized .med-sidebar-title {
  display: none;
}

/* Sidebar Patient Info */
.med-sidebar-patient {
  padding: 0 20px 20px;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 20px;
}

.med-sidebar-patient-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.med-sidebar-patient-header {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.med-sidebar-patient-basic-info {
  flex: 1;
  min-width: 0;
}

.med-sidebar-patient-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.med-sidebar-patient-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.med-sidebar-patient-details {
  width: 100%;
}

.med-sidebar-patient-table {
  width: 100%;
}

.med-sidebar-patient-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  font-size: 11px;
  line-height: 1.3;
}

.med-sidebar-patient-label {
  color: #6c757d;
  font-weight: 500;
  flex-shrink: 0;
  min-width: 60px;
}

.med-sidebar-patient-value {
  color: #2c3e50;
  font-weight: 400;
  text-align: left;
  word-break: break-word;
  flex: 1;
  margin-left: 12px;
}

.med-sidebar-patient-name {
  font-size: 16px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 6px;
}

.med-sidebar-patient-meta {
  color: #6c757d;
  font-size: 12px;
  margin-bottom: 8px;
}



.med-sidebar-menu {
  list-style: none;
  padding: 0;
  margin: 0;
}

.med-sidebar-item {
  margin-bottom: 2px;
}

.med-sidebar-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  text-decoration: none;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  background: none;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
}

.med-sidebar-link:hover {
  background-color: #f8f9fa;
  color: #495057;
}

.med-sidebar-link.med-active {
  background-color: #e8f4f8;
  color: #17a2b8;
  border-left-color: #17a2b8;
  font-weight: 600;
}

/* Sidebar Icon Styling for Lucide Icons */
.med-sidebar-icon {
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  color: inherit;
}

.med-sidebar-icon svg {
  width: 20px;
  height: 20px;
  stroke-width: 1.5;
  color: inherit;
}

/* Icon colors for different states */
.med-sidebar-link .med-sidebar-icon {
  color: #6c757d;
}

.med-sidebar-link:hover .med-sidebar-icon {
  color: #495057;
}

.med-sidebar-link.med-active .med-sidebar-icon {
  color: #17a2b8;
}

/* Minimized sidebar icon adjustments */
.med-sidebar-minimized .med-sidebar-icon {
  margin-right: 0;
}

.med-sidebar-minimized .med-sidebar-icon svg {
  width: 20px;
  height: 20px;
}

.med-sidebar-minimized .med-sidebar-link {
  padding: 12px;
  justify-content: center;
}

/* Ensure icons are properly aligned in minimized state */
.med-sidebar-minimized .med-sidebar-link .med-sidebar-icon {
  margin: 0;
}

.med-sidebar-minimized .med-sidebar-header {
  padding: 0 10px 20px;
  justify-content: center;
}

.med-sidebar-minimized .med-minimize-btn {
  padding: 4px;
  gap: 0;
}

/* Main Content */
.med-main-content {
  flex: 1;
  margin-left: 280px; /* Match sidebar width */
  margin-top: 60px; /* Account for header */
  padding: 20px;
  background-color: #f8f9fa;
  transition: margin-left 0.3s ease;
  min-height: calc(100vh - 60px);
}

.med-main-content-expanded {
  margin-left: 70px; /* Match minimized sidebar width */
}

/* Remove patient header styles since it's now only in sidebar */
.med-patient-header {
  display: none;
}

/* Alerts Section */
.med-alerts-section {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 24px;
  margin-top: 0;
}

.med-alert-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.med-alert-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
}

.med-alert-card.med-allergies {
  background: #FFF3DF;
  border-left: 4px solid #f39c12;
}

.med-alert-card.med-warnings {
  background: #FFE7EC;
  border-left: 4px solid #e74c3c;
}

.med-alert-card.med-attention {
  background: #EFFCFC;
  border-left: 4px solid #37b7c3;
}

.med-alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.med-alert-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
}

.med-alert-icon {
  color: #6c757d;
  flex-shrink: 0;
}

.med-alert-card.med-allergies .med-alert-icon {
  color: #f39c12;
}

.med-alert-card.med-warnings .med-alert-icon {
  color: #e74c3c;
}

.med-alert-card.med-attention .med-alert-icon {
  color: #37b7c3;
}

.med-edit-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #6c757d;
  padding: 4px;
  margin-left: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.med-edit-btn:hover {
  background-color: #f8f9fa;
  color: #37b7c3;
}

.med-edit-btn svg {
  width: 14px;
  height: 14px;
}

.med-alert-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.med-alert-list li {
  font-size: 14px;
  color: #2c3e50;
  margin-bottom: 8px;
  line-height: 1.4;
}

.med-alert-list li:last-child {
  margin-bottom: 0;
}

/* Vitals Section */
.med-vitals-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.med-section-title {
  font-size: 18px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 20px 0;
}

.med-vitals-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 1px;
  background: #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.med-vital-item {
  background: white;
  padding: 16px 12px;
  text-align: center;
  transition: background-color 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.med-vital-item:hover {
  background-color: #f8f9fa;
}

.med-vital-label {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-style: normal;
  font-size: 14px;
  line-height: 150%;
  letter-spacing: 0%;
  color: #090914;
  margin-bottom: 8px;
  width: 94px;
  height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.med-vital-value {
  font-family: 'Poppins', sans-serif;
  font-weight: 500;
  font-style: normal;
  font-size: 20px;
  line-height: 150%;
  letter-spacing: 0%;
  color: #565656;
  width: 84px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Content Grid */
.med-content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

/* Section Cards */
.med-section-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid #e9ecef;
}

.med-section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.med-section-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.med-add-btn {
  background: #37b7c3;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.med-add-btn:hover {
  background: #2c929e;
  transform: translateY(-1px);
}

.med-resolved-count {
  font-size: 12px;
  color: #6c757d;
}

/* Tables */
.med-table-container {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.med-data-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.med-data-table th {
  background: #EBF4F6;
  color: #2c3e50;
  font-weight: 600;
  padding: 14px 16px;
  text-align: left;
  border-bottom: 1px solid #e9ecef;
  font-size: 14px;
}

.med-data-table td {
  padding: 14px 16px;
  border-bottom: 1px solid #e9ecef;
  color: #2c3e50;
  transition: background-color 0.2s ease;
}

.med-data-table tbody tr:hover {
  background-color: #f8f9fa;
}

.med-status-yyy {
  color: #37b7c3;
  font-weight: 500;
}

.med-priority-high {
  color: #e74c3c;
  font-weight: 500;
}

.med-action-btn {
  background: #37b7c3;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.med-action-btn:hover {
  background: #2c929e;
  transform: translateY(-1px);
}

/* Studies Grid */
.med-studies-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.med-studies-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.med-study-category {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  background: white;
}

.med-study-category-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #EBF4F6;
  padding: 12px 16px;
  font-weight: 600;
  font-size: 14px;
  color: #2c3e50;
}

.med-study-category-title {
  font-weight: 600;
  color: #2c3e50;
}

.med-study-count {
  background: #37b7c3;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 700;
  min-width: 24px;
  text-align: center;
}

.med-study-items {
  padding: 0;
}

.med-study-item {
  padding: 10px 16px;
  border-top: 1px solid #e9ecef;
  font-size: 13px;
  color: #2c3e50;
  transition: background-color 0.2s ease;
}

.med-study-item:first-child {
  border-top: none;
}

.med-study-item:hover {
  background-color: #f8f9fa;
}

/* Documentation Grid */
.med-documentation-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.med-doc-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.med-doc-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
}

.med-doc-label {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.med-doc-count {
  font-size: 18px;
  font-weight: 700;
  color: #37b7c3;
}

/* Responsive Design */
@media (max-width: 1400px) {
  .med-vitals-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1200px) {
  .med-content-grid {
    grid-template-columns: 1fr;
  }
  
  .med-vitals-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .med-alerts-section {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 992px) {
  .med-content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .med-studies-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .med-sidebar {
    width: 100%;
    position: relative;
    height: auto;
    margin-top: 0;
  }
  
  .med-main-content {
    margin-left: 0;
    margin-top: 0;
    padding: 15px;
  }
  
  .med-alerts-section {
    grid-template-columns: 1fr;
  }
  
  .med-vitals-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .med-section-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .med-data-table th,
  .med-data-table td {
    font-size: 12px;
    padding: 10px 12px;
  }
}

/* Patient Logs - Full Width */
.med-section-card:last-child {
  grid-column: 1 / -1;
}

/* Scrollbar Styling */
.med-sidebar::-webkit-scrollbar {
  width: 6px;
}

.med-sidebar::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.med-sidebar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.med-sidebar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Integration with existing app layout */
.med-dashboard-container {
  position: relative;
  z-index: 1;
}

/* Ensure proper spacing with existing header */
.med-dashboard-container {
  padding-top: 0;
}

/* Mobile responsiveness improvements */
@media (max-width: 480px) {
  .med-dashboard-container {
    flex-direction: column;
  }
  
  .med-sidebar {
    position: relative;
    width: 100%;
    height: auto;
    margin-top: 0;
  }
  
  .med-main-content {
    margin-left: 0;
    margin-top: 0;
  }
  
  .med-alert-card,
  .med-vitals-section,
  .med-section-card {
    padding: 16px;
  }
  
  .med-vitals-grid {
    grid-template-columns: 1fr;
  }
}

/* Animation for hover effects */
.med-alert-card,
.med-vital-item,
.med-study-item,
.med-data-table tbody tr,
.med-doc-item {
  transition: all 0.2s ease;
}

/* Focus states for accessibility */
.med-add-btn:focus,
.med-action-btn:focus,
.med-edit-btn:focus {
  outline: 2px solid #37b7c3;
  outline-offset: 2px;
}

/* 2-column main content grid for dashboard */
.med-content-2col-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 24px;
}

.med-content-col {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

@media (max-width: 1200px) {
  .med-content-2col-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
}
