 
import { useState, useEffect } from "react";
import DashboardFormCard from "@/components/DashboardFormCard";
import { 
  useGetFormsByPatient, 
  useGetUnsubmittedFormsByPatient, 
  useGetIncompleteSubmissionsByPatient,
  useGetSubmissionByFormAndPatient,
  useCreateSubmission,
  useUpdateSubmission
} from "@/hooks/form.query";
import Preloader from "@/components/common/Preloader";
import { useCurrentUserQuery } from "@/hooks/user.query";
import { ArrowLeft, AlertCircle, FileText } from "lucide-react";
import RebuildForm, { SavedFormData } from "@/pages/form-builder/SurveyFormRenderer";
import { useGetFormByUuid } from "@/hooks/form.query";
import DocumentationSidebar from "@/components/common/DocumentationSidebar";

// Define the SubmissionData interface to fix type errors
interface SubmissionData {
  formDetails?: {
    name?: string;
    description?: string;
    categories?: string;
    privacy?: string;
    password?: string;
    study?: string | null;
  };
  sections?: Array<{
    id: string | number;
    name?: string;
    description?: string;
    questions?: Array<{
      id: string | number;
      type: string;
      required?: boolean;
      [key: string]: unknown;
    }>;
    [key: string]: unknown;
  }>;
  [key: string]: unknown;
}

export default function FormsClinical() {
  // Get patient UUID from current user data instead of selected patient store
  const { data: currentUser } = useCurrentUserQuery();
  // Use type assertion to handle patient_uuid property
  const patientUuid = (currentUser as any)?.patient_uuid || "";
  
  console.log("Patient UUID in PatientForm from user data:", patientUuid);
  
  const [selectedFormUuid, setSelectedFormUuid] = useState<string | null>(null);
  const [processingError, setProcessingError] = useState<string | null>(null);
  const [submissionMode, setSubmissionMode] = useState<"new" | "edit">("new");
  const [currentFormSubmissionUuid, setCurrentFormSubmissionUuid] = useState<string | null>(null);
  
  // State for validation errors
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [, setIsFormSubmitting] = useState(false);
  const [submissionResult, setSubmissionResult] = useState<{success: boolean, message: string} | null>(null);
  
  const [activeTab, setActiveTab] = useState('showAll');
  const [patientsMode, setPatientsMode] = useState(false);
  
  useGetFormsByPatient(patientUuid);
    // Add QueryClient for data invalidation
  
  // Only get available forms (we no longer need draft submissions for display)
  const { data: emptyForms, isLoading: isLoadingEmptyForms } = useGetUnsubmittedFormsByPatient(patientUuid);
  // We still need this for the form edit functionality, even if we don't show drafts in the list
  const { isLoading: isLoadingDrafts } = useGetIncompleteSubmissionsByPatient(patientUuid);
  
  const { data: selectedFormData, isLoading: isLoadingSelectedForm } = useGetFormByUuid(selectedFormUuid || "", {
    enabled: !!selectedFormUuid
  });
  
  const { 
    data: existingSubmission, 
    isLoading: isLoadingSubmission, 
    exists: submissionExists 
  } = useGetSubmissionByFormAndPatient(
    selectedFormUuid || "", 
    patientUuid
  );
  
  const createSubmission = useCreateSubmission();
  const updateSubmission = useUpdateSubmission(currentFormSubmissionUuid || "");

  useEffect(() => {
    if (selectedFormUuid && patientUuid) {
      if (submissionExists && existingSubmission) {
        setSubmissionMode("edit");
        setCurrentFormSubmissionUuid(existingSubmission.uuid);
      } else {
        setSubmissionMode("new");
        setCurrentFormSubmissionUuid(null);
      }
    }
  }, [selectedFormUuid, patientUuid, submissionExists, existingSubmission]);

  useEffect(() => {
    console.log("Current user updated:", currentUser);
  }, [currentUser]);

  const handleFilterChange = (filter: string) => {
    setActiveTab(filter);
  };

  const handlePatientsModeToggle = (enabled: boolean) => {
    setPatientsMode(enabled);
  };

  const formatUserName = (user: { first_name?: string; last_name?: string; identifier?: string } | null) => {
    if (!user) return "Unknown User";
    const firstName = user.first_name || "";
    const lastName = user.last_name || "";
    return firstName || lastName ? `${firstName} ${lastName}`.trim() : user.identifier || "Unknown User";
  };

  const handleFormClick = (formUuid: string, mode: "new" | "edit" = "new") => {
    setSelectedFormUuid(formUuid);
    setSubmissionMode(mode);
    setValidationErrors([]);
    setSubmissionResult(null);
  };

  const handleBackToForms = () => {
    setSelectedFormUuid(null);
    setProcessingError(null);
    setCurrentFormSubmissionUuid(null);
    setValidationErrors([]);
  };

  // Validate required fields in the form
  const validateRequiredFields = (formData: SubmissionData): string[] => {
    const errors: string[] = [];
    
    if (!formData.sections) return ["Form has no sections"];
    
    formData.sections.forEach((section, sectionIndex) => {
      if (!section.questions) return;
      
      section.questions.forEach((question, questionIndex) => {
        // Skip if question is not required
        if (!question.required) return;
        
        const questionId = question.id;
        let hasValidAnswer = false;
        
        // Try different answer field formats
        const answerKey = `answer_${questionId}`;
        const userAnswerKey = 'user-answer';
        
        // Check if an answer exists in any of the possible formats
        if (question[answerKey] !== undefined && 
            question[answerKey] !== null && 
            question[answerKey] !== '') {
          hasValidAnswer = true;
        } else if (question[userAnswerKey] !== undefined && 
                  question[userAnswerKey] !== null && 
                  question[userAnswerKey] !== '') {
          hasValidAnswer = true;
        }
        
        // If it's an array answer, check if it has content
        if (!hasValidAnswer) {
          const answer = question[answerKey] || question[userAnswerKey];
          if (Array.isArray(answer) && answer.length > 0) {
            hasValidAnswer = true;
          }
        }
        
        if (!hasValidAnswer) {
          errors.push(`Question ${questionIndex + 1} in section "${section.name || `Section ${sectionIndex + 1}`}" is required`);
        }
      });
    });
    
    return errors;
  };

  // Create a helper function to extract files from FileList or similar structures
  const extractFilesFromAnswer = (answer: unknown): File[] => {
    const files: File[] = [];
    
    if (!answer) return files;
    
    // If answer is a FileList, convert to array of Files
    if (answer instanceof FileList) {
      return Array.from(answer);
    }
    
    // Handle case where answer might be an object with files
    if (typeof answer === 'object' && answer !== null) {
      // If the object has a "files" property that's a FileList
      if ('files' in answer && answer.files instanceof FileList) {
        return Array.from(answer.files);
      }
      
      // Try to find any File objects stored as properties
      Object.values(answer).forEach(value => {
        if (value instanceof File) {
          files.push(value);
        } else if (value instanceof FileList) {
          Array.from(value).forEach(file => files.push(file));
        }
      });
    }
    
    return files;
  };
  // Handle form submission
  const handleFormSubmit = (formData: Record<string, unknown>, isCompleted: boolean = true) => {
    if (!selectedFormUuid || !patientUuid) {
      setProcessingError("Missing form or patient data");
      return;
    }

    console.log("Submitting form with patient UUID:", patientUuid);
    
    // Always set isCompleted to false on the patient side, regardless of the input parameter
    // This ensures all patient submissions remain as drafts for healthcare professionals to review
    isCompleted = false;
    
    // Validate all required fields before submitting
    const errors = validateRequiredFields(formData as SubmissionData);
    if (errors.length > 0) {
      setValidationErrors(errors);
      setSubmissionResult({
        success: false,
        message: "Please answer all required questions before submitting"
      });
      return;
    }

    // Extract file attachments from the form data
    const fileAttachments = {
      image_attachments: [] as File[],
      video_attachments: [] as File[],
      document_attachments: [] as File[]
    };

    // Define section and question types
    interface FormQuestion {
      id: number | string;
      type: string;
      'user-answer'?: unknown;
      'user-answer-metadata'?: Array<{name: string; size: number; type: string}>;
      [key: string]: unknown;
    }
    
    interface FormSection {
      id: number | string;
      questions?: FormQuestion[];
      [key: string]: unknown;
    }
    
    // Process sections to find file attachments
    if (formData.sections && Array.isArray(formData.sections)) {
      (formData.sections as FormSection[]).forEach(section => {
        if (section.questions && Array.isArray(section.questions)) {
          section.questions.forEach((question: FormQuestion) => {
            // Only process attach-file type questions with actual answers
            if (question.type === 'attach-file' && question['user-answer']) {
              const files = extractFilesFromAnswer(question['user-answer']);
              
              if (files.length > 0) {
                console.log(`Found ${files.length} files in question ${question.id}:`, 
                  files.map(f => `${f.name} (${f.type})`));
                  
                // Store additional info in the question's user-answer for displaying in the UI
                question['user-answer-metadata'] = files.map(file => ({
                  name: file.name,
                  size: file.size,
                  type: file.type
                }));
              }
              
              // Categorize files by type
              files.forEach(file => {
                // Add explicit file type checks
                if (file.type.startsWith('image/')) {
                  console.log(`Adding image file: ${file.name}`);
                  fileAttachments.image_attachments.push(file);
                } else if (file.type.startsWith('video/')) {
                  console.log(`Adding video file: ${file.name}`);
                  fileAttachments.video_attachments.push(file);
                } else {
                  // Any other file type goes to document_attachments
                  console.log(`Adding document file: ${file.name}`);
                  fileAttachments.document_attachments.push(file);
                }
              });
            }
          });
        }
      });
    }

    // Create user object for API
    const userObject = {
      identifier: currentUser?.identifier || "0",
      first_name: currentUser?.first_name || "",
      last_name: currentUser?.last_name || "",
      email: currentUser?.email || "<EMAIL>"
    };

    setIsFormSubmitting(true);
    setProcessingError(null);
    setValidationErrors([]);
    setSubmissionResult(null);

    if (submissionMode === "edit" && currentFormSubmissionUuid) {
      // Update existing submission
      updateSubmission.mutate(
        {
          form_uuid: selectedFormUuid,
          form: selectedFormUuid,
          user: userObject,
          patient_uuid: patientUuid,
          submission: formData,
          is_completed: isCompleted,
          attachments: fileAttachments,
          final_submission: false // Never finalize on patient side
        },
        {
          onSuccess: () => {
            console.log("Form submission updated successfully");
            setSubmissionResult({
              success: true,
              message: "Your form has been saved successfully. A healthcare professional will review it."
            });
            setTimeout(handleBackToForms, 2000);
          },
          onError: (error: unknown) => {
            console.error("Error updating submission:", error);
            const err = error as { response?: { data?: { detail?: string } }, message?: string };
            const errorMessage = err.response?.data?.detail || err.message || String(error);
            setProcessingError(`Error updating submission: ${errorMessage}`);
            setSubmissionResult({
              success: false,
              message: `Failed to save form: ${errorMessage}`
            });
          },
          onSettled: () => {
            setIsFormSubmitting(false);
          }
        }
      );
    } else {
      // Create new submission
      createSubmission.mutate(
        {
          form_uuid: selectedFormUuid,
          user: userObject,
          patient_uuid: patientUuid,
          submission: formData,
          is_completed: isCompleted,
          attachments: fileAttachments,
          final_submission: false, // Never finalize on patient side
          form: ""
        },
        {
          onSuccess: () => {
            console.log(`Form submission created successfully`);
            setSubmissionResult({
              success: true,
              message: "Your form has been saved successfully. A healthcare professional will review it."
            });
            setTimeout(handleBackToForms, 2000);
          },
          onError: (error) => {
            console.error("Error creating submission:", error);
            const err = error as { response?: { data?: { detail?: string } }, message?: string };
            const errorDetails = err.response?.data?.detail || err.message || String(error);
            setProcessingError(`Error creating submission: ${errorDetails}`);
            setSubmissionResult({
              success: false,
              message: `Failed to save form: ${errorDetails}`
            });
          },
          onSettled: () => {
            setIsFormSubmitting(false);
          }
        }
      );
    }
  };

  const processFormData = (): SavedFormData | null => {
    if (!selectedFormData) return null;
    
    try {
      let formStructure;
      
      if (selectedFormData.active_version?.form_structure) {
        if (typeof selectedFormData.active_version.form_structure === 'string') {
          try {
            formStructure = JSON.parse(selectedFormData.active_version.form_structure);
          } catch (parseError) {
            console.error("Failed to parse form_structure string:", parseError);
          }
        } else {
          formStructure = selectedFormData.active_version.form_structure;
        }
      } else if (selectedFormData.active_version?.structure && typeof selectedFormData.active_version.structure === 'string') {
        try {
          formStructure = JSON.parse(selectedFormData.active_version.structure);
        } catch (parseError) {
          console.error("Failed to parse structure string:", parseError);
          
          try {
            formStructure = JSON.parse(JSON.parse(selectedFormData.active_version.structure));
          } catch (doubleParseError) {
            console.error("Failed to parse double-encoded structure string:", doubleParseError);
          }
        }
      } else if (selectedFormData.form_structure) {
        if (typeof selectedFormData.form_structure === 'string') {
          try {
            formStructure = JSON.parse(selectedFormData.form_structure);
          } catch (parseError) {
            console.error("Failed to parse form_structure string:", parseError);
          }
        } else {
          formStructure = selectedFormData.form_structure;
        }
      }
      
      if (!formStructure || !formStructure.sections) {
        setProcessingError("Invalid form structure in the response");
        return null;
      }
      
      // Process existing submission data if available in edit mode
      let existingSubmissionData = {};
      if (submissionMode === "edit" && existingSubmission && existingSubmission.submission) {
        console.log("Processing existing submission data:", existingSubmission.submission);
        existingSubmissionData = existingSubmission.submission;
      }
      
      // Extract form details
      const formName = selectedFormData.name || (selectedFormData.active_version as { name?: string })?.name || "Untitled Form";
      const formDescription = selectedFormData.description || selectedFormData.active_version?.description || "";
      const formStudy = selectedFormData.active_version?.study || null;
      
      // Define a type for category and tag items
      type CategoryOrTagItem = string | { name?: string; [key: string]: unknown };

      // Helper function to extract names
      const extractNames = (items: CategoryOrTagItem[]): string => {
        return items
          .map(item => (typeof item === 'string' ? item : item.name || String(item)))
          .join(", ");
      };

      let categories = "";
      if (Array.isArray(selectedFormData.categories)) {
        categories = extractNames(selectedFormData.categories as CategoryOrTagItem[]);
      } else {
        const formDataAny = selectedFormData as { tags?: unknown[] };
        if (Array.isArray(formDataAny.tags)) {
          categories = extractNames(formDataAny.tags as CategoryOrTagItem[]);
        } else if (Array.isArray(selectedFormData.active_version?.categories)) {
          categories = selectedFormData.active_version.categories.join(", ");
        }
      }
      
      return {
        formDetails: {
          name: formName,
          description: formDescription,
          categories: categories,
          privacy: selectedFormData.active_version?.password || "",
          password: selectedFormData.active_version?.password || "",
          study: formStudy,
        },
        sections: formStructure.sections,
        existingSubmissionData // Include the existing submission data
      };
      
    } catch (e) {
      console.error("Error processing form data:", e);
      setProcessingError(`Error processing form data: ${e instanceof Error ? e.message : "Unknown error"}`);
      return null;
    }
  };

  const staticFormData: SavedFormData = {
    formDetails: {
      name: "Form Not Available",
      description: "This form could not be loaded properly",
      categories: "",
      privacy: "Public",
      password: "",
      study: null,
    },
    sections: [
      {
        id: 1, 
        name: "Fallback Section", 
        description: "This is a fallback section", 
        questions: [
          {
            id: 1, 
            type: "short-text", 
            answers: [""], 
            required: false, 
            nonClinical: false, 
            questionName: "This form could not be loaded properly"
          }
        ]
      }
    ]
  };

  const formData = processFormData() || staticFormData;
  // Get all available forms to display
  const getFormsToDisplay = () => {
    // For the patient side, we only want to show empty forms,
    // not forms with drafts as per the new requirement
    const forms = [...(emptyForms || [])];
    
    // We're no longer adding draft submissions to the list
    // This ensures patients don't see forms they've already started
    
  return forms;
  };

  // Removed the unused getFormState function

  if (selectedFormUuid) {
    return (
      <div className="form-view-container">
        <div className="form-view-header">
          <h2>Form View {submissionMode === "edit" ? "(Edit)" : "(New)"}</h2>
          <button 
            className="back-to-forms-button"
            onClick={handleBackToForms}
          >
            <ArrowLeft size={18} />
            Back to Forms List
          </button>
        </div>
        
        <div style={{ display: 'flex' }}>
          <DocumentationSidebar 
            onTabChange={handleFilterChange}
            onPatientsModeToggle={handlePatientsModeToggle}
            currentTab={activeTab}
            currentPatientsMode={patientsMode}
          />
          
          <div className="form-view-content" style={{ flexGrow: 1 }}>
            {isLoadingSelectedForm || isLoadingSubmission ? (
              <div className="loading-indicator">
                <div className="spinner"></div>
                <p>Loading form data...</p>
              </div>
            ) : processingError ? (
              <div className="error-message">
                <AlertCircle size={20} />
                <span>{processingError}</span>
              </div>
            ) : (
              <>
                {/* Validation errors display */}
                {validationErrors.length > 0 && (
                  <div className="bg-red-100 text-red-800 p-4 rounded-md mb-4">
                    <div className="font-bold mb-2">Please correct the following errors:</div>
                    <ul className="list-disc pl-5">
                      {validationErrors.map((error, index) => (
                        <li key={index}>{error}</li>
                      ))}
                    </ul>
                  </div>
                )}
                
                {/* Form submission result */}
                {submissionResult && (
                  <div className={`p-4 mb-4 rounded-md ${submissionResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                    {submissionResult.message}
                  </div>
                )}
              
                <RebuildForm 
                  formData={formData} 
                  formUuid={selectedFormUuid}
                  activeFilter={activeTab}
                  patientsMode={patientsMode}
                  onSubmit={handleFormSubmit}
                  onSaveDraft={(data) => handleFormSubmit(data, false)}
                  isEditMode={submissionMode === "edit"}
                  // We will validate required fields in our own code
                />
              </>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Render the simplified list of available forms
  return (
    <div>
      <Preloader />
      <div style={{ marginTop: "30px", paddingBottom: "80px" }}>
        {!patientUuid ? (
          <div className="text-center py-5">No patient selected. Please select a patient first.</div>
        ) : (
          <>
            <h2 className="patclin-section-title">My Forms</h2>
            
            {/* Status message display for form submission */}
            {submissionResult && (
              <div className={`submission-result ${submissionResult.success ? 'success' : 'error'}`} style={{
                padding: '12px 16px',
                borderRadius: '4px',
                marginBottom: '16px',
                background: submissionResult.success ? '#d1fae5' : '#fee2e2',
                color: submissionResult.success ? '#047857' : '#b91c1c',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <AlertCircle size={18} />
                <span>{submissionResult.message}</span>
              </div>
            )}
            
            {isLoadingEmptyForms || isLoadingDrafts ? (
              <div className="loading-indicator">
                <div className="spinner"></div>
                <p>Loading forms...</p>
              </div>
            ) : getFormsToDisplay().length > 0 ? (
              <div className="row y-gap-30 mt-30" style={{ marginBottom: "30px" }}>
                {getFormsToDisplay().map((form: any, index: number) => (
                  <div className="col-xl-12 col-lg-12 col-md-12" key={form.uuid || index}>
                    <div 
                      className="form-card"
                      onClick={() => {
                        const mode = form.has_draft ? "edit" : "new";
                        handleFormClick(form.uuid, mode);
                      }}
                    >                      <DashboardFormCard
                        title={form.name || "Unnamed Form"}
                        status={form.has_draft ? "Draft" : "New"}
                        date={form.created_at ? new Date(form.created_at).toLocaleDateString() : undefined}
                        lastUpdatedAt={form.updated_at ? new Date(form.updated_at).toLocaleDateString() : undefined}
                        createdBy={form.user ? formatUserName(form.user) : undefined}
                        lastUpdatedBy={form.updatedBy ? formatUserName(form.updatedBy) : undefined}
                      />
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="empty-forms-container">
                <FileText size={48} className="text-light-3" />
                <h4>No Forms Available</h4>
                <p>There are no forms assigned to you at this time.</p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}
