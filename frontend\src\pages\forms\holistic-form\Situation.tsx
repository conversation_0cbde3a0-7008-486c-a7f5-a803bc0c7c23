import { ChangeEvent, useState, useRef } from "react";
import NurtifyRadio from "@/components/NurtifyRadio";
import NurtifyRange from "@/components/NurtifyRange";
import NurtifyText from "@/components/NurtifyText";
import useHolisticFormStore from "@/store/holisticFormState";
import NurtifyTextArea from "@/components/NurtifyTextArea";
import useHolisticFormTabStore from "@/store/holisticFormTabState";

// import { X } from "lucide-react";

interface ArrivalMode {
  label: string;
  value: string;
  showModal?: boolean;
  modalType?: "a4" | "b4" | "c4" | "d4";
}

const ARRIVAL_MODES: ArrivalMode[] = [
  // { label: "Self Presented", value: "self-presented" },
  {
    label: "Brought in by ambulance (Not transfer)",
    value: "Brought in by ambulance (Not transfer)",
  },
  {
    label: "Transferred from other Hospital via ambulance",
    value: "Transferred from other Hospital via ambulance",
    showModal: true,
    modalType: "a4",
  },
  {
    label: "Referred from other Hospital",
    value: "Referred from other Hospital",
    showModal: true,
    modalType: "b4",
  },
  {
    label: "Referred from Different department",
    value: "Referred from Different department",
    showModal: true,
    modalType: "c4",
  },
  { label: "Brought in by police", value: "Brought in by police" },
];

export default function Situation() {
  const { situation, setSituation } = useHolisticFormStore();
  const { goToPrevious, goToNext } = useHolisticFormTabStore();
  const [modalState, setModalState] = useState({
    isOptionsModalShowing: false,
    activeModalType: null as ArrivalMode["modalType"] | null,
  });
  const modalRef = useRef<HTMLDivElement>(null);

  const handleInputChange = (field: keyof typeof situation, value: string) => {
    setSituation({ ...situation, [field]: value });
  };

  /*
  const handleNext = () => {
    console.log(situation);
  };
*/
  const handleModeOfArrivalChange = (
    event: React.ChangeEvent<HTMLInputElement>,
    modeOfArrival: ArrivalMode
  ) => {
    const { value } = event.target;
    const {showModal} = modeOfArrival;
    if(showModal) {
      console.log(modeOfArrival);
      setModalState({ ...modalState, isOptionsModalShowing: true, activeModalType: modeOfArrival.modalType });
    }
    let situationText = `${situation.age} year-old`;

    if (situation.sex && situation.sex !== "skip") {
      situationText += ` ${situation.sex}`;
    }

    situationText += " patient";

    const arrivalMode = ARRIVAL_MODES.find(
      (mode) => mode.value === value
    );
    if (arrivalMode) {
      situationText += ` ${arrivalMode.label}`;
    }

    setSituation({ ...situation, situationSentence: situationText, modeOfArrival: value });
  };

  const handleMobilityOnArrivalChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newMobilityOnArrival = event.target.value;
    setSituation({ ...situation, mobilityOnArrival: newMobilityOnArrival });
  };
  const [mobilityOnArrivalOther, setMobilityOnArrivalOther] = useState(situation.mobilityOnArrival || "");
  const [mobilityOnArrivalOtherActive, setMobilityOnArrivalOtherActive] = useState(
    situation.mobilityOnArrival === mobilityOnArrivalOther && mobilityOnArrivalOther !== ""
  );

  const handleMobilityOnArrivalOther = () => {
    setMobilityOnArrivalOtherActive(true);
      setSituation({
        ...situation,
        mobilityOnArrival: mobilityOnArrivalOther,
      });   
  };

  const handleSexChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newSex = event.target.value;
    handleInputChange("sex", newSex);
  };

  const handleAgeChange = (value: string) => {
    console.log(value);
    const numericValue = Number(value);
    if (numericValue > 120 || numericValue < 14) {
      return;
    }
    handleInputChange("age", value);
  };

  const handleCloseModal = () => {
    setModalState({ ...modalState, isOptionsModalShowing: false });
  };

 
  return (
    <div className="">
      <div className="">
        <div className="container-fluid" style={{ padding: "30px 20px" }}>
          <div className="row">
            <div className="-dark-bg-dark-1">
              <div className="py-30">
                <div className="row y-gap-30">
                  <section className="col-12">
                    <div className="row">
                      <div className="col-md-6">
                        <NurtifyText label="Sex" className="headinqQuestion" />
                        <div className="d-flex flex-column gap-2">
                          <NurtifyRadio
                            label="Male"
                            name="sex"
                            value="male"
                            onChange={handleSexChange}
                            checked={situation.sex === "male"}
                          />
                          <NurtifyRadio
                            label="Female"
                            name="sex"
                            value="female"
                            onChange={handleSexChange}
                            checked={situation.sex === "female"}
                          />
                          <NurtifyRadio
                            label="Skip"
                            name="sex"
                            value="skip"
                            onChange={handleSexChange}
                            checked={situation.sex === "skip"}
                          />
                        </div>
                      </div>

                      <div className="col-md-6">
                        <NurtifyText label="Age" className="headinqQuestion mb-4" />
                        <div className="d-flex flex-column gap-3 mt-5">
                          <NurtifyRange
                            min={14}
                            max={120}
                            value={Number(situation.age)}
                            onChange={(value: ChangeEvent<HTMLInputElement>) =>
                              handleAgeChange(value.target.value)
                            }
                          />
                        </div>
                      </div>
                    </div>
                  </section>

                  <section className="col-12">
                    <NurtifyText label="Mode of Arrival" className="headinqQuestion" />
                    <div className="row g-4">
                      {ARRIVAL_MODES.map((mode: ArrivalMode) => (
                        <div className="col-lg-6" key={mode.value}>
                          <NurtifyRadio
                            label={mode.label}
                            name="mode-of-arrival"
                            value={mode.value}
                            onChange={(event) => handleModeOfArrivalChange(event, mode)}
                            checked={situation.modeOfArrival === mode.value}
                          />
                        </div>
                      ))}
                    </div>
                  </section>

                  <section className="col-12">
                    <NurtifyText label="Mobility on Arrival" className="headinqQuestion" />
                    <div className="row g-4">
                      <div className="col-lg-6">
                        <NurtifyRadio
                          label="Ambulatory"
                          name="mobility-on-arrival"
                          value="ambulatory"
                          onChange={handleMobilityOnArrivalChange}
                          checked={situation.mobilityOnArrival === "ambulatory"}
                        />
                      </div>
                      <div className="col-lg-6">
                        <NurtifyRadio
                          label="On wheelchair"
                          name="mobility-on-arrival"
                          value="on-wheelchair"
                          onChange={handleMobilityOnArrivalChange}
                          checked={situation.mobilityOnArrival === "on-wheelchair"}
                        />
                      </div>
                      <div className="col-lg-6">
                        <NurtifyRadio
                          label="Walking with stick"
                          name="mobility-on-arrival"
                          value="walking-with-stick"
                          onChange={handleMobilityOnArrivalChange}
                          checked={situation.mobilityOnArrival === "walking-with-stick"}
                        />
                      </div>
                      <div className="col-lg-6">
                        <NurtifyRadio
                          label="Walking with frame"
                          name="mobility-on-arrival"
                          value="walking-with-frame"
                          onChange={handleMobilityOnArrivalChange}
                          checked={situation.mobilityOnArrival === "walking-with-frame"}
                        />
                      </div>
                      <div className="col-lg-6">
                        <NurtifyRadio
                          label="Other"
                          name="mobility-on-arrival"
                          value={mobilityOnArrivalOther}
                          onChange={handleMobilityOnArrivalOther}
                          checked={mobilityOnArrivalOtherActive === true && situation.mobilityOnArrival === mobilityOnArrivalOther}
                        />
                        {mobilityOnArrivalOtherActive  && situation.mobilityOnArrival === mobilityOnArrivalOther && (
                        <div className="col-md-8 col-lg-6 my-3">
                          <NurtifyTextArea
                            placeholder="Type here for mobility on arrival..."
                            name="mobility-on-arrival"
                            value={mobilityOnArrivalOther}
                            onChange={(e) => {
                              setMobilityOnArrivalOther(e.target.value);
                              setSituation({
                                ...situation,
                                mobilityOnArrival: e.target.value,
                              }); 
                            }}
                          />
                        </div>
                      )}
                        
                      </div>
                    </div>

                  </section>

                  <section className="col-12">
                    <div className="d-flex justify-content-between">
                      <button 
                      className="button -md btn-nurtify-lighter"
                      onClick={goToPrevious}
                      >
                        Prev
                      </button>
                      <button
                        className="button -md btn-nurtify text-white"
                        onClick={goToNext}
                      >
                        Next
                      </button>
                    </div>
                  </section>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {modalState.isOptionsModalShowing && (
        <div className="options-modal">

          <div className="modal-content bg-white rounded-8 relative" ref={modalRef}>
          {/* <X size={24} className="absolute top-0 right-0 hover:text-gray-700 hover:cursor-pointer" onClick={handleCloseModal} /> */}
            {modalState.activeModalType === "a4" && (
              <>
                <h2>Hospital Name (Transferred from)</h2>
                <input type="text" placeholder="Enter hospital name" onChange={(e) => handleInputChange("transferFromHospital", e.target.value)} />
              </>
            )}
            {modalState.activeModalType === "b4" && (
              <>
                <h2>Hospital Name (Referred from)</h2>
                <input type="text" placeholder="Enter hospital name" onChange={(e) => handleInputChange("referredFromHospital", e.target.value)} />
              </>
            )}
            {modalState.activeModalType === "c4" && (
              <>
                <h2>Department Name (Referred from)</h2>
                <input type="text" placeholder="Enter department name" onChange={(e) => handleInputChange("referingDepartment", e.target.value)} />
              </>
            )}
            <div className="d-flex mt-3" style={{gap: '10px'}}>
            <button className="btn-nurtify text-white" onClick={handleCloseModal}>Submit</button>
            <button className="btn-nurtify text-white" onClick={handleCloseModal}>Cancel</button>
            </div>
           </div>
        </div>
      )}
    </div>
  );
}
