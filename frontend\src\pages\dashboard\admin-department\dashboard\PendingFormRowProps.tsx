import React from "react";
import { SquareArrowOutUpRight } from "lucide-react";
import { useAcceptForm, useRejectForm } from "@/hooks/form.query";
import { Form } from "@/services/api/types";

interface PendingFormRowProps {
  form: Form; // Remplacez par le bon type Form si disponible.
  onUserLists: () => void;
}

const PendingFormRow: React.FC<PendingFormRowProps> = ({ form, onUserLists }) => {
  const { mutate: acceptMutate } = useAcceptForm();
  const { mutate: rejectMutate } = useRejectForm();

  const handleAccept = () => {
    acceptMutate(form.uuid);
  };

  const handleDecline = () => {
    rejectMutate(form.uuid);
  };

  return (
    <tr key={form.uuid}>
      <td>{form.name}</td>
      <td>{form.user.first_name}</td>
      <td>
        <button className="action-button accept-button" onClick={handleAccept}>
          Accept
        </button>
        <button className="action-button decline-button" onClick={handleDecline}>
          Decline
        </button>
        <button
          className="action-button editIcon"
          aria-label="View User List"
          onClick={onUserLists}
        >
          <SquareArrowOutUpRight size={15} />
        </button>
      </td>
    </tr>
  );
};

export default PendingFormRow;