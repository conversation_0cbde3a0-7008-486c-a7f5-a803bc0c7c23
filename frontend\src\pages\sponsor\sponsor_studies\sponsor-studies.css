/* Sponsor Studies Page Styles */

.studies-container {
  background-color: var(--color-white);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  padding: 32px;
  margin-bottom: 32px;
  font-family: var(--font-primary);
}

/* Header Section */
.studies-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--color-light-3);
}

.studies-header h1 {
  font-size: 28px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Primary Action Button */
.create-study-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 24px;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 600;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, #2da1ac 100%);
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
  text-decoration: none;
}

.create-study-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(55, 183, 195, 0.4);
  background: linear-gradient(135deg, #2da1ac 0%, var(--color-purple-1) 100%);
  color: white;
}

.create-study-btn:active {
  transform: translateY(0);
}

.create-study-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.2);
}

/* Study Form Container */
.study-info-container {
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-light-4) 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 32px;
  border: 1px solid var(--color-light-3);
  position: relative;
  overflow: hidden;
}

.study-info-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-purple-1) 0%, #2da1ac 100%);
}

.study-info-container h2 {
  font-size: 24px;
  font-weight: 700;
  color: var(--color-dark-1);
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Form Styles */
.schedule-event-form {
  display: grid;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.text-danger {
  color: var(--color-red-1);
}

/* Error Message Styles */
.error-message {
  background-color: var(--color-red-2);
  border: 1px solid var(--color-red-1);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  color: var(--color-red-3);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.success-message {
  background-color: var(--color-green-6);
  border: 1px solid var(--color-green-4);
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 16px;
  color: var(--color-green-5);
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--color-light-3);
}

.cancel-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  background-color: var(--color-light-5);
  color: var(--color-dark-3);
  border: 1px solid var(--color-light-8);
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  background-color: var(--color-light-8);
  transform: translateY(-1px);
}

/* Search and Controls */
.studies-controls {
  margin-bottom: 24px;
}

.search-filter-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.policy-search-container {
  flex: 1;
  min-width: 300px;
}

.policy-search-box {
  position: relative;
  width: 100%;
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--color-dark-3);
  z-index: 2;
}

.policy-search-input {
  width: 100%;
  padding: 16px 20px 16px 48px;
  border: 2px solid var(--color-light-3);
  border-radius: 12px;
  font-size: 15px;
  background-color: var(--color-white);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.policy-search-input:focus {
  outline: none;
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
}

.policy-search-input::placeholder {
  color: var(--color-dark-3);
}

.clear-search {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--color-dark-3);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-search:hover {
  color: var(--color-red-1);
  background-color: var(--color-light-4);
}

/* Loading and Empty States */
.loading-studies,
.no-studies {
  padding: 60px 40px;
  text-align: center;
  color: var(--color-dark-3);
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
  border: 2px dashed var(--color-light-8);
  border-radius: 16px;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-studies {
  background: linear-gradient(135deg, var(--color-blue-2) 0%, var(--color-light-6) 100%);
  color: var(--color-blue-5);
}

.loading-studies::after {
  content: "";
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-blue-5);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
  margin-left: 12px;
  vertical-align: middle;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Visit Templates Section */
.visit-templates-section {
  background-color: var(--color-white);
  border-radius: 12px;
  padding: 24px;
  margin-top: 24px;
  border: 1px solid var(--color-light-3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--color-light-3);
}

.section-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-visit-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  background-color: var(--color-green-4);
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.add-visit-btn:hover {
  background-color: var(--color-green-5);
  transform: translateY(-1px);
}

/* Visit Template Items */
.visit-templates-list {
  display: grid;
  gap: 16px;
}

.visit-template-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, var(--color-white) 0%, var(--color-light-4) 100%);
  border: 1px solid var(--color-light-3);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.visit-template-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border-color: var(--color-purple-1);
}

.visit-template-info h3 {
  font-size: 18px;
  font-weight: 600;
  color: var(--color-dark-1);
  margin: 0 0 8px 0;
}

.visit-template-info p {
  font-size: 14px;
  color: var(--color-dark-3);
  margin: 4px 0;
}

.visit-template-actions {
  display: flex;
  gap: 8px;
}

.edit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: var(--color-orange-4);
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-btn:hover {
  background-color: var(--color-orange-6);
  transform: translateY(-1px);
}

.delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background-color: var(--color-red-1);
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.delete-btn:hover {
  background-color: var(--color-red-3);
  transform: translateY(-1px);
}

.loading-visits,
.no-visits {
  padding: 40px 20px;
  text-align: center;
  color: var(--color-dark-3);
  background-color: var(--color-light-4);
  border-radius: 8px;
  font-size: 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

/* Enhanced DataTable Integration */
.studies-container .data-table-container {
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(55, 183, 195, 0.08),
              0 2px 4px rgba(55, 183, 195, 0.04);
  border: 1px solid rgba(55, 183, 195, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.studies-container .data-table-container:hover {
  box-shadow: 0 12px 32px rgba(55, 183, 195, 0.12),
              0 4px 8px rgba(55, 183, 195, 0.06);
  transform: translateY(-2px);
}

/* Custom table header styling */
.studies-container .data-table thead th {
  background: linear-gradient(135deg, rgba(55, 183, 195, 0.08) 0%, rgba(55, 183, 195, 0.12) 100%);
  color: var(--color-dark-1);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 12px;
  border-bottom: 2px solid rgba(55, 183, 195, 0.15);
}

.studies-container .data-table tbody tr:hover {
  background: rgba(55, 183, 195, 0.03);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.08);
}

/* Pagination styling */
.studies-container .data-table-pagination {
  background: linear-gradient(135deg, rgba(55, 183, 195, 0.02) 0%, rgba(55, 183, 195, 0.05) 100%);
  border-top: 1px solid rgba(55, 183, 195, 0.15);
}

.studies-container .pagination-page.active {
  background: var(--color-purple-1);
  border-color: var(--color-purple-1);
  color: white;
  box-shadow: 0 4px 12px rgba(55, 183, 195, 0.3);
}

.studies-container .pagination-button:hover:not(:disabled),
.studies-container .pagination-page:hover:not(.active) {
  background: rgba(55, 183, 195, 0.1);
  border-color: rgba(55, 183, 195, 0.2);
  color: var(--color-purple-1);
}

/* Responsive Design */
@media (max-width: 1200px) {
  .studies-container {
    padding: 24px;
  }
  
  .study-info-container {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .studies-container {
    padding: 20px;
    margin-bottom: 20px;
  }

  .studies-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 24px;
  }

  .studies-header h1 {
    font-size: 24px;
  }

  .create-study-btn {
    width: 100%;
    justify-content: center;
    padding: 16px 24px;
  }

  .study-info-container {
    padding: 20px;
    margin-bottom: 24px;
  }

  .study-info-container h2 {
    font-size: 20px;
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 12px;
  }

  .form-actions button {
    width: 100%;
    justify-content: center;
  }

  .search-filter-container {
    flex-direction: column;
    align-items: stretch;
  }

  .policy-search-container {
    min-width: unset;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .add-visit-btn {
    width: 100%;
    justify-content: center;
  }

  .visit-template-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .visit-template-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .loading-studies,
  .no-studies {
    padding: 40px 20px;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .studies-container {
    padding: 16px;
    border-radius: 8px;
  }

  .studies-header h1 {
    font-size: 20px;
  }

  .study-info-container {
    padding: 16px;
    border-radius: 12px;
  }

  .study-info-container h2 {
    font-size: 18px;
  }

  .visit-templates-section {
    padding: 16px;
  }

  .section-header h3 {
    font-size: 18px;
  }

  .visit-template-item {
    padding: 16px;
  }

  .visit-template-info h3 {
    font-size: 16px;
  }

  .policy-search-input {
    padding: 14px 16px 14px 44px;
    font-size: 14px;
  }
}

/* Animation for smooth transitions */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.studies-container,
.study-info-container,
.visit-templates-section {
  animation: fadeInUp 0.5s ease-out;
}

/* Focus states for accessibility */
.create-study-btn:focus,
.cancel-btn:focus,
.add-visit-btn:focus,
.edit-btn:focus,
.delete-btn:focus {
  outline: 2px solid var(--color-purple-1);
  outline-offset: 2px;
}

.policy-search-input:focus {
  outline: none;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .studies-container,
  .study-info-container,
  .visit-templates-section {
    border-width: 2px;
  }
  
  .visit-template-item {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
