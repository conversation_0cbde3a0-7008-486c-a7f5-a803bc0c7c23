
.KPIinnerCard {
  width: 200px;
  background: #37B7C359;
  box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid #E5E7EB;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}



.KPIinnerCard:hover {
  box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.KPIcardMainContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
  min-width: 0; /* Prevents flex child from overflowing */
}

.KPIformTitleSection {
  display: flex;
  align-items: center;
  gap: 12px;
}

.KPIformIcon{
  width: 56px;
  height: 56px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  margin-left: auto;
  background-color: #f5f7fa;
  color: #4318FF;
}

.KPIformTitle {
  font-size: small;
  font-weight: 500;
  color: #140342;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.KPIformNumber {
  font-size: x-large;
  font-weight: 500;
  color: #140342;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


/* Responsive Styles */
@media (max-width: 768px) {
  .KPIinnerCard {
    flex-direction: column;
    align-items: flex-start;
    padding: 12px;
  }

}

@media (max-width: 480px) {
  .KPIformTitle {
    font-size: 14px;
  }

}


/***/


