.user-info-container {
    padding: 15px 30px 30px 30px; /* Changed top padding */
    min-height: calc(100vh - 80px); /* Adjust based on your header height */
    background-color: #ffffff; /* Change background color to white */
}

.user-info-header {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 20px;
}

.user-info-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-info-subtitle {
    margin-top: 10px;
    color: #666;
}

.user-info-form {
    max-width: 600px;
    margin: 0 auto;
}

.user-form-section {
    margin-bottom: 20px;
}

.user-form-section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.user-form-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.user-btn-submit, .user-btn-delete {
    padding: 10px 20px;
    border: none;
    border-radius: 0; /* Changed */
    cursor: pointer;
}

.user-btn-submit {
    background-color: #37B7C3;
    color: white;
}

.user-btn-delete {
    background-color: red;
    color: white;
}
