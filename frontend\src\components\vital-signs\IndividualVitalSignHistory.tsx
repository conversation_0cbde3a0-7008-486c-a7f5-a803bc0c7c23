import React, { useState, useEffect, useCallback } from 'react';
import { getFieldHistory } from '../../services/api/vital-signs.service';
import { formatDate } from '../../utils/vitalSignsUtils';
import './IndividualVitalSignHistory.css';

interface VitalSignHistoryEntry {
  uuid: string;
  field_name: string;
  old_value: string | null;
  new_value: string;
  changed_by: {
    id: number;
    email: string;
    first_name: string;
    last_name: string;
  };
  changed_at: string;
  notes?: string | null;
  vital_sign_uuid: string;
  recorded_at: string;
}

interface IndividualVitalSignHistoryProps {
  patientId: string;
  fieldName: string;
  fieldDisplayName: string;
  isVisible: boolean;
  onClose: () => void;
}

const IndividualVitalSignHistory: React.FC<IndividualVitalSignHistoryProps> = ({ 
  patientId, 
  fieldName, 
  fieldDisplayName,
  isVisible, 
  onClose 
}) => {
  const [history, setHistory] = useState<VitalSignHistoryEntry[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    changed_by: '',
    start_date: '',
    end_date: ''
  });

  const loadHistory = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const historyData = await getFieldHistory(patientId, fieldName, {
        changed_by: filters.changed_by || undefined,
        start_date: filters.start_date || undefined,
        end_date: filters.end_date || undefined,
        ordering: '-changed_at'
      });
      
      setHistory(historyData);
    } catch (err) {
      console.error('Error loading field history:', err);
      setError('Failed to load history data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [patientId, fieldName, filters]);

  useEffect(() => {
    if (isVisible && patientId && fieldName) {
      loadHistory();
    }
  }, [isVisible, patientId, fieldName, loadHistory]);

  const getChangeType = (fieldName: string, oldValue: string | null, newValue: string): string => {
    if (fieldName === 'created') return 'created';
    if (oldValue === null) return 'added';
    if (newValue === '') return 'removed';
    return 'updated';
  };

  const getChangeIcon = (changeType: string): string => {
    switch (changeType) {
      case 'created': return '➕';
      case 'added': return '➕';
      case 'updated': return '✏️';
      case 'removed': return '➖';
      default: return '📝';
    }
  };

  const getChangeColor = (changeType: string): string => {
    switch (changeType) {
      case 'created': return '#10b981';
      case 'added': return '#10b981';
      case 'updated': return '#f59e0b';
      case 'removed': return '#ef4444';
      default: return '#6b7280';
    }
  };

  const formatValue = (value: string | null, fieldName: string): string => {
    if (value === null || value === '') return '—';
    
    // Handle boolean values
    if (value === 'true') return 'Yes';
    if (value === 'false') return 'No';
    
    // Handle special field formatting
    switch (fieldName) {
      case 'consciousness_level': {
        const levelMap: Record<string, string> = {
          'A': 'Alert',
          'V': 'Voice',
          'P': 'Pain',
          'U': 'Unresponsive'
        };
        return levelMap[value] || value;
      }
      case 'news_severity':
        return value.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase());
      default:
        return value;
    }
  };

  const getFieldUnit = (fieldName: string): string => {
    const unitMap: Record<string, string> = {
      'temperature': '°C',
      'heart_rate': 'bpm',
      'systolic_bp': 'mmHg',
      'diastolic_bp': 'mmHg',
      'respiratory_rate': 'breaths/min',
      'oxygen_saturation': '%',
      'blood_sugar': 'mg/dL',
      'height': 'cm',
      'weight': 'kg'
    };
    return unitMap[fieldName] || '';
  };

  if (!isVisible) return null;

  return (
    <div className="individual-vital-sign-history-overlay">
      <div className="individual-vital-sign-history-modal">
        <div className="history-header">
          <h2>{fieldDisplayName} History</h2>
          <button className="close-button" onClick={onClose}>×</button>
        </div>

        <div className="history-filters">
          <div className="filter-group">
            <label>Changed By:</label>
            <input
              type="text"
              placeholder="Search by user..."
              value={filters.changed_by}
              onChange={(e) => setFilters(prev => ({ ...prev, changed_by: e.target.value }))}
            />
          </div>
          
          <div className="filter-group">
            <label>Date Range:</label>
            <div className="date-range">
              <input
                type="date"
                placeholder="Start date"
                value={filters.start_date}
                onChange={(e) => setFilters(prev => ({ ...prev, start_date: e.target.value }))}
              />
              <span>to</span>
              <input
                type="date"
                placeholder="End date"
                value={filters.end_date}
                onChange={(e) => setFilters(prev => ({ ...prev, end_date: e.target.value }))}
              />
            </div>
          </div>
          
          <button className="btn btn-primary" onClick={loadHistory}>
            Refresh
          </button>
        </div>

        <div className="history-content">
          {isLoading ? (
            <div className="history-loading">
              <div className="loading-spinner"></div>
              <p>Loading {fieldDisplayName} history...</p>
            </div>
          ) : error ? (
            <div className="history-error">
              <p>{error}</p>
              <button className="btn btn-primary" onClick={loadHistory}>
                Try Again
              </button>
            </div>
          ) : history.length === 0 ? (
            <div className="history-empty">
              <p>No {fieldDisplayName} history records found.</p>
            </div>
          ) : (
            <div className="history-timeline">
              {history.map((entry) => {
                const changeType = getChangeType(entry.field_name, entry.old_value, entry.new_value);
                const changeIcon = getChangeIcon(changeType);
                const changeColor = getChangeColor(changeType);
                const unit = getFieldUnit(fieldName);
                
                return (
                  <div key={entry.uuid} className="history-entry">
                    <div className="history-timeline-marker" style={{ backgroundColor: changeColor }}>
                      <span className="change-icon">{changeIcon}</span>
                    </div>
                    
                    <div className="history-entry-content">
                      <div className="history-entry-header">
                        <h4>{fieldDisplayName}</h4>
                        <span className="change-type" style={{ color: changeColor }}>
                          {changeType.charAt(0).toUpperCase() + changeType.slice(1)}
                        </span>
                      </div>
                      
                      <div className="history-entry-details">
                        {entry.old_value !== null && (
                          <div className="value-change">
                            <span className="old-value">
                              From: {formatValue(entry.old_value, entry.field_name)}{unit ? ` ${unit}` : ''}
                            </span>
                            <span className="arrow">→</span>
                            <span className="new-value">
                              To: {formatValue(entry.new_value, entry.field_name)}{unit ? ` ${unit}` : ''}
                            </span>
                          </div>
                        )}
                        
                        {entry.old_value === null && (
                          <div className="value-added">
                            <span className="new-value">
                              Set to: {formatValue(entry.new_value, entry.field_name)}{unit ? ` ${unit}` : ''}
                            </span>
                          </div>
                        )}
                        
                        <div className="history-meta">
                          <div className="history-user">
                            <span className="user-label">Changed by:</span>
                            <span className="user-name">
                              {entry.changed_by.first_name} {entry.changed_by.last_name}
                            </span>
                            <span className="user-email">({entry.changed_by.email})</span>
                          </div>
                          
                          <div className="history-timestamp">
                            <span className="timestamp-label">Date:</span>
                            <span className="timestamp-value">{formatDate(entry.changed_at)}</span>
                          </div>
                          
                          {entry.notes && (
                            <div className="history-notes">
                              <span className="notes-label">Notes:</span>
                              <span className="notes-value">{entry.notes}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default IndividualVitalSignHistory; 