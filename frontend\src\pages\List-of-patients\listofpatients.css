.patient-details-container {
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.patient-details-header {
    margin-bottom: 1.5rem;
}

.page-title {
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
    font-weight: 600;
}

.policy-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.policy-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-container {
    position: relative;
    /* width: 250px; */
}

.search-icon {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.search-input {
    width: 100%;
    padding: 8px 10px 8px 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.clear-search {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-search:hover {
    color: #333;
}

.policy-search-container {
    margin-top: 20px;
}

.policy-search-box {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.policy-search-input {
    width: 100%;
    padding: 12px 40px 12px 40px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.policy-table-container {
    overflow-x: auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.policy-table {
    width: 100%;
    border-collapse: collapse;
}

.policy-table th, .policy-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.policy-table th {
    background-color: #37B7C3;
    color: #fff;
    font-weight: 600;
    position: sticky;
    top: 0;
}

.policy-table tr:hover {
    background-color: #f5f9fa;
}

.policy-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.arrowUp {
    background-color: #37B7C3;
    border: none;
    border-radius: 8px;
    color: white;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.arrowUp:hover {
    background-color: #2da8b4;
    transform: translateY(-2px);
}

.downloadd {
    background-color: #4F547B;
    border: none;
    border-radius: 8px;
    color: white;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.downloadd:hover {
    background-color: #3f4361;
    transform: translateY(-2px);
}

.search-loading {
    display: inline-flex;
    align-items: center;
    margin-left: 10px;
    color: #37B7C3;
}

.search-loading svg {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.no-results {
    text-align: center;
    padding: 40px 0;
    color: #666;
}

.no-results svg {
    margin-bottom: 16px;
    color: #999;
}

.error-message {
    text-align: center;
    padding: 20px;
    color: #d32f2f;
    background-color: #ffebee;
    border-radius: 8px;
    margin: 20px 0;
}

/* Patients specific styles */
.patients-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

.patients-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.patients-table {
    width: 100%;
    border-collapse: collapse;
}

.patients-table th, .patients-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.patients-table th {
    background-color: #37B7C3;
    color: #fff;
    font-weight: 600;
    position: sticky;
    top: 0;
}

.patients-table tr:hover {
    background-color: #f5f9fa;
}

.table-container {
    overflow-x: auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.tabs-wrapper {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.tab-button {
    padding: 10px 20px;
    border: none;
    background-color: #f5f5f5;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-weight: 500;
    color: #666;
}

.action-button {
    background-color: #37B7C3;
    border: none;
    border-radius: 8px;
    color: white;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-button:hover {
    background-color: #2da8b4;
    transform: translateY(-2px);
}

.loading, .error, .no-patients {
    text-align: center;
    padding: 20px;
    font-size: 18px;
    color: #666;
}

.error-container {
    text-align: center;
    padding: 50px 20px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    margin: 30px auto;
    max-width: 600px;
}

.refresh-button {
    display: flex;
    align-items: center;
    gap: 5px;
    background-color: #37B7C3;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    margin: 20px auto 0;
}

.delete-confirmation-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.delete-confirmation-content {
    background-color: white;
    padding: 25px;
    border-radius: 8px;
    width: 90%;
    max-width: 450px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.delete-confirmation-content h3 {
    margin-top: 0;
    color: #d32f2f;
}

.delete-confirmation-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.delete-confirmation-buttons .cancel-button {
    background-color: #f5f5f5;
    color: #333;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.delete-confirmation-buttons .delete-button {
    background-color: #d32f2f;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
}

.delete-confirmation-buttons .delete-button:disabled {
    background-color: #e57373;
    cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .policy-actions, .patients-actions {
        flex-direction: column;
        align-items: center;
        gap: 8px;
    }

    .policy-table th, .policy-table td,
    .patients-table th, .patients-table td {
        padding: 12px 8px;
    }

    .policy-title h1 {
        font-size: 1.5rem;
    }

    .patients-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .patients-actions {
        width: 100%;
        flex-wrap: wrap;
    }

    .search-container {
        width: 100%;
    }

    .tab-button {
        padding: 8px 16px;
        font-size: 14px;
    }
}

/* Visit specific styles */
.visit-detail {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px;
  margin-bottom: 4px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.visit-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.visit-name {
  font-weight: 500;
  color: #2c3e50;
}

.nurse-name {
  font-size: 0.875rem;
  color: #666;
}

.visit-status {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  margin-left: 12px;
}

/* Status colors */
.visit-status.notarraived {
  background-color: #ffd700;
  color: #856404;
}

.visit-status.inhospital {
  background-color: #17a2b8;
  color: #fff;
}

.visit-status.discharged {
  background-color: #28a745;
  color: white;
}

/* Patient Profile Picture Styles */
.patient-profile-picture {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.patient-profile-picture:hover {
    transform: scale(1.05);
    border-color: #37B7C3;
    box-shadow: 0 4px 12px rgba(55, 183, 195, 0.2);
}

.patient-profile-picture img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

.patient-profile-picture .profile-picture-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: #6c757d;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    transition: all 0.3s ease;
}

.patient-profile-picture .profile-picture-placeholder.hidden {
    display: none;
}

.patient-profile-picture:hover img,
.patient-profile-picture:hover .profile-picture-placeholder {
    transform: scale(1.1);
}

