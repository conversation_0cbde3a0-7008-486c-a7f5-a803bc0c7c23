import "./addsponsororg.css";
import { useNavigate } from "react-router-dom";
import { useState } from "react";
import NurtifyText from "@components/NurtifyText.tsx";
import NurtifyInput from "@components/NurtifyInput.tsx";
import NurtifyTextArea from "@components/NurtifyTextArea.tsx";
import { motion } from "framer-motion";
import { Building, MapPin, CheckCircle } from "lucide-react";
import { useCreateSponsorOrgMutation } from "@/hooks/sponsorOrg.query";
import AddDepHospModal from "@/components/modal/AddDepHospModal";

const SPECIALITY_CHOICES = [
  { value: "pharma", label: "Pharmaceutical Company" },
  { value: "research", label: "Research Institute" },
  { value: "government", label: "Government Body" },
  { value: "private", label: "Private Organization" },
  { value: "other", label: "Other" } 
];

export default function AddSponsorOrg() {
  const createSponsorOrgMutation = useCreateSponsorOrgMutation();
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [newSponsorOrgUuid, setNewSponsorOrgUuid] = useState<string | null>(null);
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const [formData, setFormData] = useState({
    name: "",
    phone_number: "",
    extension: "",
    point_of_contact_Person: "",
    primary_address: "",
    secondary_address: "", // This is optional in validation
    postcode: "",
    country: "",
    organization_type: "",
    description: "", // Added description
  });

  const validateField = (name: string, value: string) => {
    const maxLengths: Record<string, number> = {
      name: 50,
      phone_number: 20,
      extension: 20,
      point_of_contact_Person: 50,
      primary_address: 50,
      secondary_address: 50, // Max length even if optional
      postcode: 20,
      country: 20,
      organization_type: 50, // Assuming max length for 'other' if it becomes a text input
      description: 100,
    };

    // Fields that are not strictly required (can be empty, but have max length if filled)
    const optionalFields = ['secondary_address', 'description'];

    if (optionalFields.includes(name)) {
      if (value && value.length > maxLengths[name]) {
        return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} must not exceed ${maxLengths[name]} characters`;
      }
      return ''; // No error if optional field is empty or within limits
    }
    
    // Required fields validation
    if (!value.trim()) {
      return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} is required`;
    }
    if (value.length > maxLengths[name]) {
      return `${name.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())} must not exceed ${maxLengths[name]} characters`;
    }
    return '';
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  const handleSelectChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
        ...prev,
        [name]: value
    }));

    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const newErrors: Record<string, string> = {};
    Object.keys(formData).forEach(key => {
      const error = validateField(key, formData[key as keyof typeof formData]);
      if (error) newErrors[key] = error;
    });

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      try {
        // Transform sponsor org name: replace spaces with underscores (optional, based on backend requirement)
        const formattedData = {
          ...formData,
          name: formData.name.trim().replace(/\s+/g, '_') 
        };

        const response = await createSponsorOrgMutation.mutateAsync(formattedData);
        setNewSponsorOrgUuid(response.uuid);
        setShowPopup(true);
      } catch (error) {
        console.error("Error creating sponsor org:", error);
        // Potentially set a general form error here if API returns errors
      }
    } else {
      const step1Fields = ['name', 'organization_type', 'phone_number', 'extension', 'point_of_contact_Person'];
      const hasStep1Errors = step1Fields.some(field => newErrors[field]);
      setCurrentStep(hasStep1Errors ? 1 : 2);
    }
  };
  
  const nextStep = () => {
    // For a better UX, you might want to validate *current step's fields* here
    // before allowing to go to the next step.
    // However, to strictly follow AddHospital's logic (validate on change and full submit),
    // we don't block here. The submit will handle redirection to error step.
    if (currentStep < 2) { // Assuming 2 steps
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handlePopupYes = () => {
    if (newSponsorOrgUuid) {
      navigate(`/org/dashboard/sponsorOrg-details/${newSponsorOrgUuid}/sponsorAdmin`); 
    }
    setShowPopup(false);
  };

  const handlePopupNo = () => {
    navigate("/org/dashboard/sponsor-orgs", { replace: true });
    setShowPopup(false);
  };

  return (
    <div className="add-sponsor-org-container">
      <div className="add-sponsor-org-header">
        <div className="add-sponsor-org-title">
          <h1>
            <Building size={24} style={{ marginRight: "10px" }} />
            Add New Sponsor Org
          </h1>
        </div>
        <div className="add-sponsor-org-subtitle">
          <h6>Create and manage sponsor org information in your network</h6>
        </div>
      </div>
      
      <div className="sponsor-org-progress">
        <div
          className={`sponsor-org-step ${currentStep >= 1 ? "active" : ""} ${
            currentStep > 1 ? "completed" : ""
          }`}
        >
          <div className="step-indicator">
            {currentStep > 1 ? <CheckCircle size={16} /> : 1}
          </div>
          <div className="step-label">Sponsor Org Info</div>
        </div>
        <div
          className={`sponsor-org-step ${currentStep >= 2 ? "active" : ""}`}
        >
          <div className="step-indicator">2</div>
          <div className="step-label">Address Details</div>
        </div>
      </div>
      
      <form className="add-sponsor-org-form" onSubmit={handleSubmit}>
        {/* Step 1: Sponsor Org Information */}
        {currentStep === 1 && (
          <motion.div
            className="form-section"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="form-section-title">
              <Building size={18} style={{ marginRight: "8px", verticalAlign: "middle" }} />
              Sponsor Org Information
            </h3>
            <div className="row y-gap-30">
              <div className="col-md-6">
                <NurtifyText label="Sponsor Org Name*" />
                <NurtifyInput
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter Sponsor Org Name here"
                  maxLength={50} 
                />
                {errors.name && <div className="field-error">{errors.name}</div>}
              </div>

              <div className="col-md-6">
                <NurtifyText label="Organization Type*" />
                <select
                  name="organization_type"
                  value={formData.organization_type}
                  onChange={handleSelectChange}
                  className="nurtify-select" // Added class for styling
                >
                  <option value="">Select Organization Type</option>
                  {SPECIALITY_CHOICES.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {errors.organization_type && <div className="field-error">{errors.organization_type}</div>}
              </div>
            </div>

            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Phone*" />
                <NurtifyInput
                  type="text" // Changed to text to allow symbols like + or ()
                  name="phone_number"
                  value={formData.phone_number}
                  onChange={handleInputChange}
                  placeholder="Enter Phone Number here"
                  maxLength={20}
                />
                {errors.phone_number && <div className="field-error">{errors.phone_number}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Extension*" />
                <NurtifyInput
                  type="text" // Changed to text
                  name="extension"
                  value={formData.extension}
                  onChange={handleInputChange}
                  placeholder="Enter Extension here"
                  maxLength={20}
                />
                {errors.extension && <div className="field-error">{errors.extension}</div>}
              </div>
            </div>

            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Point of Contact Person*" />
                <NurtifyInput
                  type="text"
                  name="point_of_contact_Person"
                  value={formData.point_of_contact_Person}
                  onChange={handleInputChange}
                  placeholder="Enter Point of Contact Person here"
                  maxLength={50}
                />
                {errors.point_of_contact_Person && <div className="field-error">{errors.point_of_contact_Person}</div>}
              </div>
            </div>
            
            <div className="sponsor-org-form-actions">
              <button
                type="button"
                className="sponsor-org-btn-submit"
                onClick={nextStep}
              >
                Next
              </button>
            </div>
          </motion.div>
        )}
        
        {/* Step 2: Address Information */}
        {currentStep === 2 && (
          <motion.div
            className="form-section"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <h3 className="form-section-title">
              <MapPin size={18} style={{ marginRight: "8px", verticalAlign: "middle" }} />
              Address Information
            </h3>
            
            <div className="row y-gap-30">
              <div className="col-md-6">
                <NurtifyText label="Primary Address*" />
                <NurtifyInput // Changed from NurtifyTextArea to NurtifyInput for consistency with AddHospital
                  type="text"
                  name="primary_address"
                  value={formData.primary_address}
                  onChange={handleInputChange}
                  placeholder="Enter Primary Address here"
                  maxLength={50}
                />
                {errors.primary_address && <div className="field-error">{errors.primary_address}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Secondary Address" /> {/* Optional */}
                <NurtifyInput // Changed from NurtifyTextArea
                  type="text"
                  name="secondary_address"
                  value={formData.secondary_address}
                  onChange={handleInputChange}
                  placeholder="Enter Secondary Address here (Optional)"
                  maxLength={50}
                />
                {errors.secondary_address && <div className="field-error">{errors.secondary_address}</div>}
              </div>
            </div>
            
            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-6">
                <NurtifyText label="Postcode*" />
                <NurtifyInput
                  type="text"
                  name="postcode"
                  value={formData.postcode}
                  onChange={handleInputChange}
                  placeholder="Enter Postcode here"
                  maxLength={20}
                />
                {errors.postcode && <div className="field-error">{errors.postcode}</div>}
              </div>
              <div className="col-md-6">
                <NurtifyText label="Country*" />
                <NurtifyInput
                  type="text"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  placeholder="Enter Country here"
                  maxLength={20}
                />
                {errors.country && <div className="field-error">{errors.country}</div>}
              </div>  
            </div>
            
            <div className="row y-gap-30" style={{ marginTop: "20px" }}>
              <div className="col-md-12">
                <NurtifyText label="Description" /> {/* Optional */}
                <NurtifyTextArea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Enter Description here (Optional)"
                  maxLength={100}
                />
                {errors.description && <div className="field-error">{errors.description}</div>}
              </div>
            </div>
            
            <div className="sponsor-org-form-actions">
              <button
                type="button"
                className="sponsor-org-btn-cancel"
                onClick={prevStep}
              >
                Back
              </button>
              <button
                type="submit"
                className="sponsor-org-btn-submit"
                disabled={createSponsorOrgMutation.isPending}
              >
                {createSponsorOrgMutation.isPending
                  ? "Submitting..."
                  : "Add Sponsor Org"}
              </button>
            </div>
          </motion.div>
        )}
      </form>
      {showPopup && (
        <AddDepHospModal
          type="SponsorOrg"
          isOpen={showPopup}
          onNo={handlePopupNo}
          onYes={handlePopupYes}
          // Pass item name and type for a better modal message, if AddDepHospModal supports it
          // itemName={formData.name.trim().replace(/\s+/g, '_')} 
          // itemType="Sponsor Org"
        />
      )}
    </div>
  );
}