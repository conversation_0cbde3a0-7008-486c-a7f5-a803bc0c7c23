import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import LightFooter from "@/shared/LightFooter";
import useHolisticFormTabStore from "@/store/holisticFormTabState";
import "../forms.css";
import PatientDetails from "./PatientDetails";
import Situation from "./Situation";
import Background from "./Background";
import Assessment from "./Assessment";
import Recommendation from "./Recommendation";

const buttons: string[] = [
  "Patient Details",
  "Situation",
  "Background",
  "Assessment",
  "Recommendation",
];

const HolisticForm: React.FC = (): JSX.Element => {
  const { activeTab, setActiveTab } = useHolisticFormTabStore();

  return (
    <>
      <Wrapper>
        <Preloader />
        <div
          className="content-wrapper js-content-wrapper overflow-hidden"
          style={{ paddingBottom: "88px" }}
        >
          <div className="dashboard__content bg-light-4">
            <div className="row y-gap-20">
              <div className="col-12">
                <div className="tabs-wrapper">
                  {buttons.map((elm, i) => (
                    <button
                      key={i}
                      onClick={() => setActiveTab(i + 1)}
                      className={`tab-button ${activeTab === i + 1 ? "active" : ""}`}
                      type="button"
                    >
                      {elm}
                    </button>
                  ))}
                </div>

                <div className="tabs__content pt-30">
                  {activeTab === 1 && <PatientDetails />}
                  {activeTab === 2 && <Situation />}
                  {activeTab === 3 && <Background />}
                  {activeTab === 4 && <Assessment />}
                  {activeTab === 5 && <Recommendation />}
                </div>
              </div>
            </div>
          </div>
          <LightFooter />
        </div>
      </Wrapper>
    </>
  );
};

export default HolisticForm;