import "./test-new-dashboard.css";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { 
  Users, 
  FileText, 
  UserCheck, 
  ChevronDown, 
  Plus,
  CheckCircle,
  XCircle,
  Eye,
  MessageSquare,
  BarChart,
  Hospital,
  Building,
  Handshake,
  UserPlus,
  Layers,
  FolderPlus,
  FilePlus,
  Scissors,
  Calendar,
  Send,
  MessageCircleQuestion,
  User,
} from "lucide-react";
import { User as UserData } from "@/services/api/types.ts";
import { useAnalyticsDepartmentAdminQuery, useCurrentUserQuery } from "@/hooks/user.query";
import Header from "@/shared/Header";
import DashboardSidebar from "@/components/common/DashboardSidebar";
import { hasPermission, PermissionKey } from "@/services/permission-system";

type MenuItem = {
  icon: JSX.Element;
  label: string;
  path: string;
  onClick?: () => void;
  permissionKey: string;
};

export default function TestNewDashboard() {
  const { departmentUuid } = useParams<{ departmentUuid: string }>();
  const [, setSnippetsModalOpen] = useState(false);
  const currentUser = useCurrentUserQuery();
  const hospitalUuid = !currentUser.data?.is_superuser ? (currentUser.data?.hospital?.uuid || null) : null;

  const {
    data: analyticsData,
  } = useAnalyticsDepartmentAdminQuery(departmentUuid || "");
  
  const [, setUserData] = useState<Partial<UserData>[]>([]);

  useEffect(() => {
    if (analyticsData?.data) {
      setUserData(analyticsData.data.users || []);
    }
  }, [analyticsData]);

  const basePath = "/org/dashboard";

  // Mock data for demonstration - replace with real data
  const mockStudyInvitations = [
    { id: 1, studyName: "Name goes h", source: "Name goes h", status: "Pending" },
    { id: 2, studyName: "Name goes h", source: "Name goes h", status: "Pending" },
    { id: 3, studyName: "Name goes h", source: "Name goes h", status: "Pending" },
  ];

  const mockRequests = [
    { id: 1, name: "Name goes h", nhsId: "1234545", source: "Doctor", status: "Pending" },
    { id: 2, name: "Name goes h", nhsId: "1234545", source: "Doctor", status: "Pending" },
    { id: 3, name: "Name goes h", nhsId: "1234545", source: "Doctor", status: "Pending" },
    { id: 4, name: "Name goes h", nhsId: "1234545", source: "Doctor", status: "Pending" },
    { id: 5, name: "Name goes h", nhsId: "1234545", source: "Doctor", status: "Pending" },
  ];

  const menuItems: MenuItem[] = [
    // super user
    {
      icon: <BarChart size={20} />,
      label: "Nurtify Dashboard",
      path: "/analytics", // Changed from "/dashboard/analytics"
      permissionKey: "viewDashboard",
    },
    {
      icon: <Hospital size={20} />,
      label: "Hospitals",
      path: "/hospitals", // Changed from "/dashboard/hospitals"
      permissionKey: "viewHospitals",
    },
    {
      icon: <Building size={20} />,
      label: "Add Hospital",
      path: "/add-hospital", // Changed from "/dashboard/add-hospital"
      permissionKey: "addHospital",
    },
    {
      icon: <Handshake size={20} />,
      label: "Sponsors",
      path: "/sponsors", // Changed from "/dashboard/add-hospital"
      permissionKey: "viewSponsors",
    },
    {
      icon: <UserPlus size={20} />,
      label: "Add Sponsor",
      path: "/add-sponsor", // Changed from "/dashboard/add-hospital"
      permissionKey: "addSponsor",
    },
    // hospital Admin
    {
      icon: <BarChart size={20} />,
      label: "Hospital Dashboard",
      path: "/hospital-dashboard", // Still needs the dynamic UUID
      permissionKey: "adminHospital",
    },
    // department Admin
    {
      icon: <BarChart size={20} />,
      label: "Department Dashboard",
      path: `/admin-department/${departmentUuid}/`, // Still needs the dynamic UUID
      permissionKey: "adminDepartment",
    },
    {
      icon: <BarChart size={20} />,
      label: "My Hospital",
      path: `/my-hospital/${hospitalUuid}/`, // Still needs the dynamic UUID
      permissionKey: "MyHospital",
    },
    {
      icon: <BarChart size={20} />,
      label: "My Department",
      path: `/my-department/${departmentUuid}/`, // Still needs the dynamic UUID
      permissionKey: "MyDepartment",
    },  
    {
      icon: <Layers size={20} />,
      label: "Departments",
      path: "/department", // Changed from "/dashboard/department"
      permissionKey: "viewDepartment",
    },
    {
      icon: <FolderPlus size={20} />,
      label: "Add Department",
      path: "/add-department", // Changed from "/dashboard/add-department"
      permissionKey: "addDepartment",
    },
    {
      icon: <Users size={20} />,
      label: "Users List",
      path: "/users", // Changed from "/dashboard/users"
      permissionKey: "viewUsers",
    },
    {
      icon: <UserPlus size={20} />,
      label: "Add User",
      path: "/add-user", // Changed from "/dashboard/add-user"
      permissionKey: "addUser",
    },
    // department Admin + department user
    {
      icon: <FileText size={20} />,
      label: "Policy",
      path: "/policy", // Changed from "/dashboard/policy"
      permissionKey: "viewPolicy",
    },
    {
      icon: <FilePlus size={20} />,
      label: "Add Policy",
      path: "/add-policy", // Changed from "/dashboard/add-policy"
      permissionKey: "addPolicy",
    },
    {
      icon: <Scissors size={20} />,
      label: "Snippets",
      path: "/snippets", // Changed from "/dashboard/snippets"
      onClick: () => setSnippetsModalOpen(true),
      permissionKey: "viewSnippets",
    },
    {
      icon: <Calendar size={20} />,
      label: "Study Settings",
      path: "/study-settings", // Changed from "/dashboard/schedule-event"
      permissionKey: "scheduleEvent",
    },
    {
      icon: <FileText size={20} />,
      label: "Studies",
      path: "/studies", // Changed from "/dashboard/studies"
      permissionKey: "viewStudies",
    },
    {
      icon: <Send size={20} />,
      label: "Studies Invitations",
      path:"/invitations",
      permissionKey:"studiesInvitations",
    },
    // super user + department Admin + department user
    {
      icon: <MessageCircleQuestion size={20} />,
      label: "Pending Request",
      path:"/pending-request",
      permissionKey:"pendingRequest",
    },
    {
      icon: <MessageSquare size={20} />,
      label: "Live Chat",
      path: "/live-chat",
      permissionKey: "viewLiveChat", // Assuming this permission will be added/handled
    },
    {
      icon: <Calendar size={20} />,
      label: "My Logs",
      path: "/user-logs",
      permissionKey: "myLogs",
    },
    {
      icon: <User size={20} />,
      label: "Profile",
      path: "/profile", // Changed from "/dashboard/profile"
      permissionKey: "viewProfile",
    },
  ];

  const filteredMenuItems = menuItems.filter((item) =>
    hasPermission(item.permissionKey as PermissionKey, currentUser.data)
  );

  return (
    <div className="test-full-dashboard">
      {/* Top Header */}
     <Header/>

      <div className="test-main-layout">
        {/* Sidebar */}
        <DashboardSidebar menuItems={filteredMenuItems} basePath={basePath} />

        {/* Main Content */}
        <main className="test-main-content">
          <div className="test-dashboard-container">
            {/* Header */}
            <div className="test-dashboard-header">
              <h1 className="test-welcome-title">
                Welcome Back, <span className="test-user-name">Amine Haouem</span>
              </h1>
              <p className="test-welcome-subtitle">Lorem Ipsum</p>
            </div>

            {/* Top KPI Cards */}
            <div className="test-kpi-row">
              <div className="test-kpi-card">
                <div className="test-kpi-header">
                  <div className="test-kpi-icon test-patients-icon">
                    <Users size={20} />
                  </div>
                  <div className="test-kpi-title-section">
                    <h3>Patients</h3>
                    <div className="test-kpi-dropdown">
                      This month <ChevronDown size={16} />
                    </div>
                  </div>
                </div>
                <div className="test-kpi-number">154</div>
                <div className="test-kpi-change test-negative">
                  <span className="test-change-text">Decreased -10 Patients This month!</span>
                  <span className="test-change-percentage">7.1% ↓</span>
                </div>
              </div>

              <div className="test-kpi-card">
                <div className="test-kpi-header">
                  <div className="test-kpi-icon test-policies-icon">
                    <FileText size={20} />
                  </div>
                  <div className="test-kpi-title-section">
                    <h3>Policies</h3>
                    <div className="test-kpi-dropdown">
                      This month <ChevronDown size={16} />
                    </div>
                  </div>
                </div>
                <div className="test-kpi-number">24</div>
                <div className="test-kpi-change test-negative">
                  <span className="test-change-text">Decreased -10 Policies This month!</span>
                  <span className="test-change-percentage">7.1% ↓</span>
                </div>
              </div>

              <div className="test-kpi-card">
                <div className="test-kpi-header">
                  <div className="test-kpi-icon test-sponsors-icon">
                    <UserCheck size={20} />
                  </div>
                  <div className="test-kpi-title-section">
                    <h3>Sponsors</h3>
                    <div className="test-kpi-dropdown">
                      This month <ChevronDown size={16} />
                    </div>
                  </div>
                </div>
                <div className="test-kpi-number">64</div>
                <div className="test-kpi-change test-positive">
                  <span className="test-change-text">Gained +13 Sponsors This month!</span>
                  <span className="test-change-percentage">8.3% ↑</span>
                </div>
              </div>
            </div>

            {/* Middle Section */}
            <div className="test-middle-section">
              {/* Staff/Users Card */}
              <div className="test-staff-card">
                <div className="test-staff-header">
                  <div>
                    <h3>Staff/Users</h3>
                    <div className="test-staff-dropdown">
                      This month <ChevronDown size={16} />
                    </div>
                  </div>
                  <button className="test-add-user-btn">
                    <Plus size={16} />
                    Add New User
                  </button>
                </div>
                
                <div className="test-staff-number">32</div>
                <div className="test-staff-change test-positive">
                  <span className="test-change-text">Added +3 Users This month!</span>
                  <span className="test-change-percentage">8.3% ↑</span>
                </div>

                <div className="test-staff-breakdown">
                  <div className="test-staff-type">
                    <div className="test-staff-type-header">
                      <div className="test-staff-type-icon test-doctors-icon">
                        <Users size={16} />
                      </div>
                      <span>Doctors</span>
                      <span className="test-staff-count">06</span>
                    </div>
                    <div className="test-progress-bar">
                      <div className="test-progress-fill test-doctors-progress" style={{width: '56%'}}></div>
                    </div>
                    <span className="test-progress-percentage">56%</span>
                  </div>

                  <div className="test-staff-type">
                    <div className="test-staff-type-header">
                      <div className="test-staff-type-icon test-nurses-icon">
                        <Users size={16} />
                      </div>
                      <span>Nurses</span>
                      <span className="test-staff-count">08</span>
                    </div>
                    <div className="test-progress-bar">
                      <div className="test-progress-fill test-nurses-progress" style={{width: '56%'}}></div>
                    </div>
                    <span className="test-progress-percentage">56%</span>
                  </div>

                  <div className="test-staff-type">
                    <div className="test-staff-type-header">
                      <div className="test-staff-type-icon test-receptionist-icon">
                        <Users size={16} />
                      </div>
                      <span>Receptionist</span>
                      <span className="test-staff-count">06</span>
                    </div>
                    <div className="test-progress-bar">
                      <div className="test-progress-fill test-receptionist-progress" style={{width: '56%'}}></div>
                    </div>
                    <span className="test-progress-percentage">56%</span>
                  </div>

                  <div className="test-staff-type">
                    <div className="test-staff-type-header">
                      <div className="test-staff-type-icon test-others-icon">
                        <Users size={16} />
                      </div>
                      <span>Others</span>
                      <span className="test-staff-count">12</span>
                    </div>
                    <div className="test-progress-bar">
                      <div className="test-progress-fill test-others-progress" style={{width: '56%'}}></div>
                    </div>
                    <span className="test-progress-percentage">56%</span>
                  </div>
                </div>
              </div>

              {/* Studies Invitations Card */}
              <div className="test-studies-card">
                <h3>Studies Invitations</h3>
                
                <div className="test-studies-stats">
                  <div className="test-stat-item">
                    <span className="test-stat-label">Total Invitations:</span>
                    <span className="test-stat-value">04</span>
                  </div>
                  <div className="test-stat-item">
                    <span className="test-stat-label">Accepted:</span>
                    <span className="test-stat-value">04</span>
                  </div>
                  <div className="test-stat-item">
                    <span className="test-stat-label">Rejected:</span>
                    <span className="test-stat-value">04</span>
                  </div>
                </div>

                <div className="test-studies-table-container">
                  <table className="test-studies-table">
                    <thead>
                      <tr>
                        <th>Study Name</th>
                        <th>Source</th>
                        <th>Status</th>
                        <th>Action</th>
                      </tr>
                    </thead>
                    <tbody>
                      {mockStudyInvitations.map((invitation) => (
                        <tr key={invitation.id}>
                          <td>{invitation.studyName}</td>
                          <td>{invitation.source}</td>
                          <td>
                            <span className="test-status-badge test-pending">{invitation.status}</span>
                          </td>
                          <td>
                            <div className="test-action-buttons">
                              <button className="test-action-btn test-accept">
                                <CheckCircle size={16} />
                              </button>
                              <button className="test-action-btn test-reject">
                                <XCircle size={16} />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            {/* Requests Section */}
            <div className="test-requests-section">
              <div className="test-requests-header">
                <h3>Requests</h3>
                <div className="test-requests-stats">
                  <span className="test-total-requests">Total Requests: 04</span>
                  <div className="test-requests-chart">
                    <div className="test-chart-bar test-accepted" style={{height: '60%'}}></div>
                    <div className="test-chart-bar test-pending" style={{height: '40%'}}></div>
                    <div className="test-chart-bar test-rejected" style={{height: '20%'}}></div>
                  </div>
                  <div className="test-chart-legend">
                    <div className="test-legend-item">
                      <div className="test-legend-color test-accepted"></div>
                      <span>04 Accepted</span>
                    </div>
                    <div className="test-legend-item">
                      <div className="test-legend-color test-pending"></div>
                      <span>03 Pending</span>
                    </div>
                    <div className="test-legend-item">
                      <div className="test-legend-color test-rejected"></div>
                      <span>02 Rejected</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="test-requests-table-container">
                <table className="test-requests-table">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>NHS Id</th>
                      <th>Source</th>
                      <th>Status</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {mockRequests.map((request) => (
                      <tr key={request.id}>
                        <td>{request.name}</td>
                        <td>{request.nhsId}</td>
                        <td>{request.source}</td>
                        <td>
                          <span className="test-status-badge test-pending">{request.status}</span>
                        </td>
                        <td>
                          <button className="test-details-btn">
                            <Eye size={16} />
                            Details
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
