 
import { useState } from "react";
import Preloader from "@/components/common/Preloader";
import Wrapper from "@/components/common/Wrapper";
import DataTable, { DataTableSearch } from "@/components/common/DataTable";
import { 
  useStudyEnrollmentsByPatientQuery
} from "@/hooks/study.query";
import { usePatientsQuery } from "@/hooks/patient.query";
import { useCurrentUserQuery } from "@/hooks/user.query";

// Define type for enrollment data
interface Enrollment {
  uuid: string;
  study_uuid: string;
  study_id?: number;
  patient_uuid?: string;
  patient_nhs_number: string;
  first_visit: string;
  referred_by: string;
  referred_date: string;
  patient_code?: string;
  comments?: string;
  team_email?: string;
  reminder_email?: string;
  created_at: string;
  study_name: string;
  patient_name: string;
  study_status: string;
}

export default function StudiesSection() {
  const { data: currentUser } = useCurrentUserQuery();
  const patientUuid = (currentUser as any)?.patient_uuid || "";
  const { data: enrollments, isLoading: isLoadingEnrollments } = useStudyEnrollmentsByPatientQuery(patientUuid);
  usePatientsQuery();

  const [filteredEnrollments, setFilteredEnrollments] = useState<Enrollment[]>([]);

  // Define columns for the enrollments DataTable
  const enrollmentColumns = [
    {
      key: "study_name" as keyof Enrollment,
      header: "Study Name",
    },
    {
      key: "patient_code" as keyof Enrollment,
      header: "Patient ID",
      render: (value: any) => {
        const patientCode = value as string | undefined;
        return patientCode || "N/A";
      },
    },
    {
      key: "study_status" as keyof Enrollment,
      header: "Status",
      render: (value: any) => {
        const status = value as string;
        return (
          <span className={`status-badge status-${status?.toLowerCase()}`}>
            {status || "N/A"}
          </span>
        );
      },
    },
    {
      key: "first_visit" as keyof Enrollment,
      header: "First Visit Date",
      render: (value: any) => {
        const date = value as string;
        return new Date(date).toLocaleDateString();
      },
    },
    {
      key: "referred_by" as keyof Enrollment,
      header: "Referred By",
      render: (value: any) => {
        const referredBy = value as string | undefined;
        return referredBy || "N/A";
      },
    },
    {
      key: "referred_date" as keyof Enrollment,
      header: "Referred Date",
      render: (value: any) => {
        const date = value as string;
        return new Date(date).toLocaleDateString();
      },
    },
    {
      key: "created_at" as keyof Enrollment,
      header: "Enrollment Date",
      render: (value: any) => {
        const date = value as string;
        return new Date(date).toLocaleDateString();
      },
    },
  ];

  return (
    <>
      <Wrapper>
        <Preloader />

        <div className="content-wrapper js-content-wrapper">
          <div className="bg-light-4 px-3 py-5">
            <div className="container-fluid py-6 px-6">
              <div className="patient-details-container">
                <div className="patient-details-header">
                  <h1 className="page-title">Patient Studies</h1>
                  <p className="page-description">Manage your studies and visits.</p>
                </div>

                {isLoadingEnrollments ? (
                  <div className="loading-studies">Loading enrollments...</div>
                ) : enrollments && enrollments.length > 0 ? (
                  <div>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      marginBottom: '20px'
                    }}>
                      <h3 style={{ margin: 0, fontSize: '18px', fontWeight: 600 }}></h3>
                      <div style={{ width: '300px' }}>
                        <DataTableSearch
                          data={enrollments}
                          onFilter={setFilteredEnrollments}
                          placeholder="Search enrollments..."
                        />
                      </div>
                    </div>
                    
                    <DataTable 
                      data={filteredEnrollments.length > 0 ? filteredEnrollments : enrollments}
                      columns={enrollmentColumns}
                    />
                  </div>
                ) : (
                  <div className="no-studies">
                    You are not currently enrolled in any studies.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </Wrapper>
    </>
  );
}
