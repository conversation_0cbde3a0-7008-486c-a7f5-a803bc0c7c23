.main-box2 {
  display: flex;
  flex-direction: column;
  width: 100%;
  transition: all 0.3s ease;
}

.main-box2:hover {
  /* Removed translateY effect */
}

.tier2-box {
  background-color: #ffffff;
  height: 180px;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border: 1px solid #e9ecef;
  margin-bottom: 15px;
}

.box-icon2 {
  width: 80px;
  height: 80px;
  object-fit: contain;
  transition: transform 0.3s ease;
}

.main-box2:hover .box-icon2 {
  transform: scale(1.1);
}

.box-icon2-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 80px;
  height: 80px;
  font-size: 60px;
  color: #3dc6d6;
  transition: transform 0.3s ease;
}

.main-box2:hover .box-icon2-container {
  transform: scale(1.1);
}

.all-titles {
  text-align: center;
  padding: 0 10px;
}

.box-title2 {
  color: #140342;
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
  margin-bottom: 5px;
}

.box-sub2 {
  color: #4f547b;
  font-size: 14px;
  font-weight: 400;
  margin: 0;
}

/* Medium screens */
@media only screen and (max-width: 992px) {
  .main-box2 {
    width: calc(50% - 15px);
  }
  
  .tier2-box {
    height: 160px;
  }
}

/* Small screens */
@media only screen and (max-width: 768px) {
  .main-box2 {
    width: 100%;
  }
}

/* Extra small screens */
@media only screen and (max-width: 576px) {
  .tier2-box {
    height: 140px;
    padding: 15px;
  }
  
  .box-icon2, .box-icon2-container {
    width: 60px;
    height: 60px;
  }
  
  .box-icon2-container {
    font-size: 40px;
  }
  
  .box-title2 {
    font-size: 16px;
  }
  
  .box-sub2 {
    font-size: 12px;
  }
}
