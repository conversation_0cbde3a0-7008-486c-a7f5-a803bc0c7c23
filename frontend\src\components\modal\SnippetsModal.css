.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(3px);
}

.snippets-modal-content {
  width: 800px;
  max-height: 80vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
  position: relative;
  padding: 24px;
  display: flex;
  flex-direction: column;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.paragraph-content {
  display: flex;
  align-items: center;
  background: rgba(71, 200, 57, 0.15);
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border-left: 4px solid rgba(32, 110, 18, 0.7);
}

.paragraph-content p {
  margin: 0;
  font-size: 14px;
  color: #204e12;
  display: flex;
  align-items: center;
  line-height: 1.5;
}

.add-snippet-button {
  align-self: flex-end;
  margin-bottom: 20px;
  padding: 10px 18px;
  background-color: #37b7c3;
  color: white;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  border-radius: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(55, 183, 195, 0.3);
}

.add-snippet-button:hover {
  background-color: #2da8b4;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(55, 183, 195, 0.4);
}

.snippet-list-container {
  flex-grow: 1;
  overflow-y: auto;
  margin-bottom: 20px;
  border-radius: 8px;
  border: 1px solid #eaeaea;
  padding: 0 10px;
  background-color: #f9f9f9;
}

.snippet-list-title {
  font-size: 18px;
  font-weight: 600;
  margin: 15px 0;
  color: #333;
  padding-left: 10px;
}

.snippet-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.snippet-item {
  display: flex;
  flex-direction: column;
  padding: 12px;
  border-bottom: 1px solid #eaeaea;
  background-color: white;
  margin-bottom: 10px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.snippet-item:hover {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.snippet-item:last-child {
  margin-bottom: 15px;
}

.snippet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
  color: #333;
}

.snippet-details {
  margin-top: 12px;
  padding: 10px;
  background-color: #f5f5f5;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.5;
}

.snippet-details p {
  margin: 8px 0;
}

.snippet-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.snippet-actions .copy-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  background-color: #e6f7f9;
  color: #37b7c3;
  border: 1px solid #b7e8ed;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.snippet-actions .copy-button:hover {
  background-color: #d0f0f3;
  color: #2da8b4;
}

.snippet-list-container::-webkit-scrollbar {
  width: 8px;
}

.snippet-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.snippet-list-container::-webkit-scrollbar-thumb {
  background: #37b7c3;
  border-radius: 4px;
}

.snippet-list-container::-webkit-scrollbar-thumb:hover {
  background: #2d919a;
}

.close-button {
  padding: 10px 20px;
  background-color: #f0f0f0;
  color: #555;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-left: auto;
}

.close-button:hover {
  background-color: #e0e0e0;
  color: #333;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.SquareX {
  background-color: #ffebee !important;
  border-radius: 5px;
  border: 1px solid #ffcdd2;
  color: #d32f2f;
  padding: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.SquareX:hover {
  background-color: #ffcdd2 !important;
  transform: translateY(-2px);
}

.SquarePen {
  background-color: #e1f5fe !important;
  border-radius: 5px;
  border: 1px solid #b3e5fc;
  color: #0288d1;
  padding: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.SquarePen:hover {
  background-color: #b3e5fc !important;
  transform: translateY(-2px);
}
