.patient-details-container {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

/* Patient Summary Cards Styles */
.patient-summary-cards {
  margin-bottom: 32px;
}

.summary-card {
  background: var(--color-white);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(55, 183, 195, 0.06);
  border: 1px solid var(--color-light-2);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  overflow: hidden;
  height: 100%;
}

.summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  transition: all 0.3s ease;
}

.summary-card.allergies-card::before {
  background: linear-gradient(90deg, var(--color-red-3) 0%, var(--color-red-1) 100%);
}

.summary-card.warnings-card::before {
  background: linear-gradient(90deg, var(--color-orange-4) 0%, var(--color-orange-1) 100%);
}

.summary-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(55, 183, 195, 0.12);
}

.summary-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px 16px 24px;
  border-bottom: 1px solid var(--color-light-2);
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
}

.summary-card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: var(--text-18);
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0;
  line-height: 1.3;
}

.summary-card-title i {
  font-size: var(--text-20);
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.allergies-card .summary-card-title i {
  background: linear-gradient(135deg, var(--color-red-2) 0%, var(--color-red-4) 100%);
  color: var(--color-red-1);
}

.warnings-card .summary-card-title i {
  background: linear-gradient(135deg, var(--color-orange-2) 0%, var(--color-orange-4) 100%);
  color: var(--color-orange-1);
}

.summary-card-edit-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  color: var(--color-white);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: var(--text-14);
  position: relative;
  overflow: hidden;
}

.summary-card-edit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.summary-card-edit-btn:hover::before {
  left: 100%;
}

.summary-card-edit-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(55, 183, 195, 0.3);
}

.summary-card-content {
  padding: 20px 24px 24px 24px;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.summary-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  font-size: var(--text-15);
  color: var(--color-light-1);
  font-weight: 500;
  position: relative;
}

.summary-loading::after {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-light-2);
  border-top: 2px solid var(--color-purple-1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.summary-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.summary-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
  border-radius: 8px;
  border-left: 3px solid var(--color-purple-1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-size: var(--text-14);
  font-weight: 500;
  color: var(--color-dark-1);
}

.summary-list-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(55, 183, 195, 0.02) 100%);
  pointer-events: none;
}

.summary-list-item:hover {
  transform: translateX(4px);
  border-left-color: var(--color-blue-1);
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-light-4) 100%);
}

.warning-name {
  flex: 1;
  font-weight: 600;
  color: var(--color-dark-1);
}

.warning-severity {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--text-11);
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-left: 12px;
}

.warning-severity.severity-low {
  background: linear-gradient(135deg, var(--color-blue-2) 0%, var(--color-blue-6) 100%);
  color: var(--color-blue-3);
  border: 1px solid var(--color-blue-4);
}

.warning-severity.severity-medium {
  background: linear-gradient(135deg, var(--color-yellow-2) 0%, var(--color-yellow-5) 100%);
  color: var(--color-yellow-1);
  border: 1px solid var(--color-yellow-3);
}

.warning-severity.severity-high {
  background: linear-gradient(135deg, var(--color-orange-2) 0%, var(--color-orange-5) 100%);
  color: var(--color-orange-1);
  border: 1px solid var(--color-orange-4);
}

.warning-severity.severity-critical {
  background: linear-gradient(135deg, var(--color-red-2) 0%, var(--color-error-1) 100%);
  color: var(--color-red-1);
  border: 1px solid var(--color-red-3);
}

.summary-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
  border-radius: 12px;
  border: 2px dashed var(--color-light-2);
  color: var(--color-light-1);
  font-size: var(--text-15);
  font-weight: 500;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.summary-empty::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(55, 183, 195, 0.02) 100%);
  pointer-events: none;
}

.patient-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--color-light-2);
}

.page-title {
  font-size: var(--text-35);
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0;
  line-height: 1.2;
}

.patient-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.patient-details-card {
  background: var(--color-white);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(55, 183, 195, 0.08);
  padding: 40px;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  border: 1px solid var(--color-light-2);
  position: relative;
  overflow: hidden;
}

.patient-details-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
}

.patient-details-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(55, 183, 195, 0.12);
}

.patient-info-section {
  display: flex;
  flex-direction: column;
  gap: 28px;
}

.patient-info-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  position: relative;
}

.info-label {
  font-size: var(--text-13);
  font-weight: 600;
  color: var(--color-light-1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.info-value {
  font-size: var(--text-16);
  font-weight: 500;
  color: var(--color-dark-1);
  padding: 16px 20px;
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-3) 100%);
  border-radius: 12px;
  border-left: 4px solid var(--color-purple-1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.info-value::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(55, 183, 195, 0.02) 100%);
  pointer-events: none;
}

.info-value:hover {
  transform: translateX(2px);
  border-left-color: var(--color-blue-1);
  background: linear-gradient(135deg, var(--color-light-3) 0%, var(--color-light-4) 100%);
}

/* Medical History Styles */
.section-title {
  font-size: var(--text-24);
  font-weight: 700;
  color: var(--color-dark-1);
  margin: 0;
  position: relative;
  padding-bottom: 12px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 2px;
}

.medical-history-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 24px;
}

.medical-history-item {
  background: var(--color-white);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(55, 183, 195, 0.06);
  border: 1px solid var(--color-light-2);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  overflow: hidden;
  position: relative;
}

.medical-history-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--color-green-4) 0%, var(--color-blue-1) 100%);
}

.medical-history-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 32px rgba(55, 183, 195, 0.12);
}

.conditions-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 0;
}

.condition-item {
  padding: 24px;
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
  border-radius: 12px;
  border-left: 4px solid var(--color-purple-1);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.condition-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, transparent 0%, rgba(55, 183, 195, 0.02) 100%);
  pointer-events: none;
}

.condition-item:hover {
  transform: translateX(4px);
  border-left-color: var(--color-blue-1);
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-light-4) 100%);
}

.medical-history-info {
  margin-bottom: 16px;
}

.condition-title {
  font-size: var(--text-20);
  font-weight: 700;
  color: var(--color-dark-1);
  margin-bottom: 8px;
  line-height: 1.3;
}

.diagnosis-date {
  font-size: var(--text-14);
  color: var(--color-light-1);
  font-weight: 500;
  margin: 0;
}

.medical-history-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.medical-history-details p {
  margin: 0;
  font-size: var(--text-15);
  color: var(--color-dark-1);
  font-weight: 500;
}

.medical-history-details strong {
  color: var(--color-dark-1);
  font-weight: 600;
}

/* Enhanced Status Badge Styles */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: var(--text-13);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.status-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s ease;
}

.status-badge:hover::before {
  left: 100%;
}

.status-resolved {
  background: linear-gradient(135deg, var(--color-green-2) 0%, var(--color-green-3) 100%);
  color: var(--color-green-5);
  border: 1px solid var(--color-green-4);
}

.status-active {
  background: linear-gradient(135deg, var(--color-orange-2) 0%, var(--color-orange-3) 100%);
  color: var(--color-orange-1);
  border: 1px solid var(--color-orange-4);
}

.status-current {
  background: linear-gradient(135deg, var(--color-blue-2) 0%, var(--color-blue-6) 100%);
  color: var(--color-blue-3);
  border: 1px solid var(--color-blue-4);
}

.condition-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid var(--color-light-2);
}

.medical-history-meta {
  padding: 16px 24px;
  background: var(--color-light-4);
  border-top: 1px solid var(--color-light-2);
  font-size: var(--text-13);
  color: var(--color-light-1);
  font-style: italic;
}

.no-medical-history {
  text-align: center;
  padding: 60px 40px;
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
  border-radius: 16px;
  border: 2px dashed var(--color-light-2);
  color: var(--color-light-1);
  font-size: var(--text-16);
  font-weight: 500;
}

.no-conditions {
  text-align: center;
  padding: 40px 24px;
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
  border-radius: 12px;
  border: 1px dashed var(--color-light-2);
  color: var(--color-light-1);
  font-size: var(--text-15);
  font-weight: 500;
}

/* Modal Form Styles */
.medical-history-modal-form {
  padding: 8px 0;
}

.medical-history-modal-form .form-group {
  margin-bottom: 24px;
}

.medical-history-modal-form .form-label {
  display: block;
  margin-bottom: 8px;
  font-size: var(--text-14);
  font-weight: 600;
  color: var(--color-dark-1);
}

.medical-history-modal-form .form-control {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid var(--color-light-2);
  border-radius: 8px;
  font-size: var(--text-15);
  color: var(--color-dark-1);
  background-color: var(--color-white);
  transition: all 0.3s ease;
}

.medical-history-modal-form .form-control:focus {
  border-color: var(--color-purple-1);
  outline: none;
  box-shadow: 0 0 0 3px rgba(55, 183, 195, 0.1);
  background-color: var(--color-light-6);
}

.medical-history-modal-form .form-control::placeholder {
  color: var(--color-light-1);
  font-weight: 400;
}

.modal-form-actions {
  background: var(--color-light-4);
  margin: 0 -24px -24px -24px;
  padding: 20px 24px;
  border-radius: 0 0 12px 12px;
}

.text-danger {
  color: var(--color-red-1) !important;
}

/* Edit Form Styles */
.edit-form {
  background: var(--color-white);
  padding: 24px;
  border-radius: 12px;
  border: 2px solid var(--color-purple-1);
  box-shadow: 0 4px 20px rgba(55, 183, 195, 0.1);
}

.edit-form .form-group {
  margin-bottom: 20px;
}

.edit-form .form-control {
  padding: 10px 14px;
  border: 1px solid var(--color-light-2);
  border-radius: 6px;
  font-size: var(--text-14);
  transition: all 0.3s ease;
}

.edit-form .form-control:focus {
  border-color: var(--color-purple-1);
  box-shadow: 0 0 0 2px rgba(55, 183, 195, 0.1);
}

/* Enhanced Patient Warning Modal Form Styles */
.patient-warning-modal-form {
  padding: 16px 0;
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-white) 100%);
  border-radius: 16px;
  position: relative;
  overflow: hidden;
}

.patient-warning-modal-form::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
}

.patient-warning-modal-form .form-group {
  margin-bottom: 28px;
  position: relative;
}

.patient-warning-modal-form .form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: var(--text-15);
  font-weight: 700;
  color: var(--color-dark-1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.patient-warning-modal-form .form-label::before {
  content: '';
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-radius: 2px;
}

.patient-warning-modal-form .form-control {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid var(--color-light-2);
  border-radius: 12px;
  font-size: var(--text-16);
  color: var(--color-dark-1);
  background: linear-gradient(135deg, var(--color-white) 0%, var(--color-light-6) 100%);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  position: relative;
  box-shadow: 0 2px 8px rgba(55, 183, 195, 0.04);
}

.patient-warning-modal-form .form-control:focus {
  border-color: var(--color-purple-1);
  outline: none;
  box-shadow: 0 0 0 4px rgba(55, 183, 195, 0.15), 0 4px 16px rgba(55, 183, 195, 0.1);
  background: var(--color-white);
  transform: translateY(-2px);
}

.patient-warning-modal-form .form-control::placeholder {
  color: var(--color-light-1);
  font-weight: 500;
  font-style: italic;
}

.patient-warning-modal-form textarea.form-control {
  resize: vertical;
  min-height: 120px;
  line-height: 1.6;
  font-family: inherit;
}

.patient-warning-modal-form select.form-control {
  cursor: pointer;
  background-image: linear-gradient(45deg, transparent 50%, var(--color-purple-1) 50%), 
                    linear-gradient(135deg, var(--color-purple-1) 50%, transparent 50%);
  background-position: calc(100% - 20px) calc(1em + 2px), calc(100% - 15px) calc(1em + 2px);
  background-size: 5px 5px, 5px 5px;
  background-repeat: no-repeat;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

.patient-warning-modal-form select.form-control:focus {
  background-image: linear-gradient(45deg, transparent 50%, var(--color-blue-1) 50%), 
                    linear-gradient(135deg, var(--color-blue-1) 50%, transparent 50%);
}

/* Enhanced Severity Select Styling */
.severity-select-wrapper {
  position: relative;
}

.severity-select-wrapper::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 8px solid var(--color-purple-1);
  pointer-events: none;
  transition: all 0.3s ease;
}

.severity-select-wrapper:hover::after {
  border-top-color: var(--color-blue-1);
}

/* Enhanced Checkbox Styling */
.patient-warning-modal-form .form-check {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
  border-radius: 16px;
  border: 2px solid var(--color-light-2);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.patient-warning-modal-form .form-check::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(55, 183, 195, 0.1), transparent);
  transition: left 0.6s ease;
}

.patient-warning-modal-form .form-check:hover {
  background: linear-gradient(135deg, var(--color-light-6) 0%, var(--color-light-4) 100%);
  border-color: var(--color-purple-1);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(55, 183, 195, 0.1);
}

.patient-warning-modal-form .form-check:hover::before {
  left: 100%;
}

.patient-warning-modal-form .form-check-input {
  width: 24px;
  height: 24px;
  border: 3px solid var(--color-light-2);
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  cursor: pointer;
  position: relative;
  background: var(--color-white);
  flex-shrink: 0;
}

.patient-warning-modal-form .form-check-input:checked {
  background: linear-gradient(135deg, var(--color-purple-1) 0%, var(--color-blue-1) 100%);
  border-color: var(--color-purple-1);
  transform: scale(1.1);
}

.patient-warning-modal-form .form-check-input:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--color-white);
  font-size: var(--text-12);
  font-weight: 700;
}

.patient-warning-modal-form .form-check-label {
  font-size: var(--text-16);
  font-weight: 600;
  color: var(--color-dark-1);
  cursor: pointer;
  user-select: none;
  line-height: 1.4;
  flex: 1;
}

/* Enhanced Modal Actions */
.modal-form-actions {
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
  margin: 24px -24px -24px -24px;
  padding: 24px;
  border-radius: 0 0 16px 16px;
  border-top: 2px solid var(--color-light-2);
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  position: relative;
}

.patient-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  border-bottom: 1px solid #E0E5F2;
  padding-bottom: 10px;
}

.tab-button {
  padding: 8px 16px;
  border: none;
  background: none;
  color: #6c757d;
  font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.2s;
}

.tab-button:hover {
  background-color: #f8f9fa;
  color: #2B3674;
}

.tab-button.active {
  color: #37b7c3;
  font-weight: 500;
  border-bottom: 2px solid #37b7c3;
}

.tab-content {
  padding: 24px;
  background: var(--color-white);
  border-radius: 0 0 16px 16px;
  border: 1px solid var(--color-light-2);
  border-top: none;
}

/* Profile Picture Styles */
.patient-profile-picture-section {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
}

.profile-picture-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.profile-picture-wrapper {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
  border: 3px solid var(--color-purple-1);
  transition: all 0.3s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: 0 8px 32px rgba(55, 183, 195, 0.15);
}

.profile-picture-wrapper:hover {
  transform: scale(1.05);
  box-shadow: 0 12px 48px rgba(55, 183, 195, 0.25);
  border-color: var(--color-blue-1);
}

.profile-picture {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.profile-picture-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  color: var(--color-light-1);
  background: linear-gradient(135deg, var(--color-light-4) 0%, var(--color-light-6) 100%);
  transition: all 0.3s ease;
}

.profile-picture-placeholder.hidden {
  display: none;
}

.profile-picture-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(55, 183, 195, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--color-white);
  opacity: 0;
  transition: all 0.3s ease;
  font-size: var(--text-12);
  font-weight: 600;
  text-align: center;
  gap: 8px;
}

.profile-picture-wrapper:hover .profile-picture-overlay {
  opacity: 1;
}

.profile-picture-wrapper:hover .profile-picture,
.profile-picture-wrapper:hover .profile-picture-placeholder {
  transform: scale(1.1);
}

.profile-picture-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  font-size: var(--text-14);
  color: var(--color-purple-1);
  font-weight: 500;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--color-light-2);
  border-top: 2px solid var(--color-purple-1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
